{"version": "2.0.0", "tasks": [{"label": "Build APK - Development", "command": "flutter build apk --flavor development --dart-define-from-file=env.json", "type": "shell", "args": [], "presentation": {"reveal": "always"}, "group": "build"}, {"label": "Build APK - Production", "command": "flutter build apk --flavor production --dart-define-from-file=env.prod.json", "type": "shell", "args": [], "presentation": {"reveal": "always"}, "group": "build"}]}