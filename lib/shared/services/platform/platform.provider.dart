import 'package:flutter/cupertino.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/services/platform/platform.service.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:rxdart/rxdart.dart';

class PlatformProvider with ChangeNotifier {
  List<PlatformKeys> messagingPlatformsKeys = [
    PlatformKeys.instagram,
    PlatformKeys.facebook,
  ];
  List<PlatformKeys> socialNetworkPlatformsKeys = [
    PlatformKeys.facebook,
    PlatformKeys.instagram,
    PlatformKeys.mapstr,
  ];
  List<PlatformKeys> reviewsPlatformsKeys = PlatformKeys.values
      .where(
          (key) => key != PlatformKeys.instagram && key != PlatformKeys.manual)
      .toList();
  RestaurantProvider? restaurantProvider;

  PlatformProvider({this.restaurantProvider});

  BehaviorSubject<List<Platform>> connectedSocialNetworksPlatforms$ =
      BehaviorSubject<List<Platform>>.seeded([]);

  BehaviorSubject<List<Platform>> connectedPostsPlatforms$ =
      BehaviorSubject<List<Platform>>.seeded([]);

  BehaviorSubject<List<Platform>> platforms$ =
      BehaviorSubject<List<Platform>>.seeded([]);
  BehaviorSubject<List<Platform>> connectedMessagingPlatforms$ =
      BehaviorSubject<List<Platform>>.seeded([]);
  BehaviorSubject<List<Platform>> connectedReviewsPlatforms$ =
      BehaviorSubject<List<Platform>>.seeded([]);

  setPlatforms(List<Platform> pls) {
    platforms$.add(pls);

    connectedMessagingPlatforms$.add(pls
        .where((element) =>
            messagingPlatformsKeys.contains(parsePlatformKey(element.key)))
        .toList());
    connectedSocialNetworksPlatforms$.add(pls
        .where((element) =>
            socialNetworkPlatformsKeys.map((e) => e.name).contains(element.key))
        .toList());

    connectedPostsPlatforms$.add([
      ...connectedSocialNetworksPlatforms$.value,
      ...pls.where((element) => element.key == PlatformKeys.gmb.name)
    ]);
    connectedReviewsPlatforms$.add(pls
        .where((element) =>
            reviewsPlatformsKeys.contains(parsePlatformKey(element.key)))
        .toList());

    notifyListeners();
  }

  Future<List<Platform>> getPlatformsForRestaurant(String restaurantId) async {
    try {
      final res = await PlatformService.getPlatformsForRestaurant(restaurantId);
      final supportedPlatforms = [
        PlatformKeys.facebook.name,
        PlatformKeys.instagram.name,
        PlatformKeys.gmb.name,
      ];
      final platforms =
          res.data?.where((p) => supportedPlatforms.contains(p.key)).toList();

      for (Platform platform in platforms ?? []) {
        final result = await PlatformService.getPlatformPermission(platform.id);
        platform.isValid = result.data?.isValid ?? false;
      }

      if (res.error) {
        setPlatforms([]);
        throw Exception(res.message);
      }
      setPlatforms(platforms ?? []);
      return platforms ?? [];
    } on Exception {
      rethrow;
    }
  }
}
