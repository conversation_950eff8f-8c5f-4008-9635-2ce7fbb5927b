import 'package:malou/app_config.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/helpers/http_client.dart';
import 'package:malou/shared/models/platform.model.dart';

final apiUrl = '${AppConfig.shared.baseUrl}/platforms';
final httpClient = DioClient.instance.client;

class PlatformService {
  static Future<ApiResult<List<Platform>>> getPlatformsForRestaurant(
      String restaurantId) async {
    try {
      final response =
          await httpClient.get('$apiUrl/restaurants/$restaurantId');
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        ApiResult<List<Platform>> result = ApiResult.fromJson(responseData,
            fromJsonT: (json) => Platform.fromJsonList(json));
        return result;
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  static Future<ApiResult<RestaurantPermissionDto>> getPlatformPermission(
      String platformId) async {
    try {
      final response = await httpClient.get(
          '${AppConfig.shared.baseUrl}/credentials/permissions/?platform_id=$platformId');
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        ApiResult<RestaurantPermissionDto> result =
            ApiResult.fromJson(responseData, fromJsonT: (data) {
          return (isValid: data['isValid'] as bool);
        });
        return result;
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }
}

typedef RestaurantPermissionDto = ({bool isValid});
