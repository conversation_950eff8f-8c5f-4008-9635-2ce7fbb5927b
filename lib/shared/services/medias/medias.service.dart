import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:malou/app_config.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/helpers/http_client.dart';
import 'package:malou/shared/helpers/pagination.model.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:photo_manager/photo_manager.dart';

final apiUrl = '${AppConfig.shared.baseUrl}/media';
final processingMediaApiUrl = '${AppConfig.shared.baseUrl}/processing-medias';
final httpClient = DioClient.instance.client;

class MediasService {
  static Future<ApiResult<List<Media>>> getMedias({
    required String restaurantId,
    required Pagination pagination,
    MediaType mediaType = MediaType.all,
    bool neverPostedOnly = false,
    String? searchText,
    String? folderId,
  }) async {
    try {
      final response = await httpClient.get(
        '$apiUrl/restaurants/$restaurantId',
        queryParameters: {
          ...pagination.toSnakeCaseJson(),
          'media_type': mediaType.name,
          'is_never_used': neverPostedOnly,
          'title': searchText ?? '',
          'folder_id': folderId ??
              "null", // We replace null by "null" because the validator takes only a string value which can be "objectId" or "null"
        },
      );
      Map<String, dynamic> responseData = response.data;
      var result = ApiResult<List<Media>>.fromJson(responseData,
          fromJsonT: (json) => Media.fromJsonList(json['medias']));
      return result;
    } catch (e) {
      return ApiResult.error(e.toString());
    }
  }

  static Future<ApiResult<Media>> uploadAssetEntity(
      {required String restaurantId,
      required AssetEntity assetEntity,
      String category = 'additional'}) async {
    try {
      // convert asset entities to files
      File assetEntityFile = await assetEntity.file as File;
      MultipartFile file = MultipartFile.fromFileSync(
        assetEntityFile.path,
        filename: assetEntityFile.path.split('/').last,
      );
      final data = FormData.fromMap({
        'media': file,
      });

      data.fields.add(MapEntry(
          'metadata',
          jsonEncode([
            {
              'title': assetEntity.title,
              'restaurantId': restaurantId,
              'category': category,
              'width': assetEntity.width,
              'height': assetEntity.height,
            }
          ])));

      // send request
      final response = await httpClient.post(
        '$apiUrl/',
        data: data,
        queryParameters: {
          'restaurant_id': restaurantId,
        },
      );
      Map<String, dynamic> responseData = response.data;
      var result = ApiResult<Media>.fromJson(responseData,
          fromJsonT: (json) => Media.fromJson(json[0]));
      return result;
    } catch (e) {
      return ApiResult.error(e.toString());
    }
  }

  Future<ApiResult<Media>> uploadAssetEntityV2(
      {required String restaurantId,
      required AssetEntity assetEntity,
      String category = 'additional'}) async {
    try {
      // convert asset entities to files
      File assetEntityFile = await assetEntity.file as File;
      MultipartFile file = MultipartFile.fromFileSync(
        assetEntityFile.path,
        filename: assetEntityFile.path.split('/').last,
      );
      final data = FormData.fromMap({
        'media': file,
      });

      data.fields.add(MapEntry(
          'metadata',
          jsonEncode([
            {
              'title': assetEntity.title,
              'restaurantId': restaurantId,
              'category': category,
              'width': assetEntity.width,
              'height': assetEntity.height,
            }
          ])));

      // send request
      final response = await httpClient.post(
        '$apiUrl/upload-v2',
        data: data,
        queryParameters: {
          'restaurantId': restaurantId,
        },
      );
      Map<String, dynamic> responseData = response.data["data"];

      String processingMediaId = responseData['processingMediaId'];

      String processingMediaStatus = 'IN_PROGRESS';
      dynamic processingMediaData;

      do {
        await Future.delayed(const Duration(seconds: 1));
        var processingMediaResponse = await httpClient.get(
          '$processingMediaApiUrl/$processingMediaId',
        );
        processingMediaData = processingMediaResponse.data["data"];
        processingMediaStatus = processingMediaData['status'];
      } while (processingMediaStatus == 'IN_PROGRESS');

      if (processingMediaStatus == 'ERROR') {
        throw Exception('Upload failed');
      }

      var mediaId = processingMediaData['mediaId'];

      if (mediaId == null) {
        throw Exception('Upload failed');
      }

      var mediaResult = await getMediumById(mediaId);
      return mediaResult;
    } catch (e) {
      return ApiResult.error(e.toString());
    }
  }

  Future<ApiResult<Media>> uploadFile(
      {required String restaurantId,
      required Uint8List fileData,
      String category = 'additional'}) async {
    try {
      MultipartFile file = MultipartFile.fromBytes(fileData);

      // send request
      final response = await httpClient.post(
        '$apiUrl/',
        data: FormData.fromMap({
          'media': file,
        }),
        queryParameters: {
          'restaurant_id': restaurantId,
          'category': category,
        },
      );

      Map<String, dynamic> responseData = response.data;
      var result = ApiResult<Media>.fromJson(responseData,
          fromJsonT: (json) => Media.fromJson(json));
      return result;
    } catch (e) {
      return ApiResult.error(e.toString());
    }
  }

  Future<ApiResult<void>> fetchMediaDescription(
      {required String mediaId}) async {
    final response = await httpClient.get(
      '$apiUrl/fetch-description',
      queryParameters: {
        'media_ids': [mediaId, mediaId],
      },
    );

    Map<String, dynamic> responseData = response.data;
    var result =
        ApiResult<void>.fromJson(responseData, fromJsonT: (json) => json);
    return result;
  }

  Future<ApiResult<Media>> getMediumById(String mediumId) async {
    final response = await httpClient.get(
      '$apiUrl/$mediumId',
    );

    Map<String, dynamic> responseData = response.data;
    var result = ApiResult<Media>.fromJson(
      responseData,
      fromJsonT: (json) => Media.fromJson(json),
    );
    return result;
  }
}
