import 'dart:typed_data';

import 'package:flutter/cupertino.dart';
import 'package:malou/shared/helpers/pagination.model.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/services/medias/medias.service.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:photo_manager/photo_manager.dart';

class MediasProvider extends ChangeNotifier {
  RestaurantProvider restaurantProvider;
  MediasService mediasService;
  MediasProvider(
      {RestaurantProvider? restaurantProvider, MediasService? mediasService})
      : restaurantProvider = restaurantProvider ?? RestaurantProvider(),
        mediasService = mediasService ?? MediasService();

  Future<List<Media>> getRestaurantMedias(int pageNumber,
      {MediaType mediaType = MediaType.all}) async {
    try {
      final res = await MediasService.getMedias(
          restaurantId: restaurantProvider.currentRestaurant!.id,
          pagination:
              Pagination(pageNumber: pageNumber, pageSize: 30, total: null),
          mediaType: mediaType);
      if (res.error) {
        throw Exception(res.message);
      }

      return res.data ?? [];
    } on Exception {
      rethrow;
    }
  }

  Future<Media> uploadDeviceMedia(
      String restaurantId, AssetEntity asset) async {
    try {
      final res = await MediasService.uploadAssetEntity(
          restaurantId: restaurantId, assetEntity: asset);
      if (res.error) {
        throw Exception(res.message);
      }

      return res.data!;
    } on Exception {
      rethrow;
    }
  }

  Future<Media> uploadFile(String restaurantId, Uint8List fileData) async {
    try {
      final res = await mediasService.uploadFile(
          restaurantId: restaurantId, fileData: fileData);
      if (res.error) {
        throw Exception(res.message);
      }

      return res.data!;
    } on Exception {
      rethrow;
    }
  }
}
