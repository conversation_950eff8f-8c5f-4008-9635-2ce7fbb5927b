import 'package:flutter/material.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/models/feedback.model.dart' as feedback_model;
import 'package:malou/shared/models/user.model.dart';
import 'package:malou/shared/services/feedbacks/feedbacks.service.dart';

class FeedbacksProvider extends ChangeNotifier {
  final String AT = '@';
  final String USER_TAGGED = '{USER}';
  FeedbacksService feedbacksService;

  FeedbacksProvider({
    FeedbacksService? feedbacksService,
  }) : feedbacksService = feedbacksService ?? FeedbacksService();

  Future<feedback_model.Feedback?> getFeedbackById(
      String feedbackId, String restaurantId) async {
    ApiResult<feedback_model.Feedback?> res =
        await feedbacksService.getFeedbackById(feedbackId, restaurantId);
    return res.data;
  }

  Future<feedback_model.Feedback> createFeedback(String postId) async {
    ApiResult<feedback_model.Feedback?> res =
        await feedbacksService.createFeedback(postId);
    return res.data!;
  }

  Future<feedback_model.FeedbackMessage> createFeedbackMessage(
      CreateFeedbackMessageParams params) async {
    CreateFeedbackMessageParams newParams = CreateFeedbackMessageParams(
        feedbackId: params.feedbackId,
        restaurantId: params.restaurantId,
        text: mapTextBeforeSend(params.text, params.managersPattern),
        type: params.type,
        visibility: params.visibility,
        author: params.author,
        createdAt: params.createdAt,
        managersPattern: params.managersPattern,
        managers: getTaggedManagersFromText(params.managers, params.text));

    ApiResult<feedback_model.Feedback> res =
        await feedbacksService.createFeedbackMessage(newParams);
    return res.data!.feedbackMessages.last;
  }

  Future<feedback_model.FeedbackMessage?> markFeedbackConversation(
      MarkFeedbackConversationParams params) async {
    if (params.type == feedback_model.FeedbackMessageType.reopen) {
      await feedbacksService.updateFeedback(
          params.feedbackId, params.restaurantId, true);
      await feedbacksService.deleteFeedbackMessage((DeleteFeedbackMessageParams(
        feedbackId: params.feedbackId,
        messageId: params.lastFeedbackMessageId,
        restaurantId: params.restaurantId,
      )));
      return null;
    } else {
      if (params.type == feedback_model.FeedbackMessageType.close) {
        await feedbacksService.updateFeedback(
            params.feedbackId, params.restaurantId, false);
      }
      CreateFeedbackMessageParams newParams = CreateFeedbackMessageParams(
          feedbackId: params.feedbackId,
          restaurantId: params.restaurantId,
          text: mapTextBeforeSend(params.text, params.managersPattern),
          type: params.type,
          visibility: params.visibility,
          author: params.author,
          createdAt: params.createdAt,
          managersPattern: params.managersPattern,
          managers: getTaggedManagersFromText(params.managers, params.text));

      ApiResult<feedback_model.Feedback> res =
          await feedbacksService.createFeedbackMessage(newParams);
      return res.data!.feedbackMessages.last;
    }
  }

  Future<String> deleteFeedbackMessage(
      DeleteFeedbackMessageParams params) async {
    ApiResult<String> res =
        await feedbacksService.deleteFeedbackMessage(params);
    return res.data!;
  }

  Future<feedback_model.FeedbackMessage> updateFeedbackMessage(
      UpdateFeedbackMessageParams params) async {
    UpdateFeedbackMessageParams newParams = UpdateFeedbackMessageParams(
        feedbackId: params.feedbackId,
        messageId: params.messageId,
        restaurantId: params.restaurantId,
        text: mapTextBeforeSend(params.text, params.managersPattern)!,
        visibility: params.visibility,
        managersPattern: params.managersPattern);

    ApiResult<feedback_model.Feedback> res =
        await feedbacksService.updateFeedbackMessage(newParams);
    return res.data!.feedbackMessages.last;
  }

  String? mapTextBeforeSend(String? text, String managersPattern) {
    if (text == null || text.isEmpty) return text;

    return text.replaceAllMapped(
        RegExp(managersPattern, caseSensitive: false, multiLine: true),
        (match) {
      return "$USER_TAGGED${match.group(0)?.substring(AT.length)}";
    });
  }

  String? mapTextBeforeDisplay(String? text, String managersPattern) {
    if (text == null || text.isEmpty) return text;

    return text.replaceAllMapped(
        RegExp(managersPattern, caseSensitive: false, multiLine: true),
        (match) {
      return "$AT${match.group(0)?.substring(USER_TAGGED.length)}";
    });
  }

  List<Manager> getTaggedManagersFromText(
      List<Manager> managers, String? text) {
    if (text == null || text.isEmpty) return [];

    List<Manager> taggedManagers = [];
    String textLowerCase = text.toLowerCase();
    for (var manager in managers) {
      String managerToMatch =
          "$AT${manager.name.toLowerCase()} ${manager.lastname.toLowerCase()}";
      if (textLowerCase.contains(managerToMatch)) {
        taggedManagers.add(manager);
      }
    }
    return taggedManagers;
  }
}
