import 'package:malou/app_config.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/helpers/http_client.dart';
import 'package:malou/shared/models/feedback.model.dart';
import 'package:malou/shared/models/user.model.dart';

final apiUrl = '${AppConfig.shared.baseUrl}/feedbacks';
final httpClient = DioClient.instance.client;

class FeedbacksService {
  Future<ApiResult<Feedback>> getFeedbackById(
      String feedbackId, String restaurantId) async {
    try {
      final response = await httpClient.get("$apiUrl/$feedbackId");
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        return ApiResult<Feedback>.fromJson(responseData,
            fromJsonT: (data) => Feedback.fromJson(data));
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  Future<ApiResult<Feedback>> createFeedback(String postId) async {
    final response = await httpClient.post(apiUrl, data: {
      'postId': postId,
    });
    Map<String, dynamic> responseData = response.data;
    return ApiResult<Feedback>.fromJson(responseData,
        fromJsonT: (data) => Feedback.fromJson(data));
  }

  Future<ApiResult<Feedback>> updateFeedback(
      String feedbackId, String restaurantId, bool isOpen) async {
    final response = await httpClient.put("$apiUrl/$feedbackId", data: {
      'isOpen': isOpen,
    }, queryParameters: {
      'restaurant_id': restaurantId,
    });
    Map<String, dynamic> responseData = response.data;
    return ApiResult<Feedback>.fromJson(responseData,
        fromJsonT: (data) => Feedback.fromJson(data));
  }

  Future<ApiResult<Feedback>> createFeedbackMessage(
      CreateFeedbackMessageParams params) async {
    try {
      final response =
          await httpClient.post("$apiUrl/${params.feedbackId}/messages", data: {
        'message': {
          'text': params.text,
          'type': params.type.name,
          'visibility': params.visibility?.name,
          'author': params.author.toJson(),
          'createdAt': params.createdAt.toIso8601String(),
        },
        'participants': params.managers.map((m) => m.toJson()).toList(),
      }, queryParameters: {
        'restaurant_id': params.restaurantId,
      });
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        return ApiResult<Feedback>.fromJson(responseData,
            fromJsonT: (data) => Feedback.fromJson(data));
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  Future<ApiResult<Feedback>> updateFeedbackMessage(
    UpdateFeedbackMessageParams params,
  ) async {
    try {
      final response = await httpClient.put(
          "$apiUrl/${params.feedbackId}/messages/${params.messageId}",
          data: {
            'message': {
              'text': params.text,
              'visibility': params.visibility.name,
            },
            'participants': [],
          },
          queryParameters: {
            'restaurant_id': params.restaurantId,
          });
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        return ApiResult<Feedback>.fromJson(responseData,
            fromJsonT: (data) => Feedback.fromJson(data));
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  Future<ApiResult<String>> deleteFeedbackMessage(
      DeleteFeedbackMessageParams params) async {
    try {
      final response = await httpClient.delete(
          "$apiUrl/${params.feedbackId}/messages/${params.messageId}",
          queryParameters: {
            'restaurant_id': params.restaurantId,
            'send_fake_opened_email': false
          });
      if (response.statusCode == 200) {
        return ApiResult(message: '', data: params.messageId);
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }
}

class CreateFeedbackMessageParams {
  String feedbackId;
  String restaurantId;
  String? text;
  Author author;
  FeedbackVisibility? visibility;
  DateTime createdAt;
  FeedbackMessageType type;
  String managersPattern;
  List<Manager> managers;

  CreateFeedbackMessageParams({
    required this.feedbackId,
    required this.restaurantId,
    this.text,
    required this.author,
    this.visibility,
    required this.createdAt,
    required this.type,
    required this.managersPattern,
    required this.managers,
  });
}

class UpdateFeedbackMessageParams {
  String text;
  FeedbackVisibility visibility;
  String feedbackId;
  String restaurantId;
  String messageId;
  String managersPattern;

  UpdateFeedbackMessageParams({
    required this.text,
    required this.visibility,
    required this.feedbackId,
    required this.restaurantId,
    required this.messageId,
    required this.managersPattern,
  });
}

class DeleteFeedbackMessageParams {
  String feedbackId;
  String restaurantId;
  String messageId;

  DeleteFeedbackMessageParams({
    required this.feedbackId,
    required this.restaurantId,
    required this.messageId,
  });
}

class MarkFeedbackConversationParams {
  String feedbackId;
  String restaurantId;
  String lastFeedbackMessageId;
  String? text;
  Author author;
  FeedbackVisibility? visibility;
  DateTime createdAt;
  FeedbackMessageType type;
  String managersPattern;
  List<Manager> managers;

  MarkFeedbackConversationParams({
    required this.feedbackId,
    required this.restaurantId,
    required this.lastFeedbackMessageId,
    this.text,
    required this.author,
    this.visibility,
    required this.createdAt,
    required this.type,
    required this.managersPattern,
    required this.managers,
  });
}
