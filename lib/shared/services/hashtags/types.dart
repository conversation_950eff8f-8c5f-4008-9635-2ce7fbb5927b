import 'package:malou/shared/models/hashtags/hashtag.model.dart';

class GetHashtagsSuggestionPayload {
  String restaurantName;
  String postDescription;
  List<String> favoriteHashtags;
  List<String> restaurantCategory;
  String restaurantLocality;
  String restaurantPostalCode;
  List<String> keywords;

  GetHashtagsSuggestionPayload({
    required this.restaurantName,
    required this.postDescription,
    required this.favoriteHashtags,
    required this.restaurantCategory,
    required this.restaurantLocality,
    required this.restaurantPostalCode,
    required this.keywords,
  });

  Map<String, dynamic> toJson() {
    return {
      'restaurantName': restaurantName,
      'postDescription': postDescription,
      'favoriteHashtags': favoriteHashtags,
      'restaurantCategory': restaurantCategory,
      'restaurantLocality': restaurantLocality,
      'restaurantPostalCode': restaurantPostalCode,
      'keywords': keywords,
    };
  }
}

class GeneratedHashtagDto {
  String text;
  HashtagType type;
  bool isPreSelected;
  GeneratedHashtagDto({
    required this.text,
    required this.type,
    required this.isPreSelected,
  });

  factory GeneratedHashtagDto.fromJson(Map<String, dynamic> json) {
    return GeneratedHashtagDto(
      text: json['text'],
      type: hashtagsValues.map[json['type']]!,
      isPreSelected: json['isPreSelected'],
    );
  }
}

class PostHashtagsGenerationResult {
  List<Hashtag> generatedHashtags;
  List<Hashtag> preSelectedHashtags;

  PostHashtagsGenerationResult({
    required this.generatedHashtags,
    required this.preSelectedHashtags,
  });
}
