import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:malou/shared/models/hashtags/hashtag.model.dart';
import 'package:malou/shared/models/posts/post-hashtags.model.dart';
import 'package:malou/shared/models/restaurant.model.dart';
import 'package:malou/shared/services/hashtags/hashtags.service.dart';
import 'package:malou/shared/services/hashtags/types.dart';
import 'package:objectid/objectid.dart';

import 'package:rxdart/rxdart.dart';

class HashtagsProvider extends ChangeNotifier {
  HashtagsService hashtagsService = HashtagsService();

  HashtagsProvider();

  PostHashtags postHashtags = PostHashtags(selected: [], suggested: []);

  BehaviorSubject<List<Hashtag>> restaurantHashtags$ =
      BehaviorSubject<List<Hashtag>>.seeded([]);

  removeHashtagFromFavorite(Hashtag hashtag) async {
    restaurantHashtags$.add(restaurantHashtags$.value
        .where((element) => element.text != hashtag.text)
        .toList());
    await hashtagsService.deleteHashtag(hashtag.id);
  }

  addHashtagToFavorite(Hashtag hashtag) async {
    restaurantHashtags$.add([...restaurantHashtags$.value, hashtag]);
    await hashtagsService.saveHashtag(hashtag: hashtag);
  }

  Future<void> getRestaurantHashtags(Restaurant restaurant) async {
    final result = await hashtagsService.getRestaurantHashtags(restaurant.id);
    Hashtag? brandHashtag =
        result.data!.firstWhereOrNull((element) => element.isMain == true);
    if (brandHashtag == null) {
      brandHashtag = Hashtag(
          id: ObjectId().toString(),
          text: restaurant.getRestaurantNameAsBrandHashtag(),
          restaurantId: restaurant.id,
          type: HashtagType.RESTAURANT,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isMain: true);
      await hashtagsService.saveHashtag(hashtag: brandHashtag);
    }

    result.data?.remove(brandHashtag);
    result.data?.insert(0, brandHashtag);
    restaurantHashtags$.add(result.data ?? []);
  }

  Future<PostHashtagsGenerationResult> getHashtagsSuggestions(
      {required String restaurantId,
      required String restaurantName,
      required String postDescription,
      required List<String> favoriteHashtags,
      required List<String> restaurantCategory,
      required String restaurantLocality,
      required String restaurantPostalCode,
      required List<String> keywords}) async {
    final result = await hashtagsService.getHashtagsSuggestions(
        GetHashtagsSuggestionPayload(
            restaurantName: restaurantName,
            postDescription: postDescription,
            favoriteHashtags: favoriteHashtags,
            restaurantCategory: restaurantCategory,
            restaurantLocality: restaurantLocality,
            restaurantPostalCode: restaurantPostalCode,
            keywords: keywords));
    final resultData = result.data?.where((hashtag) => restaurantHashtags$.value
            .where((element) => element.text == hashtag.text)
            .isEmpty) ??
        [];
    final brandHashtag = restaurantHashtags$.value
        .firstWhere((element) => element.isMain == true);
    return PostHashtagsGenerationResult(generatedHashtags: [
      brandHashtag,
      ...resultData
          .map((e) => Hashtag.fromAiGeneration(e, restaurantId))
          .toList()
    ], preSelectedHashtags: [
      brandHashtag,
      ...(result.data ?? [])
          .where((element) => element.isPreSelected == true)
          .toList()
          .map((e) => Hashtag.fromAiGeneration(e, restaurantId))
          .toList()
    ]);
  }

  Future<void> editMainHashtag(String newText) {
    final brandHashtag = restaurantHashtags$.value
        .firstWhere((element) => element.isMain == true);
    Hashtag newBrandHashtag = brandHashtag.copyWith(text: newText);
    restaurantHashtags$.add(restaurantHashtags$.value
        .map((element) =>
            element.id == brandHashtag.id ? newBrandHashtag : element)
        .toList());

    return hashtagsService.editMainHashtagText(
      text: newText,
      hashtagId: brandHashtag.id,
    );
  }
}
