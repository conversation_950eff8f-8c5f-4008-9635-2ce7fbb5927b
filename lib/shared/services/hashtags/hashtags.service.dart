import 'dart:convert';

import 'package:malou/app_config.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/helpers/http_client.dart';
import 'package:malou/shared/models/hashtags/hashtag.model.dart';
import 'package:malou/shared/services/hashtags/types.dart';

final apiUrl = '${AppConfig.shared.baseUrl}/hashtags';
final httpClient = DioClient.instance.client;

class HashtagsService {
  Future<ApiResult<List<Hashtag>>> getRestaurantHashtags(
    String restaurantId,
  ) async {
    final response = await httpClient.get('$apiUrl/restaurants/$restaurantId');
    if (response.statusCode == 200) {
      Map<String, dynamic> responseData = response.data;
      return ApiResult.fromJson(responseData,
          fromJsonT: (json) =>
              List<Hashtag>.from(json.map((x) => Hashtag.fromJson(x))));
    }
    return ApiResult.error(response.data);
  }

  Future<ApiResult<List<GeneratedHashtagDto>>> getHashtagsSuggestions(
      GetHashtagsSuggestionPayload payload) async {
    final response =
        await httpClient.post('$apiUrl/suggest', data: payload.toJson());
    if (response.statusCode == 200) {
      Map<String, dynamic> responseData = response.data;
      return ApiResult.fromJson(responseData,
          fromJsonT: (json) => List<GeneratedHashtagDto>.from(
              json.map((x) => GeneratedHashtagDto.fromJson(x))));
    }
    return ApiResult.error(response.data);
  }

  Future<ApiResult<Hashtag>> saveHashtag({required Hashtag hashtag}) async {
    final response =
        await httpClient.post('$apiUrl/', data: jsonEncode(hashtag.toJson()));
    if (response.statusCode == 200) {
      Map<String, dynamic> responseData = response.data;
      return ApiResult.fromJson(responseData,
          fromJsonT: (json) => Hashtag.fromJson(json));
    }
    return ApiResult.error(response.data);
  }

  Future<void> deleteHashtag(String hashtagId) async {
    await httpClient.delete('$apiUrl/$hashtagId');
  }

  Future<void> editMainHashtagText({
    required String hashtagId,
    required String text,
  }) async {
    try {
      await httpClient.put(
        '$apiUrl/$hashtagId',
        data: {'text': text},
      );
    } catch (e) {
      print(e);
    }
  }
}
