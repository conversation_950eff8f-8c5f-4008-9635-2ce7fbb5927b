import 'package:dio/dio.dart';
import 'package:malou/app_config.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/helpers/http_client.dart';
import 'package:malou/shared/helpers/pagination.model.dart';
import 'package:malou/shared/models/conversation.model.dart';
import 'package:malou/shared/models/message.model.dart';
import 'package:malou/shared/services/messaging/messaging.filters.dart';

final apiUrl = '${AppConfig.shared.baseUrl}/messages';
final httpClient = DioClient.instance.client;

class MessagingService {
  static Future<ApiResult<ConversationWithMessagesApiResult>>
      getConversationsWithMessages(String restaurantId,
          SortedPagination pagination, MessagingFilters filters) async {
    try {
      final response = await httpClient.get(
          '$apiUrl/restaurants/$restaurantId/conversations',
          queryParameters: {
            ...pagination.toSnakeCaseJson(),
            ...filters.toJson(),
          });
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        var result = ApiResult<ConversationWithMessagesApiResult>.fromJson(
            responseData,
            fromJsonT: (json) =>
                ConversationWithMessagesApiResult.fromJson(json));
        return result;
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  static Future<ApiResult<int>> getUnreadConversationsCount(
      {required String restaurantId,
      required List<String> connectedPlatforms}) async {
    try {
      final response = await httpClient.get(
        '$apiUrl/restaurants/$restaurantId/conversations/unread',
        queryParameters: {
          'connected_platforms[]': connectedPlatforms,
        },
      );
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        var result = ApiResult<int>.fromJson(responseData,
            fromJsonT: (json) => json ?? 0);
        return result;
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  static Future<ApiResult<MessagesApiResult>> loadMoreMessages(
      {required String conversationId, required Pagination pagination}) async {
    try {
      final response = await httpClient.get(
          '$apiUrl/conversations/$conversationId/messages',
          queryParameters: pagination.toSnakeCaseJson());
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        var result = ApiResult<MessagesApiResult>.fromJson(responseData,
            fromJsonT: (json) => MessagesApiResult.fromJson(json));
        return result;
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  static Future<ApiResult<Message>> sendMessage(
      {required MessageDTO message, required String restaurantId}) async {
    try {
      final response = await httpClient.post(
          '$apiUrl/platforms/${message.key}/conversations/${message.conversationId}/send',
          data: {
            'message': message.toJson(),
            'restaurantId': restaurantId,
          },
          options: Options(
              sendTimeout: const Duration(minutes: 1),
              receiveTimeout: const Duration(minutes: 1)));
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        var result = ApiResult<Message>.fromJson(responseData,
            fromJsonT: (json) => Message.fromJson(json));
        return result;
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  static Future<ApiResult<Message>> sendReaction({
    required String messageId,
    required String restaurantId,
    required String reactionType,
  }) async {
    try {
      final response = await httpClient
          .put('$apiUrl/restaurants/$restaurantId/reaction/$messageId', data: {
        'reactionType': reactionType,
      });
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        var result = ApiResult<Message>.fromJson(responseData,
            fromJsonT: (json) => Message.fromJson(json));
        return result;
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  static Future<ApiResult> updateConversation({
    required String conversationId,
    required Map<String, dynamic> data,
  }) async {
    try {
      final response = await httpClient
          .put('$apiUrl/conversations/$conversationId', data: data);
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        var result = ApiResult.fromJson(responseData);
        return result;
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  static Future<ApiResult<Conversation>> getConversation(
      {required String conversationId}) async {
    try {
      final response =
          await httpClient.get('$apiUrl/conversations/$conversationId');
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        var result = ApiResult<Conversation>.fromJson(responseData,
            fromJsonT: (json) => Conversation.fromJson(json));
        return result;
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }
}
