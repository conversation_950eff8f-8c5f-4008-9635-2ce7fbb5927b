// ignore_for_file: avoid_print

import 'dart:async';
import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:malou/app_config.dart';
import 'package:malou/shared/helpers/pagination.model.dart';
import 'package:malou/shared/models/conversation.model.dart';
import 'package:malou/shared/models/message.model.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/services/messaging/messaging.filters.dart';
import 'package:malou/shared/services/messaging/messaging.service.dart';
import 'package:malou/shared/services/platform/platform.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:pusher_channels_flutter/pusher_channels_flutter.dart';
import 'package:rxdart/rxdart.dart';

enum MessageEvent { messageReceived, reactionReceived }

enum ConversationEvent {
  conversationDeleted,
  conversationArchived,
  conversationUnarchived
}

class ConversationChangeEvent {
  final ConversationWithMessages conversationWithMessages;
  final ConversationEvent event;
  int? previousElemenntIndex;

  ConversationChangeEvent(
      {required this.conversationWithMessages,
      required this.event,
      this.previousElemenntIndex});
}

class MessageStreamData {
  final Message message;
  final Conversation conversation;
  final MessageEvent event;

  MessageStreamData(this.event,
      {required this.message, required this.conversation});
}

class MessagingProvider extends ChangeNotifier {
  PusherChannelsFlutter? pusher;

  MessagingProvider(
      {RestaurantProvider? restaurantProvider,
      PlatformProvider? platformProvider}) {
    pusher = PusherChannelsFlutter.getInstance();
  }
  // --------------------------------------------------------------------------

  // Props
  BehaviorSubject<int?> unreadConversationsCount$ =
      BehaviorSubject<int?>.seeded(0);
  List<ConversationWithMessages> _conversationsWithMessages = [];
  String? selectedConversationId;

  PusherChannel? channel;

  final StreamController<MessageStreamData> _messageStreamController =
      StreamController<MessageStreamData>.broadcast();

  final StreamController<ConversationChangeEvent>
      _conversationStreamController =
      StreamController<ConversationChangeEvent>.broadcast();

  Map<String, PagingController<int, Message>> messagesPagingControllers = {};

  List<ConversationWithMessages> get conversationsWithMessages =>
      _conversationsWithMessages;

  StreamController<MessageStreamData> get messageStreamController =>
      _messageStreamController;
  Stream<MessageStreamData> get messageStream =>
      _messageStreamController.stream;

  StreamController<ConversationChangeEvent> get conversationStreamController =>
      _conversationStreamController;

  Stream<ConversationChangeEvent> get conversationStream =>
      _conversationStreamController.stream;

  ConversationWithMessages? currentCWM() {
    return conversationsWithMessages.isEmpty
        ? null
        : conversationsWithMessages.firstWhereOrNull(
            (element) => element.conversation.id == selectedConversationId);
  }

  PagingController<int, Message> getPagingControllerForConversation(
      String conversationId) {
    return messagesPagingControllers[conversationId]!;
  }

  initializeMessaging(RestaurantProvider? restaurantProvider,
      PlatformProvider? platformProvider) {
    pusher!.init(
        apiKey: AppConfig.shared.pusherKey,
        cluster: 'eu',
        authEndpoint: '${AppConfig.shared.baseUrl}/messages/pusher/auth',
        logToConsole: true,
        onError: (message, code, error) =>
            print('ERROR: $message, $code, $error'));
    if (restaurantProvider?.currentRestaurant != null &&
        platformProvider?.connectedMessagingPlatforms$.value != null &&
        platformProvider!.connectedMessagingPlatforms$.value.isNotEmpty) {
      initPusherChannels(restaurantProvider!.currentRestaurant!.id);
      initUnreadConversationsCount(restaurantProvider.currentRestaurant!.id,
          platformProvider.connectedMessagingPlatforms$.value);
    }
  }

  set conversationsWithMessages(List<ConversationWithMessages> value) {
    if (value.isEmpty) {
      _conversationsWithMessages = value;
      notifyListeners();
      return;
    }
    value.sort((a, b) {
      if (a.messages.isEmpty) {
        return 1;
      }
      if (b.messages.isEmpty) {
        return -1;
      }
      return b.messages.last.createdAt.compareTo(a.messages.last.createdAt);
    });
    _conversationsWithMessages = value;
    notifyListeners();
  }

  // --------------------------------------------------------------------------

  addNewMessageToConversation(Message message) {
    conversationsWithMessages = conversationsWithMessages.map((e) {
      if (e.conversation.id == message.conversationId) {
        e.messages.add(message);
      }
      return e;
    }).toList();
  }

  replaceMessageInConversationByIndex(Message message, String oldMessageId) {
    conversationsWithMessages = conversationsWithMessages.map((e) {
      if (e.conversation.id == message.conversationId) {
        e.messages = e.messages.map((m) {
          if (m.id == oldMessageId) {
            m = message;
          }
          return m;
        }).toList();
      }
      return e;
    }).toList();
  }

  addReactionToMessage(Message message, String restaurantId) {
    var selectedConversationIndex = conversationsWithMessages.indexWhere(
        (element) => element.conversation.id == message.conversationId);
    sendReaction(message.id, restaurantId).then((value) {
      conversationsWithMessages[selectedConversationIndex].messages =
          conversationsWithMessages[selectedConversationIndex]
              .messages
              .map((e) {
        if (e.id == message.id) {
          e.reactions = value.reactions;
        }
        return e;
      }).toList();
    });
  }

  removeReactionFromMessage(Message message, String restaurantId) {
    var selectedConversationIndex = conversationsWithMessages.indexWhere(
        (element) => element.conversation.id == message.conversationId);
    sendReaction(message.id, restaurantId, reactionType: 'unreact')
        .then((value) {
      conversationsWithMessages[selectedConversationIndex].messages =
          conversationsWithMessages[selectedConversationIndex]
              .messages
              .map((e) {
        if (e.id == message.id) {
          e.reactions = value.reactions;
        }
        return e;
      }).toList();
    });
  }

  addPagingControllerForConversation(String conversationId) {
    messagesPagingControllers[conversationId] =
        PagingController(firstPageKey: 0);
  }

  removePagingControllerForConversation(String conversationId) {
    // dispose
    messagesPagingControllers[conversationId]?.dispose();
    messagesPagingControllers.remove(conversationId);
  }

  insertNewMessageInConversationPagingController(
      Message message, String conversationId) {
    messagesPagingControllers[conversationId]?.itemList?.insert(0, message);
    notifyListeners();
  }
  // --------------------------------------------------------------------------
  // Methods

  Future<List<ConversationWithMessages>> getConversationsWithMessages(
      String restaurantId,
      List<MessagingPlatformKeys> connectedMessagingPlatforms,
      {int currentTab = 0,
      int page = 0,
      int pageSize = 20,
      String sortBy = 'latestMessageAt'}) async {
    try {
      Map<int, ConversationType> tabToFilter = {
        0: ConversationType.general,
        1: ConversationType.favorite,
        2: ConversationType.archived,
      };

      MessagingFilters filters = MessagingFilters(
          type: tabToFilter[currentTab] ?? ConversationType.general,
          platforms: connectedMessagingPlatforms);
      SortedPagination pagination = SortedPagination(
          pageNumber: page, pageSize: pageSize, sortBy: sortBy, total: null);
      final res = await MessagingService.getConversationsWithMessages(
          restaurantId, pagination, filters);
      if (res.error) {
        conversationsWithMessages = [];
        throw Exception(res.message);
      }

      conversationsWithMessages = res.data?.data ?? [];
      return conversationsWithMessages;
    } on Exception {
      rethrow;
    }
  }

  Future<List<Message>> loadMoreMessages(int pageNumber) async {
    var selectedConversationIndex = conversationsWithMessages.indexWhere(
        (element) => element.conversation.id == selectedConversationId);
    try {
      final res = await MessagingService.loadMoreMessages(
          conversationId: conversationsWithMessages[selectedConversationIndex]
              .conversation
              .id,
          pagination:
              Pagination(pageNumber: pageNumber, pageSize: 10, total: null));
      if (res.error) {
        throw Exception(res.message);
      }

      conversationsWithMessages[selectedConversationIndex]
          .messages
          .insertAll(0, res.data?.data ?? []);
      return res.data?.data ?? [];
    } on Exception {
      rethrow;
    }
  }

  Future<int> initUnreadConversationsCount(
      String restaurantId, List<Platform> connectedMessagingPlatforms) async {
    try {
      final res = await MessagingService.getUnreadConversationsCount(
          restaurantId: restaurantId,
          connectedPlatforms:
              connectedMessagingPlatforms.map((e) => e.key).toList());
      if (res.error) {
        unreadConversationsCount$.add(0);
        throw Exception(res.message);
      }
      unreadConversationsCount$.add(res.data ?? 0);
      return res.data ?? 0;
    } on Exception {
      rethrow;
    }
  }

  Future<Message> sendMessage(MessageDTO message, String restaurantId,
      {int retries = 0}) async {
    const int maxRetries = 1;
    try {
      final res = await MessagingService.sendMessage(
          message: message, restaurantId: restaurantId);
      if (res.error) {
        throw Exception(res.message);
      }
      return res.data!;
    } on Exception {
      if (retries < maxRetries) {
        retries++;
        return sendMessage(message, restaurantId, retries: retries);
      }
      return Message.fromMessageDTO(message.copyWith(
        status: MessageStatus.ERROR,
      ));
    }
  }

  Future<Message> sendReaction(String messageId, String restaurantId,
      {String reactionType = 'react'}) async {
    try {
      final res = await MessagingService.sendReaction(
          messageId: messageId,
          reactionType: reactionType,
          restaurantId: restaurantId);
      if (res.error) {
        throw Exception(res.message);
      }
      return res.data!;
    } on Exception {
      rethrow;
    }
  }

  Future<void> updateConversation(
      String conversationId, Map<String, dynamic> data) async {
    try {
      conversationsWithMessages = conversationsWithMessages.map((e) {
        if (e.conversation.id == conversationId) {
          e.conversation.copyFromMap({
            ...e.conversation.toJson(),
            ...data,
          });
        }
        return e;
      }).toList();
      final res = await MessagingService.updateConversation(
          data: data, conversationId: conversationId);

      if (res.error) {
        throw Exception(res.message);
      }
    } on Exception {
      rethrow;
    }
  }

  Future<ConversationWithMessages> getConversation(
      String conversationId) async {
    try {
      final res = await MessagingService.getConversation(
          conversationId: conversationId);
      if (res.error) {
        throw Exception(res.message);
      }
      ConversationWithMessages conversationWithMessages =
          ConversationWithMessages(conversation: res.data!, messages: []);
      if (conversationsWithMessages.isEmpty) {
        conversationsWithMessages = [conversationWithMessages];
      } else {
        conversationsWithMessages = conversationsWithMessages.map((e) {
          if (e.conversation.id == conversationId) {
            e = conversationWithMessages;
          }
          return e;
        }).toList();
      }
      return conversationWithMessages;
    } on Exception {
      rethrow;
    }
  }

// --------------------------------------------------------------------------
// Pusher
// --------------------------------------------------------------------------

  Future<void> initPusherChannels(String restaurantId) async {
    if (channel != null) {
      await pusher?.unsubscribe(channelName: channel!.channelName);
      channel = null;
    }
    channel = await pusher!.subscribe(
        channelName: 'messaging-restaurant-$restaurantId', onEvent: _onEvent);
    await pusher?.connect();
  }

  _onEvent(event) {
    switch (event.eventName) {
      case 'message-received':
        _onMessageReceived(event);
        break;

      case 'reaction-received':
        _onReactionReceived(event);
        break;
      default:
    }
  }

  void _onReactionReceived(event) {
    Map<String, dynamic> data = jsonDecode(event.data);
    Message message = Message.fromJson(data['message']);
    if (conversationsWithMessages
        .any((element) => element.conversation.id == message.conversationId)) {
      conversationsWithMessages = conversationsWithMessages.map((e) {
        if (e.conversation.id == message.conversationId) {
          e.messages = e.messages.map((m) {
            if (m.id == message.id) {
              m.reactions = message.reactions;
            }
            return m;
          }).toList();
        }
        return e;
      }).toList();
    }
    messageStreamController.add(MessageStreamData(MessageEvent.reactionReceived,
        message: message,
        conversation: conversationsWithMessages
            .firstWhere(
                (element) => element.conversation.id == message.conversationId)
            .conversation));
  }

  void _onMessageReceived(event) {
    Map<String, dynamic> data = jsonDecode(event.data);
    Conversation conversation = Conversation.fromJson(data['conversation']);
    Message message = Message.fromJson(data['message']);
    if (message.isFromRestaurant) {
      return;
    }

    if (conversationsWithMessages
        .any((element) => element.conversation.id == message.conversationId)) {
      conversationsWithMessages = conversationsWithMessages.map((e) {
        if (e.conversation.id == message.conversationId) {
          e.messages.add(message);
          e.conversation.status = message.isFromRestaurant ||
                  selectedConversationId == message.conversationId
              ? ConversationStatus.READ
              : ConversationStatus.UNREAD;
        }
        return e;
      }).toList();
    } else {
      conversationsWithMessages.add(ConversationWithMessages(
          conversation: conversation, messages: [message]));
    }

    insertNewMessageInConversationPagingController(message, conversation.id);
  }
}
