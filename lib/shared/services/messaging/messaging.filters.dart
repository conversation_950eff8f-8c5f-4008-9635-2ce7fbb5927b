// ignore_for_file: constant_identifier_names

import 'package:malou/shared/models/platform.model.dart';

enum ConversationType { general, favorite, archived }

enum ConversationStatus {
  UNREAD,
  READ,
}

class MessagingFilters {
  List<MessagingPlatformKeys> platforms;
  ConversationType type;
  List<ConversationStatus> status;

  MessagingFilters(
      {required this.platforms,
      this.type = ConversationType.general,
      this.status = ConversationStatus.values});

  Map<String, dynamic> toJson() {
    return {
      'platforms': platforms.map((e) => e.name).toList(),
      'conversation_type': type.toString().split('.')[1],
      'status': status.map((e) => e.toString().split('.')[1]).toList(),
    };
  }

  Map<String, dynamic> toSnakeCaseJson() {
    return {
      'platforms': platforms.map((e) => e.name).toList(),
      'conversation_type': type.toString().split('.')[1],
      'status': status.map((e) => e.toString().split('.')[1]).toList(),
    };
  }
}
