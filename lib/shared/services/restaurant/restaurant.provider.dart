import 'package:flutter/cupertino.dart';
import 'package:malou/shared/helpers/experimentation.service.dart';
import 'package:malou/shared/helpers/sentry.service.dart';
import 'package:malou/shared/models/restaurant.model.dart';
import 'package:malou/shared/models/user.model.dart';
import 'package:malou/shared/services/restaurant/restaurant.service.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:rxdart/rxdart.dart';

class RestaurantProvider extends ChangeNotifier {
  UserProvider? userProvider;

  static const int AI_HARD_LIMIT_CALL_COUNT = 500;

  // todo: remove this, use restaurantSubject$ instead
  Restaurant? _currentRestaurant;
  List<Restaurant> _userRestaurants = [];
  int selectedBottomAppBarIndex = 0;

  final BehaviorSubject<Restaurant?> restaurantSubject$ =
      BehaviorSubject<Restaurant>();

  final BehaviorSubject<int> aiRemainingCredits$ =
      BehaviorSubject<int>.seeded(0);

  Restaurant? get currentRestaurant => _currentRestaurant;
  List<Restaurant> get userRestaurants => _userRestaurants;

  final BehaviorSubject<List<Restaurant>> filteredUserRestaurants =
      BehaviorSubject<List<Restaurant>>();

  BehaviorSubject<List<Manager>> managers$ = BehaviorSubject.seeded([]);

  set currentRestaurant(Restaurant? restaurant) {
    if (restaurant == currentRestaurant) return;
    _currentRestaurant = restaurant;
    restaurantSubject$.add(restaurant!);
    aiRemainingCredits$
        .add(AI_HARD_LIMIT_CALL_COUNT - restaurant.ai.monthlyCallCount);
    SentryService.instance.addRestaurantInfos(restaurant);
    ExperimentationService.instance
        .initialize(userProvider?.currentUser, restaurant.id);
    notifyListeners();
  }

  set userRestaurants(List<Restaurant> restaurants) {
    _userRestaurants = restaurants;
    notifyListeners();
  }

  void clear() {
    _currentRestaurant = null;
    notifyListeners();
  }

  Future<void> setDefaultRestaurantSelected(String userId) async {
    await fetchAndSetUserRestaurants(userId).then((value) {
      if (userRestaurants.isNotEmpty && userRestaurants.length == 1) {
        currentRestaurant = userRestaurants[0];
      }
    }).catchError((error) => throw Exception(error));
  }

  Future<void> fetchAndSetUserRestaurants(String userId) async {
    try {
      final res = await RestaurantService.getRestaurants();
      if (res.error) {
        userRestaurants = [];
        throw Exception(res.message);
      }
      userRestaurants = res.data ?? [];
      // sort the restaurants by name
      userRestaurants.sort((a, b) => a.name.compareTo(b.name));
      filteredUserRestaurants.add(userRestaurants);
      return;
    } on Exception {
      rethrow;
    }
  }

  Future<void> reloadCurrentRestaurant() async {
    try {
      final res = await RestaurantService.getRestaurantById(
          currentRestaurant!.id.toString());
      if (res.error) {
        currentRestaurant = null;
        throw Exception(res.message);
      }
      currentRestaurant = res.data;
      return;
    } on Exception {
      rethrow;
    }
  }

  Restaurant? getUserRestaurantById({String? restaurantId}) {
    restaurantId ??= currentRestaurant!.id.toString();

    return userRestaurants.cast<Restaurant?>().firstWhere(
        (restaurant) => restaurant?.id == restaurantId,
        orElse: () => null);
  }

  Future<List<Manager>> getManagers(String restaurantId) async {
    final response = await RestaurantService.getManagers(restaurantId);
    if (response.data == null) return [];
    List<Manager> managers = response.data!
        .where(
            (manager) => manager.name.isNotEmpty && manager.lastname.isNotEmpty)
        .toList();
    return managers;
  }

  Future<Restaurant?> getRestaurantById(String restaurantId) async {
    final response = await RestaurantService.getRestaurantById(restaurantId);
    return response.data;
  }

  List<Restaurant> getPagedRestaurants(int pageKey) {
    final startIndex = pageKey * 10;
    final endIndex = startIndex + 10;
    if (endIndex > userRestaurants.length) {
      return userRestaurants.sublist(startIndex);
    }
    return userRestaurants.sublist(startIndex, endIndex);
  }
}
