import 'package:malou/app_config.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/helpers/http_client.dart';
import 'package:malou/shared/models/restaurant.model.dart';
import 'package:malou/shared/models/user.model.dart';

final apiUrl = '${AppConfig.shared.baseUrl}/restaurants';
final httpClient = DioClient.instance.client;

class RestaurantService {
  static Future<ApiResult<List<Restaurant>>> getRestaurants() async {
    try {
      final response = await httpClient.get(apiUrl, queryParameters: {
        'fields':
            'name type address cover logo updatedAt currentState regularHours phone website menuUrl bricks organizationId relatedUrls ai category platformCategories'
      });
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        return ApiResult.fromJson(responseData,
            fromJsonT: (json) => Restaurant.fromJsonList(json));
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  static Future<ApiResult<Restaurant>> getRestaurantById(
      String restaurantId) async {
    try {
      final response = await httpClient.get("$apiUrl/$restaurantId");
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        return ApiResult.fromJson(responseData,
            fromJsonT: (json) => Restaurant.fromJson(json));
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  static Future<ApiResult<List<Manager>>> getManagers(
      String restaurantId) async {
    try {
      final response = await httpClient.get("$apiUrl/$restaurantId/users");
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        return ApiResult.fromJson(responseData,
            fromJsonT: (json) => Manager.fromJsonList(json));
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }
}
