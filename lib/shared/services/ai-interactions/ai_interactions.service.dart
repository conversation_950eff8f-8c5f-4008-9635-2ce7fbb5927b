import 'package:malou/app_config.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/helpers/http_client.dart';
import 'package:malou/shared/models/ai_interaction.model.dart';

final apiUrl = '${AppConfig.shared.baseUrl}/ai-interactions';
final httpClient = DioClient.instance.client;

class AiInteractionsService {
  Future<ApiResult<List<AiInteraction>>> getAutoAiPropositionReviewAnswer(
      String relatedEntityId,
      RelatedCollectionEntity relatedEntityCollection) async {
    final response = await httpClient.get(
      '$apiUrl/related-entity-id/$relatedEntityId?related_entity_collection=${relatedEntityCollection.name}',
    );
    Map<String, dynamic> responseData = response.data;
    return ApiResult.fromJson(responseData,
        fromJsonT: (json) => List<AiInteraction>.from(
            json.map((aiInt) => AiInteraction.fromJson(aiInt))));
  }
}
