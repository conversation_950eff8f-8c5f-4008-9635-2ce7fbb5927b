import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:malou/generated/l10n.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum AppLocale { fr, en, it, es }

enum Language { french, english, italian, spanish }

class LanguageProvider extends ChangeNotifier {
  SharedPreferences preferences;
  late AppLocale _language;
  AppLocale get language => _language;

  LanguageProvider({required this.preferences}) {
    // get device language
    final deviceLanguage = Platform.localeName.split('_').first;
    AppLocale defaultLocale;
    try {
      defaultLocale = AppLocale.values.byName(deviceLanguage);
    } catch (err) {
      defaultLocale = AppLocale.fr;
    }
    final String? storedLocale = preferences.getString('language');
    _language = storedLocale != null
        ? AppLocale.values.byName(storedLocale)
        : defaultLocale;
  }

  Map<AppLocale, String> getAvailableLanguages(BuildContext context) {
    return {
      AppLocale.fr: Translation.of(context).fr,
      AppLocale.en: Translation.of(context).en,
      AppLocale.it: Translation.of(context).it,
      AppLocale.es: Translation.of(context).es,
    };
  }

  Map<Language, String> getAvailableLanguagesForAI(BuildContext context) {
    return {
      Language.french: Translation.of(context).fr,
      Language.english: Translation.of(context).en,
      Language.italian: Translation.of(context).it,
      Language.spanish: Translation.of(context).es,
    };
  }

  void changeLanguage(AppLocale language) {
    _language = language;
    preferences.setString('language', language.name);
    notifyListeners();
  }
}
