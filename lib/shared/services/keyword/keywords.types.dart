import 'package:malou/shared/models/keywords/keywords_score.model.dart';
import 'package:malou/shared/models/restaurant.model.dart';
import 'package:malou/shared/models/reviewer_name_validation.model.dart';

typedef ComputeKeywordsScoreAndTipsParams = ({
  String textToCompute,
  Restaurant restaurant,
  String? lang,
  KeywordScoreTextType textType,
  String? reviewerName,
  int? responseTime,
  bool? isLowRatedReview,
  String? reviewText,
  ReviewerNameValidation? reviewerNameValidation,
});
