import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:malou/shared/helpers/experimentation.service.dart';
import 'package:malou/shared/models/brick.model.dart';
import 'package:malou/shared/models/keywords/keyword.model.dart';
import 'package:malou/shared/models/keywords/keywords_score.model.dart';
import 'package:malou/shared/models/restaurant_ai_settings.model.dart';
import 'package:malou/shared/models/reviewer_name_validation.model.dart';
import 'package:malou/shared/services/keyword/keyword.service.dart';
import 'package:malou/shared/services/keyword/keywords.types.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/services/restaurant-ai-settings/restaurant_ai_setting.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:rxdart/rxdart.dart';

class KeywordProvider extends ChangeNotifier {
  KeywordService keywordService;
  RestaurantProvider? restaurantProvider;
  RestaurantAiSettingsProvider? restaurantAiSettingsProvider;
  LanguageProvider? languageProvider;
  BehaviorSubject<List<RestaurantKeyword>> allKeywords =
      BehaviorSubject<List<RestaurantKeyword>>.seeded([]);
  BehaviorSubject<List<RestaurantKeyword>> selectedKeywords =
      BehaviorSubject<List<RestaurantKeyword>>.seeded([]);

  final experimentationService = ExperimentationService.instance;

  KeywordProvider(
      {this.restaurantProvider,
      this.restaurantAiSettingsProvider,
      this.languageProvider,
      KeywordService? keywordService})
      : keywordService = keywordService ?? KeywordService();

  loadKeywords() async {
    try {
      final res = await keywordService
          .getKeywordsByRestaurantId(restaurantProvider!.currentRestaurant!.id);
      if (res.error) {
        throw Exception(res.message);
      }
      allKeywords.add(res.data ?? []);
      selectedKeywords.add(([
        RestaurantKeyword(
          id: 'restaurantName',
          keywordId: 'restaurantName',
          lastRankingRefresh: null,
          keyword: Keyword(
            id: 'restaurantName',
            language: languageProvider!.language.name,
            apiLocationId: '',
            isCustomerInput: false,
            volume: 0,
            text: restaurantProvider!.currentRestaurant!.name,
            bricks: [
              SimpleBrick(
                category: 'restaurantName',
                text: restaurantProvider!.currentRestaurant!.name,
              )
            ],
            volumeHistory: [],
          ),
          restaurantId: restaurantProvider!.currentRestaurant!.id,
          createdAt: DateTime.now(),
          selected: true,
          updatedAt: DateTime.now(),
        ),
        ...(res.data ?? [])
      ].cast<RestaurantKeyword>())
          .where((keyword) => keyword.selected == true)
          .toList());
      return;
    } on Exception {
      rethrow;
    }
  }

  Future<KeywordsScoreProcessResult> computeKeywordsScoreAndTips(
      ComputeKeywordsScoreAndTipsParams params) async {
    String rightLanguage = params.lang ??
        KeywordHelper.getLangWithMostOccurences(selectedKeywords.value);

    RestaurantAiSettings? restaurantAiSettings =
        restaurantAiSettingsProvider!.restaurantAiSettings$.hasValue
            ? restaurantAiSettingsProvider!.restaurantAiSettings$.value
            : null;
    List<SimpleBrick> bricks = KeywordHelper.buildSimpleBricks(
        selectedKeywords.value, params.restaurant, rightLanguage,
        reviewerName: params.reviewerName,
        isLowRatedReview: params.isLowRatedReview ?? false,
        restaurantAiSettings: restaurantAiSettings);

    List<SimpleBrick> bricksAssociatedWithKeywords = bricks
        .where((element) =>
            ['restaurantName', 'reviewerName'].contains(element.category) ==
            false)
        .toList();

    String venueName = params.restaurant.name;

    if (experimentationService.isOn('release-custom-ai-settings') &&
        restaurantAiSettings?.restaurantName != null) {
      venueName = restaurantAiSettings!.restaurantName;
    }

    var keywordsScorePayload = KeywordsScorePayload(
        textType: params.textType,
        text: params.textToCompute,
        bricks: bricksAssociatedWithKeywords.map((e) => e.text).toList(),
        venueName: venueName,
        language: rightLanguage,
        responseTime: params.responseTime,
        reviewerName: params.reviewerName,
        reviewText: params.reviewText,
        keywordScoreMethod: 'adjustable',
        reviewerNameValidation: params.reviewerNameValidation ??
            ReviewerNameValidation(
                gender: Civility.other,
                firstName: "",
                isFirstNameValid: false,
                lastName: "",
                isLastNameValid: false));

    final res = await keywordService.processKeywordsScore(keywordsScorePayload);

    final List<KeywordScoreDetail> keywordScoreTips = res.data!.details.map(
      (e) {
        return KeywordScoreDetail(
          fulfilled: e.fulfilled,
          criteria: e.criteria,
          value: e.value,
          fulfilledValue: e.fulfilledValue,
        );
      },
    ).toList();

    if (res.data!.restaurantNameFound) {
      final restaurantNameBrick = bricks
          .firstWhereOrNull((brick) => brick.category == 'restaurantName');
      if (restaurantNameBrick != null) {
        res.data!.bricksFound.add(restaurantNameBrick);
      }
    }

    if (res.data!.reviewerNameFound ?? false) {
      final reviewerNameBrick =
          bricks.firstWhereOrNull((brick) => brick.category == 'reviewerName');
      if (reviewerNameBrick != null) {
        res.data!.bricksFound.add(reviewerNameBrick);
      }
    }

    final KeywordsScoreProcessResult score = KeywordsScoreProcessResult(
      score: res.data!.score,
      details: keywordScoreTips,
      bricksFound: bricks
          .where((brick) =>
              res.data!.bricksFound
                  .firstWhereOrNull((element) => element.text == brick.text) !=
              null)
          .toList(),
      restaurantNameFound: res.data!.restaurantNameFound,
      reviewerNameFound: res.data!.reviewerNameFound,
    );

    return score;
  }

  bool isBrickUsed(
    SimpleBrick brick,
    KeywordsScoreProcessResult? score,
    String lang,
  ) {
    if (score == null) {
      return false;
    }

    if (brick.category == 'restaurantName') {
      return score.restaurantNameFound;
    }

    if (brick.category == 'reviewerName') {
      var isReviewerNameFulFilled = score.reviewerNameFound ??
          score.details
              .firstWhereOrNull(
                  (element) => element.criteria == KeywordCriteria.reviewerName)
              ?.fulfilled ??
          false;

      return isReviewerNameFulFilled;
    }

    var bricksFoundTexts = score.bricksFound.map((b) => b.text.toLowerCase());
    return bricksFoundTexts.contains(brick.text.toLowerCase()) ||
        bricksFoundTexts.contains(brick.translations?.get(lang)?.toLowerCase());
  }

  @override
  void dispose() {
    allKeywords.close();
    selectedKeywords.close();
    super.dispose();
  }
}
