import 'package:malou/app_config.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/helpers/http_client.dart';
import 'package:malou/shared/models/keywords/keyword.model.dart';
import 'package:malou/shared/models/keywords/keywords_score.model.dart';

final apiUrl = '${AppConfig.shared.baseUrl}/keywords';
final httpClient = DioClient.instance.client;

class KeywordService {
  Future<ApiResult<List<RestaurantKeyword>>> getKeywordsByRestaurantId(
    String restaurantId,
  ) async {
    try {
      final response =
          await httpClient.get('$apiUrl/restaurants/$restaurantId');
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        return ApiResult.fromJson(responseData,
            fromJsonT: (json) => List<RestaurantKeyword>.from(
                json.map((x) => RestaurantKeyword.fromJson(x))));
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  Future<ApiResult<KeywordsScoreProcessResult>> processKeywordsScore(
    KeywordsScorePayload keywordsScorePayload,
  ) async {
    final json = keywordsScorePayload.toJson();
    final response = await httpClient.post(
      '$apiUrl/score',
      data: json,
    );
    Map<String, dynamic> responseData = response.data;
    return ApiResult.fromJson(responseData,
        fromJsonT: (json) => KeywordsScoreProcessResult.fromJson(json));
  }
}
