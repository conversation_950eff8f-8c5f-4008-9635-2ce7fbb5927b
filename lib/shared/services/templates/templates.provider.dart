import 'package:flutter/cupertino.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/models/template.model.dart';
import 'package:malou/shared/services/templates/templates.service.dart';

class TemplatesProvider extends ChangeNotifier {
  Future<List<Template>> getTemplates(
      String restaurantId, TemplateType type) async {
    try {
      ApiResult<List<Template>> res =
          await TemplatesService.getTemplates(restaurantId, type);

      if (res.error) {
        throw res.error;
      }

      return res.data ?? [];
    } catch (e) {
      rethrow;
    }
  }
}
