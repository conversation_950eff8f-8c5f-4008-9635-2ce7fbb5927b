import 'package:malou/app_config.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/helpers/http_client.dart';
import 'package:malou/shared/models/template.model.dart';

final apiUrl = '${AppConfig.shared.baseUrl}/templates';
final httpClient = DioClient.instance.client;

class TemplatesService {
  static Future<ApiResult<List<Template>>> getTemplates(
      String restaurantId, TemplateType type) async {
    try {
      final response = await httpClient.get(apiUrl, queryParameters: {
        'restaurant_id': restaurantId,
        'type': type.name.toLowerCase()
      });
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        var result = ApiResult<List<Template>>.fromJson(responseData,
            fromJsonT: (json) =>
                json.map<Template>((e) => Template.fromJson(e)).toList());
        return result;
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }
}
