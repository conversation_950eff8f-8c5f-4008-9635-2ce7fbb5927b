import 'package:malou/app_config.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/helpers/http_client.dart';
import 'package:malou/shared/models/restaurant_ai_settings.model.dart';

final apiUrl = '${AppConfig.shared.baseUrl}/restaurant-ai-settings';
final httpClient = DioClient.instance.client;

class RestaurantAiSettingsService {
  Future<ApiResult<RestaurantAiSettings>> getRestaurantAiSettings(
      String restaurantId) async {
    final response = await httpClient.get(
      '$apiUrl/$restaurantId',
    );
    Map<String, dynamic> responseData = response.data;
    // check if responsaData is an empty map
    if (responseData.isEmpty) {
      return ApiResult(data: null, message: 'No data found');
    }
    return ApiResult.fromJson(responseData,
        fromJsonT: (json) => RestaurantAiSettings.fromJson(json));
  }
}
