import 'package:flutter/material.dart';
import 'package:malou/shared/models/restaurant_ai_settings.model.dart';
import 'package:malou/shared/services/restaurant-ai-settings/restaurant_ai_settings.service.dart';
import 'package:rxdart/rxdart.dart';

class RestaurantAiSettingsProvider extends ChangeNotifier {
  final BehaviorSubject<RestaurantAiSettings?> restaurantAiSettings$ =
      BehaviorSubject<RestaurantAiSettings?>.seeded(null);

  final RestaurantAiSettingsService restaurantAiSettingsService = RestaurantAiSettingsService();

  RestaurantAiSettingsProvider();

  void fetchAndSetAiSettings(String restaurantId) async {
    final response =
        await restaurantAiSettingsService.getRestaurantAiSettings(restaurantId);
    restaurantAiSettings$.add(response.data);
  }
}
