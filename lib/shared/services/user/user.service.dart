import 'package:malou/app_config.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/helpers/http_client.dart';
import 'package:malou/shared/models/user.model.dart';

final apiUrl = '${AppConfig.shared.baseUrl}/users';
final httpClient = DioClient.instance.client;

class UserService {
  static Future<ApiResult<User>> getUserById(String userId) async {
    try {
      final response = await httpClient.get('$apiUrl/$userId');
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        return ApiResult.fromJson(responseData,
            fromJsonT: (json) => User.fromJson(json));
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  static Future<ApiResult<User>> updateUser(
      String userId, Map<String, dynamic> user) async {
    try {
      final response = await httpClient.put('$apiUrl/$userId', data: user);
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        return ApiResult.fromJson(responseData,
            fromJsonT: (json) => User.fromJson(json));
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }
}
