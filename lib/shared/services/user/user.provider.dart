import 'package:flutter/cupertino.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/models/organization.model.dart';
import 'package:malou/shared/services/user/user.service.dart';
import 'package:malou/shared/services/userRestaurant/userRestaurant.service.dart';

import '../../models/user.model.dart';

class UserProvider extends ChangeNotifier {
  User? _currentUser;
  User? get currentUser => _currentUser;
  String? _deviceToken;
  String? get deviceToken => _deviceToken;

  set currentUser(User? user) {
    _currentUser = user;
    notifyListeners();
  }

  void clearUser() {
    _currentUser = null;
    notifyListeners();
  }

  setDisplaySemanticAnalyses(bool value, String restaurantId) async {
    if (_currentUser != null) {
      UserRestaurant userRestaurant =
          _currentUser!.getUserRestaurantByRestaurantId(restaurantId);
      userRestaurant.setDisplaySemanticAnalyses(value);
      await UserRestaurantService.updateUserRestaurantById(
          userRestaurant.id,
          UserRestaurantDTO(
              displaySemanticAnalyses: userRestaurant.displaySemanticAnalyses));
    }
    notifyListeners();
  }

  void addDeviceToken(String token) {
    _deviceToken = token;
    if (_currentUser != null) {
      if (_currentUser!.settings.notificationSettings == null) {
        _currentUser!.settings.notificationSettings =
            NotificationSettings(userDevicesTokens: [token]);
        _currentUser!.settings.newNotifications.mobile.userDevicesTokens
            .add(token);
      } else if (!_currentUser!
          .settings.notificationSettings!.userDevicesTokens!
          .contains(token)) {
        _currentUser!.settings.notificationSettings!.userDevicesTokens!
            .add(token);
        _currentUser!.settings.newNotifications.mobile.userDevicesTokens
            .add(token);
      }

      updateUser({
        'settings': _currentUser?.settings.toJson(),
      });
    }
    notifyListeners();
  }

  Future<void> updateUser(Map<String, dynamic> data) async {
    await UserService.updateUser(_currentUser!.id, data);
  }

  Future<void> refetchUser() async {
    ApiResult<User> res = await UserService.getUserById(_currentUser!.id);
    if (res.error) throw ErrorDescription(res.message);
    currentUser = res.data;
  }

  Organization? getOrganizationById(String organizationId) {
    return _currentUser?.organizations.cast<Organization?>().firstWhere(
        (element) => element?.id == organizationId,
        orElse: () => null);
  }
}
