import 'package:flutter/cupertino.dart';
import 'package:malou/shared/models/organization.model.dart';
import 'package:malou/shared/services/organization/organization.service.dart';
import 'package:rxdart/rxdart.dart';

class OrganizationProvider extends ChangeNotifier {
  final BehaviorSubject<Organization> currentOrganization =
      BehaviorSubject<Organization>();

  Future<void> fetchAndSetOrganization(String organizationId) async {
    try {
      final res = await OrganizationService.getOrganization(organizationId);
      if (res.error) {
        throw Exception(res.message);
      }
      currentOrganization.add(res.data!);
      return;
    } on Exception {
      rethrow;
    }
  }
}
