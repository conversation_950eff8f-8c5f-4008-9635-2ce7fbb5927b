import 'package:malou/app_config.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/helpers/http_client.dart';
import 'package:malou/shared/models/organization.model.dart';

final apiUrl = '${AppConfig.shared.baseUrl}/organizations';
final httpClient = DioClient.instance.client;

class OrganizationService {
  static Future<ApiResult<Organization>> getOrganization(
      String organizationId) async {
    try {
      final response = await httpClient.get("$apiUrl/$organizationId");
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        return ApiResult.fromJson(responseData,
            fromJsonT: (json) => Organization.fromJson(json));
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }
}
