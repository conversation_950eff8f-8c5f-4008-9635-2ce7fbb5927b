import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:rxdart/rxdart.dart';

class ConnectivityProvider extends ChangeNotifier {
  BehaviorSubject<bool> isConnected$ = BehaviorSubject<bool>.seeded(true);

  ConnectivityProvider() {
    initConnectivitySubscription();
  }

  void initConnectivitySubscription() {
    Connectivity().onConnectivityChanged.listen((ConnectivityResult result) {
      isConnected$.add([
        ConnectivityResult.wifi,
        ConnectivityResult.mobile,
        ConnectivityResult.ethernet,
      ].contains(result));
    });
  }
}
