import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/helpers/experimentation.service.dart';
import 'package:malou/shared/helpers/heap.service.dart';
import 'package:malou/shared/models/user.model.dart';
import 'package:malou/shared/services/auth/auth.model.dart';
import 'package:malou/shared/services/auth/auth.service.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/services/user/user.service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AuthProvider extends ChangeNotifier {
  UserProvider? userProvider;
  RestaurantProvider? restaurantProvider;

  AuthProvider({this.userProvider, this.restaurantProvider});

  String? token;
  bool get isLogged => token!.isNotEmpty;

  Future logout() async {
    token = null;
    SharedPreferences preferences = await SharedPreferences.getInstance();
    User? user = userProvider!.currentUser;
    preferences.remove('auth_token');
    preferences.remove('user');
    if (user == null) {
      return;
    }
    HeapService.instance.track('user_disconnected', {
      'userId': user.id,
      'email': user.email,
      'name': user.name,
      'role': user.role,
    });
    notifyListeners();
  }

  Future<ApiResult<AuthResponse>> login(AuthPayload payload) async {
    ApiResult<AuthResponse> res = await AuthService.login(payload);
    if (res.error) return res;
    if (res.data!.token.isNotEmpty) {
      SharedPreferences preferences = await SharedPreferences.getInstance();
      preferences.setString('auth_token', res.data!.token);
      token = res.data!.token;
      User user = await getUserFromToken(token!);
      dynamic jsonUser = user.toJson();
      await preferences.setString('user', jsonEncode(jsonUser));
      userProvider?.currentUser = user;
      await ExperimentationService.instance
          .initialize(user, restaurantProvider?.currentRestaurant?.id);
    }

    HeapService.instance.track('user_connected', {
      'userId': userProvider!.currentUser!.id,
      'email': userProvider!.currentUser!.email,
      'name': userProvider!.currentUser!.name,
      'role': userProvider!.currentUser!.role,
    });

    return res;
  }

  Future initializeUserInformation() async {
    await restaurantProvider
        ?.fetchAndSetUserRestaurants(userProvider!.currentUser!.id);
    if (userProvider?.currentUser?.restaurants.length == 1 &&
        restaurantProvider!.userRestaurants.isNotEmpty) {
      restaurantProvider?.currentRestaurant =
          restaurantProvider!.userRestaurants[0];
    }
    await ExperimentationService.instance.initialize(
        userProvider!.currentUser!, restaurantProvider?.currentRestaurant?.id);
  }

  Future saveCredentialsInStorage(String email, String password) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    preferences.setString('email', email);
    preferences.setString('password', password);
  }

  Future<(String? email, String? password)> getCredentialFromStorage() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    String? email = preferences.getString('email');
    String? password = preferences.getString('password');
    return (email, password);
  }

  Future<bool> checkForSession() async {
    SharedPreferences storage = await SharedPreferences.getInstance();
    try {
      String? token = storage.getString('auth_token');
      if (token == null) return false;
      User userFromToken = await getUserFromToken(token);
      userProvider?.currentUser = userFromToken;
      await storage.setString('user', jsonEncode(userFromToken.toJson()));
      this.token = token;

      return true;
    } catch (e) {
      await storage.clear();
      return false;
    }
  }

  Future<ApiResult> forgotPassword(String email) {
    return AuthService.forgotPassword(email);
  }

  Future<User> getUserFromToken(String token) async {
    Map<String, dynamic> decodedJwt = JwtDecoder.decode(token);
    if (decodedJwt['_id'] == null) throw ErrorDescription('Invalid token!');

    String userId = decodedJwt['_id'];
    ApiResult<User> res = await UserService.getUserById(userId);

    if (res.error) throw ErrorDescription(res.message);
    return res.data!;
  }
}
