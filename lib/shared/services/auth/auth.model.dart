// To parse this JSON data, do
//
//     final authResponse = authResponseFrom<PERSON>son(jsonString);

import 'dart:convert';

AuthResponse authResponseFromJson(String str) =>
    AuthResponse.fromJson(json.decode(str));

String authResponseToJson(AuthResponse data) => json.encode(data.toJson());

class AuthResponse {
  AuthResponse({
    required this.token,
  });

  String token;

  factory AuthResponse.fromJson(Map<String, dynamic> json) => AuthResponse(
        token: json["token"],
      );

  Map<String, dynamic> toJson() => {
        "token": token,
      };
}

class AuthPayload {
  AuthPayload({
    required this.email,
    required this.password,
  });

  String email;
  String password;

  factory AuthPayload.fromJson(Map<String, dynamic> json) => AuthPayload(
        email: json["email"],
        password: json["password"],
      );

  Map<String, dynamic> toJson() => {
        "email": email,
        "password": password,
      };
}
