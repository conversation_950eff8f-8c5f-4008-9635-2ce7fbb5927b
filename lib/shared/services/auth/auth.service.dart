import 'package:malou/app_config.dart';
import 'package:malou/shared/helpers/http_client.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/services/auth/auth.model.dart';

final apiUrl = '${AppConfig.shared.baseUrl}/users';
final httpClient = DioClient.instance.client;

class AuthService {
  static Future<ApiResult<AuthResponse>> login(AuthPayload request) async {
    try {
      final response = await httpClient.post(
        '$apiUrl/login',
        data: request.toJson(),
      );
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        return ApiResult.fromJson(responseData,
            fromJsonT: (json) => AuthResponse.fromJson(json));
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  static Future<ApiResult> forgotPassword(String email) async {
    try {
      final response = await httpClient.post(
        '$apiUrl/password/sendResetEmail',
        data: {'email': email},
      );
      if (response.statusCode == 200) {
        return ApiResult.fromJson(response.data);
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }
}
