import 'dart:convert';

import 'package:malou/app_config.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/helpers/http_client.dart';
import 'package:malou/shared/models/ai_interaction.model.dart';
import 'package:malou/shared/models/user.model.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

final apiUrl = '${AppConfig.shared.baseUrl}/ai';
final httpClient = DioClient.instance.client;

enum AiTextToOptimizeType {
  REVIEW_ANSWER,
  SOCIAL_NETWORK_POST,
  SEO_POST,
}

typedef GenerateReviewAnswerFromAIParams = ({
  String lang,
  String reviewId,
  String restaurantId
});

typedef OptimizeFromAIParams = ({
  String relatedEntityId,
  String restaurantId,
  String textToOptimize,
  String textToOptimizeType,
  String? lang
});

class AiService {
  Future<ApiResult<String>> generateSeoPostText(
      {postId, description, lang, restaurantId}) async {
    final response = await httpClient.post(
      '$apiUrl/generate-seo-post-text',
      data: {
        'postId': postId,
        'description': description,
        'lang': lang,
        'restaurantId': restaurantId,
      },
    );
    Map<String, dynamic> responseData = response.data;
    return ApiResult.fromJson(responseData, fromJsonT: (json) => json);
  }

  Future<ApiResult<List<String>>> generateSeoPostTextSettings(
      {postId, description, lang, restaurantId, shouldUseImageAnalysis}) async {
    final response = await httpClient.post(
      '$apiUrl/generate-seo-post-text-settings',
      data: {
        'postId': postId,
        'description': description,
        'lang': lang,
        'restaurantId': restaurantId,
        'shouldUseImageAnalysis': shouldUseImageAnalysis
      },
    );
    Map<String, dynamic> responseData = response.data;
    var responseDataAsList =
        (responseData['data']['captions'] as List).map((item) => item as String).toList();
    return ApiResult.fromJson(responseData,
        fromJsonT: (json) => responseDataAsList);
  }

  Future<ApiResult<String>> generateSocialNetworkPostText(
      {postId, description, restaurantId, lang}) async {
    final response = await httpClient.post(
      '$apiUrl/generate-social-network-post-text',
      data: {
        'postId': postId,
        'description': description,
        'lang': lang,
        'restaurantId': restaurantId,
      },
    );
    Map<String, dynamic> responseData = response.data;
    return ApiResult.fromJson(responseData, fromJsonT: (json) => json);
  }

  Future<ApiResult<List<String>>> generateSocialNetworkPostTextWithSettings(
      {postId, description, restaurantId, lang, shouldUseImageAnalysis}) async {
    final response = await httpClient.post(
      '$apiUrl/generate-social-network-post-text-settings',
      data: {
        'postId': postId,
        'description': description,
        'lang': lang,
        'restaurantId': restaurantId,
        'shouldUseImageAnalysis': shouldUseImageAnalysis
      },
    );
    Map<String, dynamic> responseData = response.data;
    var responseDataAsList =
        (responseData['data']['captions'] as List).map((item) => item as String).toList();
    return ApiResult.fromJson(responseData,
        fromJsonT: (json) => responseDataAsList);
  }

  Future<ApiResult<String>> translate(
      {required String relatedEntityCollection,
      required String relatedEntityId,
      required String restaurantId,
      required AiInteractionType type,
      String text = '',
      required String lang}) async {
    final response = await httpClient.post(
      '$apiUrl/translate',
      data: {
        'relatedEntityCollection': relatedEntityCollection,
        'relatedEntityId': relatedEntityId,
        'lang': lang,
        'type': type.name,
        'restaurantId': restaurantId,
        'text': text
      },
    );
    Map<String, dynamic> responseData = response.data;
    return ApiResult.fromJson(responseData, fromJsonT: (json) => json);
  }

  Future<ApiResult<String>> optimizeText(
      {relatedEntityId,
      restaurantId,
      textToOptimize,
      textToOptimizeType,
      lang}) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    final User user = User.fromJson(jsonDecode(preferences.getString('user')!));
    final AppLocale defaultLanguage = user.defaultLanguage;
    final response = await httpClient.post(
      '$apiUrl/optimize-text',
      data: {
        'relatedEntityId': relatedEntityId,
        'restaurantId': restaurantId,
        'textToOptimize': textToOptimize,
        'textToOptimizeType': textToOptimizeType,
        'lang': lang ?? defaultLanguage.name
      },
    );
    Map<String, dynamic> responseData = response.data;
    return ApiResult.fromJson(responseData,
        fromJsonT: (json) => json['optimizedText']);
  }

  Future<ApiResult<List<String>>> generateHashtags(
      {hashtags, postText, restaurantId, postId}) async {
    final response = await httpClient.post(
      '$apiUrl/choose-post-hashtags',
      data: {
        'hashtags': hashtags,
        'postText': postText,
        'restaurantId': restaurantId,
        'postId': postId
      },
    );
    Map<String, dynamic> responseData = response.data;
    return ApiResult.fromJson(responseData,
        fromJsonT: (json) => List.from(json.map((x) => x.toString())));
  }

  Future<ApiResult<String>> generateReviewAnswer(
      GenerateReviewAnswerFromAIParams params) async {
    final response = await httpClient.post(
      '$apiUrl/answer-review',
      data: {
        'lang': params.lang,
        'restaurantId': params.restaurantId,
        'reviewId': params.reviewId
      },
    );
    Map<String, dynamic> responseData = response.data;
    return ApiResult.fromJson(responseData, fromJsonT: (json) => json);
  }
}
