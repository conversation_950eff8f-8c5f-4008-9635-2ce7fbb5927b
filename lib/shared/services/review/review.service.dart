import 'package:dio/dio.dart';
import 'package:malou/app_config.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/helpers/http_client.dart';
import 'package:malou/shared/helpers/pagination.model.dart';
import 'package:malou/shared/models/reviewer_name_validation.model.dart';
import 'package:malou/shared/models/reviews.model.dart';
import 'package:malou/shared/models/reviews_filters.model.dart';
import 'package:malou/shared/services/review/review.provider.dart';

final apiUrl = '${AppConfig.shared.baseUrl}/reviews';
final httpClient = DioClient.instance.client;

class ReviewService {
  static Future<ApiResult<Review?>> getReviewById(String reviewId) async {
    final response = await httpClient.get("$apiUrl/$reviewId");
    Map<String, dynamic> responseData = response.data;

    if (responseData["data"] == null) {
      return ApiResult(message: '', data: null, error: false);
    }

    var result = ApiResult<Review>.fromJson(responseData,
        fromJsonT: (json) => Review.fromJson(json));

    return result;
  }

  static Future<ApiResult<PaginatedReviewsReponse>> getReviews(
      String restaurantId,
      Pagination pagination,
      ReviewsFilters filters) async {
    var queryParams = {
      ...pagination.toSnakeCaseJson(),
      ...filters.toSnakeCaseJson(),
      "restaurant_ids": [restaurantId],
      "text": ""
    };
    queryParams.removeWhere((key, value) => value == null || value == '');
    final response =
        await httpClient.get("$apiUrl/v2", queryParameters: queryParams);
    Map<String, dynamic> responseData = response.data;

    var result = ApiResult<PaginatedReviewsReponse>.fromJson(responseData,
        fromJsonT: (json) => PaginatedReviewsReponse(
            pagination: Pagination.fromJson(json['pagination']),
            reviews: List<Review>.from(
                json["reviews"].map((x) => Review.fromJson(x)))));

    return result;
  }

  static Future<int> countReviews(
      String restaurantId, ReviewsFilters filters) async {
    var body = {
      ...filters.toJson(),
      "restaurantIds": [restaurantId],
      "text": ""
    };
    body.removeWhere((key, value) => value == null || value == '');
    final response = await httpClient.post("$apiUrl/count", data: body);

    return response.data['data']['count'];
  }

  static Future<bool> synchronize(String restaurantId) async {
    try {
      final response = await httpClient
          .get("$apiUrl/restaurants/synchronize", queryParameters: {
        "restaurant_ids": [restaurantId, restaurantId]
      });
      if (response.statusCode == 201) {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  static Future<bool> canAnswer(String restaurantId) async {
    try {
      final response =
          await httpClient.get("$apiUrl/restaurants/$restaurantId/can_answer",
              options: Options(headers: {
                "Access-Control-Allow-Credentials": "true",
              }));
      if (response.statusCode == 200) {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  static Future<ApiResult<Review>> reply(ReviewReplyParams params) async {
    final response = await httpClient.post(
        "$apiUrl/${params.review.id}/comments/restaurants/${params.restaurantId}",
        data: params.toDTO(),
        options: Options(headers: {
          "Access-Control-Allow-Credentials": "true",
        }));
    Map<String, dynamic> responseData = response.data;

    var result = ApiResult<Review>.fromJson(responseData,
        fromJsonT: (json) => Review.fromJson(json));

    return result;
  }

  static Future<ApiResult<Review>> replyPrivate(
      ReviewReplyByMailParams params) async {
    try {
      final response = await httpClient.post(
          "$apiUrl/${params.review.id}/comments/restaurants/${params.restaurantId}/private",
          data: {
            "comment": {
              "author": {"_id": params.user.id, "name": params.user.fullName},
              "content": {
                "from": {"name": params.fromName, "email": params.fromEmail},
                "object": params.object,
                "messageHTML": params.text
              },
              "socialUpdatedAt": DateTime.now().toIso8601String(),
              "templateIdUsed": null,
              "text": params.text
            }
          },
          options: Options(headers: {
            "Access-Control-Allow-Credentials": "true",
          }));
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;

        var result = ApiResult<Review>.fromJson(responseData,
            fromJsonT: (json) => Review.fromJson(json));

        return result;
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  static Future<int> getRestaurantReviewsTotal(
      String restaurantId, List<String> platforms,
      [MalouComparisonPeriod? comparisonPeriod]) async {
    var startDate = DateTime.now().subtract(const Duration(days: 30));
    var endDate = DateTime.now();
    try {
      var response = await httpClient.get(
          "$apiUrl/restaurants/$restaurantId/chart/evolution/total",
          queryParameters: {
            "start_date": startDate.toIso8601String(),
            "end_date": endDate.toIso8601String(),
            "platforms": platforms,
            if (comparisonPeriod?.name != null)
              "comparison_period": comparisonPeriod?.name,
          });
      if (response.statusCode == 200) {
        RestaurantReviewsStatsResponse res =
            RestaurantReviewsStatsResponse.fromJson(response.data['data']!);
        if (res.results.isEmpty) {
          return 0;
        }
        return res.results.first.total ?? 0;
      }
      return 0;
    } catch (e) {
      return 0;
    }
  }

  static Future<double> getRestaurantReviewsAverage(
      String restaurantId, List<String> platforms,
      [MalouComparisonPeriod? comparisonPeriod]) async {
    // todo fix in back end to allow front end to send a null range
    var startDate = DateTime.now().subtract(const Duration(days: 200000));
    var endDate = DateTime.now();
    try {
      var response = await httpClient.get(
          "$apiUrl/restaurants/$restaurantId/chart/rating/average",
          queryParameters: {
            "start_date": startDate.toIso8601String(),
            "end_date": endDate.toIso8601String(),
            "platforms": platforms,
            if (comparisonPeriod?.name != null)
              "comparison_period": comparisonPeriod?.name,
          });
      if (response.statusCode == 200) {
        RestaurantReviewsStatsResponse res =
            RestaurantReviewsStatsResponse.fromJson(response.data['data']!);
        if (res.results.isEmpty) {
          return 0;
        }
        return res.results.first.averageRating ?? 0;
      }
      return 0;
    } catch (e) {
      return 0;
    }
  }

  deleteReviewById(String reviewId) async {
    try {
      final response = await httpClient.delete("$apiUrl/$reviewId");
      if (response.statusCode == 200) {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  Future<ApiResult<ReviewerNameValidationResponse>> getReviewerNameValidation(
      String reviewId, String lang) async {
    final response = await httpClient.get(
        "$apiUrl/$reviewId/reviewer-name-validation",
        queryParameters: {"lang": lang});

    Map<String, dynamic> responseData = response.data;

    var result = ApiResult<ReviewerNameValidationResponse>.fromJson(
        responseData,
        fromJsonT: (json) => ReviewerNameValidationResponse.fromJson(json));

    return result;
  }

  static Future<ApiResult<Review>> createReviewTextTranslation(
      {required String reviewId,
      required String language,
      required bool isPrivateReview,
      required String translation}) async {
    final response =
        await httpClient.post("$apiUrl/$reviewId/translations", data: {
      "language": language,
      "isPrivateReview": isPrivateReview,
      "translation": translation,
      "source": "serverless-ai-text-generator"
    });
    Map<String, dynamic> responseData = response.data;

    var result = ApiResult<Review>.fromJson(responseData,
        fromJsonT: (json) => Review.fromJson(json));

    return result;
  }
}

class RestaurantReviewsStatsResponse {
  List<ReviewsEvolution> results = [];

  RestaurantReviewsStatsResponse({this.results = const []});

  RestaurantReviewsStatsResponse.fromJson(Map<String, dynamic> json) {
    if (json['results'] != null) {
      results = <ReviewsEvolution>[];
      json['results'].forEach((v) {
        results.add(ReviewsEvolution.fromJson(v));
      });
    }
  }
}

class ReviewsEvolution {
  int? total = 0;
  double? averageRating = 0;

  ReviewsEvolution({this.total = 0, this.averageRating = 0});

  ReviewsEvolution.fromJson(Map<String, dynamic> json) {
    total = json['total'];
    averageRating = (json['averageRating'] != null
        ? double.parse(json['averageRating'].toString())
        : 0);
  }
}
