import 'dart:async';
import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:malou/pages/restaurant/pages/reviews/reviews.page.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/helpers/constant.dart';
import 'package:malou/shared/helpers/pagination.model.dart';
import 'package:malou/shared/helpers/promotion_offers.dart';
import 'package:malou/shared/models/ai_interaction.model.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/models/restaurant.model.dart';
import 'package:malou/shared/models/reviewer_name_validation.model.dart';
import 'package:malou/shared/models/reviews.model.dart';
import 'package:malou/shared/models/reviews_filters.model.dart';
import 'package:malou/shared/models/user.model.dart';
import 'package:malou/shared/services/ai-interactions/ai_interactions.service.dart';
import 'package:malou/shared/services/ai/ai.service.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/services/platform/platform.provider.dart';
import 'package:malou/shared/services/private-review/private_review.service.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.service.dart';
import 'package:malou/shared/services/review/review.service.dart';
import 'package:rxdart/rxdart.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../models/keywords/keywords_analysis.model.dart';

class ReviewProvider with ChangeNotifier {
  static const String RELATED_ENTITY = 'REVIEWS';
  RestaurantProvider? restaurantProvider;
  PlatformProvider? platformProvider;
  ReviewService reviewService;
  final Map<String, Watcher<Restaurant>> watchers = {};
  SharedPreferences preferences;

  final BehaviorSubject<ReviewsFilters> filters =
      BehaviorSubject<ReviewsFilters>();

  Watcher<Restaurant> currentRestaurantReviewsWatcher =
      Watcher<Restaurant>(timer: null, value: BehaviorSubject<Restaurant?>());

  BehaviorSubject<ReviewsStateSnackBar?> reviewsStateSnackbar =
      BehaviorSubject<ReviewsStateSnackBar?>();

  BehaviorSubject<int?> urgent = BehaviorSubject<int?>.seeded(0);
  BehaviorSubject<int?> unAnswered = BehaviorSubject<int?>.seeded(0);
  BehaviorSubject<int?> total = BehaviorSubject<int?>.seeded(0);

  final PagingController<int, Review> pagingController =
      PagingController(firstPageKey: 0);

  // allow user to not lose his drafted text when he changes page
  BehaviorSubject<Map<String, String>> draftedReviewTexts =
      BehaviorSubject<Map<String, String>>.seeded({});

  BehaviorSubject<int> currentIndexReviewsTab = BehaviorSubject<int>.seeded(0);

  AiService aiService;
  AiInteractionsService aiInteractionsService;
  LanguageProvider languageProvider;

  ReviewProvider(
      {this.restaurantProvider,
      this.platformProvider,
      AiService? aiService,
      required this.preferences})
      : aiService = aiService ?? AiService(),
        aiInteractionsService = AiInteractionsService(),
        languageProvider = LanguageProvider(preferences: preferences),
        reviewService = ReviewService() {
    ReviewsFilters defaultFilters = ReviewsFilters(
        answered: true,
        notAnswered: true,
        pending: true,
        showPrivate: true,
        withText: true,
        withoutText: true,
        archived: false,
        unarchived: true,
        ratings: ['0', '1', '2', '3', '4', '5'],
        sortOrder: -1,
        sortBy: 'date',
        platforms: AppConstant.PLATFORMS_THAT_HAVE_REVIEWS,
        privatePlatforms: PrivatePlatform.values,
        answerable: false,
        period: MalouPeriod.last_three_months);
    ReviewsFilters? filtersFromPrefs =
        preferences.getString('reviewsFilters') != null
            ? ReviewsFilters.fromJson(
                jsonDecode(preferences.getString('reviewsFilters')!))
            : null;
    filters.add(filtersFromPrefs ?? defaultFilters);
  }

  Future startWatchingReviewsSynchronization(
      String restaurantId, Watcher<Restaurant> restaurantWatcher) async {
    if (restaurantProvider?.currentRestaurant != null) {
      await ReviewService.synchronize(restaurantId);
      var timer = Timer.periodic(const Duration(seconds: 3), (timer) async {
        if (restaurantWatcher.value.isClosed) {
          return;
        }

        var rest = await RestaurantService.getRestaurantById(restaurantId);
        restaurantWatcher.value.add(rest.data!);

        var hasFinished = rest.data?.currentState?.reviews?.fetched.values
                .every((state) =>
                    state.status == FetchedStatus.success ||
                    state.status == FetchedStatus.error) ??
            false;

        if (hasFinished) {
          timer.cancel();
          restaurantWatcher.value.add(null);
        }
      });

      restaurantWatcher.timer = timer;

      // will override if already exists
      watchers.addEntries([MapEntry(restaurantId, restaurantWatcher)]);
    }
    return true;
  }

  Future<PaginatedReviewsReponse> getReviews(int pageNumber) async {
    final res = await ReviewService.getReviews(
        restaurantProvider!.currentRestaurant!.id,
        Pagination(pageNumber: pageNumber, total: 0, pageSize: 20),
        filters.value);

    return res.data ??
        PaginatedReviewsReponse(
            pagination: Pagination(pageNumber: 0, total: 0, pageSize: 20),
            reviews: []);
  }

  Future<int> countUrgentReviews() async {
    final count = await ReviewService.countReviews(
        restaurantProvider!.currentRestaurant!.id,
        filters.value.copyWith(
            answered: false,
            ratings: ['1', '2', '3'],
            notAnswered: true,
            pending: false,
            answerable: true,
            unarchived: true,
            showPrivate: true,
            withText: true,
            withoutText: true,
            platforms: AppConstant.PLATFORMS_THAT_HAVE_REVIEWS,
            privatePlatforms: [PrivatePlatform.campaign]));

    return count;
  }

  Future<int> countUnansweredReviews() async {
    final count = await ReviewService.countReviews(
        restaurantProvider!.currentRestaurant!.id,
        filters.value.copyWith(
            notAnswered: true,
            answered: false,
            ratings: ['1', '2', '3', '4', '5'],
            pending: false,
            answerable: true,
            unarchived: true,
            platforms: AppConstant.PLATFORMS_THAT_HAVE_REVIEWS,
            privatePlatforms: [PrivatePlatform.campaign],
            showPrivate: true));

    return count;
  }

  Future<int> countTotalReviews() async {
    final count = await ReviewService.countReviews(
        restaurantProvider!.currentRestaurant!.id,
        filters.value.copyWith(
          answered: true,
          notAnswered: true,
          pending: true,
          showPrivate: true,
          withText: true,
          withoutText: true,
          archived: false,
          unarchived: true,
          ratings: ['0', '1', '2', '3', '4', '5'],
          sortOrder: -1,
          sortBy: 'date',
          platforms: AppConstant.PLATFORMS_THAT_HAVE_REVIEWS,
          privatePlatforms: PrivatePlatform.values,
          answerable: false,
        ));
    return count;
  }

  bool isSyncRunning(String restaurantId) {
    return watchers[restaurantId] != null &&
        watchers[restaurantId]!.value.hasValue &&
        !watchers[restaurantId]!.value.isClosed;
  }

  Future<Review> getReviewById(String reviewId) async {
    ApiResult<Review?>? res;
    try {
      res = await ReviewService.getReviewById(reviewId);
      if (res.data != null) {
        return res.data!;
      }
    } catch (e) {}

    final resPrivateReview =
        await PrivateReviewService.getPrivateReviewById(reviewId);
    if (resPrivateReview.data != null) {
      return resPrivateReview.data!;
    }
    throw Exception(res?.message ?? 'Review not found');
  }

  Future<Review> reply(ReviewReplyParams params) async {
    try {
      final res = await ReviewService.canAnswer(params.restaurantId);

      if (!res) {
        throw Exception('unauthorized to reply');
      }

      final replyRes = await ReviewService.reply(params);
      if (replyRes.error) {
        throw Exception(replyRes.message);
      }

      if (!Platform.hasApi(params.review.key)) {
        await Clipboard.setData(ClipboardData(text: params.text));
        if (params.review.businessSocialLink != null) {
          var redirectionUrl = _getRedirectionUrl(
              params.review.businessSocialLink!, params.review.key);
          if (await canLaunchUrl(redirectionUrl)) {
            launchUrl(redirectionUrl, mode: LaunchMode.externalApplication);
          }
        }
      }

      return replyRes.data!;
    } on Exception {
      rethrow;
    }
  }

  Uri _getRedirectionUrl(String link, PlatformKeys key) {
    final uri = Uri.parse(link.trim());
    var domainMap = AppConstant.getDetails(key).domainMap;
    if (domainMap == null) {
      return uri;
    }
    var language = languageProvider.language.name;
    var domain = domainMap[language];
    domain ??= domainMap.values.first;
    final newUri = uri.replace(host: domain);
    return newUri;
  }

  Future<Review> replyByMail(ReviewReplyByMailParams params) async {
    try {
      final res = await ReviewService.canAnswer(params.restaurantId);

      if (!res) {
        throw Exception('unauthorized to reply');
      }

      final replyRes = await ReviewService.replyPrivate(params);
      if (replyRes.error) {
        throw Exception(replyRes.message);
      }

      return replyRes.data!;
    } on Exception {
      rethrow;
    }
  }

  void handleReviewListAfterReply(Review rev) {
    if (pagingController.itemList != null) {
      ReviewsTabs idx = ReviewsTabs.values[currentIndexReviewsTab.value];
      if ([ReviewsTabs.unanswered, ReviewsTabs.urgent].contains(idx)) {
        pagingController.itemList = pagingController.itemList!.where((r) {
          return r.id != rev.id;
        }).toList();
      } else {
        pagingController.itemList = pagingController.itemList!.map((r) {
          if (r.id == rev.id) {
            return rev;
          }
          return r;
        }).toList();
      }

      setTotalReviews();
      setTotalUrgentReviews();
      setTotalUnansweredReviews();
    }
  }

  void setTotalUrgentReviews() {
    countUrgentReviews().then((value) => urgent.add(value));
  }

  void setTotalUnansweredReviews() {
    countUnansweredReviews().then((value) => unAnswered.add(value));
  }

  void setTotalReviews() {
    countTotalReviews().then((value) => total.add(value));
  }

  Future<String> generateTextFromAI(
      GenerateReviewAnswerFromAIParams params) async {
    var result = await aiService.generateReviewAnswer(params);
    return result.data!;
  }

  Future<String> translateAnswerFromAI(
      TranslateReviewAnswerFromAIParams params) async {
    var result = await aiService.translate(
        relatedEntityCollection: RELATED_ENTITY,
        relatedEntityId: params.reviewId,
        restaurantId: params.restaurantId,
        type: AiInteractionType.review_answer_translation,
        lang: params.lang,
        text: params.text);
    return result.data!;
  }

  Future<Review> translateReviewFromAI(
      TranslateReviewTextFromAIParams params) async {
    var translateResult = await aiService.translate(
        relatedEntityCollection: RELATED_ENTITY,
        relatedEntityId: params.reviewId,
        restaurantId: params.restaurantId,
        type: AiInteractionType.review_translation,
        lang: params.lang,
        text: params.text);
    var createReviewTextTranslationResult =
        await ReviewService.createReviewTextTranslation(
      isPrivateReview: params.isPrivateReview,
      language: params.lang,
      reviewId: params.reviewId,
      translation: translateResult.data!,
    );
    return createReviewTextTranslationResult.data!;
  }

  Future<String> optimizeTextFromAI(OptimizeFromAIParams params) async {
    var result = await aiService.optimizeText(
        relatedEntityId: params.relatedEntityId,
        restaurantId: params.restaurantId,
        textToOptimize: params.textToOptimize,
        textToOptimizeType: params.textToOptimizeType,
        lang: params.lang);
    return result.data!;
  }

  Future<String> getAutoAiPropositionReviewAnswer(String reviewId) async {
    var result = await aiInteractionsService.getAutoAiPropositionReviewAnswer(
        reviewId, RelatedCollectionEntity.REVIEWS);
    if (result.data != null && result.data!.isEmpty) {
      return '';
    }
    var reviewsInteractions = result.data!
        .where((aiInteraction) => [
              AiInteractionType.answer_review,
              AiInteractionType.answer_review_advanced_settings,
              AiInteractionType.optimize_review_answer,
              AiInteractionType.optimize_review_answer_advanced_settings
            ].contains(aiInteraction.type))
        .toList();
    if (reviewsInteractions.isEmpty) {
      return '';
    }
    reviewsInteractions.sort((a, b) => a.createdAt.compareTo(b.createdAt));
    AiInteraction? firstAiInteraction = reviewsInteractions.first;
    if (firstAiInteraction.completionText != null) {
      return firstAiInteraction.completionText!;
    }
    return '';
  }

  Future<bool> deleteReviewById(String reviewId) async {
    try {
      final res = await reviewService.deleteReviewById(reviewId);

      if (res.error) {
        throw Exception(res.message);
      }

      return true;
    } on Exception {
      rethrow;
    }
  }

  Future<ApiResult<ReviewerNameValidationResponse>> getReviewerNameValidation(
      String reviewId, String lang) async {
    try {
      final res = await reviewService.getReviewerNameValidation(reviewId, lang);

      if (res.error) {
        throw Exception(res.message);
      }

      return res;
    } on Exception {
      rethrow;
    }
  }
}

class Watcher<T> {
  Timer? timer;
  BehaviorSubject<T?> value;

  Watcher({
    required this.timer,
    required this.value,
  });
}

class ReviewsStateSnackBar {
  final FetchedStatus status;
  final List<PlatformKeys> platformsKeys;
  ReviewsStateSnackBar({required this.status, required this.platformsKeys});
}

class ReviewReplyParams {
  final Review review;
  final String restaurantId;
  final String text;
  final KeywordAnalysis keywordsAnalysis;
  final UbereatsPromotionValue? ubereatsPromotionValue;

  ReviewReplyParams(
      {required this.review,
      required this.restaurantId,
      required this.text,
      required this.keywordsAnalysis,
      this.ubereatsPromotionValue});

  toDTO() {
    if (ubereatsPromotionValue == null) {
      return {
        "comment": {"text": text, "keywordAnalysis": keywordsAnalysis.toJson()}
      };
    }
    return {
      "comment": {
        "text": text,
        "keywordAnalysis": keywordsAnalysis.toJson(),
        "ubereatsPromotionValue": ubereatsPromotionValue?.name
      }
    };
  }
}

class ReviewReplyByMailParams {
  final User user;
  final Review review;
  final String restaurantId;
  final String fromName;
  final String fromEmail;
  final String object;
  final String text;

  ReviewReplyByMailParams(
      {required this.user,
      required this.review,
      required this.restaurantId,
      required this.fromName,
      required this.fromEmail,
      required this.object,
      required this.text});
}

typedef TranslateReviewAnswerFromAIParams = ({
  String lang,
  String reviewId,
  String restaurantId,
  String text
});

typedef TranslateReviewTextFromAIParams = ({
  String lang,
  String reviewId,
  String restaurantId,
  String text,
  bool isPrivateReview
});
