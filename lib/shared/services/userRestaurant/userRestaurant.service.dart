import 'package:malou/app_config.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/helpers/http_client.dart';
import 'package:malou/shared/models/user.model.dart';

final apiUrl = '${AppConfig.shared.baseUrl}/users/restaurants';
final httpClient = DioClient.instance.client;

class UserRestaurantService {
  static Future<ApiResult<UserRestaurant>> updateUserRestaurantById(
      String userRestaurantId, UserRestaurantDTO userRestaurantDTO) async {
    try {
      final response = await httpClient.put('$apiUrl/$userRestaurantId',
          data: userRestaurantDTO.toJson());
      if (response.statusCode == 201) {
        Map<String, dynamic> responseData = response.data;
        return ApiResult.fromJson(responseData,
            fromJsonT: (json) => UserRestaurant.fromJson(json));
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }
}
