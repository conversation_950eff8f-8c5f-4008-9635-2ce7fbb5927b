import 'package:malou/app_config.dart';
import 'package:malou/shared/helpers/api_result.model.dart';

import 'package:malou/shared/helpers/http_client.dart';
import 'package:malou/shared/models/medias/folder.model.dart';

final apiUrl = '${AppConfig.shared.baseUrl}/folders';
final httpClient = DioClient.instance.client;

class FolderService {
  Future<ApiResult<List<Folder>>> getFolders(
    String restaurantId,
  ) async {
    final response = await httpClient.get('$apiUrl/restaurants/$restaurantId');

    Map<String, dynamic> responseData = response.data;
    var result = ApiResult<List<Folder>>.fromJson(responseData,
        fromJsonT: (json) =>
            (json as List).map((e) => Folder.fromJson(e)).toList());
    return result;
  }
}
