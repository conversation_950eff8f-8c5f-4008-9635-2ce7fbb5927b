import 'dart:async';

import 'package:malou/app_config.dart';
import 'package:malou/pages/restaurant/pages/posts/tabs/social-networks/feed/feed.controller.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/helpers/http_client.dart';
import 'package:malou/shared/helpers/pagination.model.dart';
import 'package:malou/shared/helpers/utils.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/medias/medias.service.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

final apiUrl = '${AppConfig.shared.baseUrl}/posts';
final httpClient = DioClient.instance.client;

class PostsService {
  final MediasService mediaService = MediasService();

  Future<ApiResult<PaginatedPosts>> getPostsPaginated({
    required String restaurantId,
    required int pageNumber,
    required int pageSize,
    required String source,
    required List<PublicationStatus> publicationStatus,
    required List<String> platforms,
    bool isStory = false,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final response = await httpClient
          .get("$apiUrl/restaurants/$restaurantId", queryParameters: {
        'page_number': pageNumber,
        'page_size': pageSize,
        'source': source,
        'platforms': platforms,
        'category': source,
        'sort_order': -1,
        'sort_by': 'date',
        'period': 'last_and_coming_six_months',
        'publication_status':
            publicationStatus.map((pS) => pS.name.toLowerCase()).toList(),
        'is_story': isStory,
        'total': 0,
        'start_date':
            startDate ?? DateTime.now().subtract(const Duration(days: 180)),
        'end_date': endDate ?? DateTime.now().add(const Duration(days: 180)),
      });
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        var result = ApiResult<PaginatedPosts>.fromJson(responseData,
            fromJsonT: (data) => PaginatedPosts(
                pagination: Pagination.fromJson(data['pagination']),
                posts: List<Post>.from(
                    data["posts"].map((x) => Post.fromJson(x)))));
        if (publicationStatus.contains(PublicationStatus.PUBLISHED)) {
          result.data?.posts = result.data!.posts
              .where((element) => element.socialLink != null
                  ? !element.socialLink!.contains('photo.php')
                  : true)
              .toList();
        }
        return result;
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  Future<ApiResult<void>> synchronize(
      String restaurantId, List<String> platforms) async {
    try {
      final response = await httpClient.post(
          "$apiUrl/restaurants/$restaurantId/synchronize",
          data: {'platformsKeys': platforms});
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        return ApiResult.fromJson(responseData);
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  Future<ApiResult<void>> synchronizeStories(
      String restaurantId, List<String> platforms) async {
    try {
      final response = await httpClient.post(
          "$apiUrl/restaurants/$restaurantId/stories/synchronize",
          data: {'platformsKeys': platforms});
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        return ApiResult.fromJson(responseData);
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  Future<ApiResult<List<PostLocation>>> searchPageLocationByText(
      String text,
      bool onlyWithLocation,
      List<String> whitelistedPageIds,
      String? wantedPlatformSocialId,
      String? city) async {
    try {
      final response = await httpClient.get(
        "${AppConfig.shared.baseUrl}/facebook/me/pages",
        queryParameters: {
          'q': text,
          'onlyWithLocation': onlyWithLocation,
          'whitelistedPageIds[]': whitelistedPageIds,
          'wantedPlatformSocialId': wantedPlatformSocialId,
          'city': city,
        },
      );
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        return ApiResult<List<PostLocation>>.fromJson(responseData,
            fromJsonT: (data) => (data as List<dynamic>)
                .map((postLocation) => PostLocation.fromJson(postLocation))
                .toList());
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  Future<ApiResult<Post>> createPost(Post post, bool? isDraft) async {
    Post postData = post.copyWith(id: null);
    postData.createdFromDeviceType = DeviceType.MOBILE;

    try {
      final response = await httpClient
          .post("$apiUrl/restaurants/${post.restaurantId}", data: {
        'post': removeNullFieldsFromJson(postData.toJson()),
        'keys': post.keys.map((e) => e.name).toList(),
        'draft': isDraft
      });
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        return ApiResult<Post>.fromJson(responseData,
            fromJsonT: (data) => Post.fromJson(data));
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  Future<ApiResult<List<Media>>> uploadAndResizePostMedias(Post post,
      {bool shouldResize = true}) async {
    try {
      if (post.attachments
          .where((element) => element.assetEntity != null)
          .isNotEmpty) {
        List<Media> uploadedMedias = await uploadAssetsV2(
            post.attachments
                .where((element) => element.assetEntity != null)
                .toList()
                .cast<Media>(),
            post.restaurantId);

        post = post.copyWith(
            attachments: post.attachments
                .map((e) => e.assetEntity != null
                    ? uploadedMedias
                        .firstWhere((element) =>
                            element.assetEntity?.id == e.assetEntity?.id)
                        .copyWith(
                            resizeMetadata: e.resizeMetadata,
                            isDirty: true,
                            transformationController:
                                e.transformationController)
                    : e)
                .cast<Media>()
                .toList());
      }

      if (!shouldResize) {
        return ApiResult(message: 'Media uploaded', data: post.attachments);
      }
      ApiResult<List<Media>> res = await resizeMediasBeforePost(
          post.attachments, post.source, post.postType!);
      if (res.error) {
        throw Exception('Error resizing attachments : ${res.message}');
      }
      return res;
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  Future<ApiResult<Post>> preparePost(Post post, bool isDraft,
      {List<Media>? resizedMedias}) async {
    if (resizedMedias != null && resizedMedias.isNotEmpty) {
      post = post.copyWith(attachments: resizedMedias);
    }
    Map<String, dynamic> postJson = removeNullFieldsFromJson(post.toJson());
    try {
      final response =
          await httpClient.put("$apiUrl/${post.id}/prepare", data: {
        'post': postJson,
        'keys': post.keys.map((e) => e.name).toList(),
        'draft': post.publicationDatePlanned == PublicationDatePlanned.draft
      }, queryParameters: {
        'restaurantId': post.restaurantId
      });
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        return ApiResult<Post>.fromJson(responseData,
            fromJsonT: (data) => Post.fromJson(data));
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e) {
      await Sentry.captureException(e);
      return ApiResult.error(e);
    }
  }

  Future<List<Media>> uploadAssets(
      List<Media> medias, String restaurantId) async {
    final res = await Future.wait(medias.map((media) async {
      if (media.assetEntity != null) {
        final uploadedMedia = await MediasService.uploadAssetEntity(
          restaurantId: restaurantId,
          assetEntity: media.assetEntity!,
        );
        return uploadedMedia.data?.copyWith(assetEntity: media.assetEntity);
      }
      return media;
    }));

    return res.cast<Media>();
  }

  Future<List<Media>> uploadAssetsV2(
      List<Media> medias, String restaurantId) async {
    final res = await Future.wait(medias.map((media) async {
      if (media.assetEntity != null) {
        final uploadedMedia = await mediaService.uploadAssetEntityV2(
          restaurantId: restaurantId,
          assetEntity: media.assetEntity!,
        );
        return uploadedMedia.data?.copyWith(assetEntity: media.assetEntity);
      }
      return media;
    }));

    return res.cast<Media>();
  }

  Future<ApiResult<List<Media>>> resizeMediasBeforePost(
      List<Media> medias, Source postSource, PostType type) async {
    try {
      List<Media> mediasWithTransformations = medias.map((e) {
        if (e.isVideo()) {
          return e;
        }
        return e.copyWith(resizeMetadata: e.getImageTransformations());
      }).toList();

      final response =
          await httpClient.post("$apiUrl/attachments/resize", data: {
        'postMedias': mediasWithTransformations.map((e) => e.toJson()).toList(),
        'postSource': postSource.name.toLowerCase(),
        'postType': type.name,
      });
      if (response.statusCode == 201) {
        Map<String, dynamic> responseData = response.data;
        return ApiResult<List<Media>>.fromJson(responseData,
            fromJsonT: (data) => (data as List<dynamic>)
                .map((media) => Media.fromJson(media))
                .toList());
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  Future<ApiResult<Post>> getPostById(String postId) async {
    try {
      final response = await httpClient.get("$apiUrl/$postId");
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        return ApiResult<Post>.fromJson(responseData,
            fromJsonT: (data) => Post.fromJson(data));
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  Future<ApiResult<Post>> refresh(String postId) async {
    try {
      final response = await httpClient.get("$apiUrl/$postId/refresh");
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        return ApiResult<Post>.fromJson(responseData,
            fromJsonT: (data) => Post.fromJson(data));
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  Future<ApiResult<void>> deleteManyPost(
      List<String> postIds, String restaurantId) async {
    try {
      final response = await httpClient
          .post("$apiUrl/delete?restaurantId=$restaurantId", data: {
        'ids': postIds,
      });
      if (response.statusCode == 200) {
        return ApiResult<void>.fromJson(response.data);
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  Future<ApiResult<List<Post>>> getPostsByBindingId({
    required String bindingId,
    String bindingIdKey = "bindingId",
  }) async {
    try {
      final response = await httpClient.get(
          '$apiUrl/by_binding_id?binding_id=$bindingId&binding_id_key=$bindingIdKey');
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        return ApiResult<List<Post>>.fromJson(responseData,
            fromJsonT: (data) =>
                (data as List<dynamic>).map((p) => Post.fromJson(p)).toList());
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  Future<ApiResult<Post>> updatePost(
      {required String postId,
      required Map<String, dynamic> postUpdate}) async {
    try {
      final response =
          await httpClient.put('$apiUrl/$postId', data: postUpdate);
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        return ApiResult<Post>.fromJson(responseData,
            fromJsonT: (data) => Post.fromJson(data));
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  Future<ApiResult> deletePost(String postId) async {
    try {
      final response = await httpClient.delete('$apiUrl/$postId');
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;
        return ApiResult.fromJson(responseData);
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }

  Future<void> swapPlannedPublicationDates(List<ReassignItem> reassign) async {
    final response = await httpClient.post(
        '$apiUrl/swap-planned-publication-dates',
        data: {'reassign': reassign.map((e) => e.toJson()).toList()});
    if (response.statusCode == 200) {
      return;
    } else {
      throw Exception(response.data);
    }
  }
}
