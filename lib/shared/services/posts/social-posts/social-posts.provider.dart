import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/posts/posts.service.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:rxdart/rxdart.dart';

class SocialPostsProvider extends ChangeNotifier {
  PostsService postsService;

  SocialPostsProvider(
      {RestaurantProvider? restaurantProvider, required this.postsService});

  final PagingController<int, Post> _publishedSocialPostsPagingController =
      PagingController<int, Post>(firstPageKey: 0);
  final PagingController<int, Post> _draftSocialPostsPagingController =
      PagingController<int, Post>(firstPageKey: 0);
  final PagingController<int, Post> _scheduledSocialPostsPagingController =
      PagingController<int, Post>(firstPageKey: 0);
  final PagingController<int, Post> _errorSocialPostsPagingController =
      PagingController<int, Post>(firstPageKey: 0);
  final PagingController<int, Post> _feedPagingController =
      PagingController(firstPageKey: 0);

  PagingController<int, Post> get publishedSocialPostsPagingController =>
      _publishedSocialPostsPagingController;

  PagingController<int, Post> get draftSocialPostsPagingController =>
      _draftSocialPostsPagingController;

  PagingController<int, Post> get scheduledSocialPostsPagingController =>
      _scheduledSocialPostsPagingController;

  PagingController<int, Post> get errorSocialPostsPagingController =>
      _errorSocialPostsPagingController;
  PagingController<int, Post> get feedPagingController => _feedPagingController;

  PageRequestListener? publishedSocialPostsPageRequestListener;
  PageRequestListener? draftSocialPostsPageRequestListener;
  PageRequestListener? scheduledSocialPostsPageRequestListener;
  PageRequestListener? errorSocialPostsPageRequestListener;

  BehaviorSubject<bool> canDisplayFeed$ = BehaviorSubject<bool>.seeded(false);

  BehaviorSubject<Map<String, bool>> isPublishingSocialPost$ =
      BehaviorSubject<Map<String, bool>>.seeded({});

  BehaviorSubject<bool> showErrorPosts$ = BehaviorSubject<bool>.seeded(false);
  BehaviorSubject<bool> showPublishedPosts$ =
      BehaviorSubject<bool>.seeded(true);
  BehaviorSubject<bool> showDraftPosts$ = BehaviorSubject<bool>.seeded(true);
  BehaviorSubject<bool> showScheduledPosts$ =
      BehaviorSubject<bool>.seeded(true);

  RefreshController refreshController = RefreshController();

  refreshPostsPagingControllers(
      String restaurantId, PostsProvider postsProvider,
      {required Source source}) {
    // reset behavior subjects
    showErrorPosts$.add(true);
    showPublishedPosts$.add(true);
    showDraftPosts$.add(true);
    showScheduledPosts$.add(true);
    // remove listeners
    _publishedSocialPostsPagingController.removePageRequestListener(
        publishedSocialPostsPageRequestListener ?? (_) {});
    _draftSocialPostsPagingController.removePageRequestListener(
        draftSocialPostsPageRequestListener ?? (_) {});
    _scheduledSocialPostsPagingController.removePageRequestListener(
        scheduledSocialPostsPageRequestListener ?? (_) {});
    _errorSocialPostsPagingController.removePageRequestListener(
        errorSocialPostsPageRequestListener ?? (_) {});

    _initializeSocialPagingControllers(restaurantId, postsProvider);
  }

  void _initializeSocialPagingControllers(
      String restaurantId, PostsProvider postsProvider) {
    // reset paging controllers
    _publishedSocialPostsPagingController.itemList = [];
    _draftSocialPostsPagingController.itemList = [];
    _scheduledSocialPostsPagingController.itemList = [];
    _errorSocialPostsPagingController.itemList = [];

    String source = Source.social.name;
    publishedSocialPostsPageRequestListener =
        postsProvider.buildPagingRequestListener(
            source: source,
            _publishedSocialPostsPagingController,
            restaurantId,
            PublicationStatus.PUBLISHED);
    draftSocialPostsPageRequestListener =
        postsProvider.buildPagingRequestListener(
            source: source,
            _draftSocialPostsPagingController,
            restaurantId,
            PublicationStatus.DRAFT);
    scheduledSocialPostsPageRequestListener =
        postsProvider.buildPagingRequestListener(
            source: source,
            _scheduledSocialPostsPagingController,
            restaurantId,
            PublicationStatus.PENDING);
    errorSocialPostsPageRequestListener =
        postsProvider.buildPagingRequestListener(
            source: source,
            _errorSocialPostsPagingController,
            restaurantId,
            PublicationStatus.ERROR);

    _publishedSocialPostsPagingController
        .addPageRequestListener(publishedSocialPostsPageRequestListener!);
    _draftSocialPostsPagingController
        .addPageRequestListener(draftSocialPostsPageRequestListener!);
    _scheduledSocialPostsPagingController
        .addPageRequestListener(scheduledSocialPostsPageRequestListener!);
    _errorSocialPostsPagingController
        .addPageRequestListener(errorSocialPostsPageRequestListener!);

    // refresh paging controllers
    _publishedSocialPostsPagingController.refresh();
    _draftSocialPostsPagingController.refresh();
    _scheduledSocialPostsPagingController.refresh();
    _errorSocialPostsPagingController.refresh();
  }

  Future<void> savePost(PostsProvider postsProvider) async {
    Post post = postsProvider.postToEdit;
    if (post.publicationDatePlanned == PublicationDatePlanned.now) {
      post.plannedPublicationDate = DateTime.now();
    }

    _setPostTypeBeforePublish(post);

    _addHashtagsToText(post);

    PagingController<int, Post>? pagingControllerToRemoveFrom;
    PagingController<int, Post>? pagingControllerToAddTo;
    BehaviorSubject<bool>? postsSectionToShow$;

    List<PagingController<int, Post>> pagingControllers = [
      draftSocialPostsPagingController,
      scheduledSocialPostsPagingController,
      errorSocialPostsPagingController
    ];

    for (var controller in pagingControllers) {
      var index = controller.itemList
          ?.cast<Post?>()
          .firstWhere((element) => element?.id == post.id, orElse: () => null);
      if (index != null) {
        pagingControllerToRemoveFrom = controller;
        break;
      }
    }
    if (pagingControllerToRemoveFrom != null) {
      postsProvider.removePostWithErrorFromList(
          pagingControllerToRemoveFrom, post.id!);
    }
    post.errorData = null;
    post.errorStage = null;
    switch (post.publicationDatePlanned) {
      case PublicationDatePlanned.draft:
        pagingControllerToAddTo = draftSocialPostsPagingController;
        postsSectionToShow$ = showDraftPosts$;
        post.published = PublicationStatus.DRAFT;

        break;
      default:
        pagingControllerToAddTo = scheduledSocialPostsPagingController;
        postsSectionToShow$ = showScheduledPosts$;
        post.published = PublicationStatus.PENDING;

        break;
    }

    postsProvider.updatePost(postId: post.id!, postUpdate: {
      'published': publishedValues.reverse[post.published],
    });

    postsProvider.addTempPostToList(pagingControllerToAddTo, post, post.id!);

    postsProvider.showLoadingPost(
        postsProvider.postBeingCreated$, post.id!, postsSectionToShow$);

    postsProvider.startPostCreation(
        post,
        post.id!,
        postsProvider.postBeingCreated$,
        pagingControllerToAddTo,
        postsSectionToShow$,
        isPublishingSocialPost$);
  }

  void _setPostTypeBeforePublish(Post post) {
    if (post.attachments.length == 1 &&
        (post.postType == PostType.REEL || post.attachments.first.isVideo())) {
      post.postType = PostType.REEL;
      return;
    }
    if (post.attachments.length == 1 && post.attachments.first.isImage()) {
      post.postType = PostType.IMAGE;
      return;
    }
    post.postType = PostType.CAROUSEL;
    return;
  }

  void _addHashtagsToText(Post post) {
    post.text =
        '${post.text}\n${post.hashtags.selected.map((e) => e.text).join(' ')}';
  }

  Future<PaginatedPosts> getFeed({
    required String restaurantId,
    required int pageNumber,
    required int pageSize,
  }) async {
    // todo: allow backend api to select all posts without date range
    // ignore: constant_identifier_names
    const LAST_100_YEARS = Duration(days: -100 * 365);
    // ignore: constant_identifier_names
    const NEXT_100_YEARS = Duration(days: 100 * 365);

    final res = await postsService.getPostsPaginated(
        restaurantId: restaurantId,
        pageNumber: pageNumber,
        pageSize: pageSize,
        publicationStatus: [
          PublicationStatus.DRAFT,
          PublicationStatus.PENDING,
          PublicationStatus.PUBLISHED,
        ],
        source: Source.social.name,
        isStory: false,
        platforms: [PlatformKeys.instagram.name],
        startDate: DateTime.now().add(LAST_100_YEARS),
        endDate: DateTime.now().add(NEXT_100_YEARS));
    return PaginatedPosts(
      pagination: res.data!.pagination,
      posts: res.data!.posts,
    );
  }

  Future<bool> canDisplayFeed({
    required String restaurantId,
  }) async {
    final res = await postsService.getPostsPaginated(
        restaurantId: restaurantId,
        pageNumber: 0,
        pageSize: 0,
        publicationStatus: [
          PublicationStatus.DRAFT,
          PublicationStatus.PENDING,
          PublicationStatus.PUBLISHED,
        ],
        source: Source.social.name,
        isStory: false,
        platforms: [PlatformKeys.instagram.name]);
    return (res.data?.pagination.total ?? 0) > 0;
  }

  bool removePostFromPagingController(Post post) {
    if (!post.canBeDeleted()) {
      return false;
    }
    switch (post.published) {
      case PublicationStatus.DRAFT:
        draftSocialPostsPagingController.itemList =
            draftSocialPostsPagingController.itemList!
                .where((p) => p.id != post.id)
                .toList();
        break;
      case PublicationStatus.PENDING:
        scheduledSocialPostsPagingController.itemList =
            scheduledSocialPostsPagingController.itemList!
                .where((p) => p.id != post.id)
                .toList();
        break;
      case PublicationStatus.ERROR || PublicationStatus.REJECTED:
        errorSocialPostsPagingController.itemList =
            errorSocialPostsPagingController.itemList!
                .where((p) => p.id != post.id)
                .toList();
        break;
      case PublicationStatus.PUBLISHED:
        publishedSocialPostsPagingController.itemList =
            publishedSocialPostsPagingController.itemList!
                .where((p) => p.id != post.id)
                .toList();
        break;
      default:
        break;
    }
    return true;
  }

  @override
  void dispose() {
    _publishedSocialPostsPagingController.dispose();
    _draftSocialPostsPagingController.dispose();
    _scheduledSocialPostsPagingController.dispose();
    _errorSocialPostsPagingController.dispose();
    super.dispose();
  }
}
