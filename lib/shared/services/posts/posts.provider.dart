import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/helpers/clarify_error.dart';
import 'package:malou/shared/helpers/text_editing_controller_stream.dart';
import 'package:malou/shared/models/ai_interaction.model.dart';
import 'package:malou/shared/models/brick.model.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/models/posts/post-hashtags.model.dart';
import 'package:malou/shared/models/posts/post-status.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/pages/ai_generation/pages/models/help_idea_button_state.model.dart';
import 'package:malou/shared/services/ai-interactions/ai_interactions.service.dart';
import 'package:malou/shared/services/ai/ai.service.dart';
import 'package:malou/shared/services/medias/medias.service.dart';
import 'package:malou/shared/services/posts/posts.service.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:rxdart/rxdart.dart';

typedef PostBeingCreated = ({String? postId, StepStatus? stepStatus});

class PostsProvider extends ChangeNotifier {
  // Dependencies
  // ----------------------------------------------------------------------------------------------
  // ignore: constant_identifier_names
  static const int MAX_POST_ATTACHMENTS = 10;
  static const String RELATED_ENTITY = 'POSTS';
  PostsService postsService;
  RestaurantProvider? restaurantProvider;
  AiService aiService;
  MediasService mediasService;

  BehaviorSubject<num> currentScore$ = BehaviorSubject.seeded(0);
  BehaviorSubject<List<({SimpleBrick brick, bool selected})>>
      brickSuggestions$ = BehaviorSubject.seeded([]);

  PostsProvider(
      {PostsService? postsService,
      RestaurantProvider? restaurantProvider,
      MediasService? mediasService,
      AiService? aiService})
      : postsService = postsService ?? PostsService(),
        restaurantProvider = restaurantProvider ?? RestaurantProvider(),
        aiService = aiService ?? AiService(),
        mediasService = mediasService ?? MediasService();

  // different subjects for sections of the social posts page
  // ----------------------------------------------------------------------------------------------

  late TabController tabController;
  late TabController postCreationTabsController;

  BehaviorSubject<int> selectedTabIndex$ = BehaviorSubject.seeded(0);

  Subject isSocialNetworksWarningVisible$ = BehaviorSubject.seeded(true);

  Subject isGoogleDisconnectedsWarningVisible$ = BehaviorSubject.seeded(true);

  BehaviorSubject<int> onPullToRefresh$ = BehaviorSubject.seeded(0);

  BehaviorSubject<bool> isPlatformsDisconnectedErrorShown$ =
      BehaviorSubject.seeded(false);

  // ----------------------------------------------------------------------------------------------

  // different subjects for post publishing and synchronization
  // ----------------------------------------------------------------------------------------------

  BehaviorSubject<PostBeingCreated> postBeingCreated$ =
      BehaviorSubject.seeded((postId: null, stepStatus: null));

  BehaviorSubject<Map<String, bool>> syncronizationStatusByRestaurant$ =
      BehaviorSubject.seeded({});

  ScrollController scrollController = ScrollController();

  BehaviorSubject<Media?> addedNewMedia$ = BehaviorSubject.seeded(null);

  BehaviorSubject<bool> loadingPostToEdit$ = BehaviorSubject.seeded(false);
  // ----------------------------------------------------------------------------------------------

  // post creation state
  // ----------------------------------------------------------------------------------------------
  static Post defaultPost = Post(
    keys: [PlatformKeys.instagram, PlatformKeys.facebook, PlatformKeys.mapstr],
    title: '',
    attachments: [],
    hashtags: PostHashtags(selected: [], suggested: []),
    createdAt: DateTime.now(),
    creationDate: DateTime.now(),
    isReelDisplayedInFeed:
        true, // apparently all posts are set to true for this field by default (even non instagram posts)
    published: PublicationStatus.DRAFT,
    restaurantId: '',
    shouldDuplicateInOtherPlatforms: false,
    isStory: false,
    socialAttachments: [],
    source: Source.social,
    text: '',
    tries: 0,
    updatedAt: DateTime.now(),
    userTags: [],
    plannedPublicationDate:
        DateTime.now().add(const Duration(days: 1, hours: 1)),
    publicationDatePlanned: PublicationDatePlanned.scheduled,
    postTextController: TextEditingControllerStream(),
  );

  bool _isPostFormValid = false;

  BehaviorSubject<bool> fromEdit$ = BehaviorSubject.seeded(false);

  bool get isPostFormValid => _isPostFormValid;

  set isPostFormValid(bool value) {
    _isPostFormValid = value;
    notifyListeners();
  }

  Post _postToEdit = defaultPost;

  Post get postToEdit => _postToEdit;

  AiInteractionsManager interactionsManager =
      AiInteractionsManager(aiInteractions: [], currentAiInteractionIndex: 0);

  Post initializePostToEdit(Post post) {
    _postToEdit = post;
    currentScore$.add(0);
    brickSuggestions$.add([]);
    notifyListeners();
    return post;
  }

  set postToEdit(Post post) {
    _postToEdit = post;
    notifyListeners();
  }

  Post _initialPost = defaultPost;

  Post get initialPost => _initialPost;
  set initialPost(Post post) {
    _initialPost = post;
    notifyListeners();
  }

  BehaviorSubject<List<CaptionProposalButtonState>> captionsProposalButtons$ =
      BehaviorSubject<List<CaptionProposalButtonState>>.seeded([]);

  BehaviorSubject<List<Media>> lastUpdatedMedias$ = BehaviorSubject.seeded([]);

  BehaviorSubject<Media?> usablePhotoForImageAnalysis$ =
      BehaviorSubject<Media?>.seeded(null);

  void updateUsablePhotoForImageAnalysis({List<Media>? attachments}) {
    attachments ??= postToEdit.attachments;
    var hasPhotoAttachment = attachments
        .where((element) => element.type == MediaType.photo.name)
        .isNotEmpty;
    Media? usablePhotoForImageAnalysis = hasPhotoAttachment
        ? attachments
            .firstWhere((element) => element.type == MediaType.photo.name)
        : null;
    usablePhotoForImageAnalysis$.add(usablePhotoForImageAnalysis);
  }

  void updatePostMediasSelection(Media media) {
    int indexOfMedia =
        postToEdit.attachments.indexWhere((element) => element.id == media.id);
    if (indexOfMedia >= 0) {
      if (indexOfMedia == selectedPostMediaIndex) {
        postToEdit.attachments.removeWhere((element) => element.id == media.id);
        _selectedPostMediaIndex = postToEdit.attachments.length - 1;
      } else {
        selectedPostMediaIndex = indexOfMedia;
      }
    } else {
      if (postToEdit.attachments.length >= PostsProvider.MAX_POST_ATTACHMENTS) {
        return;
      }
      media.transformationController = TransformationController();
      postToEdit.attachments.add(media.copyWith(isDirty: true));
      _selectedPostMediaIndex = postToEdit.attachments.length - 1;
      addedNewMedia$.add(media);
    }
    notifyListeners();
  }

  void reorderPostToEditAttachments(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    final Media media = postToEdit.attachments.removeAt(oldIndex);
    postToEdit.attachments.insert(newIndex, media);
    _selectedPostMediaIndex = newIndex > postToEdit.attachments.length - 1
        ? postToEdit.attachments.length - 1
        : newIndex;
    notifyListeners();
  }

  void updatePostThumbnail(Media? media) {
    postToEdit.thumbnail = media;
    notifyListeners();
  }

  int _selectedPostMediaIndex = -1;

  int get selectedPostMediaIndex => _selectedPostMediaIndex;

  set selectedPostMediaIndex(int value) {
    _selectedPostMediaIndex = value;
    notifyListeners();
  }

  void startPublicationStatusWatcher(
      BehaviorSubject<Map<String, bool>> isPublishing$,
      String bindingId,
      String restaurantId) {
    CombineLatestStream.combine2(Stream.periodic(const Duration(seconds: 2)),
            isPublishing$, (a, b) => b)
        .where((event) => event[restaurantId] == true)
        .takeUntil(isPublishing$
            .skip(2)
            .where((event) => event[restaurantId] == false))
        .listen((_) async {
      ApiResult<List<Post>> res =
          await postsService.getPostsByBindingId(bindingId: bindingId);
      if (res.data!.isNotEmpty) {
        List<PostingStatus> postingStatus =
            res.data!.map((e) => PostingStatus.fromPost(e)).toList();
        if (postingStatus
            .every((element) => element.step.status != StepStatus.pending)) {
          isPublishing$
              .add({...isPublishing$.value, res.data![0].restaurantId: false});
          onPullToRefresh$.add(res.data![0].source == Source.social ? 0 : 1);
        }
      }
    });
  }
  // ----------------------------------------------------------------------------------------------

  // posts listing
  // ----------------------------------------------------------------------------------------------
  Future<List<Post>> fetchPosts({
    required String restaurantId,
    required int pageNumber,
    required int pageSize,
    required PublicationStatus publicationStatus,
    required String source,
    bool isStory = false,
  }) async {
    List<String> platforms = source == Source.seo.name
        ? [PlatformKeys.gmb.name]
        : [
            PlatformKeys.instagram.name,
            PlatformKeys.facebook.name,
            PlatformKeys.mapstr.name
          ];

    final res = await postsService.getPostsPaginated(
        restaurantId: restaurantId,
        pageNumber: pageNumber,
        pageSize: pageSize,
        publicationStatus: [publicationStatus],
        source: source,
        isStory: isStory,
        platforms: platforms);

    return res.data?.posts ?? [];
  }

  Future<void> startSyncPosts(
      String restaurantId, List<String> platforms) async {
    syncronizationStatusByRestaurant$
        .add({...syncronizationStatusByRestaurant$.value, restaurantId: true});
    await Future.wait<dynamic>([
      postsService.synchronize(restaurantId, platforms),
      postsService.synchronizeStories(restaurantId, platforms)
    ]);

    syncronizationStatusByRestaurant$
        .add({...syncronizationStatusByRestaurant$.value, restaurantId: false});
  }

  buildPagingRequestListener(PagingController pagingController,
      String restaurantId, PublicationStatus publicationStatus,
      {required String source, int pageSize = 24, bool isStory = false}) {
    return (pageKey) async {
      try {
        var newItems = await fetchPosts(
            restaurantId: restaurantId,
            pageNumber: pageKey,
            pageSize: pageSize,
            publicationStatus: publicationStatus,
            source: source,
            isStory: isStory);
        if (publicationStatus != PublicationStatus.ERROR) {
          newItems = newItems.where((element) => !element.hasError()).toList();
        }
        final isLastPage = newItems.length < pageSize;
        if (isLastPage) {
          pagingController.appendLastPage(newItems);
        } else {
          final nextPageKey = pageKey + 1;
          pagingController.appendPage(newItems, nextPageKey);
        }
      } catch (e) {
        pagingController.error = e;
      }
    };
  }

  Future<List<PostLocation>> searchPageLocationByText(
      String text, bool onlyWithLocation, List<String> whitelistedPageIds,
      [String? wantedPlatformSocialId, String? city]) async {
    final result = await postsService.searchPageLocationByText(text,
        onlyWithLocation, whitelistedPageIds, wantedPlatformSocialId, city);
    return result.data ?? [];
  }

  Future<Post> getPostById(String postId) async {
    final post = await postsService.getPostById(postId);
    return post.data!;
  }

  Future<Post?> refresh(String postId) async {
    final post = await postsService.refresh(postId);
    return post.data;
  }

  Future<void> deletePost(DeletePostParams param) {
    return postsService.deleteManyPost([param.post.id!], param.restaurantId);
  }

  Future<String> generateTextFromAI(GenerateTextFromAIParams params) async {
    if (params.keys.contains(PlatformKeys.gmb)) {
      var result = await aiService.generateSeoPostText(
          postId: params.postId,
          description: params.description,
          lang: params.lang,
          restaurantId: params.restaurantId);
      return result.data!;
    }

    var result = await aiService.generateSocialNetworkPostText(
        postId: params.postId,
        description: params.description,
        lang: params.lang,
        restaurantId: params.restaurantId);
    return result.data!;
  }

  Future<List<String>> generateTextFromAIAdvancedSettings(
      GenerateTextFromAIParams params) async {
    if (params.keys.contains(PlatformKeys.gmb)) {
      var result = await aiService.generateSeoPostTextSettings(
          postId: params.postId,
          description: params.description,
          lang: params.lang,
          restaurantId: params.restaurantId,
          shouldUseImageAnalysis: params.shouldUseImageAnalysis);
      return result.data!;
    }

    var result = await aiService.generateSocialNetworkPostTextWithSettings(
        postId: params.postId,
        description: params.description,
        lang: params.lang,
        restaurantId: params.restaurantId,
        shouldUseImageAnalysis: params.shouldUseImageAnalysis);
    return result.data!;
  }

  Future<String> translateTextFromAI(TranslateTextFromAIParams params) async {
    var result = await aiService.translate(
        relatedEntityCollection: RELATED_ENTITY,
        relatedEntityId: params.postId,
        type: AiInteractionType.post_translation,
        lang: params.lang,
        restaurantId: params.restaurantId,
        text: params.text);
    return result.data!;
  }

  Future<String> optimizeTextFromAI(OptimizeTextFromAIParams params) async {
    if (params.keys.contains(PlatformKeys.gmb)) {
      var result = await aiService.optimizeText(
          relatedEntityId: params.postId,
          restaurantId: params.restaurantId,
          textToOptimize: params.textToOptimize,
          textToOptimizeType: AiTextToOptimizeType.SEO_POST.name.toString());
      return result.data!;
    }

    var result = await aiService.optimizeText(
        relatedEntityId: params.postId,
        restaurantId: params.restaurantId,
        textToOptimize: params.textToOptimize,
        textToOptimizeType:
            AiTextToOptimizeType.SOCIAL_NETWORK_POST.name.toString());
    return result.data!;
  }

  Future<List<String>> generateHashtags(
      GenerateHashtagsFromAIParams params) async {
    if (params.postText.isEmpty) {
      throw Exception(
          MalouErrorCode.TEXT_IS_REQUIRED_FOR_AI_GENERATION.name.toString());
    }

    if (params.hashtags.isEmpty) {
      throw Exception(MalouErrorCode
          .HASHTAGS_ARE_REQUIRED_FOR_AI_GENERATION.name
          .toString());
    }

    var result = await aiService.generateHashtags(
        hashtags: params.hashtags,
        postText: params.postText,
        restaurantId: params.restaurantId,
        postId: params.postId);

    return result.data!;
  }

  Future<Post> createPost(Post post, bool isDraft) async {
    ApiResult<Post> res = await postsService.createPost(post, isDraft);
    if (res.error == true) {
      throw Exception(res.message);
    }

    return res.data!;
  }

  Future<Post> updatePost(
      {required String postId,
      required Map<String, dynamic> postUpdate}) async {
    ApiResult<Post> res =
        await postsService.updatePost(postId: postId, postUpdate: postUpdate);
    if (res.error == true) {
      throw Exception(res.message);
    }

    return res.data!;
  }

  Future<void> fetchMediaDescription({required String mediaId}) async {
    await mediasService.fetchMediaDescription(mediaId: mediaId);
  }

  void startPostCreation(
    Post post,
    String tempId,
    BehaviorSubject<PostBeingCreated> postBeingCreated$,
    PagingController<int, Post> pagingController,
    BehaviorSubject<bool> postsSectionToShow$,
    BehaviorSubject<Map<String, bool>> isPublishingPost$,
  ) async {
    try {
      ApiResult<List<Media>> resizedMediasResponse = await postsService
          .uploadAndResizePostMedias(post, shouldResize: !post.isReel());

      var res = await postsService.preparePost(
          post, post.publicationDatePlanned == PublicationDatePlanned.draft,
          resizedMedias: resizedMediasResponse.data);

      pagingController.itemList = pagingController.itemList!
          .map((e) => e.id == tempId ? res.data! : e)
          .toList();

      if (res.error == true) {
        onCreatePostError(
            tempId, post, res.message, postBeingCreated$, pagingController);
      }

      if (post.publicationDatePlanned == PublicationDatePlanned.now) {
        startSocialPostsStatusWatcher(res, isPublishingPost$);
      }

      postsSectionToShow$.add(true);

      if (post.publicationDatePlanned != PublicationDatePlanned.now) {
        postBeingCreated$.add((postId: null, stepStatus: StepStatus.success));
      }
    } catch (e) {
      onCreatePostError(
          tempId, post, e.toString(), postBeingCreated$, pagingController);
    }
  }

  void startSocialPostsStatusWatcher(ApiResult<Post> res,
      BehaviorSubject<Map<String, bool>> isPublishingPost$) {
    if (res.data?.bindingId == null) return;
    startPublicationStatusWatcher(
        isPublishingPost$, res.data!.bindingId!, res.data!.restaurantId);
    if (res.data?.restaurantId != null) {
      isPublishingPost$
          .add({...isPublishingPost$.value, res.data!.restaurantId: true});
    }
  }

  void showLoadingPost(BehaviorSubject<PostBeingCreated> postBeingCreated$,
      String tempId, BehaviorSubject<bool> postsSectionToShow$) {
    postBeingCreated$.add((postId: tempId, stepStatus: null));
    postsSectionToShow$.add(true);
  }

  void addTempPostToList(
      PagingController<int, Post> pagingController, Post post, String tempId) {
    pagingController.itemList = [
      post.copyWith(id: tempId),
      ...(pagingController.itemList ?? [])
    ];
  }

  void removePostWithErrorFromList(
      PagingController<int, Post> pagingController, String postId) {
    pagingController.itemList =
        pagingController.itemList!.where((p) => p.id != postId).toList();
  }

  void onCreatePostError(
      String tempId,
      Post post,
      String message,
      BehaviorSubject<PostBeingCreated> postBeingCreated$,
      PagingController<int, Post> pagingController) {
    post.errorData = message;
    pagingController.itemList = pagingController.itemList!
        .map((e) => e.id == tempId ? post : e)
        .toList();
    postBeingCreated$.add((postId: null, stepStatus: StepStatus.error));

    throw Exception(message);
  }

  Future<void> initPostInteractions() async {
    final result = await AiInteractionsService()
        .getAutoAiPropositionReviewAnswer(
            postToEdit.id!, RelatedCollectionEntity.POSTS);
    result.data?.sort((a, b) => a.createdAt.compareTo(b.createdAt));
    final interactions = result.data
            ?.where((interaction) =>
                interaction.isSuccessful() &&
                interaction.type != AiInteractionType.choose_post_hashtags &&
                interaction.type != AiInteractionType.translate)
            .toList() ??
        [];
    interactionsManager = AiInteractionsManager(
        aiInteractions: interactions
            .map((e) => MinimalAiInteraction(id: e.id, text: e.completionText!))
            .toList(),
        currentAiInteractionIndex:
            interactions.isEmpty ? 0 : interactions.length - 1);
  }

  @override
  void dispose() {
    tabController.dispose();
    postCreationTabsController.dispose();
    onPullToRefresh$.close();
    super.dispose();
  }
  // ----------------------------------------------------------------------------------------------
}

class DeletePostParams {
  final Post post;
  final String restaurantId;

  DeletePostParams({
    required this.post,
    required this.restaurantId,
  });
}

class GenerateTextFromAIParams {
  final String postId;
  final String description;
  final String restaurantId;
  final String lang;
  final List<PlatformKeys> keys;
  final bool? shouldUseImageAnalysis;

  GenerateTextFromAIParams(
      {required this.postId,
      required this.description,
      required this.restaurantId,
      required this.lang,
      required this.keys,
      this.shouldUseImageAnalysis = false});
}

class TranslateTextFromAIParams {
  final String postId;
  final String text;
  final String restaurantId;
  final String lang;

  TranslateTextFromAIParams({
    required this.postId,
    required this.text,
    required this.restaurantId,
    required this.lang,
  });
}

class OptimizeTextFromAIParams {
  final String postId;
  final String textToOptimize;
  final String restaurantId;
  final List<PlatformKeys> keys;

  OptimizeTextFromAIParams({
    required this.postId,
    required this.textToOptimize,
    required this.restaurantId,
    required this.keys,
  });
}

class GenerateHashtagsFromAIParams {
  final List<String> hashtags;
  final String postText;
  final String restaurantId;
  final String postId;

  GenerateHashtagsFromAIParams(
      {required this.restaurantId,
      required this.postId,
      required this.postText,
      required this.hashtags});
}
