import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/models/posts/story.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/posts/posts.service.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:rxdart/rxdart.dart';

class StoriesProvider extends ChangeNotifier {
  PostsService postsService;

  StoriesProvider(
      {RestaurantProvider? restaurantProvider, required this.postsService});

  BehaviorSubject<bool> showActiveStories$ =
      BehaviorSubject<bool>.seeded(false);

  final List<Story> _activeStories = [];

  List<Story> get activeStories => _activeStories;

  set activeStories(List<Story> stories) {
    _activeStories.clear();
    _activeStories.addAll(stories);
    notifyListeners();
  }

  final PagingController<int, Post> _publishedStoriesPagingController =
      PagingController<int, Post>(firstPageKey: 0);
  final PagingController<int, Post> _draftStoriesPagingController =
      PagingController<int, Post>(firstPageKey: 0);
  final PagingController<int, Post> _scheduledStoriesPagingController =
      PagingController<int, Post>(firstPageKey: 0);
  final PagingController<int, Post> _errorStoriesPagingController =
      PagingController<int, Post>(firstPageKey: 0);

  PagingController<int, Post> get publishedStoriesPagingController =>
      _publishedStoriesPagingController;

  PagingController<int, Post> get draftStoriesPagingController =>
      _draftStoriesPagingController;

  PagingController<int, Post> get scheduledStoriesPagingController =>
      _scheduledStoriesPagingController;

  PagingController<int, Post> get errorStoriesPagingController =>
      _errorStoriesPagingController;

  PageRequestListener? publishedStoriesPageRequestListener;
  PageRequestListener? draftStoriesPageRequestListener;
  PageRequestListener? scheduledStoriesPageRequestListener;
  PageRequestListener? errorStoriesPageRequestListener;

  BehaviorSubject<bool> showErrorPosts$ = BehaviorSubject<bool>.seeded(false);
  BehaviorSubject<bool> showPublishedPosts$ =
      BehaviorSubject<bool>.seeded(true);
  BehaviorSubject<bool> showDraftPosts$ = BehaviorSubject<bool>.seeded(true);
  BehaviorSubject<bool> showScheduledPosts$ =
      BehaviorSubject<bool>.seeded(true);

  refreshPostsPagingControllers(
      String restaurantId, PostsProvider postsProvider,
      {required Source source}) {
    // reset behavior subjects
    showErrorPosts$.add(false);
    showPublishedPosts$.add(true);
    showDraftPosts$.add(true);
    showScheduledPosts$.add(true);

    // remove listeners
    _publishedStoriesPagingController.removePageRequestListener(
        publishedStoriesPageRequestListener ?? (_) {});
    _draftStoriesPagingController
        .removePageRequestListener(draftStoriesPageRequestListener ?? (_) {});
    _scheduledStoriesPagingController.removePageRequestListener(
        scheduledStoriesPageRequestListener ?? (_) {});
    _errorStoriesPagingController
        .removePageRequestListener(errorStoriesPageRequestListener ?? (_) {});

    _initializeStoriesgControllers(restaurantId, postsProvider);
  }

  void _initializeStoriesgControllers(
      String restaurantId, PostsProvider postsProvider) {
    // reset paging controllers
    _publishedStoriesPagingController.itemList = [];
    _draftStoriesPagingController.itemList = [];
    _scheduledStoriesPagingController.itemList = [];
    _errorStoriesPagingController.itemList = [];

    String source = Source.social.name;
    publishedStoriesPageRequestListener =
        postsProvider.buildPagingRequestListener(
            source: source,
            _publishedStoriesPagingController,
            restaurantId,
            PublicationStatus.PUBLISHED,
            isStory: true);
    publishedStoriesPagingController.addListener(() {
      if (publishedStoriesPagingController.itemList != null &&
          publishedStoriesPagingController.itemList!.isNotEmpty &&
          publishedStoriesPagingController.itemList!
              .where((element) =>
                  element.socialCreatedAt != null &&
                  element.published == PublicationStatus.PUBLISHED &&
                  DateTime.now().difference(element.socialCreatedAt!).inDays <=
                      1)
              .isNotEmpty) {
        showActiveStories$.add(true);
        activeStories = publishedStoriesPagingController.itemList!
            .where((element) {
              const maxDaysBeforeExpiring = 0;
              final diffDays =
                  DateTime.now().difference(element.socialCreatedAt!).inDays;

              return element.socialCreatedAt != null &&
                  element.published == PublicationStatus.PUBLISHED &&
                  diffDays <= maxDaysBeforeExpiring;
            })
            .map((e) => Story.fromPost(e))
            .toList();
        activeStories
            .sort((a, b) => b.socialCreatedAt!.compareTo(a.socialCreatedAt!));
        notifyListeners();
      } else {
        showActiveStories$.add(false);
      }
    });
    draftStoriesPageRequestListener = postsProvider.buildPagingRequestListener(
        source: source,
        _draftStoriesPagingController,
        restaurantId,
        PublicationStatus.DRAFT,
        isStory: true);
    scheduledStoriesPageRequestListener =
        postsProvider.buildPagingRequestListener(
            source: source,
            _scheduledStoriesPagingController,
            restaurantId,
            PublicationStatus.PENDING,
            isStory: true);
    errorStoriesPageRequestListener = postsProvider.buildPagingRequestListener(
        source: source,
        _errorStoriesPagingController,
        restaurantId,
        PublicationStatus.ERROR,
        isStory: true);

    _publishedStoriesPagingController
        .addPageRequestListener(publishedStoriesPageRequestListener!);
    _draftStoriesPagingController
        .addPageRequestListener(draftStoriesPageRequestListener!);
    _scheduledStoriesPagingController
        .addPageRequestListener(scheduledStoriesPageRequestListener!);
    _errorStoriesPagingController
        .addPageRequestListener(errorStoriesPageRequestListener!);

    // refresh paging controllers
    _publishedStoriesPagingController.refresh();
    _draftStoriesPagingController.refresh();
    _scheduledStoriesPagingController.refresh();
    _errorStoriesPagingController.refresh();
  }

  bool removePostFromPagingController(Post post) {
    if (!post.canBeDeleted()) {
      return false;
    }
    switch (post.published) {
      case PublicationStatus.DRAFT:
        draftStoriesPagingController.itemList = draftStoriesPagingController
            .itemList!
            .where((p) => p.id != post.id)
            .toList();
        break;
      case PublicationStatus.PENDING:
        scheduledStoriesPagingController.itemList =
            scheduledStoriesPagingController.itemList!
                .where((p) => p.id != post.id)
                .toList();
        break;
      case PublicationStatus.ERROR || PublicationStatus.REJECTED:
        errorStoriesPagingController.itemList = errorStoriesPagingController
            .itemList!
            .where((p) => p.id != post.id)
            .toList();
        break;
      case PublicationStatus.PUBLISHED:
        publishedStoriesPagingController.itemList =
            publishedStoriesPagingController.itemList!
                .where((p) => p.id != post.id)
                .toList();
        break;
      default:
        break;
    }
    return true;
  }

  @override
  void dispose() {
    _publishedStoriesPagingController.dispose();
    _draftStoriesPagingController.dispose();
    _scheduledStoriesPagingController.dispose();
    _errorStoriesPagingController.dispose();
    super.dispose();
  }
}
