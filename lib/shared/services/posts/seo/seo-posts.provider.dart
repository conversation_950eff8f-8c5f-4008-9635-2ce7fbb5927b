import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/posts/posts.service.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:rxdart/rxdart.dart';

class SeoPostsProvider extends ChangeNotifier {
  PostsService postsService;

  SeoPostsProvider(
      {RestaurantProvider? restaurantProvider, required this.postsService});

  final PagingController<int, Post> _publishedSeoPostsPagingController =
      PagingController<int, Post>(firstPageKey: 0);
  final PagingController<int, Post> _draftSeoPostsPagingController =
      PagingController<int, Post>(firstPageKey: 0);
  final PagingController<int, Post> _scheduledSeoPostsPagingController =
      PagingController<int, Post>(firstPageKey: 0);
  final PagingController<int, Post> _errorSeoPostsPagingController =
      PagingController<int, Post>(firstPageKey: 0);

  PagingController<int, Post> get publishedSeoPostsPagingController =>
      _publishedSeoPostsPagingController;

  PagingController<int, Post> get draftSeoPostsPagingController =>
      _draftSeoPostsPagingController;

  PagingController<int, Post> get scheduledSeoPostsPagingController =>
      _scheduledSeoPostsPagingController;

  PagingController<int, Post> get errorSeoPostsPagingController =>
      _errorSeoPostsPagingController;

  PageRequestListener? publishedSeoPostsPageRequestListener;
  PageRequestListener? draftSeoPostsPageRequestListener;
  PageRequestListener? scheduledSeoPostsPageRequestListener;
  PageRequestListener? errorSeoPostsPageRequestListener;

  BehaviorSubject<Map<String, bool>> isPublishingSeoPost$ =
      BehaviorSubject<Map<String, bool>>.seeded({});

  BehaviorSubject<bool> showErrorPosts$ = BehaviorSubject<bool>.seeded(false);
  BehaviorSubject<bool> showPublishedPosts$ =
      BehaviorSubject<bool>.seeded(true);
  BehaviorSubject<bool> showDraftPosts$ = BehaviorSubject<bool>.seeded(true);
  BehaviorSubject<bool> showScheduledPosts$ =
      BehaviorSubject<bool>.seeded(true);

  RefreshController refreshController = RefreshController();

  refreshPostsPagingControllers(
      String restaurantId, PostsProvider postsProvider,
      {required Source source}) {
    // reset behavior subjects
    showErrorPosts$.add(true);
    showPublishedPosts$.add(true);
    showDraftPosts$.add(true);
    showScheduledPosts$.add(true);

    // remove listeners
    _publishedSeoPostsPagingController.removePageRequestListener(
        publishedSeoPostsPageRequestListener ?? (_) {});
    _draftSeoPostsPagingController
        .removePageRequestListener(draftSeoPostsPageRequestListener ?? (_) {});
    _scheduledSeoPostsPagingController.removePageRequestListener(
        scheduledSeoPostsPageRequestListener ?? (_) {});
    _errorSeoPostsPagingController
        .removePageRequestListener(errorSeoPostsPageRequestListener ?? (_) {});

    _initializeSeoPagingControllers(restaurantId, postsProvider);
  }

  void _initializeSeoPagingControllers(
      String restaurantId, PostsProvider postsProvider) {
    // reset paging controllers
    _publishedSeoPostsPagingController.itemList = [];
    _draftSeoPostsPagingController.itemList = [];
    _scheduledSeoPostsPagingController.itemList = [];
    _errorSeoPostsPagingController.itemList = [];
    String source = Source.seo.name;
    publishedSeoPostsPageRequestListener =
        postsProvider.buildPagingRequestListener(
            source: source,
            _publishedSeoPostsPagingController,
            restaurantId,
            PublicationStatus.PUBLISHED);
    draftSeoPostsPageRequestListener = postsProvider.buildPagingRequestListener(
        source: source,
        _draftSeoPostsPagingController,
        restaurantId,
        PublicationStatus.DRAFT);
    scheduledSeoPostsPageRequestListener =
        postsProvider.buildPagingRequestListener(
            source: source,
            _scheduledSeoPostsPagingController,
            restaurantId,
            PublicationStatus.PENDING);
    errorSeoPostsPageRequestListener = postsProvider.buildPagingRequestListener(
        source: source,
        _errorSeoPostsPagingController,
        restaurantId,
        PublicationStatus.ERROR);

    _publishedSeoPostsPagingController
        .addPageRequestListener(publishedSeoPostsPageRequestListener!);
    _draftSeoPostsPagingController
        .addPageRequestListener(draftSeoPostsPageRequestListener!);
    _scheduledSeoPostsPagingController
        .addPageRequestListener(scheduledSeoPostsPageRequestListener!);
    _errorSeoPostsPagingController
        .addPageRequestListener(errorSeoPostsPageRequestListener!);

    // refresh paging controllers
    _publishedSeoPostsPagingController.refresh();
    _draftSeoPostsPagingController.refresh();
    _scheduledSeoPostsPagingController.refresh();
    _errorSeoPostsPagingController.refresh();
  }

  Future<void> savePost(PostsProvider postsProvider) async {
    Post post = postsProvider.postToEdit;
    if (post.publicationDatePlanned == PublicationDatePlanned.now) {
      post.plannedPublicationDate = DateTime.now();
    }

    PagingController<int, Post>? pagingControllerToRemoveFrom;
    PagingController<int, Post>? pagingControllerToAddTo;
    BehaviorSubject<bool>? postsSectionToShow$;

    List<PagingController<int, Post>> pagingControllers = [
      draftSeoPostsPagingController,
      scheduledSeoPostsPagingController,
      errorSeoPostsPagingController
    ];

    for (var controller in pagingControllers) {
      var index = controller.itemList
          ?.cast<Post?>()
          .firstWhere((element) => element?.id == post.id, orElse: () => null);
      if (index != null) {
        pagingControllerToRemoveFrom = controller;
        break;
      }
    }
    if (pagingControllerToRemoveFrom != null) {
      postsProvider.removePostWithErrorFromList(
          pagingControllerToRemoveFrom, post.id!);
    }
    post.errorData = null;
    post.errorStage = null;
    switch (post.publicationDatePlanned) {
      case PublicationDatePlanned.draft:
        pagingControllerToAddTo = draftSeoPostsPagingController;
        postsSectionToShow$ = showDraftPosts$;
        post.published = PublicationStatus.DRAFT;

        break;
      default:
        pagingControllerToAddTo = scheduledSeoPostsPagingController;
        postsSectionToShow$ = showScheduledPosts$;
        post.published = PublicationStatus.PENDING;

        break;
    }

    postsProvider.updatePost(postId: post.id!, postUpdate: {
      'published': publishedValues.reverse[post.published],
    });

    postsProvider.addTempPostToList(pagingControllerToAddTo, post, post.id!);

    postsProvider.showLoadingPost(
        postsProvider.postBeingCreated$, post.id!, postsSectionToShow$);

    postsProvider.startPostCreation(
        post,
        post.id!,
        postsProvider.postBeingCreated$,
        pagingControllerToAddTo,
        postsSectionToShow$,
        isPublishingSeoPost$);
  }

  bool removePostFromPagingController(Post post) {
    if (!post.canBeDeleted()) {
      return false;
    }
    switch (post.published) {
      case PublicationStatus.DRAFT:
        draftSeoPostsPagingController.itemList = draftSeoPostsPagingController
            .itemList!
            .where((p) => p.id != post.id)
            .toList();
        break;
      case PublicationStatus.PENDING:
        scheduledSeoPostsPagingController.itemList =
            scheduledSeoPostsPagingController.itemList!
                .where((p) => p.id != post.id)
                .toList();
        break;
      case PublicationStatus.ERROR || PublicationStatus.REJECTED:
        errorSeoPostsPagingController.itemList = errorSeoPostsPagingController
            .itemList!
            .where((p) => p.id != post.id)
            .toList();
        break;
      case PublicationStatus.PUBLISHED:
        publishedSeoPostsPagingController.itemList =
            publishedSeoPostsPagingController.itemList!
                .where((p) => p.id != post.id)
                .toList();
        break;
      default:
        break;
    }

    return true;
  }

  @override
  void dispose() {
    _publishedSeoPostsPagingController.dispose();
    _draftSeoPostsPagingController.dispose();
    _scheduledSeoPostsPagingController.dispose();
    _errorSeoPostsPagingController.dispose();
    super.dispose();
  }
}
