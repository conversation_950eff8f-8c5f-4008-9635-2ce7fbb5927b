import 'package:malou/app_config.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/helpers/http_client.dart';
import 'package:malou/shared/models/reviews.model.dart';

final apiUrl = '${AppConfig.shared.baseUrl}/private-reviews';
final httpClient = DioClient.instance.client;

class PrivateReviewService {
  static Future<ApiResult<Review?>> getPrivateReviewById(
      String reviewId) async {
    try {
      final response = await httpClient.get("$apiUrl/$reviewId");
      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = response.data;

        if (responseData["data"] == null) {
          return ApiResult(message: '', data: null, error: false);
        }

        var result = ApiResult<Review>.fromJson(responseData,
            fromJsonT: (json) => Review.fromJson(json));

        return result;
      } else {
        return ApiResult.error(response.data);
      }
    } catch (e, stackTrace) {
      return ApiResult.error(e, stackTrace: stackTrace);
    }
  }
}
