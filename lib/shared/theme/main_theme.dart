import 'package:flutter/material.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:google_fonts/google_fonts.dart';

class MainTheme {
  static ThemeData get theme {
    return ThemeData(
      useMaterial3: true,
      primaryColor: MalouColors.primary,
      scaffoldBackgroundColor: Colors.white,
      primarySwatch: Colors.blue,
      visualDensity: VisualDensity.adaptivePlatformDensity,
      fontFamily: GoogleFonts.poppins().fontFamily,
      textTheme: GoogleFonts.poppinsTextTheme().copyWith(
        /*Different text sizes */
        titleLarge: const TextStyle(
            height: 1.5,
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: MalouColors.textTitle),
        titleMedium: const TextStyle(
            height: 1.5,
            fontSize: 15,
            fontWeight: FontWeight.w600,
            color: MalouColors.textTitle),
        titleSmall: const TextStyle(
          height: 1.5,
          fontSize: 14.0,
          color: MalouColors.textBody,
        ),
        bodyLarge: const TextStyle(
          height: 1.5,
          fontSize: 13.0,
          color: MalouColors.textBody,
        ),
        bodyMedium: const TextStyle(
          height: 1.5,
          fontSize: 12.0,
          color: MalouColors.textBody,
        ),
        bodySmall: const TextStyle(
          height: 1.5,
          fontSize: 10.0,
          color: MalouColors.textBody,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
            backgroundColor: Colors.transparent,
            foregroundColor: Colors.white,
            disabledForegroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 26, vertical: 15),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            textStyle: const TextStyle(
              color: Colors.white,
              fontSize: 13,
              fontWeight: FontWeight.w600,
            ),
            elevation: 0),
      ),
      iconButtonTheme: IconButtonThemeData(
        style: IconButton.styleFrom(
          iconSize: 26,
          disabledForegroundColor: Colors.white,
          padding: const EdgeInsets.all(5),
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: MalouColors.primary,
          disabledForegroundColor: MalouColors.primary.withOpacity(0.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      ),
      iconTheme: const IconThemeData(
        color: Colors.white,
        size: 26,
      ),
      radioTheme: RadioThemeData(
        fillColor: WidgetStateColor.resolveWith((states) {
          const Set<WidgetState> interactiveStates = <WidgetState>{
            WidgetState.pressed,
            WidgetState.selected,
          };
          if (states.any(interactiveStates.contains)) {
            return MalouColors.primary;
          }
          return MalouColors.borderSecondary;
        }),
        overlayColor: WidgetStateProperty.all(MalouColors.primary),
      ),
      tooltipTheme: TooltipThemeData(
        triggerMode: TooltipTriggerMode.tap,
        decoration: BoxDecoration(
          color: MalouColors.textTitle,
          borderRadius: BorderRadius.circular(10),
        ),
      ),
      switchTheme: SwitchThemeData(
        trackOutlineColor:
            WidgetStateProperty.all(MalouColors.backgroundDark),
        thumbColor: WidgetStateColor.resolveWith((states) {
          const Set<WidgetState> interactiveStates = <WidgetState>{
            WidgetState.pressed,
            WidgetState.selected,
          };
          if (states.any(interactiveStates.contains)) {
            return MalouColors.primary;
          }
          return MalouColors.textBody;
        }),
        trackColor: WidgetStateProperty.all(MalouColors.backgroundDark),
        thumbIcon: WidgetStateProperty.all(
          const Icon(
            Icons.check,
            color: Colors.transparent,
            size: 20,
          ),
        ),
      ),
      dropdownMenuTheme: DropdownMenuThemeData(
        menuStyle: MenuStyle(
          alignment: Alignment.bottomCenter,
          backgroundColor: WidgetStateProperty.all(Colors.red),
        ),
      ),
    );
  }
}
