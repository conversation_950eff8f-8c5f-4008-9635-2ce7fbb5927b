enum Civility {
  female,
  male,
  other,
}

class ReviewerNameValidation {
  final Civility gender;
  final String firstName;
  final bool isFirstNameValid;
  final String lastName;
  final bool isLastNameValid;

  ReviewerNameValidation({
    required this.gender,
    required this.firstName,
    required this.isFirstNameValid,
    required this.lastName,
    required this.isLastNameValid,
  });

  factory ReviewerNameValidation.fromJson(Map<String, dynamic> json) {
    return ReviewerNameValidation(
      gender: Civility.values.byName(json["gender"]),
      firstName: json["firstName"],
      isFirstNameValid: json["isFirstNameValid"],
      lastName: json["lastName"],
      isLastNameValid: json["isLastNameValid"],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'gender': gender.name,
      'firstName': firstName,
      'isFirstNameValid': isFirstNameValid,
      'lastName': lastName,
      'isLastNameValid': isLastNameValid,
    };
    return json;
  }

  bool isDefaultReviewerNameValidation() {
    return gender == Civility.other &&
        firstName.isEmpty &&
        !isFirstNameValid &&
        lastName.isEmpty &&
        !isLastNameValid;
  }
}

class ReviewerNameValidationResponse {
  final ReviewerNameValidation reviewerNameValidation;

  ReviewerNameValidationResponse({
    required this.reviewerNameValidation,
  });

  factory ReviewerNameValidationResponse.fromJson(Map<String, dynamic> json) {
    return ReviewerNameValidationResponse(
      reviewerNameValidation:
          ReviewerNameValidation.fromJson(json['reviewerNameValidation']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'reviewer_name_validation': reviewerNameValidation.toJson(),
    };
  }
}
