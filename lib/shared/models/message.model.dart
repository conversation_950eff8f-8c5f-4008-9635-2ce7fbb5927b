import 'dart:convert';

import 'package:intl/intl.dart';
import 'package:malou/shared/helpers/utils.dart';
import 'package:malou/shared/models/message_user_info.model.dart';
import 'package:malou/shared/models/user.model.dart';
import 'package:photo_manager/photo_manager.dart';

// ignore: constant_identifier_names
enum MessageStatus { DELIVERED, PENDING, ERROR }

class MessagesApiResult {
  int count;
  List<Message> data;

  MessagesApiResult({
    required this.count,
    required this.data,
  });

  factory MessagesApiResult.fromJson(Map<String, dynamic> json) =>
      MessagesApiResult(
        count: json["count"],
        data: List<Message>.from(
            json["messages"].map((x) => Message.fromJson(x))),
      );

  factory MessagesApiResult.fromRawJson(String str) =>
      MessagesApiResult.fromJson(json.decode(str));
}

class Message {
  Message({
    required this.id,
    required this.conversationId,
    required this.key,
    required this.socialMessageId,
    required this.attachments,
    required this.createdAt,
    this.isDeleted = false,
    required this.isFromRestaurant,
    this.isUnsupportedAttachment = false,
    required this.reactions,
    required this.socialConversationId,
    required this.socialCreatedAt,
    this.status = MessageStatus.DELIVERED,
    this.story,
    this.text,
    required this.updatedAt,
    required this.userInfo,
    this.malouAuthor,
  });

  String id;
  String conversationId;
  String key;
  String socialMessageId;
  List<Attachment>? attachments;
  DateTime createdAt;
  bool isDeleted;
  bool isFromRestaurant;
  bool isUnsupportedAttachment;
  List<Reaction> reactions = [];
  String socialConversationId;
  DateTime socialCreatedAt;
  MessageStatus status;
  Story? story;
  String? text;
  DateTime updatedAt;
  UserInfo userInfo;
  dynamic malouAuthor;

  factory Message.fromRawJson(String str) => Message.fromJson(json.decode(str));

  factory Message.fromMessageDTO(MessageDTO messageDTO) {
    return Message(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      conversationId: messageDTO.conversationId,
      key: messageDTO.key,
      socialMessageId: '',
      attachments: messageDTO.attachments,
      createdAt: DateTime.now(),
      isDeleted: false,
      isFromRestaurant: messageDTO.isFromRestaurant,
      isUnsupportedAttachment: false,
      reactions: [],
      socialConversationId: messageDTO.socialConversationId,
      socialCreatedAt: DateTime.now(),
      status: messageDTO.status,
      story: null,
      text: messageDTO.text,
      updatedAt: DateTime.now(),
      userInfo: messageDTO.userInfo,
      malouAuthor: messageDTO.malouAuthor,
    );
  }

  String toRawJson() => json.encode(toJson());

  factory Message.fromJson(Map<String, dynamic> json) => Message(
        id: json["_id"],
        conversationId: json["conversationId"],
        key: json["key"],
        socialMessageId: json["socialMessageId"] ?? '',
        attachments: json["attachments"] != null
            ? List<Attachment>.from(
                json["attachments"]?.map((x) => Attachment.fromJson(x)))
            : null,
        createdAt: DateTime.parse(json["createdAt"]),
        isDeleted: json["isDeleted"],
        isFromRestaurant: json["isFromRestaurant"],
        isUnsupportedAttachment: json["isUnsupportedAttachment"],
        reactions: json["reactions"] != null
            ? List<Reaction>.from(
                json["reactions"]?.map((x) => Reaction.fromJson(x)))
            : [],
        socialConversationId: json["socialConversationId"] ?? '',
        socialCreatedAt: DateTime.parse(json["socialCreatedAt"]),
        status: EnumValues<MessageStatus>({
          "DELIVERED": MessageStatus.DELIVERED,
          "PENDING": MessageStatus.PENDING,
          "ERROR": MessageStatus.ERROR
        }).fromString(json["status"])!,
        story: json["story"] != null ? Story.fromJson(json["story"]) : null,
        text: json["text"],
        updatedAt: DateTime.parse(json["updatedAt"]),
        userInfo: UserInfo.fromJson(json["userInfo"]),
        malouAuthor: json["malouAuthor"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "conversationId": conversationId,
        "key": key,
        "socialMessageId": socialMessageId,
        "attachments": attachments != null
            ? List<dynamic>.from(attachments!.map((x) => x.toJson()))
            : null,
        "createdAt": createdAt.toIso8601String(),
        "isDeleted": isDeleted,
        "isFromRestaurant": isFromRestaurant,
        "isUnsupportedAttachment": isUnsupportedAttachment,
        "reactions": reactions.isNotEmpty
            ? List<dynamic>.from(reactions.map((x) => x.toJson()))
            : [],
        "socialConversationId": socialConversationId,
        "socialCreatedAt": socialCreatedAt.toIso8601String(),
        "status": status.name,
        "story": story?.toJson(),
        "text": text,
        "updatedAt": updatedAt.toIso8601String(),
        "userInfo": userInfo.toJson(),
        "malouAuthor": malouAuthor,
      };

  String displayMessageSentAt() {
    return DateFormat('HH:mm').format(socialCreatedAt.toLocal());
  }

  String getMessageDate() {
    return DateFormat('dd MMMM yyyy').format(createdAt);
  }

  bool compareSocialCreatedAt(Message message) {
    return message.socialCreatedAt == socialCreatedAt;
  }

  Message copyWith({
    String? id,
    String? conversationId,
    String? key,
    String? socialMessageId,
    List<Attachment>? attachments,
    DateTime? createdAt,
    bool? isDeleted,
    bool? isFromRestaurant,
    bool? isUnsupportedAttachment,
    List<Reaction>? reactions,
    String? socialConversationId,
    DateTime? socialCreatedAt,
    MessageStatus? status,
    Story? story,
    String? text,
    DateTime? updatedAt,
    UserInfo? userInfo,
    dynamic malouAuthor,
  }) {
    return Message(
      id: id ?? this.id,
      conversationId: conversationId ?? this.conversationId,
      key: key ?? this.key,
      socialMessageId: socialMessageId ?? this.socialMessageId,
      attachments: attachments ?? this.attachments,
      createdAt: createdAt ?? this.createdAt,
      isDeleted: isDeleted ?? this.isDeleted,
      isFromRestaurant: isFromRestaurant ?? this.isFromRestaurant,
      isUnsupportedAttachment:
          isUnsupportedAttachment ?? this.isUnsupportedAttachment,
      reactions: reactions ?? this.reactions,
      socialConversationId: socialConversationId ?? this.socialConversationId,
      socialCreatedAt: socialCreatedAt ?? this.socialCreatedAt,
      status: status ?? this.status,
      story: story ?? this.story,
      text: text ?? this.text,
      updatedAt: updatedAt ?? this.updatedAt,
      userInfo: userInfo ?? this.userInfo,
      malouAuthor: malouAuthor ?? this.malouAuthor,
    );
  }
}

class Attachment {
  Attachment({
    required this.url,
    required this.type,
    this.isDeviceMedia = false,
    this.assetEntity,
  });

  String url;
  String type;
  bool isDeviceMedia;
  AssetEntity? assetEntity;

  factory Attachment.fromRawJson(String str) =>
      Attachment.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Attachment.fromJson(Map<String, dynamic> json) => Attachment(
        url: json["url"],
        type: json["type"],
      );

  Map<String, dynamic> toJson() => {
        "url": url,
        "type": type,
      };
}

class Reaction {
  Reaction({
    this.reactionType,
    this.userInfo,
  });

  String? reactionType;
  UserInfo? userInfo;

  factory Reaction.fromRawJson(String str) =>
      Reaction.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Reaction.fromJson(Map<String, dynamic> json) => Reaction(
        reactionType: json["reactionType"],
        userInfo: json["userInfo"] == null
            ? null
            : UserInfo.fromJson(json["userInfo"]),
      );

  Map<String, dynamic> toJson() => {
        "reactionType": reactionType,
        "userInfo": userInfo?.toJson(),
      };
}

enum StoryType { mention, reply }

class Story {
  Story({
    required this.storyType,
    required this.storySocialUrl,
    required this.storySocialId,
    required this.storyMediaType,
  });

  String? storyType;
  String? storySocialUrl;
  String? storySocialId;
  String? storyMediaType;

  factory Story.fromRawJson(String str) => Story.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Story.fromJson(Map<String, dynamic> json) => Story(
        storyType: json["storyType"],
        storySocialUrl: json["storySocialUrl"],
        storySocialId: json["storySocialId"],
        storyMediaType: json["storyMediaType"] ?? 'image',
      );

  Map<String, dynamic> toJson() => {
        "storyType": storyType,
        "storySocialUrl": storySocialUrl,
        "storySocialId": storySocialId,
      };
}

class MessageDTO {
  String? text;
  List<Attachment>? attachments;
  String socialConversationId;
  String? socialCreatedAt;
  MessageStatus status;
  String? storyType;
  bool isFromRestaurant;
  String key;
  String conversationId;
  String malouAuthorId;
  User malouAuthor;
  UserInfo userInfo;

  MessageDTO({
    this.text,
    this.attachments,
    required this.socialConversationId,
    this.socialCreatedAt,
    this.status = MessageStatus.PENDING,
    this.storyType,
    this.isFromRestaurant = true,
    required this.key,
    required this.conversationId,
    required this.malouAuthorId,
    required this.malouAuthor,
    required this.userInfo,
  });

  factory MessageDTO.fromMessage(Message message) {
    return MessageDTO(
      text: message.text,
      attachments: message.attachments,
      socialConversationId: message.socialConversationId,
      socialCreatedAt: message.socialCreatedAt.toIso8601String(),
      status: message.status,
      storyType: message.story?.storyType,
      isFromRestaurant: message.isFromRestaurant,
      key: message.key,
      conversationId: message.conversationId,
      malouAuthorId: message.malouAuthor.id,
      malouAuthor: message.malouAuthor,
      userInfo: message.userInfo,
    );
  }

  Map<String, dynamic> toJson() => {
        "text": text,
        "attachments": attachments != null
            ? List<dynamic>.from(attachments!.map((x) => x.toJson()))
            : null,
        "socialConversationId": socialConversationId,
        "socialCreatedAt": socialCreatedAt,
        "storyType": storyType,
        "isFromRestaurant": isFromRestaurant,
        "key": key,
        "conversationId": conversationId,
        "malouAuthorId": malouAuthorId,
        "malouAuthor": malouAuthor.toJson(),
        "userInfo": userInfo.toJson(),
        "status": status.name,
      };

  MessageDTO copyWith({
    String? text,
    List<Attachment>? attachments,
    String? socialConversationId,
    String? socialCreatedAt,
    MessageStatus? status,
    String? storyType,
    bool? isFromRestaurant,
    String? key,
    String? conversationId,
    String? malouAuthorId,
    User? malouAuthor,
    UserInfo? userInfo,
  }) {
    return MessageDTO(
      text: text ?? this.text,
      attachments: attachments ?? this.attachments,
      socialConversationId: socialConversationId ?? this.socialConversationId,
      socialCreatedAt: socialCreatedAt ?? this.socialCreatedAt,
      status: status ?? this.status,
      storyType: storyType ?? this.storyType,
      isFromRestaurant: isFromRestaurant ?? this.isFromRestaurant,
      key: key ?? this.key,
      conversationId: conversationId ?? this.conversationId,
      malouAuthorId: malouAuthorId ?? this.malouAuthorId,
      malouAuthor: malouAuthor ?? this.malouAuthor,
      userInfo: userInfo ?? this.userInfo,
    );
  }
}
