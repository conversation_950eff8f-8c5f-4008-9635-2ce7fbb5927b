import 'package:malou/shared/services/hashtags/types.dart';
import 'package:objectid/objectid.dart';

import '../../helpers/utils.dart';

class Hashtag {
  String id;
  String text;
  String? restaurantId;
  bool isCustomerInput;
  bool isMain;
  HashtagType type;
  DateTime createdAt;
  DateTime updatedAt;
  bool? isNew;

  Hashtag({
    required this.id,
    required this.text,
    required this.type,
    required this.createdAt,
    required this.updatedAt,
    this.restaurantId,
    this.isCustomerInput = false,
    this.isMain = false,
    this.isNew = false,
  });

  factory Hashtag.fromJson(Map<String, dynamic> json) {
    return Hashtag(
      id: json['id'] ?? json['_id'],
      text: json['text'],
      restaurantId: json['restaurantId'],
      isCustomerInput: json['isCustomerInput'],
      isMain: json['isMain'] ?? false,
      type: hashtagsValues.map[json['type']]!,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : DateTime.now(),
    );
  }

  factory Hashtag.fromAiGeneration(
          GeneratedHashtagDto dto, String restaurantId) =>
      Hashtag(
          id: ObjectId().toString(),
          text: dto.text,
          restaurantId: restaurantId,
          type: dto.type,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now());

  Map<String, dynamic> toJson() => {
        'id': id,
        'text': text,
        'restaurantId': restaurantId,
        'isCustomerInput': isCustomerInput,
        'isMain': isMain,
        'type': type.name.toLowerCase(),
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
      };

  Hashtag copyWith({
    String? id,
    String? text,
    String? restaurantId,
    bool? isCustomerInput,
    bool? isMain,
    HashtagType? type,
    DateTime? createdAt,
    DateTime? updatedAt,
    isNew,
  }) =>
      Hashtag(
        id: id ?? this.id,
        text: text ?? this.text,
        restaurantId: restaurantId ?? this.restaurantId,
        isCustomerInput: isCustomerInput ?? this.isCustomerInput,
        isMain: isMain ?? this.isMain,
        type: type ?? this.type,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        isNew: isNew ?? this.isNew,
      );
}

enum HashtagType { RESTAURANT, DESCRIPTION, UNKNOWN }

final hashtagsValues = EnumValues({
  "description": HashtagType.DESCRIPTION,
  "restaurant": HashtagType.RESTAURANT,
  "unknown": HashtagType.UNKNOWN
});
