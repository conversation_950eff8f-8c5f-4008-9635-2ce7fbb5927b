// ignore_for_file: hash_and_equals

class Brick {
  Key key;
  Key text;
  String id;
  String category;
  DateTime createdAt;
  Gender? gender;
  int? meanSearchVol;
  Nature? nature;
  Number? number;
  int? sumSearchVol;
  DateTime updatedAt;
  String brickId;
  String subCategory;

  Brick({
    required this.key,
    required this.text,
    required this.id,
    required this.category,
    required this.createdAt,
    required this.gender,
    required this.meanSearchVol,
    required this.nature,
    required this.number,
    required this.sumSearchVol,
    required this.updatedAt,
    required this.brickId,
    required this.subCategory,
  });

  factory Brick.fromJson(Map<String, dynamic> json) => Brick(
        key: Key.fromJson(json["key"]),
        text: Key.fromJson(json["text"]),
        id: json["_id"],
        category: json['category'],
        createdAt: DateTime.parse(json["createdAt"]),
        gender: (json["gender"] != null && json["gender"] != "")
            ? Gender.values.byName(json["gender"])
            : null,
        meanSearchVol: json["meanSearchVol"],
        nature: (json["nature"] != null && json["nature"] != "")
            ? Nature.values.byName(json["nature"])
            : null,
        number: (json["number"] != null && json["number"] != "")
            ? Number.values.byName(json["number"])
            : null,
        sumSearchVol: json["sumSearchVol"],
        updatedAt: DateTime.parse(json["updatedAt"]),
        brickId: json["brickId"],
        subCategory: json["subCategory"],
      );

  Map<String, dynamic> toJson() => {
        "key": key.toJson(),
        "text": text.toJson(),
        "_id": id,
        "category": category,
        "createdAt": createdAt.toIso8601String(),
        "gender": gender,
        "meanSearchVol": meanSearchVol,
        "nature": nature,
        "number": number,
        "sumSearchVol": sumSearchVol,
        "updatedAt": updatedAt.toIso8601String(),
        "brickId": brickId,
        "subCategory": subCategory,
      };
}

enum Gender { neutral, male, female }

class Key {
  String? fr;
  String? en;

  Key({
    required this.fr,
    required this.en,
  });

  factory Key.fromJson(Map<String, dynamic> json) => Key(
        fr: json["fr"],
        en: json["en"],
      );

  Map<String, dynamic> toJson() => {
        "fr": fr,
        "en": en,
      };

  String? get(String? lang) {
    switch (lang) {
      case 'fr':
        return fr;
      case 'en':
        return en;
      default:
        return null;
    }
  }
}

enum Nature { noun, adjective }

enum Number { single, plural, neutral }

class SimpleBrick {
  String? category;
  String text;
  TranslationBrick? translations;

  SimpleBrick({
    this.category,
    required this.text,
    this.translations,
  });

  SimpleBrick copyWith({
    String? category,
    String? text,
    TranslationBrick? translations,
  }) =>
      SimpleBrick(
        category: category ?? this.category,
        text: text ?? this.text,
        translations: translations ?? this.translations,
      );

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SimpleBrick &&
          runtimeType == other.runtimeType &&
          text == other.text;

  @override
  int get hashCode => text.hashCode;

  hasTranslation(String lang) {
    if (translations == null) return false;
    switch (lang) {
      case 'fr':
        return translations?.fr?.isNotEmpty;
      case 'en':
        return translations?.en?.isNotEmpty;
      case 'es':
        return translations?.es?.isNotEmpty;
      case 'it':
        return translations?.it?.isNotEmpty;
      default:
        return false;
    }
  }

  factory SimpleBrick.fromJson(Map<String, dynamic> json) => SimpleBrick(
        category: json["category"],
        text: json["text"] ?? '',
        translations: json["translations"] != null
            ? TranslationBrick.fromJson(json["translations"])
            : null,
      );

  Map<String, dynamic> toJson() =>
      {"category": category, "text": text, "translations": translations};
}

class TranslationBrick {
  String? fr;
  String? en;
  String? es;
  String? it;
  String language;

  TranslationBrick({
    required this.fr,
    required this.en,
    this.es,
    this.it,
    required this.language,
  });

  factory TranslationBrick.fromJson(Map<String, dynamic> json) =>
      TranslationBrick(
        fr: json["fr"],
        en: json["en"],
        es: json["es"],
        it: json["it"],
        language: json["language"],
      );

  Map<String, dynamic> toJson() => {
        "fr": fr,
        "en": en,
        "es": es,
        "it": it,
        "language": language,
      };

  String? get(String lang) {
    switch (lang) {
      case 'fr':
        return fr;
      case 'en':
        return en;
      case 'es':
        return es;
      case 'it':
        return it;
      default:
        return null;
    }
  }
}
