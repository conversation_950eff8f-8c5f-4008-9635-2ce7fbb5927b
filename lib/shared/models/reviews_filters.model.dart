// ignore_for_file: constant_identifier_names

import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/object_to_snake_case.dart';
import 'package:malou/shared/models/platform.model.dart';

enum MalouPeriod {
  last_seven_days,
  last_thirty_days,
  last_three_months,
  last_six_months,
  last_twelve_months,
  all
}

enum MalouComparisonPeriod {
  previous_period,
  same_period_first_year,
  since_start
}

class ReviewsFilters {
  List<PlatformKeys>? platforms;
  DateTime? startDate;
  DateTime? endDate;
  MalouPeriod? period;
  String? sortBy;
  int? sortOrder;
  String? text;
  List<String?>? ratings;
  bool? answered;
  bool? notAnswered;
  bool? pending;
  bool? archived;
  bool? unarchived;
  bool? showPrivate;
  bool? withText;
  bool? withoutText;
  bool? answerable;
  List<PrivatePlatform>? privatePlatforms;

  ReviewsFilters({
    this.platforms,
    this.startDate,
    this.endDate,
    this.period,
    this.sortBy,
    this.sortOrder,
    this.text,
    this.ratings,
    this.answered,
    this.notAnswered,
    this.pending,
    this.archived,
    this.unarchived,
    this.showPrivate,
    this.withText,
    this.withoutText,
    this.answerable,
    this.privatePlatforms,
  }) {
    if (period != null) {
      List<DateTime?> dates = getRangeDatesFromPeriod();
      startDate = dates[0];
      endDate = dates[1];
    }
  }

  ReviewsFilters copyWith({
    List<PlatformKeys>? platforms,
    DateTime? startDate,
    DateTime? endDate,
    MalouPeriod? period,
    String? sortBy,
    int? sortOrder,
    String? text,
    List<String?>? ratings,
    bool? answered,
    bool? notAnswered,
    bool? pending,
    bool? archived,
    bool? unarchived,
    bool? showPrivate,
    bool? withText,
    bool? withoutText,
    bool? answerable,
    List<PrivatePlatform>? privatePlatforms,
  }) {
    return ReviewsFilters(
      platforms: platforms ?? this.platforms,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      period: period ?? this.period,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
      text: text ?? this.text,
      ratings: ratings ?? this.ratings,
      answered: answered ?? this.answered,
      notAnswered: notAnswered ?? this.notAnswered,
      pending: pending ?? this.pending,
      archived: archived ?? this.archived,
      unarchived: unarchived ?? this.unarchived,
      showPrivate: showPrivate ?? this.showPrivate,
      withText: withText ?? this.withText,
      withoutText: withoutText ?? this.withoutText,
      answerable: answerable ?? this.answerable,
      privatePlatforms: privatePlatforms ?? this.privatePlatforms,
    );
  }

  factory ReviewsFilters.fromJson(Map<String, dynamic> json) => ReviewsFilters(
      platforms: json['platforms'] != null
          ? (json['platforms'] as List)
              .map((e) => PlatformKeys.values
                  .firstWhere((element) => element.name.toString() == e))
              .toList()
          : null,
      startDate:
          json['startDate'] != null ? DateTime.parse(json['startDate']) : null,
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
      period: json['period'] != null
          ? MalouPeriod.values.byName(json['period'])
          : null,
      sortBy: json['sortBy'],
      sortOrder: json['sortOrder'],
      text: json['text'],
      ratings: json['ratings'] != null
          ? (json['ratings'] as List).map((e) => e.toString()).toList()
          : null,
      answered: json['answered'],
      notAnswered: json['notAnswered'],
      pending: json['pending'],
      archived: json['archived'],
      unarchived: json['unarchived'],
      showPrivate: json['showPrivate'],
      withText: json['withText'],
      withoutText: json['withoutText'],
      answerable: json['answerable'],
      privatePlatforms: List.from(json['privatePlatforms'])
          .map((e) => PrivatePlatform.values.byName(e))
          .toList());

  Map<String, dynamic> toJson() => {
        'platforms': platforms?.map((e) => e.name.toString()).toList(),
        'startDate': startDate?.toIso8601String(),
        'endDate': endDate?.toIso8601String(),
        'period': period?.name.toString(),
        'sortBy': sortBy,
        'sortOrder': sortOrder,
        'text': text,
        'ratings': ratings,
        'answered': answered,
        'notAnswered': notAnswered,
        'pending': pending,
        'archived': archived,
        'unarchived': unarchived,
        'showPrivate': showPrivate,
        'withText': withText,
        'withoutText': withoutText,
        'answerable': answerable,
        'privatePlatforms':
            privatePlatforms?.map((e) => e.name.toString()).toList(),
      };

  List<DateTime?> getRangeDatesFromPeriod() {
    final now = DateTime.now();
    switch (period) {
      case MalouPeriod.last_seven_days:
        return [
          now.subtract(const Duration(days: 7)),
          now,
        ];
      case MalouPeriod.last_thirty_days:
        return [
          now.subtract(const Duration(days: 30)),
          now,
        ];
      case MalouPeriod.last_three_months:
        return [
          now.subtract(const Duration(days: 90)),
          now,
        ];
      case MalouPeriod.last_six_months:
        return [
          now.subtract(const Duration(days: 180)),
          now,
        ];
      case MalouPeriod.last_twelve_months:
        return [
          now.subtract(const Duration(days: 365)),
          now,
        ];
      case MalouPeriod.all:
        return [null, null];
      default:
        return [null, null];
    }
  }

  static translatePeriod(MalouPeriod period, BuildContext context) {
    switch (period) {
      case MalouPeriod.last_seven_days:
        return Translation.of(context).lastSevenDays;
      case MalouPeriod.last_thirty_days:
        return Translation.of(context).lastThirtyDays_date_range;
      case MalouPeriod.last_three_months:
        return Translation.of(context).lastThreeMonths;
      case MalouPeriod.last_six_months:
        return Translation.of(context).lastSixmonths;
      case MalouPeriod.last_twelve_months:
        return Translation.of(context).lastTwelveMonths;
      case MalouPeriod.all:
        return Translation.of(context).all;
      default:
        return "";
    }
  }

  Map<String, dynamic> toSnakeCaseJson() => objectToSnakeCase(toJson());
}
