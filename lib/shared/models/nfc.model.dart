class Scan {
  String id;
  String nfcId;
  DateTime scannedAt;
  DateTime redirectedAt;
  int starClicked;
  DateTime createdAt;
  DateTime updatedAt;
  Nfc? nfc;
  Nfc? nfcSnapshot;

  Scan({
    required this.id,
    required this.nfcId,
    required this.scannedAt,
    required this.redirectedAt,
    required this.starClicked,
    required this.createdAt,
    required this.updatedAt,
    required this.nfc,
    required this.nfcSnapshot,
  });

  factory Scan.fromJson(Map<String, dynamic> json) => Scan(
        id: json["id"],
        nfcId: json["nfcId"],
        scannedAt: DateTime.parse(json["scannedAt"]),
        redirectedAt: DateTime.parse(json["redirectedAt"]),
        starClicked: json["starClicked"],
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
        nfc: json["nfc"] == null ? null : Nfc.fromJson(json["nfc"]),
        nfcSnapshot: json["nfcSnapshot"] == null
            ? null
            : Nfc.fromJson(json["nfcSnapshot"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "nfcId": nfcId,
        "scannedAt": scannedAt.toIso8601String(),
        "redirectedAt": redirectedAt.toIso8601String(),
        "starClicked": starClicked,
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
        "nfc": nfc?.toJson(),
        "nfcSnapshot": nfcSnapshot?.toJson(),
      };

  String getNfcName() {
    if (nfc != null) {
      return nfc?.chipName ?? "";
    } else if (nfcSnapshot != null) {
      return nfcSnapshot?.chipName ?? "";
    } else {
      return "";
    }
  }
}

class Nfc {
  String id;
  String? chipName;
  String restaurantId;
  bool active;
  String? name;
  String platformKey;
  String redirectionLink;
  String? notes;
  List<int> starsRedirected;
  DateTime createdAt;
  DateTime updatedAt;

  Nfc({
    required this.id,
    required this.chipName,
    required this.restaurantId,
    required this.active,
    required this.name,
    required this.platformKey,
    required this.redirectionLink,
    required this.notes,
    required this.starsRedirected,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Nfc.fromJson(Map<String, dynamic> json) => Nfc(
        id: json["id"],
        chipName: json["chipName"],
        restaurantId: json["restaurantId"],
        active: json["active"],
        name: json["name"],
        platformKey: json["platformKey"],
        redirectionLink: json["redirectionLink"],
        notes: json["notes"],
        starsRedirected: List<int>.from(json["starsRedirected"].map((x) => x)),
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "chipName": chipName,
        "restaurantId": restaurantId,
        "active": active,
        "name": name,
        "platformKey": platformKey,
        "redirectionLink": redirectionLink,
        "notes": notes,
        "starsRedirected": List<dynamic>.from(starsRedirected.map((x) => x)),
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
      };
}
