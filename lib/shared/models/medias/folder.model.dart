import 'dart:convert';

Folder folderFromJson(String str) => Folder.fromJson(json.decode(str));

String folderToJson(Folder data) => json.encode(data.toJson());

class Folder {
  String id;
  String name;
  String? parentFolderId;
  int mediaCount;

  Folder({
    required this.id,
    required this.name,
    this.parentFolderId,
    required this.mediaCount,
  });

  factory Folder.fromJson(Map<String, dynamic> json) => Folder(
        id: json["id"],
        name: json["name"],
        parentFolderId: json["parentFolderId"],
        mediaCount: json["mediaCount"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "parentFolderId": parentFolderId,
        "mediaCount": mediaCount,
      };
}
