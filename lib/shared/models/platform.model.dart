import 'dart:convert';
import 'dart:ui';

import 'package:malou/shared/models/credential.model.dart';
import 'package:malou/shared/models/informations.model.dart';

// parse enum value from string
PlatformKeys? parsePlatformKey(String value) {
  var map = PlatformKeys.values.asNameMap();
  return map[value];
}

enum MessagingPlatformKeys { gmb, facebook, instagram }

enum PlatformKeys {
  abc,
  deliveroo,
  doordash,
  facebook,
  foursquare,
  gmb,
  instagram,
  lafourchette,
  malou,
  malouPrivate,
  manual,
  mapstr,
  opentable,
  resy,
  sevenrooms,
  tiktok,
  tripadvisor,
  ubereats,
  yelp,
  zenchef,

  // has been removed but we keep it for preferences.getString('reviewsFilters')
  pagesjaunes
}

enum PrivatePlatform { campaign, totem }

class PlatformDetails {
  final PlatformKeys key;
  final String name;
  final Color borderColor;
  final bool hasRating;
  Map<String, String>? domainMap;

  PlatformDetails({
    required this.key,
    required this.name,
    required this.borderColor,
    required this.hasRating,
    this.domainMap,
  });

  String resolveLogo() {
    return 'assets/logos/${key.name}.png';
  }

  static String resolvePlatformLogo(PlatformKeys key) {
    return 'assets/logos/${key.name}.png';
  }

  static String resolvePlatformLogoFromName(String key) {
    return 'assets/logos/$key.png';
  }
}

class Platform {
  String id;
  String key;
  String restaurantId;
  String? apiEndpoint;
  List<String>? attributes;
  List<dynamic>? categoryList;
  DateTime createdAt;
  List<Credential>? credentials;
  List<Description>? descriptions;
  List<dynamic>? lockedFields;
  List<String>? platformPropertiesToUpdate;
  List<RegularHour>? regularHours;
  String? socialId;
  List<SpecialHour>? specialHours;
  List<String>? unmappedCategories;
  DateTime updatedAt;
  List<WatchedAccount>? watchedAccounts;
  Address? address;
  BusinessSocialLinks? businessSocialLinks;
  bool? isClaimed;
  bool? isClosedTemporarily;
  String? name;
  Phone? phone;
  double? rating;
  String? socialLink;
  String? website;
  String? apiEndpointV2;
  String? category;
  DateTime? openingDate;
  String? placeId;
  Latlng? latlng;
  String? unmappedHours;

  bool? isValid;

  Platform(
      {required this.id,
      required this.key,
      required this.restaurantId,
      this.apiEndpoint,
      required this.attributes,
      required this.categoryList,
      required this.createdAt,
      required this.credentials,
      required this.descriptions,
      required this.lockedFields,
      required this.platformPropertiesToUpdate,
      required this.regularHours,
      this.socialId,
      required this.specialHours,
      required this.unmappedCategories,
      required this.updatedAt,
      required this.watchedAccounts,
      this.address,
      this.businessSocialLinks,
      this.isClaimed,
      this.isClosedTemporarily,
      this.name,
      this.phone,
      this.rating,
      this.socialLink,
      this.website,
      this.apiEndpointV2,
      this.category,
      this.openingDate,
      this.placeId,
      this.latlng,
      this.unmappedHours,
      this.isValid});

  factory Platform.fromRawJson(String str) =>
      Platform.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Platform.fromJson(Map<String, dynamic> json) => Platform(
        id: json["_id"],
        key: json["key"],
        restaurantId: json["restaurantId"],
        apiEndpoint: json["apiEndpoint"],
        attributes: json['attributes'] == null
            ? null
            : List<String>.from(json["attributes"].map((x) => x)),
        categoryList: json['categoryList'] == null
            ? null
            : List<dynamic>.from(json["categoryList"].map((x) => x)),
        createdAt: DateTime.parse(json["createdAt"]),
        credentials: json['credentials'] == null
            ? null
            : List<Credential>.from(
                json["credentials"].map((x) => Credential.fromJson(x))),
        descriptions: json['descriptions'] == null
            ? null
            : List<Description>.from(
                json["descriptions"].map((x) => Description.fromJson(x))),
        lockedFields: json['lockedFields'] == null
            ? null
            : List<dynamic>.from(json["lockedFields"].map((x) => x)),
        platformPropertiesToUpdate: json['platformPropertiesToUpdate'] == null
            ? null
            : List<String>.from(
                json["platformPropertiesToUpdate"].map((x) => x)),
        regularHours: json['regularHours'] == null
            ? null
            : List<RegularHour>.from(
                json["regularHours"].map((x) => RegularHour.fromJson(x))),
        socialId: json["socialId"],
        specialHours: json['specialHours'] == null
            ? null
            : List<SpecialHour>.from(
                json["specialHours"].map((x) => SpecialHour.fromJson(x))),
        unmappedCategories: json['unmappedCategories'] == null
            ? null
            : List<String>.from(json["unmappedCategories"].map((x) => x)),
        updatedAt: DateTime.parse(json["updatedAt"]),
        watchedAccounts: json['watchedAccounts'] == null
            ? null
            : List<WatchedAccount>.from(
                json["watchedAccounts"].map((x) => WatchedAccount.fromJson(x))),
        address:
            json["address"] == null ? null : Address.fromJson(json["address"]),
        businessSocialLinks: json["businessSocialLinks"] == null
            ? null
            : BusinessSocialLinks.fromJson(json["businessSocialLinks"]),
        isClaimed: json["isClaimed"],
        isClosedTemporarily: json["isClosedTemporarily"],
        name: json["name"],
        phone: json["phone"] == null ? null : Phone.fromJson(json["phone"]),
        rating: json["rating"]?.toDouble(),
        socialLink: json["socialLink"],
        website: json["website"],
        apiEndpointV2: json["apiEndpointV2"],
        category: json["category"],
        openingDate: json["openingDate"] == null
            ? null
            : DateTime.parse(json["openingDate"]),
        placeId: json["placeId"],
        latlng: json["latlng"] == null ? null : Latlng.fromJson(json["latlng"]),
        unmappedHours: json["unmappedHours"],
      );

  static fromJsonList(List<dynamic> json) {
    var jsonList = json.map((e) => Platform.fromJson(e));
    return jsonList.toList();
  }

  Map<String, dynamic> toJson() => {
        "_id": id,
        "key": key,
        "restaurantId": restaurantId,
        "apiEndpoint": apiEndpoint,
        "attributes": attributes != null
            ? List<dynamic>.from(attributes!.map((x) => x))
            : null,
        "categoryList": categoryList == null
            ? null
            : List<dynamic>.from(categoryList!.map((x) => x)),
        "createdAt": createdAt.toIso8601String(),
        "credentials": credentials == null
            ? null
            : List<dynamic>.from(credentials!.map((x) => x.toJson())),
        "descriptions": descriptions == null
            ? null
            : List<dynamic>.from(descriptions!.map((x) => x.toJson())),
        "lockedFields": lockedFields == null
            ? null
            : List<dynamic>.from(lockedFields!.map((x) => x)),
        "platformPropertiesToUpdate": platformPropertiesToUpdate == null
            ? null
            : List<dynamic>.from(platformPropertiesToUpdate!.map((x) => x)),
        "regularHours": regularHours == null
            ? null
            : List<dynamic>.from(regularHours!.map((x) => x.toJson())),
        "socialId": socialId,
        "specialHours": specialHours == null
            ? null
            : List<dynamic>.from(specialHours!.map((x) => x.toJson())),
        "unmappedCategories": unmappedCategories == null
            ? null
            : List<dynamic>.from(unmappedCategories!.map((x) => x)),
        "updatedAt": updatedAt.toIso8601String(),
        "watchedAccounts": watchedAccounts == null
            ? null
            : List<dynamic>.from(watchedAccounts!.map((x) => x.toJson())),
        "address": address?.toJson(),
        "businessSocialLinks": businessSocialLinks?.toJson(),
        "isClaimed": isClaimed,
        "isClosedTemporarily": isClosedTemporarily,
        "name": name,
        "phone": phone?.toJson(),
        "rating": rating,
        "socialLink": socialLink,
        "website": website,
        "apiEndpointV2": apiEndpointV2,
        "category": category,
        "openingDate": openingDate?.toIso8601String(),
        "placeId": placeId,
        "latlng": latlng?.toJson(),
        "unmappedHours": unmappedHours,
      };

  static bool hasApi(PlatformKeys platformKey) {
    return [
      PlatformKeys.facebook,
      PlatformKeys.gmb,
      PlatformKeys.zenchef,
      PlatformKeys.deliveroo,
      PlatformKeys.tripadvisor,
      PlatformKeys.ubereats
    ].contains(platformKey);
  }
}
