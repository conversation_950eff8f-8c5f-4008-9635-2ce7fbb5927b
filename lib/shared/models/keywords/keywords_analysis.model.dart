class KeywordAnalysis {
  final num score;
  final int count;
  final List<String> keywords;

  const KeywordAnalysis({
    required this.score,
    required this.count,
    required this.keywords,
  });

  factory KeywordAnalysis.fromJson(Map<String, dynamic> json) =>
      KeywordAnalysis(
        score: json["score"] ?? 0,
        count: json["count"] ?? 0,
        keywords: List<String>.from(json["keywords"]?.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "score": score,
        "count": count,
        "keywords": List<dynamic>.from(keywords.map((x) => x)),
      };
}
