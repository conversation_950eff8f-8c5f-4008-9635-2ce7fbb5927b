import 'package:diacritic/diacritic.dart';
import 'package:flutter/cupertino.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/models/brick.model.dart';
import 'package:malou/shared/models/reviewer_name_validation.model.dart';

String cleanText(String? text) {
  if (text == null) {
    return '';
  }
  String cleanText = removeDiacritics(text).trim();
  return cleanText.toLowerCase();
}

class KeywordsConditionsConfiguration {
  final int? optimalOccurences;
  final int? minimumOccurences;

  KeywordsConditionsConfiguration({
    this.optimalOccurences,
    this.minimumOccurences,
  });
}

enum KeywordCriteria {
  sorryWords,
  bricksNumber,
  bricksVariety,
  restaurantName,
  textLength,
  responseTime,
  reviewerName,
}

class KeywordScoreDetail {
  final KeywordCriteria criteria;
  final int? value;
  final num? score;
  final bool fulfilled;
  final ({int? minimumValue, int? optimalValue})? fulfilledValue;

  KeywordScoreDetail({
    required this.criteria,
    required this.fulfilled,
    this.value,
    this.score,
    this.fulfilledValue,
  });

  // fromJSON
  factory KeywordScoreDetail.fromJson(Map<String, dynamic> json) =>
      KeywordScoreDetail(
        criteria: KeywordCriteria.values.byName(json['criteria']),
        fulfilled: json['fulfilled'],
        value: json['value'],
        score: json['score'],
        fulfilledValue: json['fulfilledValue'] != null
            ? (
                minimumValue: json['fulfilledValue']['minimumValue'],
                optimalValue: json['fulfilledValue']['optimalValue'],
              )
            : null,
      );

  String getTitle(BuildContext context) {
    switch (criteria) {
      case KeywordCriteria.restaurantName:
        return Translation.of(context).keywordScoreAdviceRestaurantName;
      case KeywordCriteria.sorryWords:
        return Translation.of(context).keywordScoreAdviceSorry;
      case KeywordCriteria.bricksNumber:
        return Translation.of(context).keywordScoreAdviceSimpleBricksNumber;
      case KeywordCriteria.bricksVariety:
        return Translation.of(context).keywordScoreAdviceSimpleBricksVariety;
      case KeywordCriteria.responseTime:
        return Translation.of(context).keywordScoreAdviceResponseTime;
      case KeywordCriteria.reviewerName:
        return Translation.of(context).keywordScoreAdviceReviewerName;
      case KeywordCriteria.textLength:
        return Translation.of(context).keywordScoreAdviceTextLength;
      default:
        return '';
    }
  }

  String? getTip(BuildContext context) {
    switch (criteria) {
      case KeywordCriteria.restaurantName:
        return null;
      case KeywordCriteria.sorryWords:
        return Translation.of(context).keywordScoreAdviceSorryTip;
      case KeywordCriteria.bricksNumber:
        return Translation.of(context)
            .minOccurences(fulfilledValue?.minimumValue ?? 0);
      case KeywordCriteria.bricksVariety:
        return Translation.of(context).keywordScoreAdviceSimpleBricksVarietyTip;
      case KeywordCriteria.responseTime:
        return null;
      case KeywordCriteria.reviewerName:
        return null;
      case KeywordCriteria.textLength:
        return null;
      default:
        return null;
    }
  }

  bool shouldAvoidTips() {
    return [
          KeywordCriteria.restaurantName,
          KeywordCriteria.responseTime,
          KeywordCriteria.reviewerName,
        ].contains(criteria) &&
        value != null &&
        (fulfilledValue?.optimalValue != null ||
            fulfilledValue?.minimumValue != null);
  }
}

class KeywordsScorePayload {
  KeywordScoreTextType textType;
  String text;
  List<String> bricks;
  String venueName;
  String language;
  String? reviewerName;
  int? responseTime;
  String? reviewText;
  String? keywordScoreMethod;
  ReviewerNameValidation reviewerNameValidation;

  KeywordsScorePayload({
    required this.textType,
    required this.text,
    required this.bricks,
    required this.venueName,
    required this.language,
    this.reviewerName,
    this.responseTime,
    this.reviewText,
    this.keywordScoreMethod,
    required this.reviewerNameValidation,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'textType': textType.name,
      'text': text,
      'bricks': bricks,
      'venueName': venueName,
      'language': language,
      'reviewerName': reviewerName ?? '',
      'reviewText': reviewText ?? '',
      'keywordScoreMethod': keywordScoreMethod,
      'reviewerNameValidation': reviewerNameValidation.toJson(),
    };

    if (responseTime != null) {
      json['responseTime'] = responseTime;
    }
    return json;
  }

  copyWith({
    String? text,
    List<String>? bricks,
    String? venueName,
    String? language,
    String? reviewerName,
    int? responseTime,
    String? reviewText,
    String? keywordScoreMethod,
    ReviewerNameValidation? reviewerNameValidation,
  }) {
    return KeywordsScorePayload(
      textType: textType,
      text: text ?? this.text,
      bricks: bricks ?? this.bricks,
      venueName: venueName ?? this.venueName,
      language: language ?? this.language,
      reviewerName: reviewerName ?? this.reviewerName,
      responseTime: responseTime ?? this.responseTime,
      reviewText: reviewText ?? this.reviewText,
      keywordScoreMethod: keywordScoreMethod ?? this.keywordScoreMethod,
      reviewerNameValidation:
          reviewerNameValidation ?? this.reviewerNameValidation,
    );
  }
}

class KeywordsScoreProcessResult {
  num score;
  List<SimpleBrick> bricksFound;
  List<KeywordScoreDetail> details;
  bool restaurantNameFound;
  bool? reviewerNameFound;
  KeywordsScoreProcessResult({
    required this.score,
    required this.bricksFound,
    required this.details,
    required this.restaurantNameFound,
    this.reviewerNameFound,
  });

  factory KeywordsScoreProcessResult.fromJson(Map<String, dynamic> json) =>
      KeywordsScoreProcessResult(
        score: json['score'],
        bricksFound: json['bricksFound'] == null
            ? []
            : List<SimpleBrick>.from(
                json['bricksFound'].map((text) => SimpleBrick(text: text))),
        details: List<KeywordScoreDetail>.from(
            json['details'].map((x) => KeywordScoreDetail.fromJson(x))),
        restaurantNameFound: json['restaurantNameFound'] ?? false,
        reviewerNameFound: json['reviewerNameFound'],
      );
}

enum KeywordScoreTextType {
  lowRateReview,
  highRateReview,
  lowRateTemplate,
  highRateTemplate,
  shortDescription,
  longDescription,
  post,
  picture,
}
