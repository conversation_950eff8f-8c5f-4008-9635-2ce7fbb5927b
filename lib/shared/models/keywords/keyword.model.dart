import 'package:collection/collection.dart';
import 'package:malou/shared/models/restaurant.model.dart';
import 'package:malou/shared/models/restaurant_ai_settings.model.dart';

import '../brick.model.dart';

class RestaurantKeyword {
  String id;
  String keywordId;
  String restaurantId;
  bool selected;
  DateTime? lastRankingRefresh;
  DateTime createdAt;
  DateTime updatedAt;
  Keyword keyword;

  RestaurantKeyword({
    required this.id,
    required this.keywordId,
    required this.restaurantId,
    required this.selected,
    required this.lastRankingRefresh,
    required this.createdAt,
    required this.updatedAt,
    required this.keyword,
  });

  factory RestaurantKeyword.fromJson(Map<String, dynamic> json) {
    return RestaurantKeyword(
      id: json["id"],
      keywordId: json["keywordId"],
      restaurantId: json["restaurantId"],
      selected: json["selected"],
      lastRankingRefresh: json["lastRankingRefresh"] != null
          ? DateTime.parse(json["lastRankingRefresh"])
          : null,
      createdAt: DateTime.parse(json["createdAt"]),
      updatedAt: DateTime.parse(json["updatedAt"]),
      keyword: Keyword.fromJson(json["keyword"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "keywordId": keywordId,
        "restaurantId": restaurantId,
        "selected": selected,
        "lastRankingRefresh": lastRankingRefresh?.toIso8601String(),
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
        "keyword": keyword.toJson(),
      };
}

class Keyword {
  String id;
  String text;
  String apiLocationId;
  bool isCustomerInput;
  int volume;
  List<SimpleBrick> bricks;
  List<VolumeHistory> volumeHistory;
  String language;

  Keyword(
      {required this.id,
      required this.text,
      required this.apiLocationId,
      required this.isCustomerInput,
      required this.volume,
      required this.bricks,
      required this.volumeHistory,
      required this.language});

  factory Keyword.fromJson(Map<String, dynamic> json) => Keyword(
        id: json["id"],
        text: json["text"],
        language: json["language"],
        apiLocationId: json["apiLocationId"],
        isCustomerInput: json["isCustomerInput"],
        volume: json["volume"] ?? 0,
        bricks: List<SimpleBrick>.from(
            json["bricks"].map((x) => SimpleBrick.fromJson(x))),
        volumeHistory: List<VolumeHistory>.from(
            json["volumeHistory"].map((x) => VolumeHistory.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "text": text,
        "language": language,
        "apiLocationId": apiLocationId,
        "isCustomerInput": isCustomerInput,
        "volume": volume,
        "bricks": List<dynamic>.from(bricks.map((x) => x.toJson())),
        "volumeHistory":
            List<dynamic>.from(volumeHistory.map((x) => x.toJson())),
      };

  bool isRestaurantKeyword() {
    return bricks.any((brick) => brick.category == 'restaurantName');
  }

  List<String> getBricksTexts() {
    return bricks.map((brick) => brick.text).toList();
  }
}

class VolumeHistory {
  DateTime fetchDate;
  int volume;
  String source;

  VolumeHistory({
    required this.fetchDate,
    required this.volume,
    required this.source,
  });

  factory VolumeHistory.fromJson(Map<String, dynamic> json) => VolumeHistory(
        fetchDate: DateTime.parse(json["fetchDate"]),
        volume: json["volume"],
        source: json["source"],
      );

  Map<String, dynamic> toJson() => {
        "fetchDate": fetchDate.toIso8601String(),
        "volume": volume,
        "source": source,
      };
}

class KeywordHelper {
  static String getLangWithMostOccurences(
      List<RestaurantKeyword> restaurantKeywords) {
    Map<String, int> langOccurences = {};

    for (var restaurantKeyword in restaurantKeywords) {
      if (langOccurences[restaurantKeyword.keyword.language] == null) {
        langOccurences[restaurantKeyword.keyword.language] = 1;
      } else {
        langOccurences[restaurantKeyword.keyword.language] =
            langOccurences[restaurantKeyword.keyword.language]! + 1;
      }
    }
    String? langWithMostOccurences;
    int? mostOccurences = 0;
    langOccurences.forEach((lang, occurences) {
      if (occurences > mostOccurences!) {
        langWithMostOccurences = lang;
        mostOccurences = occurences;
      }
    });
    return langWithMostOccurences ?? 'en';
  }

  static List<SimpleBrick> buildSimpleBricks(
      List<RestaurantKeyword> restaurantKeywords,
      Restaurant restaurant,
      String targetLanguage,
      {String? reviewerName,
      bool isLowRatedReview = false,
      RestaurantAiSettings? restaurantAiSettings}) {
    // we should never get here because we always have restaurant name
    if (restaurantKeywords.isEmpty) {
      return [];
    }

    // Case where restaurant has not set any restaurantKeywords
    if (restaurantKeywords.length == 1) {
      List<SimpleBrick> bricks =
          List.from(restaurantKeywords[0].keyword.bricks);

      if (reviewerName != null) {
        var reviewerNameBrick =
            SimpleBrick(text: reviewerName, category: 'reviewerName');

        if (bricks.isNotEmpty) {
          bricks.insert(1, reviewerNameBrick);
        } else {
          bricks.add(reviewerNameBrick);
        }
      }

      return bricks;
    }

    final restaurantKeyword = restaurantKeywords
        .firstWhereOrNull((element) => element.keyword.isRestaurantKeyword());

    List<SimpleBrick> mappedBricksDependingOnReviewRate = [];
    if (isLowRatedReview) {
      mappedBricksDependingOnReviewRate = (restaurantKeyword != null
          ? [restaurantKeyword.keyword.bricks.first]
          : <SimpleBrick>[]);
    } else {
      mappedBricksDependingOnReviewRate = restaurantKeywords
          .map((restaurantKeyword) {
            if (restaurantKeyword.keyword.isCustomerInput &&
                restaurantKeyword.keyword.bricks.isEmpty) {
              return [
                SimpleBrick(
                  text: restaurantKeyword.keyword.text,
                  category: 'customerInput',
                )
              ];
            }

            return restaurantKeyword.keyword.bricks.where((brick) {
              return restaurantKeyword.keyword.language == targetLanguage ||
                  brick.hasTranslation(targetLanguage);
            }).toList();
          })
          .expand((element) => element)
          .toList();
    }

    final bricksFromKeywords = mappedBricksDependingOnReviewRate.map((brick) {
      if (brick.translations == null) {
        return brick;
      }
      return SimpleBrick(
          category: brick.category,
          text: brick.translations!.toJson()[targetLanguage]);
    }).toList();

    bricksFromKeywords.sort((a, b) {
      if (a.text.length < b.text.length) {
        return 1;
      }
      return -1;
    });

    // because you could generate both FR and EN bricks
    // need to sort by fr first to get the same behaviour as desktop app
    // https://maloufoodmarketing.slack.com/archives/C0115MRDS02/p1701960137495389
    bricksFromKeywords.sort((a, b) {
      if (a.text.length < b.text.length) {
        return 1;
      }
      return -1;
    });

    // find brick with restaurant.name text and add it at the beginning of the list
    if (restaurantAiSettings?.restaurantName != null) {
      bricksFromKeywords.insert(
          0,
          SimpleBrick(
              text: restaurantAiSettings!.restaurantName,
              category: 'restaurantName'));
    } else {
      bricksFromKeywords.insert(
          0, SimpleBrick(text: restaurant.name, category: 'restaurantName'));
    }

    if (reviewerName != null) {
      var reviewerNameBrick =
          SimpleBrick(text: reviewerName, category: 'reviewerName');

      if (bricksFromKeywords.isNotEmpty) {
        bricksFromKeywords.insert(1, reviewerNameBrick);
      } else {
        bricksFromKeywords.insert(0, reviewerNameBrick);
      }
    }

    // it works because we override == in SimpleBrick class
    var uniqueBrick = bricksFromKeywords.toSet().toList();

    return uniqueBrick;
  }
}
