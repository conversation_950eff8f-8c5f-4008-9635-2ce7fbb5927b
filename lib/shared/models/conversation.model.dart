import 'dart:convert';

import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/router.dart';
import 'package:malou/shared/models/message.model.dart';
import 'package:malou/shared/models/message_user_info.model.dart';
import 'package:malou/shared/services/messaging/messaging.filters.dart';

class ConversationWithMessagesApiResult {
  int count;
  List<ConversationWithMessages> data;

  ConversationWithMessagesApiResult({
    required this.count,
    required this.data,
  });

  factory ConversationWithMessagesApiResult.fromJson(
          Map<String, dynamic> json) =>
      ConversationWithMessagesApiResult(
        count: json["count"],
        data: List<ConversationWithMessages>.from(json["conversations"]
            .map((x) => ConversationWithMessages.fromJson(x))),
      );

  factory ConversationWithMessagesApiResult.fromRawJson(String str) =>
      ConversationWithMessagesApiResult.fromJson(json.decode(str));
}

class ConversationWithMessages {
  ConversationWithMessages({
    required this.conversation,
    required this.messages,
  });

  Conversation conversation;
  List<Message> messages;

  factory ConversationWithMessages.fromRawJson(String str) =>
      ConversationWithMessages.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ConversationWithMessages.fromJson(Map<String, dynamic> json) =>
      ConversationWithMessages(
        conversation: Conversation.fromJson(json["conversation"]),
        messages: List<Message>.from(
            json["messages"].map((x) => Message.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "conversation": conversation.toJson(),
        "messages": List<dynamic>.from(messages.map((x) => x.toJson())),
      };

  Message? getLastMessage() {
    if (messages.isEmpty) {
      return null;
    }
    return messages.last;
  }

  String getLastMessageText() {
    Message? lastMessage = getLastMessage();
    if (lastMessage?.text != null && lastMessage!.text!.isNotEmpty) {
      return lastMessage.text!;
    }

    if (lastMessage?.attachments != null &&
        lastMessage!.attachments!.isNotEmpty) {
      String mediaType = lastMessage.attachments!.first.type;
      return lastMessage.isFromRestaurant
          ? Translation.of(rootNavigatorKey.currentContext!)
              .sentAMedia(mediaType)
          : Translation.of(rootNavigatorKey.currentContext!)
              .receivedAMedia(mediaType);
    }

    if (lastMessage?.story != null) {
      switch (lastMessage!.story!.storyType) {
        case 'mention':
          return Translation.of(rootNavigatorKey.currentContext!)
              .mentionnedYouInStory;
        default:
          return Translation.of(rootNavigatorKey.currentContext!)
              .receivedAStory;
      }
    }

    return '';
  }

  String getConversationSenderName() {
    return conversation.userInfo.displayName!;
  }

  ConversationStatus getConversationStatus() {
    return conversation.status;
  }

  bool isConversationRead() {
    return conversation.status == ConversationStatus.READ;
  }
}

class Conversation {
  Conversation({
    required this.id,
    required this.key,
    required this.restaurantId,
    required this.socialConversationId,
    this.archived = false,
    required this.createdAt,
    this.favorite = false,
    required this.latestMessageAt,
    required this.status,
    required this.updatedAt,
    required this.userInfo,
  });

  String id;
  String key;
  String restaurantId;
  String socialConversationId;
  bool archived;
  DateTime createdAt;
  bool favorite;
  DateTime latestMessageAt;
  ConversationStatus status;
  DateTime updatedAt;
  UserInfo userInfo;

  factory Conversation.fromRawJson(String str) =>
      Conversation.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Conversation.fromJson(Map<String, dynamic> json) => Conversation(
        id: json["_id"],
        key: json["key"],
        restaurantId: json["restaurantId"],
        socialConversationId: json["socialConversationId"],
        archived: json["archived"],
        createdAt: DateTime.parse(
            json["createdAt"] ?? DateTime.now().toIso8601String()),
        favorite: json["favorite"],
        latestMessageAt: DateTime.parse(json["latestMessageAt"]),
        status: json["status"] == null
            ? ConversationStatus.READ
            : ConversationStatus.values.asNameMap()[json["status"]]!,
        updatedAt: DateTime.parse(json["updatedAt"]),
        userInfo: UserInfo.fromJson(json["userInfo"]),
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "key": key,
        "restaurantId": restaurantId,
        "socialConversationId": socialConversationId,
        "archived": archived,
        "createdAt": createdAt.toIso8601String(),
        "favorite": favorite,
        "latestMessageAt": latestMessageAt.toIso8601String(),
        "status": status,
        "updatedAt": updatedAt.toIso8601String(),
        "userInfo": userInfo.toJson(),
      };

  copyFromMap(Map<String, dynamic> json) {
    id = json["_id"];
    key = json["key"];
    restaurantId = json["restaurantId"];
    socialConversationId = json["socialConversationId"];
    archived = json["archived"];
    createdAt = DateTime.parse(json["createdAt"]);
    favorite = json["favorite"];
    latestMessageAt = DateTime.parse(json["latestMessageAt"]);
    status = json["status"] == null
        ? ConversationStatus.READ
        : ConversationStatus.values.asNameMap()[json["status"]] ??
            ConversationStatus.READ;
    updatedAt = DateTime.parse(json["updatedAt"]);
    userInfo = UserInfo.fromJson(json["userInfo"]);
  }
}
