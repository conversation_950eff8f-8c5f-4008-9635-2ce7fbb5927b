import 'package:malou/shared/helpers/remove_null_from_map.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/models/organization.model.dart';
import 'package:malou/shared/services/language/language.provider.dart';

class User {
  User({
    required this.id,
    this.verified,
    required this.name,
    required this.lastname,
    required this.email,
    required this.role,
    this.restaurantsLimit,
    required this.createdAt,
    required this.updatedAt,
    required this.defaultLanguage,
    required this.expireSessionBefore,
    required this.caslRole,
    required this.organizationIds,
    this.profilePicture,
    required this.settings,
    required this.restaurants,
    required this.organizations,
  });

  String id;
  bool? verified;
  String name;
  String lastname;
  String email;
  String role;
  dynamic restaurantsLimit;
  DateTime createdAt;
  DateTime updatedAt;
  AppLocale defaultLanguage;
  DateTime? expireSessionBefore;
  String caslRole;
  List<String> organizationIds;
  Media? profilePicture;
  Settings settings;
  List<UserRestaurant> restaurants;
  List<Organization> organizations;

  String get fullName => '$name $lastname';

  factory User.fromJson(Map<String, dynamic> json) => User(
        id: json["_id"],
        verified: json["verified"],
        name: json["name"],
        lastname: json["lastname"],
        email: json["email"],
        role: json["role"],
        restaurantsLimit: json["restaurantsLimit"],
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
        defaultLanguage: json["defaultLanguage"] != null
            ? AppLocale.values.byName(json["defaultLanguage"])
            : AppLocale.fr,
        expireSessionBefore: json["expireSessionBefore"] == null
            ? null
            : DateTime.parse(json["expireSessionBefore"]),
        caslRole: json["caslRole"],
        organizationIds: List<String>.from(json["organizationIds"]),
        profilePicture: json["profilePicture"] != null
            ? Media.fromJson(json["profilePicture"])
            : null,
        settings: Settings.fromJson(json["settings"]),
        restaurants: List<UserRestaurant>.from(
            json["restaurants"].map((x) => UserRestaurant.fromJson(x))),
        organizations: List<Organization>.from(
            json["organizations"].map((x) => Organization.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "verified": verified,
        "name": name,
        "lastname": lastname,
        "email": email,
        "role": role,
        "restaurantsLimit": restaurantsLimit,
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
        "defaultLanguage": defaultLanguage.name,
        "expireSessionBefore": expireSessionBefore?.toIso8601String(),
        "caslRole": caslRole,
        "organizationIds": List<dynamic>.from(organizationIds),
        "profilePicture": profilePicture?.toJson(),
        "settings": settings.toJson(),
        "restaurants": List<dynamic>.from(restaurants.map((x) => x.toJson())),
        "organizations":
            List<dynamic>.from(organizations.map((x) => x.toJson())),
      };

  UserRestaurant getUserRestaurantByRestaurantId(String restaurantId) {
    return restaurants
        .firstWhere((element) => element.restaurantId == restaurantId);
  }

  User copyWith({
    String? id,
    bool? verified,
    String? name,
    String? lastname,
    String? email,
    String? role,
    dynamic restaurantsLimit,
    DateTime? createdAt,
    DateTime? updatedAt,
    AppLocale? defaultLanguage,
    DateTime? expireSessionBefore,
    String? caslRole,
    List<String>? organizationIds,
    Media? profilePicture,
    Settings? settings,
    List<UserRestaurant>? restaurants,
    List<Organization>? organizations,
  }) {
    return User(
      id: id ?? this.id,
      verified: verified ?? this.verified,
      name: name ?? this.name,
      lastname: lastname ?? this.lastname,
      email: email ?? this.email,
      role: role ?? this.role,
      restaurantsLimit: restaurantsLimit ?? this.restaurantsLimit,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      defaultLanguage: defaultLanguage ?? this.defaultLanguage,
      expireSessionBefore: expireSessionBefore ?? this.expireSessionBefore,
      caslRole: caslRole ?? this.caslRole,
      organizationIds: organizationIds ?? this.organizationIds,
      profilePicture: profilePicture ?? this.profilePicture,
      settings: settings ?? this.settings,
      restaurants: restaurants ?? this.restaurants,
      organizations: organizations ?? this.organizations,
    );
  }
}

class UserRestaurant {
  UserRestaurant({
    required this.id,
    required this.restaurantId,
    required this.userId,
    required this.caslRole,
    required this.createdAt,
    required this.displayHashtagsModal,
    required this.displayPermissionsModal,
    required this.updatedAt,
    this.displaySemanticAnalyses,
    this.displaySyncWarningModal,
    this.lastConnectionEmailSent,
  });

  String id;
  String restaurantId;
  String userId;
  String caslRole;
  DateTime createdAt;
  bool displayHashtagsModal;
  bool displayPermissionsModal;
  DateTime updatedAt;
  bool? displaySemanticAnalyses;
  bool? displaySyncWarningModal;
  DateTime? lastConnectionEmailSent;

  factory UserRestaurant.fromJson(Map<String, dynamic> json) => UserRestaurant(
        id: json["_id"],
        restaurantId: json["restaurantId"] ?? '',
        userId: json["userId"],
        caslRole: json["caslRole"],
        createdAt: DateTime.parse(json["createdAt"]),
        displayHashtagsModal: json["displayHashtagsModal"],
        displayPermissionsModal: json["displayPermissionsModal"],
        updatedAt: DateTime.parse(json["updatedAt"]),
        displaySemanticAnalyses: json["displaySemanticAnalyses"],
        displaySyncWarningModal: json["displaySyncWarningModal"],
        lastConnectionEmailSent: json["lastConnectionEmailSent"] == null
            ? null
            : DateTime.parse(json["lastConnectionEmailSent"]),
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "restaurantId": restaurantId,
        "userId": userId,
        "caslRole": caslRole,
        "createdAt": createdAt.toIso8601String(),
        "displayHashtagsModal": displayHashtagsModal,
        "displayPermissionsModal": displayPermissionsModal,
        "updatedAt": updatedAt.toIso8601String(),
        "displaySemanticAnalyses": displaySemanticAnalyses,
        "displaySyncWarningModal": displaySyncWarningModal,
        "lastConnectionEmailSent": lastConnectionEmailSent?.toIso8601String(),
      };

  static List<UserRestaurant> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => UserRestaurant.fromJson(json)).toList();
  }

  setDisplaySemanticAnalyses(bool value) {
    displaySemanticAnalyses = value;
  }
}

class UserRestaurantDTO {
  // make all fields optional
  String? id;
  String? restaurantId;
  String? userId;
  String? caslRole;
  bool? displayHashtagsModal;
  bool? displayPermissionsModal;
  bool? displaySemanticAnalyses;
  bool? displaySyncWarningModal;
  DateTime? lastConnectionEmailSent;

  UserRestaurantDTO({
    this.id,
    this.restaurantId,
    this.userId,
    this.caslRole,
    this.displayHashtagsModal,
    this.displayPermissionsModal,
    this.displaySemanticAnalyses,
    this.displaySyncWarningModal,
    this.lastConnectionEmailSent,
  });

  Map<String, dynamic> toJson() => removeNullFromMap({
        "_id": id,
        "restaurantId": restaurantId,
        "userId": userId,
        "caslRole": caslRole,
        "displayHashtagsModal": displayHashtagsModal,
        "displayPermissionsModal": displayPermissionsModal,
        "displaySemanticAnalyses": displaySemanticAnalyses,
        "displaySyncWarningModal": displaySyncWarningModal,
        "lastConnectionEmailSent": lastConnectionEmailSent?.toIso8601String(),
      });
}

class Settings {
  Settings({
    required this.receiveFeedbacks,
    required this.notificationSettings,
    //! TODO rename this  field after feature toggle complete
    required this.newNotifications,
  });

  bool? receiveFeedbacks;
  NotificationSettings? notificationSettings;
  NewNotificationSettings newNotifications;

  factory Settings.fromJson(Map<String, dynamic> json) => Settings(
        receiveFeedbacks: json["receiveFeedbacks"],
        notificationSettings: json["notificationSettings"] == null
            ? null
            : NotificationSettings.fromJson(json["notificationSettings"]),
        newNotifications:
            NewNotificationSettings.fromJson(json["notifications"]),
      );

  Map<String, dynamic> toJson() => {
        "receiveFeedbacks": receiveFeedbacks,
        "notificationSettings": notificationSettings?.toJson(),
        "notifications": newNotifications.toJson(),
      };

  Settings copyWith({
    bool? receiveFeedbacks,
    NotificationSettings? notificationSettings,
  }) {
    return Settings(
        receiveFeedbacks: receiveFeedbacks ?? this.receiveFeedbacks,
        notificationSettings: notificationSettings ?? this.notificationSettings,
        newNotifications: newNotifications);
  }
}

class NewNotificationSettings {
  MobileNotificationSettings mobile;
  WebNotificationSettings web;
  EmailNotificationSettings email;

  NewNotificationSettings({
    required this.mobile,
    required this.web,
    required this.email,
  });

  factory NewNotificationSettings.fromJson(Map<String, dynamic> json) =>
      NewNotificationSettings(
        mobile: MobileNotificationSettings.fromJson(json["mobile"]),
        web: WebNotificationSettings.fromJson(json["web"]),
        email: EmailNotificationSettings.fromJson(json["email"]),
      );

  Map<String, dynamic> toJson() => {
        "mobile": mobile.toJson(),
        "web": web.toJson(),
        "email": email.toJson(),
      };

  NewNotificationSettings copyWith({
    MobileNotificationSettings? mobile,
    WebNotificationSettings? web,
    EmailNotificationSettings? email,
  }) {
    return NewNotificationSettings(
      mobile: mobile ?? this.mobile,
      web: web ?? this.web,
      email: email ?? this.email,
    );
  }
}

class EmailNotificationSettings {
  FeatureNotificationSetting reviewReplyReminder;
  FeatureNotificationSetting specialHourReminder;
  FeatureNotificationSetting postSuggestion;
  FeatureNotificationSetting postError;
  FeatureNotificationSetting roiActivated;
  FeatureNotificationSetting summary;

  EmailNotificationSettings({
    required this.reviewReplyReminder,
    required this.specialHourReminder,
    required this.postSuggestion,
    required this.postError,
    required this.roiActivated,
    required this.summary,
  });

  factory EmailNotificationSettings.fromJson(Map<String, dynamic> json) =>
      EmailNotificationSettings(
        reviewReplyReminder:
            FeatureNotificationSetting.fromJson(json["reviewReplyReminder"]),
        specialHourReminder:
            FeatureNotificationSetting.fromJson(json["specialHourReminder"]),
        postSuggestion:
            FeatureNotificationSetting.fromJson(json["postSuggestion"]),
        postError: FeatureNotificationSetting.fromJson(json["postError"]),
        roiActivated: FeatureNotificationSetting.fromJson(json["roiActivated"]),
        summary: FeatureNotificationSetting.fromJson(json["summary"]),
      );

  Map<String, dynamic> toJson() => {
        "reviewReplyReminder": reviewReplyReminder.toJson(),
        "specialHourReminder": specialHourReminder.toJson(),
        "postSuggestion": postSuggestion.toJson(),
        "postError": postError.toJson(),
        "roiActivated": roiActivated.toJson(),
        "summary": summary.toJson(),
      };

  EmailNotificationSettings copyWith({
    FeatureNotificationSetting? reviewReplyReminder,
    FeatureNotificationSetting? specialHourReminder,
    FeatureNotificationSetting? postSuggestion,
    FeatureNotificationSetting? postError,
    FeatureNotificationSetting? roiActivated,
    FeatureNotificationSetting? summary,
  }) {
    return EmailNotificationSettings(
      reviewReplyReminder: reviewReplyReminder ?? this.reviewReplyReminder,
      specialHourReminder: specialHourReminder ?? this.specialHourReminder,
      postSuggestion: postSuggestion ?? this.postSuggestion,
      postError: postError ?? this.postError,
      roiActivated: roiActivated ?? this.roiActivated,
      summary: summary ?? this.summary,
    );
  }
}

class WebNotificationSettings {
  List<String> restaurantIds;
  bool showFloatingToast;
  FeatureNotificationSetting newReviews;
  FeatureNotificationSetting reviewReplyReminder;
  FeatureNotificationSetting specialHourReminder;
  FeatureNotificationSetting postSuggestion;
  FeatureNotificationSetting postError;
  FeatureNotificationSetting newMessage;
  FeatureNotificationSetting roiActivated;

  WebNotificationSettings(
      {required this.restaurantIds,
      required this.newReviews,
      required this.reviewReplyReminder,
      required this.specialHourReminder,
      required this.postSuggestion,
      required this.postError,
      required this.newMessage,
      required this.roiActivated,
      required this.showFloatingToast});

  factory WebNotificationSettings.fromJson(Map<String, dynamic> json) =>
      WebNotificationSettings(
        showFloatingToast: json["showFloatingToast"],
        restaurantIds: List<String>.from(json["filters"]["restaurantIds"]),
        newReviews: FeatureNotificationSetting.fromJson(json["newReviews"]),
        reviewReplyReminder:
            FeatureNotificationSetting.fromJson(json["reviewReplyReminder"]),
        specialHourReminder:
            FeatureNotificationSetting.fromJson(json["specialHourReminder"]),
        postSuggestion:
            FeatureNotificationSetting.fromJson(json["postSuggestion"]),
        postError: FeatureNotificationSetting.fromJson(json["postError"]),
        newMessage: FeatureNotificationSetting.fromJson(json["newMessage"]),
        roiActivated: FeatureNotificationSetting.fromJson(json["roiActivated"]),
      );

  Map<String, dynamic> toJson() => {
        "filters": {
          "restaurantIds": List<dynamic>.from(restaurantIds),
        },
        "showFloatingToast": showFloatingToast,
        "newReviews": newReviews.toJson(),
        "reviewReplyReminder": reviewReplyReminder.toJson(),
        "specialHourReminder": specialHourReminder.toJson(),
        "postSuggestion": postSuggestion.toJson(),
        "postError": postError.toJson(),
        "newMessage": newMessage.toJson(),
        "roiActivated": roiActivated.toJson(),
      };

  WebNotificationSettings copyWith({
    List<String>? restaurantIds,
    FeatureNotificationSetting? newReviews,
    FeatureNotificationSetting? reviewReplyReminder,
    FeatureNotificationSetting? specialHourReminder,
    FeatureNotificationSetting? postSuggestion,
    FeatureNotificationSetting? postError,
    FeatureNotificationSetting? newMessage,
    FeatureNotificationSetting? roiActivated,
    bool? showFloatingToast,
  }) {
    return WebNotificationSettings(
      showFloatingToast: showFloatingToast ?? this.showFloatingToast,
      restaurantIds: restaurantIds ?? this.restaurantIds,
      newReviews: newReviews ?? this.newReviews,
      reviewReplyReminder: reviewReplyReminder ?? this.reviewReplyReminder,
      specialHourReminder: specialHourReminder ?? this.specialHourReminder,
      postSuggestion: postSuggestion ?? this.postSuggestion,
      postError: postError ?? this.postError,
      newMessage: newMessage ?? this.newMessage,
      roiActivated: roiActivated ?? this.roiActivated,
    );
  }
}

class MobileNotificationSettings {
  bool active;
  List<String> userDevicesTokens;
  MessagesSettings newMessage;
  ReviewsSettings newReviews;
  FeatureNotificationSetting noMoreScheduledPosts;

  MobileNotificationSettings({
    required this.active,
    required this.newMessage,
    required this.newReviews,
    required this.noMoreScheduledPosts,
    required this.userDevicesTokens,
  });

  factory MobileNotificationSettings.fromJson(Map<String, dynamic> json) =>
      MobileNotificationSettings(
        active: json["active"],
        userDevicesTokens: List<String>.from(json["userDevicesTokens"] ?? []),
        newMessage: MessagesSettings.fromJson(json["newMessage"]),
        newReviews: ReviewsSettings.fromJson(json["newReviews"]),
        noMoreScheduledPosts:
            FeatureNotificationSetting.fromJson(json["noMoreScheduledPosts"]),
      );

  Map<String, dynamic> toJson() => {
        "active": active,
        "userDevicesTokens": List<dynamic>.from(userDevicesTokens),
        "newMessage": newMessage.toJson(),
        "newReviews": newReviews.toJson(),
        "noMoreScheduledPosts": noMoreScheduledPosts.toJson(),
      };

  MobileNotificationSettings copyWith({
    bool? active,
    List<String>? userDevicesTokens,
    MessagesSettings? newMessage,
    ReviewsSettings? newReviews,
    FeatureNotificationSetting? noMoreScheduledPosts,
  }) {
    return MobileNotificationSettings(
      active: active ?? this.active,
      userDevicesTokens: userDevicesTokens ?? this.userDevicesTokens,
      newMessage: newMessage ?? this.newMessage,
      newReviews: newReviews ?? this.newReviews,
      noMoreScheduledPosts: noMoreScheduledPosts ?? this.noMoreScheduledPosts,
    );
  }
}

class NotificationSettings {
  List<String>? userDevicesTokens;
  bool active;
  ReviewsSettings? reviews;
  MessagesSettings? messages;
  PostsSettings? posts;

  NotificationSettings({
    this.userDevicesTokens,
    this.active = true,
    this.reviews,
    this.messages,
    this.posts,
  });

  factory NotificationSettings.fromJson(Map<String, dynamic> json) =>
      NotificationSettings(
        userDevicesTokens: json["userDevicesTokens"] == null
            ? []
            : List<String>.from(json["userDevicesTokens"]),
        active: json["active"] ?? true,
        reviews: json["reviews"] == null
            ? null
            : ReviewsSettings.fromJson(json["reviews"]),
        messages: json["messages"] == null
            ? null
            : MessagesSettings.fromJson(json["messages"]),
        posts: PostsSettings.fromJson(json["posts"]),
      );

  Map<String, dynamic> toJson() => {
        "userDevicesTokens": userDevicesTokens == null
            ? []
            : List<dynamic>.from(userDevicesTokens!),
        "active": active,
        "reviews": reviews?.toJson(),
        "messages": messages?.toJson(),
        "posts": posts?.toJson(),
      };

  NotificationSettings copyWith({
    List<String>? userDevicesTokens,
    bool? active,
    ReviewsSettings? reviews,
    MessagesSettings? messages,
    PostsSettings? posts,
  }) {
    return NotificationSettings(
      userDevicesTokens: userDevicesTokens ?? this.userDevicesTokens,
      active: active ?? this.active,
      reviews: reviews ?? this.reviews,
      messages: messages ?? this.messages,
      posts: posts ?? this.posts,
    );
  }
}

class ReviewsSettings {
  bool active;
  bool realtime;
  bool includeAutoRepliedReviews;
  List<int> receivingWeekDays;
  List<int> concernedRatings;

  ReviewsSettings({
    required this.active,
    required this.realtime,
    required this.receivingWeekDays,
    required this.concernedRatings,
    required this.includeAutoRepliedReviews,
  });

  factory ReviewsSettings.fromJson(Map<String, dynamic> json) =>
      ReviewsSettings(
        active: json["active"],
        realtime: json["realtime"],
        receivingWeekDays: List<int>.from(json["receivingWeekDays"]),
        concernedRatings: List<int>.from(json["concernedRatings"]),
        includeAutoRepliedReviews: json["includeAutoRepliedReviews"],
      );

  Map<String, dynamic> toJson() => {
        "active": active,
        "realtime": realtime,
        "receivingWeekDays": List<dynamic>.from(receivingWeekDays),
        "concernedRatings": List<dynamic>.from(concernedRatings),
        "includeAutoRepliedReviews": includeAutoRepliedReviews,
      };

  copyWith({
    bool? active,
    bool? realtime,
    bool? includeAutoRepliedReviews,
    List<int>? receivingWeekDays,
    List<int>? concernedRatings,
  }) {
    return ReviewsSettings(
      active: active ?? this.active,
      realtime: realtime ?? this.realtime,
      includeAutoRepliedReviews:
          includeAutoRepliedReviews ?? this.includeAutoRepliedReviews,
      receivingWeekDays: receivingWeekDays ?? this.receivingWeekDays,
      concernedRatings: concernedRatings ?? this.concernedRatings,
    );
  }
}

class MessagesSettings {
  bool active;
  bool realtime;
  List<int> receivingWeekDays;

  MessagesSettings({
    required this.active,
    required this.realtime,
    required this.receivingWeekDays,
  });

  factory MessagesSettings.fromJson(Map<String, dynamic> json) =>
      MessagesSettings(
        active: json["active"],
        realtime: json["realtime"],
        receivingWeekDays: List<int>.from(json["receivingWeekDays"]),
      );

  Map<String, dynamic> toJson() => {
        "active": active,
        "realtime": realtime,
        "receivingWeekDays": List<dynamic>.from(receivingWeekDays),
      };

  copyWith({
    bool? active,
    bool? realtime,
    List<int>? receivingWeekDays,
  }) {
    return MessagesSettings(
      active: active ?? this.active,
      realtime: realtime ?? this.realtime,
      receivingWeekDays: receivingWeekDays ?? this.receivingWeekDays,
    );
  }
}

class PostsSettings {
  FeatureNotificationSetting publishError;
  FeatureNotificationSetting noMoreScheduledPosts;

  PostsSettings({
    required this.publishError,
    required this.noMoreScheduledPosts,
  });

  factory PostsSettings.fromJson(Map<String, dynamic> json) => PostsSettings(
        publishError: FeatureNotificationSetting.fromJson(json["publishError"]),
        noMoreScheduledPosts:
            FeatureNotificationSetting.fromJson(json["noMoreScheduledPosts"]),
      );

  Map<String, dynamic> toJson() => {
        "publishError": publishError.toJson(),
        "noMoreScheduledPosts": noMoreScheduledPosts.toJson(),
      };

  copyWith({
    bool? active,
  }) {
    return PostsSettings(
      publishError:
          publishError.copyWith(active: active ?? publishError.active),
      noMoreScheduledPosts: noMoreScheduledPosts.copyWith(
          active: active ?? noMoreScheduledPosts.active),
    );
  }
}

class FeatureNotificationSetting {
  bool active;

  FeatureNotificationSetting({
    required this.active,
  });

  factory FeatureNotificationSetting.fromJson(Map<String, dynamic> json) =>
      FeatureNotificationSetting(
        active: json["active"],
      );

  Map<String, dynamic> toJson() => {
        "active": active,
      };

  copyWith({
    bool? active,
  }) {
    return FeatureNotificationSetting(
      active: active ?? this.active,
    );
  }
}

class Manager {
  String id;
  String name;
  String lastname;
  String? profilePicture;
  String role;
  String email;

  Manager({
    required this.id,
    required this.name,
    required this.lastname,
    required this.profilePicture,
    required this.role,
    required this.email,
  });

  factory Manager.fromJson(Map<String, dynamic> json) => Manager(
        id: json["_id"],
        name: json["name"] ?? "",
        lastname: json["lastname"] ?? "",
        profilePicture: json["profilePicture"],
        role: json["role"],
        email: json["email"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "id": id,
        "name": name,
        "lastname": lastname,
        "profilePicture": profilePicture,
        "role": role,
        "email": email,
      };

  static List<Manager> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => Manager.fromJson(json)).toList();
  }
}
