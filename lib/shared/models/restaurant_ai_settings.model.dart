class RestaurantAiSettings {
  String restaurantId;
  String restaurantName;
  String? signature;
  String replyTone;
  String defaultLanguageResponse;
  String customerNaming;
  List<String> restaurantKeywordIds;
  List<String> forbiddenWords;
  String catchphrase;

  RestaurantAiSettings({
    required this.restaurantId,
    required this.restaurantName,
    this.signature,
    required this.replyTone,
    required this.defaultLanguageResponse,
    required this.customerNaming,
    required this.restaurantKeywordIds,
    required this.forbiddenWords,
    required this.catchphrase,
  });

  factory RestaurantAiSettings.fromJson(Map<String, dynamic> json) {
    return RestaurantAiSettings(
      restaurantId: json['restaurantId'],
      restaurantName: json['restaurantName'],
      signature: json['signature'],
      replyTone: json['replyTone'] ?? '',
      defaultLanguageResponse: json['defaultLanguageResponse'],
      customerNaming: json['customerNaming'] ?? '',
      restaurantKeywordIds: json["restaurantKeywordIds"] == null
          ? []
          : List<String>.from(json["restaurantKeywordIds"]!.map((x) => x)),
      forbiddenWords: json["forbiddenWords"] == null
          ? []
          : List<String>.from(json["forbiddenWords"]!.map((x) => x)),
      catchphrase: json['catchphrase'] ?? '',
    );
  }

  RestaurantAiSettings copyWith({
    String? id,
    String? restaurantId,
    String? restaurantName,
    String? signature,
    String? replyTone,
    String? defaultLanguageResponse,
    String? customerNaming,
    List<String>? restaurantKeywordIds,
    List<String>? forbiddenWords,
    String? catchphrase,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) =>
      RestaurantAiSettings(
        restaurantId: restaurantId ?? this.restaurantId,
        restaurantName: restaurantName ?? this.restaurantName,
        signature: signature ?? this.signature,
        replyTone: replyTone ?? this.replyTone,
        defaultLanguageResponse:
            defaultLanguageResponse ?? this.defaultLanguageResponse,
        customerNaming: customerNaming ?? this.customerNaming,
        restaurantKeywordIds: restaurantKeywordIds ?? this.restaurantKeywordIds,
        forbiddenWords: forbiddenWords ?? this.forbiddenWords,
        catchphrase: catchphrase ?? this.catchphrase,
      );
}
