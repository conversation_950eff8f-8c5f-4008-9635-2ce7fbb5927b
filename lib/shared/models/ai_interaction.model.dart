class AiInteraction {
  String? aiInteractionId;
  String id;
  String relatedEntityCollection;
  String relatedEntityId;
  AiInteractionType? type;
  String? restaurantId;
  String? userId;
  String? promptText;
  int? promptTokenCount;
  String? completionText;
  int? completionTokenCount;
  int? responseTimeInMilliseconds;
  int? completionTimeInMilliseconds;
  DateTime updatedAt;
  DateTime createdAt;
  Error? error;

  AiInteraction({
    this.aiInteractionId,
    required this.id,
    required this.relatedEntityCollection,
    required this.relatedEntityId,
    this.type,
    this.restaurantId,
    this.userId,
    this.promptText,
    this.promptTokenCount,
    this.completionText,
    this.completionTokenCount,
    this.responseTimeInMilliseconds,
    this.completionTimeInMilliseconds,
    required this.updatedAt,
    required this.createdAt,
    this.error,
  });

  factory AiInteraction.fromJson(Map<String, dynamic> json) => AiInteraction(
        aiInteractionId: json["id"],
        id: json["_id"],
        relatedEntityCollection: json["relatedEntityCollection"],
        relatedEntityId: json["relatedEntityId"],
        type: json["type"] != null
            ? AiInteractionType.values.byName(json["type"])
            : null,
        restaurantId: json["restaurantId"],
        userId: json["userId"],
        promptText: json["promptText"],
        promptTokenCount: json["promptTokenCount"],
        completionText: json["completionText"],
        completionTokenCount: json["completionTokenCount"],
        responseTimeInMilliseconds: json["responseTimeInMilliseconds"],
        completionTimeInMilliseconds: json["completionTimeInMilliseconds"],
        updatedAt: DateTime.parse(json["updatedAt"]),
        createdAt: DateTime.parse(json["createdAt"]),
        error: json["error"] == null ? null : Error.fromJson(json["error"]),
      );

  Map<String, dynamic> toJson() => {
        "id": aiInteractionId,
        "_id": id,
        "relatedEntityCollection": relatedEntityCollection,
        "relatedEntityId": relatedEntityId,
        "type": type,
        "restaurantId": restaurantId,
        "userId": userId,
        "promptText": promptText,
        "promptTokenCount": promptTokenCount,
        "completionText": completionText,
        "completionTokenCount": completionTokenCount,
        "responseTimeInMilliseconds": responseTimeInMilliseconds,
        "completionTimeInMilliseconds": completionTimeInMilliseconds,
        "updatedAt": updatedAt.toIso8601String(),
        "createdAt": createdAt.toIso8601String(),
        "error": error?.toJson(),
      };

  bool isSuccessful() {
    return error == null &&
        completionText != null &&
        completionText!.isNotEmpty;
  }
}

class Error {
  String message;

  Error({
    required this.message,
  });

  factory Error.fromJson(Map<String, dynamic> json) => Error(
        message: json["message"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
      };
}

enum RelatedCollectionEntity { REVIEWS, POSTS }

class MinimalAiInteraction {
  String id;
  String text;

  MinimalAiInteraction({
    required this.id,
    required this.text,
  });
}

class AiInteractionsManager {
  List<MinimalAiInteraction> aiInteractions = [];
  int currentAiInteractionIndex = 0;

  AiInteractionsManager(
      {required this.aiInteractions, this.currentAiInteractionIndex = 0});

  MinimalAiInteraction get currentAiInteraction =>
      aiInteractions[currentAiInteractionIndex];

  String next() {
    if (currentAiInteractionIndex < aiInteractions.length - 1) {
      currentAiInteractionIndex++;
    }
    return currentAiInteraction.text;
  }

  String previous() {
    if (currentAiInteractionIndex > 0) {
      currentAiInteractionIndex--;
    }
    return currentAiInteraction.text;
  }

  void reset() {
    currentAiInteractionIndex = 0;
  }

  bool get isLast => currentAiInteractionIndex == aiInteractions.length - 1;
  bool get isFirst => currentAiInteractionIndex == 0;

  bool get hasPrevious => currentAiInteractionIndex > 0;
  bool get hasNext => currentAiInteractionIndex < aiInteractions.length - 1;

  void add(MinimalAiInteraction aiInteraction) {
    aiInteractions.add(aiInteraction);
    next();
  }

  void remove(MinimalAiInteraction aiInteraction) {
    previous();
    aiInteractions.remove(aiInteraction);
  }

  void removeAt(int index) {
    aiInteractions.removeAt(index);
  }

  void clear() {
    aiInteractions.clear();
  }

  set setCurrentAiInteractionIndex(int index) {
    currentAiInteractionIndex = index;
  }

  set setAiInteractions(List<MinimalAiInteraction> aiInteractions) {
    this.aiInteractions = aiInteractions;
  }
}

enum AiInteractionType {
  answer_review_advanced_settings,
  generate_social_network_post,
  generate_social_network_post_advanced_settings,
  generate_seo_post,
  generate_seo_post_advanced_settings,
  choose_post_hashtags,
  optimize_social_network_post,
  optimize_social_network_post_advanced_settings,
  optimize_seo_post,
  optimize_seo_post_advanced_settings,
  optimize_review_answer_advanced_settings,
  breakdown_and_classify_keywords,
  generate_semantic_analysis_overview,
  french_keywords_generation,
  english_keywords_generation,
  italian_keywords_generation,
  spanish_keywords_generation,
  categorize_restaurant,
  identify_similar_localities,
  identify_similar_localities_for_diagnostic,
  review_translation,
  post_translation,
  review_answer_translation,
  introductive_translation,
  signature_translation,
  generate_image_description,
  translate_tags,
  generate_seo_duplication,
  post_advanced_settings_detection,
  generate_rs_duplication,
  review_related_bricks_detection,
  review_semantic_analysis,
  topic_translation,
  review_semantic_analysis_and_topic_linking,
  review_semantic_analysis_and_topic_linking_v2,
  topic_linking,
  topic_linking_v2,
  topic_creation,
  review_item_detection,
  translation,
  language_detection,
  alternative_text,
  intelligent_subjects_detection,
  brand_keywords_identification,
  previous_reviews_analysis_and_reviewer_name_validation,
  response_style_detection,
  reviewer_name_validation,
  previous_reviews_matching,
  topic_pruning,

  // Deprecated
  translate,
  answer_review,
  optimize_review_answer,
}
