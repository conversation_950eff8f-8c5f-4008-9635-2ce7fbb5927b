import 'dart:convert';

class UserInfo {
  UserInfo({
    this.displayName,
    this.userSocialId,
    this.profilePictureUrl,
  });

  String? displayName;
  String? userSocialId;
  String? profilePictureUrl;

  factory UserInfo.fromRawJson(String str) =>
      UserInfo.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory UserInfo.fromJson(Map<String, dynamic> json) => UserInfo(
        displayName: json["displayName"] ?? 'Unknown',
        userSocialId: json["userSocialId"],
        profilePictureUrl: json["profilePictureUrl"],
      );

  Map<String, dynamic> toJson() => {
        "displayName": displayName,
        "userSocialId": userSocialId,
        "profilePictureUrl": profilePictureUrl,
      };
}
