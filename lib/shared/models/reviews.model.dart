// ignore_for_file: constant_identifier_names

import 'package:flutter/widgets.dart';
import 'package:intl/intl.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/countries.dart';
import 'package:malou/shared/helpers/experimentation.service.dart';
import 'package:malou/shared/helpers/pagination.model.dart';
import 'package:malou/shared/helpers/promotion_offers.dart';
import 'package:malou/shared/models/keywords/keywords_score.model.dart';
import 'package:malou/shared/models/nfc.model.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/models/restaurant.model.dart';
import 'package:malou/shared/models/reviewer_name_validation.model.dart';
import 'package:malou/shared/models/translation.model.dart' as model;
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';

import 'keywords/keywords_analysis.model.dart';

class Review {
  final REVIEWS_ANALYSIS_START_DATE = 31;
  final MIN_POSITIVE_REVIEW_RATING_TO_FETCH_RELEVANT_KEYWORDS = 3;
  final experimentationService = ExperimentationService.instance;
  bool isNewSemanticAnalysis = false;

  String id;
  String? platformId;
  String? socialId;
  bool archived;
  List<Comment> comments;
  DateTime? createdAt;
  PlatformKeys key;
  Labeling? labeling;
  int? rating;
  String restaurantId;
  Reviewer? reviewer;
  List<SocialAttachment>? socialAttachments;
  DateTime socialCreatedAt;
  double? socialRating;
  String? socialTranslatedText;
  DateTime? socialUpdatedAt;
  String? text;
  DateTime? updatedAt;
  bool wasAnsweredAutomatically;
  List<MenuItemReview>? menuItemReviews;
  int? nbDaysLeftToReply;
  String? businessSocialLink;
  String? lang;
  String? socialLink;
  int? eaterTotalOrders;
  bool? isReplyScheduled;
  Order? order;
  List<dynamic>? ratingTags;
  String? campaignId;
  String? clientId;
  Client? client;
  String? type;
  String? title;
  List<String>? comment;
  SemanticAnalysis? semanticAnalysis;
  List<SemanticAnalysisSegment>? semanticAnalysisSegments;
  String? scanId;
  Scan? scan;
  model.Translation? translations;
  ReviewerNameValidation reviewerNameValidation;

  /// business logic fields
  String translatedText = "";

  Review({
    required this.id,
    this.platformId,
    this.socialId,
    required this.archived,
    required this.comments,
    required this.createdAt,
    required this.key,
    this.labeling,
    this.rating,
    required this.restaurantId,
    this.reviewer,
    this.socialAttachments,
    required this.socialCreatedAt,
    this.socialRating,
    this.socialTranslatedText,
    this.socialUpdatedAt,
    this.text,
    required this.updatedAt,
    this.wasAnsweredAutomatically = false,
    this.menuItemReviews,
    this.nbDaysLeftToReply,
    this.businessSocialLink,
    this.lang,
    this.socialLink,
    this.eaterTotalOrders,
    this.isReplyScheduled,
    this.order,
    this.ratingTags,
    this.campaignId,
    this.clientId,
    this.client,
    this.type,
    this.title,
    this.comment,
    this.semanticAnalysis,
    this.semanticAnalysisSegments,
    this.scanId,
    this.scan,
    this.translations,
    required this.reviewerNameValidation,
  }) {
    isNewSemanticAnalysis =
        experimentationService.isOn('release-new-semantic-analysis');
    translatedText = text ?? "";
  }

  factory Review.fromJson(Map<String, dynamic> json) {
    return Review(
      id: json["_id"],
      platformId: json["platformId"],
      socialId: json["socialId"],
      archived: json["archived"],
      comments:
          List<Comment>.from(json["comments"].map((x) => Comment.fromJson(x))),
      createdAt:
          json["createdAt"] == null ? null : DateTime.parse(json["createdAt"]),
      key: PlatformKeys.values.byName(json["key"]),
      labeling:
          json["labeling"] == null ? null : Labeling.fromJson(json["labeling"]),
      rating: json["rating"],
      restaurantId: json["restaurantId"],
      reviewer:
          json["reviewer"] == null ? null : Reviewer.fromJson(json["reviewer"]),
      socialAttachments: json["socialAttachments"] == null
          ? []
          : List<SocialAttachment>.from(json["socialAttachments"]!
              .map((x) => SocialAttachment.fromJson(x))),
      socialCreatedAt: DateTime.parse(json["socialCreatedAt"]),
      socialRating: json["socialRating"]?.toDouble(),
      socialTranslatedText: json["socialTranslatedText"],
      socialUpdatedAt: json["socialUpdatedAt"] == null
          ? null
          : DateTime.parse(json["socialUpdatedAt"]),
      text: json["text"],
      updatedAt:
          json["updatedAt"] == null ? null : DateTime.parse(json["updatedAt"]),
      wasAnsweredAutomatically: json["wasAnsweredAutomatically"] ?? false,
      menuItemReviews: json["menuItemReviews"] == null
          ? []
          : List<MenuItemReview>.from(
              json["menuItemReviews"]!.map((x) => MenuItemReview.fromJson(x))),
      nbDaysLeftToReply: json["nbDaysLeftToReply"],
      businessSocialLink: json["businessSocialLink"],
      lang: json["lang"],
      socialLink: json["socialLink"],
      eaterTotalOrders: json["eaterTotalOrders"],
      isReplyScheduled: json["isReplyScheduled"],
      order: json["order"] == null ? null : Order.fromJson(json["order"]),
      ratingTags: json["ratingTags"] == null
          ? []
          : List<dynamic>.from(json["ratingTags"]!.map((x) => x)),
      campaignId: json["campaignId"],
      clientId: json["clientId"],
      client: json["client"] == null ? null : Client.fromJson(json["client"]),
      type: json["type"],
      title: json["title"],
      comment: json["comment"] == null
          ? []
          : List<String>.from(json["comment"]!.map((x) => x)),
      semanticAnalysis: json["semanticAnalysis"] == null
          ? null
          : SemanticAnalysis.fromJson(json["semanticAnalysis"]),
      semanticAnalysisSegments: json["semanticAnalysisSegments"] == null
          ? null
          : List<SemanticAnalysisSegment>.from(json["semanticAnalysisSegments"]!
              .map((x) => SemanticAnalysisSegment.fromJson(x))),
      scanId: json["scanId"],
      scan: json["scan"] == null ? null : Scan.fromJson(json["scan"]),
      translations: json["translations"] == null
          ? null
          : model.Translation.fromJson(json["translations"]),
      reviewerNameValidation: json["reviewerNameValidation"] == null
          ? ReviewerNameValidation(
              gender: Civility.other,
              firstName: "",
              isFirstNameValid: false,
              lastName: "",
              isLastNameValid: false,
            )
          : ReviewerNameValidation.fromJson(json["reviewerNameValidation"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "_id": id,
        "platformId": platformId,
        "socialId": socialId,
        "archived": archived,
        "comments": List<dynamic>.from(comments.map((x) => x.toJson())),
        "createdAt": createdAt?.toIso8601String(),
        "key": key,
        "labeling": labeling?.toJson(),
        "rating": rating,
        "restaurantId": restaurantId,
        "reviewer": reviewer?.toJson(),
        "socialAttachments": socialAttachments == null
            ? []
            : List<dynamic>.from(socialAttachments!.map((x) => x.toJson())),
        "socialCreatedAt": socialCreatedAt.toIso8601String(),
        "socialRating": socialRating,
        "socialTranslatedText": socialTranslatedText,
        "socialUpdatedAt": socialUpdatedAt?.toIso8601String(),
        "text": text,
        "updatedAt": updatedAt?.toIso8601String(),
        "wasAnsweredAutomatically": wasAnsweredAutomatically,
        "menuItemReviews": menuItemReviews == null
            ? []
            : List<dynamic>.from(menuItemReviews!.map((x) => x.toJson())),
        "nbDaysLeftToReply": nbDaysLeftToReply,
        "businessSocialLink": businessSocialLink,
        "lang": lang,
        "socialLink": socialLink,
        "eaterTotalOrders": eaterTotalOrders,
        "isReplyScheduled": isReplyScheduled,
        "order": order?.toJson(),
        "ratingTags": ratingTags == null
            ? []
            : List<dynamic>.from(ratingTags!.map((x) => x)),
        "campaignId": campaignId,
        "clientId": clientId,
        "client": client?.toJson(),
        "type": type,
        "semanticAnalysis": semanticAnalysis,
        "title": title,
        "comment":
            comment == null ? [] : List<dynamic>.from(comment!.map((x) => x)),
        "scanId": scanId,
        "semanticAnalysisSegments": semanticAnalysisSegments == null
            ? []
            : List<dynamic>.from(
                semanticAnalysisSegments!.map((x) => x.toJson())),
      };

  Review copyWith({
    String? id,
    String? platformId,
    String? socialId,
    int? v,
    bool? archived,
    List<Comment>? comments,
    DateTime? createdAt,
    PlatformKeys? key,
    Labeling? labeling,
    int? rating,
    String? restaurantId,
    Reviewer? reviewer,
    List<SocialAttachment>? socialAttachments,
    DateTime? socialCreatedAt,
    double? socialRating,
    String? socialTranslatedText,
    DateTime? socialUpdatedAt,
    String? text,
    DateTime? updatedAt,
    bool? wasAnsweredAutomatically,
    List<MenuItemReview>? menuItemReviews,
    int? nbDaysLeftToReply,
    String? businessSocialLink,
    String? lang,
    String? socialLink,
    int? eaterTotalOrders,
    bool? isReplyScheduled,
    Order? order,
    List<dynamic>? ratingTags,
    String? campaignId,
    String? clientId,
    Client? client,
    String? type,
    String? title,
    List<String>? comment,
    SemanticAnalysis? semanticAnalysis,
    List<SemanticAnalysisSegment>? semanticAnalysisSegments,
    String? scanId,
    model.Translation? translations,
    ReviewerNameValidation? reviewerNameValidation,
  }) {
    return Review(
      id: id ?? this.id,
      platformId: platformId ?? this.platformId,
      socialId: socialId ?? this.socialId,
      archived: archived ?? this.archived,
      comments: comments ?? this.comments,
      createdAt: createdAt ?? this.createdAt,
      key: key ?? this.key,
      labeling: labeling ?? this.labeling,
      rating: rating ?? this.rating,
      restaurantId: restaurantId ?? this.restaurantId,
      reviewer: reviewer ?? this.reviewer,
      socialAttachments: socialAttachments ?? this.socialAttachments,
      socialCreatedAt: socialCreatedAt ?? this.socialCreatedAt,
      socialRating: socialRating ?? this.socialRating,
      socialTranslatedText: socialTranslatedText ?? this.socialTranslatedText,
      socialUpdatedAt: socialUpdatedAt ?? this.socialUpdatedAt,
      text: text ?? this.text,
      updatedAt: updatedAt ?? this.updatedAt,
      wasAnsweredAutomatically:
          wasAnsweredAutomatically ?? this.wasAnsweredAutomatically,
      menuItemReviews: menuItemReviews ?? this.menuItemReviews,
      nbDaysLeftToReply: nbDaysLeftToReply ?? this.nbDaysLeftToReply,
      businessSocialLink: businessSocialLink ?? this.businessSocialLink,
      lang: lang ?? this.lang,
      socialLink: socialLink ?? this.socialLink,
      eaterTotalOrders: eaterTotalOrders ?? this.eaterTotalOrders,
      isReplyScheduled: isReplyScheduled ?? this.isReplyScheduled,
      order: order ?? this.order,
      ratingTags: ratingTags ?? this.ratingTags,
      campaignId: campaignId ?? this.campaignId,
      clientId: clientId ?? this.clientId,
      client: client ?? this.client,
      type: type ?? this.type,
      title: title ?? this.title,
      comment: comment ?? this.comment,
      semanticAnalysis: semanticAnalysis ?? this.semanticAnalysis,
      scanId: scanId ?? this.scanId,
      translations: translations ?? this.translations,
      reviewerNameValidation:
          reviewerNameValidation ?? this.reviewerNameValidation,
    );
  }

  bool hasReply() {
    return comments.isNotEmpty;
  }

  String getSocialAttachmentUrl(int index) {
    return socialAttachments![index].urls.original ??
        socialAttachments![index].urls.small ??
        "";
  }

  bool isAnswerableAndHasReplyTimeConstraint() {
    switch (key) {
      case PlatformKeys.deliveroo:
        return text != null && text!.isNotEmpty;
      case PlatformKeys.ubereats:
        return true;
      default:
        return false;
    }
  }

  int? getMaxDaysToReply() {
    switch (key) {
      case PlatformKeys.deliveroo:
        return 60;
      case PlatformKeys.ubereats:
        return 14;
      default:
        return null;
    }
  }

  // More about platform's review here : https://www.notion.so/welcomehomemalou/Sp-cificit-des-plateformes-908c4d30dad34f43a3e02806bab04913
  bool canBeAnswered() {
    switch (key) {
      case PlatformKeys.lafourchette:
      case PlatformKeys.deliveroo:
        if (text == null || text!.isEmpty) {
          return false;
        }
        break;
      case PlatformKeys.foursquare:
      case PlatformKeys.resy:
        return false;
      case PlatformKeys.malouPrivate:
        if (scanId != null) {
          return false;
        }
        break;
      default:
        break;
    }

    var nbDaysUntilCanBeAnswered = getMaxDaysToReply();
    if (nbDaysUntilCanBeAnswered == null) {
      return true;
    }
    var now = DateTime.now();
    var diff = now.difference(socialCreatedAt);
    return diff.inDays < nbDaysUntilCanBeAnswered;
  }

  int getDaysLeftToReply() {
    var nbDaysUntilCanBeAnswered = getMaxDaysToReply();
    if (nbDaysUntilCanBeAnswered == null) {
      return 0;
    }
    var now = DateTime.now();
    var diff = now.difference(socialCreatedAt);
    return nbDaysUntilCanBeAnswered - diff.inDays;
  }

  String displayName(BuildContext context) {
    switch (key) {
      case PlatformKeys.malouPrivate:
        if (scan != null) {
          return Translation.of(context).totem(scan!.getNfcName());
        }
        if (scanId != null) {
          return Translation.of(context).totemDisplayName;
        }
        if (client != null &&
            client?.firstName != null &&
            client?.lastName != null) {
          return "${client?.firstName} ${client?.lastName}";
        }
        return "- -";
      case PlatformKeys.ubereats:
        if (reviewer != null) {
          if (reviewer!.displayName.contains('courier')) {
            return Translation.of(context).courier_to_restaurant;
          }
          if (reviewer!.displayName.contains('customer')) {
            return Translation.of(context).customer_to_restaurant;
          }
          return reviewer!.displayName;
        }
        return "Unknown";
      default:
        if (reviewer != null) {
          return reviewer!.displayName;
        }
        return "Unknown";
    }
  }

  KeywordScoreTextType getKeywordScoreTextType() {
    if (rating == null || rating! < 3) {
      return KeywordScoreTextType.lowRateReview;
    }
    return KeywordScoreTextType.highRateReview;
  }

  void removeUnwantedTagsAndSentimentsFromAnalyses(
      {List<String> unwantedTags = const ["overall_experience"],
      List<String> unwantedSentiments = const ["neutral"]}) {
    if (isReviewHasBeenAnalysed()) {
      if (isNewSemanticAnalysis) {
        semanticAnalysisSegments =
            semanticAnalysisSegments!.where((segmentAnalysis) {
          var containsUnwantedTag =
              unwantedTags.contains(segmentAnalysis.category.name);
          var containsUnwantedSentiment =
              unwantedSentiments.contains(segmentAnalysis.sentiment.name);
          return !containsUnwantedTag && !containsUnwantedSentiment;
        }).toList();
        return;
      }
      semanticAnalysis!.segmentAnalyses = semanticAnalysis!.segmentAnalyses!
          .where((segmentAnalysis) =>
              !unwantedTags.contains(segmentAnalysis.tag.toLowerCase()) &&
              !unwantedSentiments
                  .contains(segmentAnalysis.sentiment.toLowerCase()))
          .toList();
    }
  }

  List<SegmentAnalysis> buildNotAnalyzedSegments(
      String reviewText, List<SegmentAnalysis> segmentAnalysisList) {
    final regexPattern = segmentAnalysisList
        .map((segmentAnalysis) => RegExp.escape(segmentAnalysis.segment))
        .join("|");

    final regex = RegExp(regexPattern);

    List<String> notAnalyzedSegments =
        reviewText.split(regex).where((word) => word != '').toList();

    return notAnalyzedSegments
        .map((segment) => SegmentAnalysis(
              tag: "not_analyzed",
              segment: segment,
              sentiment: "not_analyzed",
              probability: 0,
              originalSegment: segment,
            ))
        .toList();
  }

  List<SegmentAnalysisWithTextSpan> buildTextSegmentsFromSemanticAnalyses() {
    if (!isReviewHasBeenAnalysed()) {
      return [];
    }

    List<SegmentAnalysis> segmentsAnalysis = [];
    if (isNewSemanticAnalysis) {
      segmentsAnalysis = semanticAnalysisSegments!
          .map((e) => SegmentAnalysis(
                tag: e.category.name,
                segment: e.segment,
                sentiment: e.sentiment.name,
                probability: 0,
                originalSegment: e.segment,
              ))
          .toList();
    } else {
      segmentsAnalysis = semanticAnalysis!.segmentAnalyses!;
    }
    segmentsAnalysis
        .sort((a, b) => text!.indexOf(a.segment) - text!.indexOf(b.segment));

    List<SegmentAnalysis> sortedSegments = [];
    int currentIndex = 0;

    for (SegmentAnalysis segment in segmentsAnalysis) {
      int segmentIndex = text!.indexOf(segment.segment, currentIndex);

      if (segmentIndex > -1) {
        if (segmentIndex > currentIndex) {
          String inBetweenText = text!.substring(currentIndex, segmentIndex);
          sortedSegments.add(SegmentAnalysis(
            tag: "not_analyzed",
            segment: inBetweenText,
            sentiment: "not_analyzed",
            probability: 0,
            originalSegment: inBetweenText,
          ));
        }

        sortedSegments.add(segment);
        currentIndex = segmentIndex + segment.segment.length;
      }
    }

    if (currentIndex < text!.length) {
      String remainingText = text!.substring(currentIndex);
      sortedSegments.add(SegmentAnalysis(
        tag: "not_analyzed",
        segment: remainingText,
        sentiment: "not_analyzed",
        probability: 0,
        originalSegment: remainingText,
      ));
    }

    return sortedSegments
        .map((e) => SegmentAnalysisWithTextSpan(
            segmentAnalysis: e,
            textSpan: TextSpan(
                text: e.segment,
                style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: e.getColorFromSentiment()))))
        .toList();
  }

  List<ChipAnalysis> getUniqueChipsFromReviewAnalyses() {
    if (!isReviewHasBeenAnalysed()) {
      return [];
    }
    if (isNewSemanticAnalysis) {
      return _getUniqueChipsFromSemanticAnalysisSegments();
    }
    return _getUniqueChipsFromSemanticAnalysis();
  }

  List<ChipAnalysis> _getUniqueChipsFromSemanticAnalysis() {
    List<ChipAnalysis> chips = [];
    for (SegmentAnalysis segment in semanticAnalysis!.segmentAnalyses!) {
      // check if already exist
      if (chips.any((chip) =>
          chip.tag == segment.tag.toLowerCase() &&
          chip.sentiment == segment.sentiment.toLowerCase())) {
        continue;
      }

      chips.add(ChipAnalysis(
          tag: segment.tag.toLowerCase(),
          sentiment: segment.sentiment.toLowerCase(),
          active: false));
    }
    return chips;
  }

  List<ChipAnalysis> _getUniqueChipsFromSemanticAnalysisSegments() {
    List<ChipAnalysis> chips = [];
    for (SemanticAnalysisSegment segment in semanticAnalysisSegments!) {
      // check if already exist
      if (chips.any((chip) =>
          chip.tag == segment.category.name &&
          chip.sentiment == segment.sentiment.name)) {
        continue;
      }
      chips.add(ChipAnalysis(
          tag: segment.category.name,
          sentiment: segment.sentiment.name,
          active: false));
    }
    return chips;
  }

  bool isReviewHasBeenAnalysed() {
    if (isNewSemanticAnalysis) {
      return semanticAnalysisSegments != null &&
          semanticAnalysisSegments!.isNotEmpty &&
          text != null;
    }
    return semanticAnalysis != null &&
        semanticAnalysis!.segmentAnalyses != null &&
        semanticAnalysis!.segmentAnalyses!.isNotEmpty &&
        text != null;
  }

  String explainSemanticAnalysisStatus(BuildContext context) {
    var now = DateTime.now();
    var diff = now.difference(socialCreatedAt);
    if (semanticAnalysis == null) {
      if (diff.inDays >= REVIEWS_ANALYSIS_START_DATE) {
        return Translation.of(context).reviewAnalyzedTooOld;
      } else {
        return Translation.of(context).reviewNotYetAnalyzed;
      }
    }
    switch (semanticAnalysis!.status) {
      case "pending":
        return Translation.of(context).reviewNotYetAnalyzed;
      case "failed":
        return Translation.of(context).reviewAnalyseFailed;
      case "running":
        return Translation.of(context).reviewAnalysesRunning;
      case "error":
        return Translation.of(context).reviewAnalyzedError;
      case "done":
        if (isNewSemanticAnalysis &&
            semanticAnalysisSegments != null &&
            semanticAnalysisSegments!.isEmpty) {
          return Translation.of(context).reviewAnalyzedNoResult;
        } else if (semanticAnalysis?.segmentAnalyses != null &&
            semanticAnalysis!.segmentAnalyses!.isEmpty) {
          return Translation.of(context).reviewAnalyzedNoResult;
        }

        if (diff.inDays >= REVIEWS_ANALYSIS_START_DATE) {
          return Translation.of(context).reviewAnalyzedTooOld;
        } else {
          return Translation.of(context).reviewNotYetAnalyzed;
        }
      default:
        // should never happen
        return Translation.of(context).reviewAnalysesUnknownStatus;
    }
  }

  bool isReplyPendingStatus() {
    if (hasReply() && comments.last.posted == "pending") {
      return true;
    }
    return false;
  }

  bool canHaveMultipleReplies() {
    switch (key) {
      case PlatformKeys.facebook:
      case PlatformKeys.zenchef:
      case PlatformKeys.tripadvisor:
        return true;
      default:
        return false;
    }
  }

  bool canRepliesBeModified() {
    switch (key) {
      case PlatformKeys.gmb:
      case PlatformKeys.lafourchette:
      case PlatformKeys.tripadvisor:
        return true;
      default:
        return false;
    }
  }

  ReplyMethod getReplyMethod() {
    switch (key) {
      case PlatformKeys.malouPrivate:
        if (scanId != null) {
          return ReplyMethod.comment;
        }
        return ReplyMethod.mail;
      default:
        return ReplyMethod.comment;
    }
  }

  int getResponseTime() {
    var now = DateTime.now();
    final lastCommentDate =
        comments.isNotEmpty && comments.last.socialUpdatedAt != null
            ? comments.last.socialUpdatedAt
            : socialCreatedAt;
    var diff = now.difference(lastCommentDate!);
    return diff.inHours;
  }

  bool isLowRatedReview() {
    return rating != null && rating! < 3;
  }

  bool showTranslateBtn(AppLocale deviceLanguage) {
    if (lang != null && lang != deviceLanguage.name) {
      return true;
    }
    return false;
  }

  bool hasRightTranslation(AppLocale deviceLanguage) {
    if (text == null) {
      return true;
    }
    if (lang == deviceLanguage.name) {
      return true;
    }
    return false;
  }

  bool reviewNeedTranslation(
      AppLocale deviceLanguage, bool displaySemanticAnalyses) {
    if (translations == null) {
      return true;
    }
    if (text == translatedText) {
      return true;
    }
    if (displaySemanticAnalyses) {
      return true;
    }
    // add check by selected language
    return translations!.getTranslation(deviceLanguage) == null;
  }

  void initTranslatedText(AppLocale deviceLanguage) {
    if (translations == null) {
      translatedText = text ?? "";
      return;
    }
    switch (deviceLanguage) {
      case AppLocale.fr:
        translatedText = translations?.fr ?? text ?? "";
      case AppLocale.en:
        translatedText = translations?.en ?? text ?? "";
      case AppLocale.es:
        translatedText = translations?.es ?? text ?? "";
      case AppLocale.it:
        translatedText = translations?.it ?? text ?? "";
    }
  }

  void setOriginalReviewText() {
    translatedText = text ?? "";
  }

  bool isPrivateReview() {
    return key == PlatformKeys.malouPrivate;
  }

  bool hasTranslation(AppLocale deviceLanguage) {
    if (translations == null) {
      return false;
    }
    return translations!.getTranslation(deviceLanguage) != null;
  }

  void setTranslations(model.Translation translation) {
    translations = translation;
  }

  List<PromotionOffer> getPromotionOffers(
      Restaurant restaurant, BuildContext context) {
    switch (key) {
      case PlatformKeys.ubereats:
        if (order?.currencyCode == null) {
          return [];
        }

        var restaurantCountry = restaurant.address?.country;
        if (restaurantCountry == null) {
          return [];
        }

        var countryCode = COUNTRIES
            .where((country) =>
                country.name == restaurantCountry ||
                country.nameFr == restaurantCountry)
            .first
            .code;

        if (countryCode == null) {
          return [];
        }

        var offers = countryCodeToUbereatsOfferTiersMap[countryCode];

        if (offers == null) {
          return [];
        }

        var currencySymbol =
            NumberFormat.simpleCurrency(name: order!.currencyCode)
                .currencySymbol;

        var offerList = offers.entries
            .map((entry) => (
                  key: entry.key,
                  value: entry.value,
                  selected: false,
                  chosen: false,
                  displayFormat: "${entry.value} $currencySymbol"
                ))
            .toList();
        offerList.insert(0, (
          key: UbereatsPromotionValue.NONE,
          value: 0,
          selected: true,
          chosen: true,
          displayFormat: Translation.of(context).nop
        ));
        return offerList;
      default:
        return [];
    }
  }

  bool hasPromotionOffers(Restaurant restaurant, BuildContext context) {
    return getPromotionOffers(restaurant, context).isNotEmpty;
  }
}

enum ReplyMethod { comment, mail }

class Comment {
  String text;
  String? socialTranslatedText;
  DateTime? socialUpdatedAt;
  String? posted;
  String? templateIdUsed;
  String id;
  dynamic templatedIdUsed;
  KeywordAnalysis? keywordAnalysis;
  bool? isMalou;
  Author? author;
  String? socialId;
  UserReview? user;

  Comment({
    required this.text,
    this.socialTranslatedText,
    this.socialUpdatedAt,
    required this.posted,
    this.templateIdUsed,
    required this.id,
    this.templatedIdUsed,
    this.keywordAnalysis,
    this.isMalou,
    this.author,
    this.socialId,
    this.user,
  });

  factory Comment.fromJson(Map<String, dynamic> json) => Comment(
        text: json["text"],
        socialTranslatedText: json["socialTranslatedText"],
        socialUpdatedAt: json["socialUpdatedAt"] == null
            ? null
            : DateTime.parse(json["socialUpdatedAt"]),
        posted: json["posted"],
        templateIdUsed: json["templateIdUsed"],
        id: json["_id"],
        templatedIdUsed: json["templatedIdUsed"],
        keywordAnalysis: json["keywordAnalysis"] == null
            ? null
            : KeywordAnalysis.fromJson(json["keywordAnalysis"]),
        isMalou: json["isMalou"],
        author: json["author"] == null ? null : Author.fromJson(json["author"]),
        socialId: json["socialId"],
        user: json["user"] == null ? null : UserReview.fromJson(json["user"]),
      );

  Map<String, dynamic> toJson() => {
        "text": text,
        "socialTranslatedText": socialTranslatedText,
        "socialUpdatedAt": socialUpdatedAt?.toIso8601String(),
        "posted": posted,
        "templateIdUsed": templateIdUsed,
        "_id": id,
        "templatedIdUsed": templatedIdUsed,
        "keywordAnalysis": keywordAnalysis?.toJson(),
        "isMalou": isMalou,
        "author": author?.toJson(),
        "socialId": socialId,
        "user": user?.toJson(),
      };
}

class Author {
  String id;
  String name;
  dynamic picture;

  Author({
    required this.id,
    required this.name,
    this.picture,
  });

  factory Author.fromJson(Map<String, dynamic> json) => Author(
        id: json["_id"],
        name: json["name"],
        picture: json["picture"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "name": name,
        "picture": picture,
      };
}

class UserReview {
  String? socialId;
  dynamic displayName;

  UserReview({
    required this.socialId,
    this.displayName,
  });

  factory UserReview.fromJson(Map<String, dynamic> json) => UserReview(
        socialId: json["socialId"],
        displayName: json["displayName"],
      );

  Map<String, dynamic> toJson() => {
        "socialId": socialId,
        "displayName": displayName,
      };
}

class Labeling {
  int sentiment;

  Labeling({
    required this.sentiment,
  });

  factory Labeling.fromJson(Map<String, dynamic> json) => Labeling(
        sentiment: json["sentiment"],
      );

  Map<String, dynamic> toJson() => {
        "sentiment": sentiment,
      };
}

class MenuItemReview {
  String socialId;
  bool rating;
  String name;
  dynamic comment;
  List<String> tags;

  MenuItemReview({
    required this.socialId,
    required this.rating,
    required this.name,
    this.comment,
    required this.tags,
  });

  factory MenuItemReview.fromJson(Map<String, dynamic> json) => MenuItemReview(
        socialId: json["socialId"],
        rating: json["rating"],
        name: json["name"],
        comment: json["comment"],
        tags: List<String>.from(json["tags"].map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "socialId": socialId,
        "rating": rating,
        "name": name,
        "comment": comment,
        "tags": List<dynamic>.from(tags.map((x) => x)),
      };
}

class Order {
  String workflowId;
  DateTime deliveredAt;
  double? orderTotal;
  String currencyCode;
  String appVariant;

  Order({
    required this.workflowId,
    required this.deliveredAt,
    required this.orderTotal,
    required this.currencyCode,
    required this.appVariant,
  });

  factory Order.fromJson(Map<String, dynamic> json) => Order(
        workflowId: json["workflowId"],
        deliveredAt: DateTime.parse(json["deliveredAt"]),
        orderTotal: json["orderTotal"]?.toDouble(),
        currencyCode: json["currencyCode"],
        appVariant: json["appVariant"],
      );

  Map<String, dynamic> toJson() => {
        "workflowId": workflowId,
        "deliveredAt": deliveredAt.toIso8601String(),
        "orderTotal": orderTotal,
        "currencyCode": currencyCode,
        "appVariant": appVariant,
      };
}

class Reviewer {
  String? profilePhotoUrl;
  String displayName;
  String? socialId;
  dynamic socialUrl;

  Reviewer({
    this.profilePhotoUrl,
    required this.displayName,
    this.socialId,
    this.socialUrl,
  });

  factory Reviewer.fromJson(Map<String, dynamic> json) => Reviewer(
        profilePhotoUrl: json["profilePhotoUrl"],
        displayName: json["displayName"],
        socialId: json["socialId"],
        socialUrl: json["socialUrl"],
      );

  Map<String, dynamic> toJson() => {
        "profilePhotoUrl": profilePhotoUrl,
        "displayName": displayName,
        "socialId": socialId,
        "socialUrl": socialUrl,
      };
}

class Client {
  String id;
  String restaurantId;
  String? civility;
  String firstName;
  String lastName;
  Phone? phone;
  String email;
  String source;
  DateTime? lastVisitedAt;
  int? contactCount;
  int? visitCount;
  List<String> accepts;
  List<ReviewsLeft> reviewsLeft;
  DateTime createdAt;
  DateTime updatedAt;
  DateTime? lastContactedAt;

  Client({
    required this.id,
    required this.restaurantId,
    this.civility,
    required this.firstName,
    required this.lastName,
    this.phone,
    required this.email,
    required this.source,
    this.lastVisitedAt,
    this.contactCount,
    this.visitCount,
    required this.accepts,
    required this.reviewsLeft,
    required this.createdAt,
    required this.updatedAt,
    this.lastContactedAt,
  });

  factory Client.fromJson(Map<String, dynamic> json) => Client(
        id: json["_id"],
        restaurantId: json["restaurantId"],
        civility: json["civility"],
        firstName: json["firstName"],
        lastName: json["lastName"],
        phone: json["phone"] == null ? null : Phone.fromJson(json["phone"]),
        email: json["email"],
        source: json["source"],
        lastVisitedAt: DateTime.parse(json["lastVisitedAt"]),
        contactCount: json["contactCount"],
        visitCount: json["visitCount"],
        accepts: List<String>.from(json["accepts"].map((x) => x)),
        reviewsLeft: List<ReviewsLeft>.from(
            json["reviewsLeft"].map((x) => ReviewsLeft.fromJson(x))),
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
        lastContactedAt: DateTime.parse(json["lastContactedAt"]),
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "restaurantId": restaurantId,
        "civility": civility,
        "firstName": firstName,
        "lastName": lastName,
        "phone": phone?.toJson(),
        "email": email,
        "source": source,
        "lastVisitedAt": lastVisitedAt?.toIso8601String(),
        "contactCount": contactCount,
        "visitCount": visitCount,
        "accepts": List<dynamic>.from(accepts.map((x) => x)),
        "reviewsLeft": List<dynamic>.from(reviewsLeft.map((x) => x.toJson())),
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
        "lastContactedAt": lastContactedAt?.toIso8601String(),
      };
}

class ReviewsLeft {
  String platformKey;
  bool hasLeftReview;

  ReviewsLeft({
    required this.platformKey,
    required this.hasLeftReview,
  });

  factory ReviewsLeft.fromJson(Map<String, dynamic> json) => ReviewsLeft(
        platformKey: json["platformKey"],
        hasLeftReview: json["hasLeftReview"],
      );

  Map<String, dynamic> toJson() => {
        "platformKey": platformKey,
        "hasLeftReview": hasLeftReview,
      };
}

class SocialAttachment {
  Urls urls;
  String type;

  SocialAttachment({
    required this.urls,
    required this.type,
  });

  factory SocialAttachment.fromJson(Map<String, dynamic> json) =>
      SocialAttachment(
        urls: Urls.fromJson(json["urls"]),
        type: json["type"],
      );

  Map<String, dynamic> toJson() => {
        "urls": urls.toJson(),
        "type": type,
      };
}

class Urls {
  String? original;
  String? small;

  Urls({
    required this.original,
    required this.small,
  });

  factory Urls.fromJson(Map<String, dynamic> json) => Urls(
        original: json["original"],
        small: json["small"],
      );

  Map<String, dynamic> toJson() => {
        "original": original,
        "small": small,
      };
}

class PaginatedReviewsReponse {
  Pagination pagination;
  List<Review> reviews;

  PaginatedReviewsReponse({
    required this.pagination,
    required this.reviews,
  });
}

class SemanticAnalysis {
  String id;
  PlatformKeys platformKey;
  String reviewSocialId;
  DateTime createdAt;
  int failCount;
  String providerKey;
  List<SegmentAnalysis>? segmentAnalyses;
  String status;
  DateTime updatedAt;
  String? openaiProcessingTimeMs;
  String? providerId;
  String? rawProviderResponse;
  DateTime? socialCreatedAt;
  String? failedReason;

  SemanticAnalysis({
    required this.id,
    required this.platformKey,
    required this.reviewSocialId,
    required this.createdAt,
    required this.failCount,
    required this.providerKey,
    required this.segmentAnalyses,
    required this.status,
    required this.updatedAt,
    required this.openaiProcessingTimeMs,
    required this.providerId,
    required this.rawProviderResponse,
    required this.socialCreatedAt,
    required this.failedReason,
  });

  factory SemanticAnalysis.fromJson(Map<String, dynamic> json) =>
      SemanticAnalysis(
        id: json["_id"],
        platformKey: PlatformKeys.values.byName(json["platformKey"]),
        reviewSocialId: json["reviewSocialId"],
        createdAt: DateTime.parse(json["createdAt"]),
        failCount: json["failCount"],
        providerKey: json["providerKey"],
        segmentAnalyses:
            json["segmentAnalyses"] == null || json["segmentAnalyses"]!.isEmpty
                ? []
                : List<SegmentAnalysis>.from(json["segmentAnalyses"]
                    ?.map((x) => SegmentAnalysis.fromJson(x))),
        status: json["status"],
        updatedAt: DateTime.parse(json["updatedAt"]),
        openaiProcessingTimeMs: json["openaiProcessingTimeMs"],
        providerId: json["providerId"],
        rawProviderResponse: json["rawProviderResponse"],
        socialCreatedAt: json["socialCreatedAt"] == null
            ? null
            : DateTime.parse(json["socialCreatedAt"]),
        failedReason: json["failedReason"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "platformKey": platformKey,
        "reviewSocialId": reviewSocialId,
        "createdAt": createdAt.toIso8601String(),
        "failCount": failCount,
        "providerKey": providerKey,
        "segmentAnalyses":
            List<dynamic>.from(segmentAnalyses!.map((x) => x.toJson())),
        "status": status,
        "updatedAt": updatedAt.toIso8601String(),
        "openaiProcessingTimeMs": openaiProcessingTimeMs,
        "providerId": providerId,
        "rawProviderResponse": rawProviderResponse,
        "socialCreatedAt": socialCreatedAt?.toIso8601String(),
      };
}

class SegmentAnalysis {
  String tag;
  String segment;
  String sentiment;
  double? probability;
  String? originalSegment;

  SegmentAnalysis({
    required this.tag,
    required this.segment,
    required this.sentiment,
    required this.probability,
    required this.originalSegment,
  });

  factory SegmentAnalysis.fromJson(Map<String, dynamic> json) =>
      SegmentAnalysis(
        tag: json["tag"],
        segment: json["segment"],
        sentiment: json["sentiment"],
        probability: json["probability"]?.toDouble(),
        originalSegment: json["originalSegment"],
      );

  Map<String, dynamic> toJson() => {
        "tag": tag,
        "segment": segment,
        "sentiment": sentiment,
        "probability": probability,
        "originalSegment": originalSegment,
      };

  Color? getColorFromSentiment() {
    switch (sentiment.toLowerCase()) {
      case "positive":
        return MalouColors.success;
      case "negative":
        return MalouColors.pinkAccent;
      default:
        return null;
    }
  }
}

enum Sentiment { Positive, Negative, Neutral }

class SegmentAnalysisWithTextSpan {
  SegmentAnalysis segmentAnalysis;
  TextSpan textSpan;
  bool active = false;

  SegmentAnalysisWithTextSpan({
    required this.segmentAnalysis,
    required this.textSpan,
  });
}

enum ReviewAnalysisTag {
  food,
  service,
  price,
  atmosphere,
  hygiene,
  expeditiousness,
  overall_experience
}

class ChipAnalysis {
  String tag;
  String sentiment;
  bool active = false;
  double opacity = 1;

  ChipAnalysis({
    required this.tag,
    required this.sentiment,
    required this.active,
  });

  String translate(BuildContext context) {
    if (ReviewAnalysisTag.values.byName(tag.toLowerCase()) ==
        ReviewAnalysisTag.food) {
      return Translation.of(context).food;
    }
    if (ReviewAnalysisTag.values.byName(tag.toLowerCase()) ==
        ReviewAnalysisTag.service) {
      return Translation.of(context).service;
    }
    if (ReviewAnalysisTag.values.byName(tag.toLowerCase()) ==
        ReviewAnalysisTag.price) {
      return Translation.of(context).price;
    }
    if (ReviewAnalysisTag.values.byName(tag.toLowerCase()) ==
        ReviewAnalysisTag.atmosphere) {
      return Translation.of(context).atmosphere;
    }
    if (ReviewAnalysisTag.values.byName(tag.toLowerCase()) ==
        ReviewAnalysisTag.hygiene) {
      return Translation.of(context).hygiene;
    }
    if (ReviewAnalysisTag.values.byName(tag.toLowerCase()) ==
        ReviewAnalysisTag.expeditiousness) {
      return Translation.of(context).expeditiousness;
    }
    if (ReviewAnalysisTag.values.byName(tag.toLowerCase()) ==
        ReviewAnalysisTag.overall_experience) {
      return Translation.of(context).overallExperience;
    }
    return "";
  }
}

typedef PromotionOffer = ({
  UbereatsPromotionValue key,
  int value,
  bool selected,
  bool chosen,
  String displayFormat
});

class SemanticAnalysisSegment {
  String id;
  DateTime createdAt;
  DateTime updatedAt;
  PlatformKeys platformKey;
  String reviewSocialId;
  String platformSocialId;
  ReviewAnalysisTag category;
  String segment;
  String aiFoundSegment;
  ReviewAnalysisSentiment sentiment;
  String topic;
  DateTime? reviewSocialCreatedAt;
  ReviewAnalysisSubCategory? subcategory;
  model.Translation? reviewSegmentTranslations;
  List<String>? segmentAnalysisParentTopicIds;
  bool? isRatingTagOrMenuItem;

  SemanticAnalysisSegment({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    required this.platformKey,
    required this.reviewSocialId,
    required this.platformSocialId,
    required this.category,
    required this.segment,
    required this.aiFoundSegment,
    required this.sentiment,
    required this.topic,
    this.reviewSocialCreatedAt,
    this.subcategory,
    this.reviewSegmentTranslations,
    this.segmentAnalysisParentTopicIds = const [],
    this.isRatingTagOrMenuItem = false,
  });

  factory SemanticAnalysisSegment.fromJson(Map<String, dynamic> json) =>
      SemanticAnalysisSegment(
        id: json["id"],
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
        platformKey: PlatformKeys.values.byName(json["platformKey"]),
        reviewSocialId: json["reviewSocialId"],
        platformSocialId: json["platformSocialId"],
        category: ReviewAnalysisTag.values.byName(json["category"]),
        segment: json["segment"],
        aiFoundSegment: json["aiFoundSegment"],
        sentiment: ReviewAnalysisSentiment.values.byName(json["sentiment"]),
        topic: json["topic"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "platformKey": platformKey.name,
        "reviewSocialId": reviewSocialId,
        "reviewSocialCreatedAt": reviewSocialCreatedAt?.toIso8601String(),
        "platformSocialId": platformSocialId,
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
        "category": category.name,
        "subcategory": subcategory?.name,
        "sentiment": sentiment.name,
        "topic": topic,
        "aiFoundSegment": aiFoundSegment,
        "segment": segment,
        "reviewSegmentTranslations": reviewSegmentTranslations?.toJson(),
        "segmentAnalysisParentTopicIds": segmentAnalysisParentTopicIds,
        "isRatingTagOrMenuItem": isRatingTagOrMenuItem,
      };

  @override
  String toString() {
    return 'id: $id, platformKey: ${platformKey.name}, reviewSocialId: $reviewSocialId, category: ${category.name}, segment: $segment, sentiment: ${sentiment.name}';
  }
}

enum ReviewAnalysisSubCategory {
  menu_items,
  staff_members,
}

enum ReviewAnalysisSentiment {
  positive,
  negative,
  neutral,
}
