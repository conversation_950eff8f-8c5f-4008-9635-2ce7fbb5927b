import 'dart:convert';

class Feedback {
  String id;
  bool isOpen;
  List<FeedbackMessage> feedbackMessages;
  DateTime createdAt;
  DateTime updatedAt;
  int? v;

  Feedback({
    required this.id,
    required this.isOpen,
    required this.feedbackMessages,
    required this.createdAt,
    required this.updatedAt,
    required this.v,
  });

  Feedback copyWith({
    String? id,
    bool? isOpen,
    List<FeedbackMessage>? feedbackMessages,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? v,
  }) =>
      Feedback(
        id: id ?? this.id,
        isOpen: isOpen ?? this.isOpen,
        feedbackMessages: feedbackMessages ?? this.feedbackMessages,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        v: v ?? this.v,
      );

  factory Feedback.fromRawJson(String str) =>
      Feedback.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Feedback.fromJson(Map<String, dynamic> json) => Feedback(
        id: json["_id"] ?? json["id"],
        isOpen: json["isOpen"],
        feedbackMessages: List<FeedbackMessage>.from(
            json["feedbackMessages"].map((x) => FeedbackMessage.fromJson(x))),
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
        v: json["__v"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "isOpen": isOpen,
        "feedbackMessages":
            List<dynamic>.from(feedbackMessages.map((x) => x.toJson())),
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
        "__v": v,
      };

  int getFeedbacksCount() {
    return feedbackMessages.length;
  }
}

class FeedbackMessage {
  Author author;
  DateTime createdAt;
  String? text;
  FeedbackMessageType type;
  FeedbackVisibility? visibility;
  String id;

  FeedbackMessage({
    required this.author,
    required this.createdAt,
    required this.text,
    required this.type,
    required this.visibility,
    required this.id,
  });

  FeedbackMessage copyWith({
    Author? author,
    DateTime? createdAt,
    String? text,
    FeedbackMessageType? type,
    FeedbackVisibility? visibility,
    String? id,
  }) =>
      FeedbackMessage(
        author: author ?? this.author,
        createdAt: createdAt ?? this.createdAt,
        text: text ?? this.text,
        type: type ?? this.type,
        visibility: visibility ?? this.visibility,
        id: id ?? this.id,
      );

  factory FeedbackMessage.fromRawJson(String str) =>
      FeedbackMessage.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory FeedbackMessage.fromJson(Map<String, dynamic> json) =>
      FeedbackMessage(
        author: Author.fromJson(json["author"]),
        createdAt: DateTime.parse(json["createdAt"]).toLocal(),
        text: json["text"] ?? '',
        type: FeedbackMessageType.values.byName(json["type"]),
        visibility: json["visibility"] != null
            ? FeedbackVisibility.values.byName(json["visibility"])
            : FeedbackVisibility.basic,
        id: json["_id"] ?? json["id"],
      );

  Map<String, dynamic> toJson() => {
        "author": author.toJson(),
        "createdAt": createdAt.toIso8601String(),
        "text": text,
        "type": type.name,
        "visibility": visibility?.name,
        "_id": id,
      };
}

class Author {
  String name;
  String lastname;
  String? profilePictureUrl;
  String role;
  String email;
  String id;
  String fullname;

  Author({
    required this.name,
    required this.lastname,
    this.profilePictureUrl,
    required this.role,
    required this.email,
    required this.id,
    required this.fullname,
  });

  Author copyWith({
    String? name,
    String? lastname,
    String? profilePictureUrl,
    String? role,
    String? email,
    String? id,
    String? fullname,
  }) =>
      Author(
        name: name ?? this.name,
        lastname: lastname ?? this.lastname,
        profilePictureUrl: profilePictureUrl ?? this.profilePictureUrl,
        role: role ?? this.role,
        email: email ?? this.email,
        id: id ?? this.id,
        fullname: fullname ?? this.fullname,
      );

  factory Author.fromRawJson(String str) => Author.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Author.fromJson(Map<String, dynamic> json) => Author(
        name: json["name"],
        lastname: json["lastname"],
        profilePictureUrl: json["profilePictureUrl"],
        role: json["role"],
        email: json["email"],
        id: json["_id"] ?? json["id"],
        fullname: json["fullname"] ?? (json['name'] + " " + json['lastname']),
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "lastname": lastname,
        "profilePictureUrl": profilePictureUrl,
        "role": role,
        "email": email,
        "_id": id,
        "fullname": fullname,
      };
}

enum FeedbackVisibility { basic, admin }

enum FeedbackMessageType { text, reopen, close }
