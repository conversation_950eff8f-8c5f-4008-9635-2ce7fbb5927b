import 'dart:async';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:vector_math/vector_math_64.dart';
import 'package:video_player/video_player.dart';

enum MediaTagCategory { food, drinks, location, specialEvent, people, other }

enum MediaTagSubcategory { main, dessert, backgroundType, landmark }

final mediaTagCategoryValues = EnumValues({
  "food": MediaTagCategory.food,
  "drinks": MediaTagCategory.drinks,
  "location": MediaTagCategory.location,
  "specialEvent": MediaTagCategory.specialEvent,
  "people": MediaTagCategory.people,
  "other": MediaTagCategory.other
});

final mediaTagSubcategoryValues = EnumValues({
  "main": MediaTagSubcategory.main,
  "dessert": MediaTagSubcategory.dessert,
  "backgroundType": MediaTagSubcategory.backgroundType,
  "landmark": MediaTagSubcategory.landmark,
});

class MediaAiTag {
  MediaAiTag({
    required this.tag,
    required this.language,
    required this.category,
    this.subcategory,
  });
  String tag;
  Language language;
  MediaTagCategory category;
  MediaTagSubcategory? subcategory;

  Map<String, dynamic> toJson() => {
        "tag": tag,
        "language": languageValues.reverse[language],
        "category": mediaTagCategoryValues.reverse[category],
        "subcategory": mediaTagSubcategoryValues.reverse[subcategory],
      };

  factory MediaAiTag.fromJson(Map<String, dynamic> json) => MediaAiTag(
      tag: json['tag'],
      language: languageValues.map[json["language"]]!,
      category: mediaTagCategoryValues.map[json['category']]!,
      subcategory: json['subcategory'] == null
          ? null
          : mediaTagSubcategoryValues.map[json['subcategory']]);
}

class Media {
  Media(
      {this.id,
      required this.restaurantId,
      required this.userId,
      this.title,
      this.name,
      this.description,
      this.isDeviceMedia = false,
      this.assetEntity,
      required this.category,
      required this.format,
      required this.type,
      required this.urls,
      required this.sizes,
      required this.tagIds,
      required this.postIds,
      required this.socialId,
      required this.createdAt,
      required this.updatedAt,
      this.dimensions = const {},
      this.duration,
      this.resizeMetadata,
      this.transformationController,
      this.isDirty = false,
      this.thumbnail,
      this.aiDescription,
      this.aiTags = const [],
      this.storedObjects});

  String? id;
  String? restaurantId;
  String? userId;
  String? title;
  String? description;
  String? name;
  String? category;
  String format;
  String type;
  Map<String, String?>? urls;
  Map<String, dynamic>? sizes;
  List<String>? tagIds;
  List<String>? postIds;
  String? socialId;
  DateTime createdAt;
  DateTime updatedAt;
  bool isDeviceMedia;
  AssetEntity? assetEntity;
  Map<String, Dimension?>? dimensions;
  Duration? duration;
  ResizeMetadata? resizeMetadata;
  TransformationController? transformationController;
  bool isDirty;
  String? thumbnail;
  String? aiDescription;
  List<MediaAiTag> aiTags = [];
  StoredObjects? storedObjects;

  factory Media.fromJson(Map<String, dynamic> json) => Media(
        id: json["id"] ?? json["_id"],
        restaurantId: json["restaurantId"],
        userId: json["userId"],
        title: json["title"],
        description: json["description"],
        category: json["category"],
        format: json["format"],
        type: json["type"],
        urls: json['urls'] != null
            ? Map.from(json["urls"])
                .map((k, v) => MapEntry<String, String?>(k, v))
            : null,
        sizes: json['sizes'] != null
            ? Map.from(json["sizes"])
                .map((k, v) => MapEntry<String, dynamic>(k, v))
            : null,
        tagIds: json['tagIds'] != null
            ? List<String>.from((json["tagIds"] as List)
                .where((x) => x != null)
                .toList()
                .map((x) => x))
            : [],
        postIds: json['postIds'] != null
            ? List<String>.from((json["postIds"] as List)
                .where((element) => element != null)
                .map((x) => x))
            : [],
        socialId: json["socialId"],
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
        dimensions: json['dimensions'] != null
            ? Map.from(json["dimensions"]).map((k, v) =>
                MapEntry<String, Dimension?>(
                    k, v != null ? Dimension.fromJson(v) : null))
            : {},
        duration: json['duration'] != null
            ? Duration(seconds: (json['duration'] as num).toInt())
            : null,
        transformationController: TransformationController(),
        resizeMetadata: ResizeMetadata(
            width: 0,
            height: 0,
            cropPosition: CropPosition(left: 0, top: 0),
            aspectRatio: json['dimensions'] != null &&
                    json['dimensions']['original'] != null
                ? json['dimensions']['original']['width'] /
                    json['dimensions']['original']['height']
                : null),
        thumbnail: json['thumbnail'],
        aiDescription: json['aiDescription'],
        aiTags: json['aiTags'] != null
            ? List<MediaAiTag>.from(
                (json['aiTags'] as List).map((x) => MediaAiTag.fromJson(x)))
            : [],
        storedObjects: json['storedObjects'] != null
            ? StoredObjects.fromJson(json['storedObjects'])
            : null,
      );
  factory Media.fromSocialAttachment(SocialAttachment socialAttachment,
          String restaurantId, String? userId) =>
      Media(
        restaurantId: restaurantId,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        format: socialAttachment.getFormat(),
        userId: userId,
        category: Category.ADDITIONAL.name,
        type: socialAttachment.type ?? 'PHOTO',
        urls: {
          'original': socialAttachment.getUrls(),
          'small': socialAttachment.getUrls(),
          'igFit': socialAttachment.getUrls(),
        },
        sizes: {},
        tagIds: [],
        postIds: [],
        socialId: socialAttachment.socialId,
        transformationController: TransformationController(),
        aiTags: [],
        storedObjects: null,
      );

  static List<Media> fromJsonList(List<dynamic> jsonList) {
    List<Media> medias = [];
    for (var json in jsonList) {
      medias.add(Media.fromJson(json));
    }
    return medias;
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "restaurantId": restaurantId,
        "userId": userId,
        "title": title,
        "description": description,
        "category": category,
        "format": format,
        "type": type,
        "urls": urls,
        "sizes": sizes,
        "tagIds":
            tagIds != null ? List<dynamic>.from(tagIds!.map((x) => x)) : [],
        "postIds":
            postIds != null ? List<dynamic>.from(postIds!.map((x) => x)) : [],
        "socialId": socialId,
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
        "dimensions": dimensions != null
            ? Map.from(dimensions!)
                .map((k, v) => MapEntry<String, dynamic>(k, v?.toJson()))
            : {},
        "duration": duration?.inSeconds,
        "resizeMetadata": resizeMetadata?.toJson(),
        "thumbnail": thumbnail,
        "aiDescription": aiDescription,
        "aiTags": List<dynamic>.from(aiTags.map((x) => x.toJson())),
        'storedObjects': storedObjects?.toJson(),
      };

  Media copyWith(
      {String? id,
      String? restaurantId,
      String? userId,
      String? title,
      String? description,
      String? category,
      String? format,
      String? type,
      Map<String, String?>? urls,
      Map<String, dynamic>? sizes,
      List<String>? tagIds,
      List<String>? postIds,
      String? socialId,
      DateTime? createdAt,
      DateTime? updatedAt,
      double? resizeScale,
      double? aspectRatio,
      Map<String, Dimension?>? dimensions,
      AssetEntity? assetEntity,
      Duration? duration,
      ResizeMetadata? resizeMetadata,
      TransformationController? transformationController,
      bool? isDirty,
      String? thumbnail,
      String? aiDescription,
      List<MediaAiTag>? aiTags}) {
    return Media(
        id: id ?? this.id,
        restaurantId: restaurantId ?? this.restaurantId,
        userId: userId ?? this.userId,
        title: title ?? this.title,
        description: description ?? this.description,
        category: category ?? this.category,
        format: format ?? this.format,
        type: type ?? this.type,
        urls: urls ?? this.urls,
        sizes: sizes ?? this.sizes,
        tagIds: tagIds ?? this.tagIds,
        postIds: postIds ?? this.postIds,
        socialId: socialId ?? this.socialId,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        dimensions: dimensions ?? this.dimensions,
        assetEntity: assetEntity ?? this.assetEntity,
        duration: duration ?? this.duration,
        resizeMetadata: resizeMetadata ?? this.resizeMetadata,
        transformationController:
            transformationController ?? this.transformationController,
        isDirty: isDirty ?? this.isDirty,
        thumbnail: thumbnail ?? this.thumbnail,
        aiDescription: aiDescription ?? this.aiDescription,
        aiTags: aiTags ?? this.aiTags);
  }

  double? getDurationInSeconds() {
    return duration?.inSeconds.toDouble();
  }

  String? getFullname() {
    if (name != null) {
      return name;
    }
    if (title != null) {
      return '$title.$format';
    }
    return 'no name.$format';
  }

  @override
  bool operator ==(Object other) {
    return other is Media && other.hashCode == hashCode;
  }

  @override
  int get hashCode => Object.hash(id, type, name);

  bool isImage() {
    return type == MediaType.photo.name ||
        getMediaUrl().contains('jpg') ||
        getMediaUrl().contains('png') ||
        getMediaUrl().contains('jpeg') ||
        getMediaUrl().contains('application/octet-stream');
  }

  bool isVideo() {
    return assetEntity != null
        ? assetEntity!.type == AssetType.video
        : type == MediaType.video.toString() ||
            getMediaUrl().toLowerCase().contains('mp4') ||
            getMediaUrl().toLowerCase().contains('mov');
  }

  String getMediaUrl({String size = 'igFit'}) {
    return urls?[size] ?? urls?['original'] ?? '';
  }

  String? getThumbnailUrl() {
    return storedObjects?.thumbnail1024Outside.publicUrl ?? thumbnail;
  }

  String? getUrls() {
    return urls?['small'] ?? urls?['igFit'] ?? urls?['original'];
  }

  Dimension getMediaDimensions({String size = 'igFit'}) {
    return dimensions?[size] ??
        dimensions?['original'] ??
        Dimension(width: 0, height: 0);
  }

  Future<Media> setAspectRatioFromUrl({String size = 'original'}) async {
    // fetch media aspect ratio from url
    String? url = getMediaUrl(size: size);
    final httpClient = Dio();
    try {
      var response = await httpClient.head(url);
      if (response.statusCode == 200) {
        List<String>? contentType = response.headers['content-type'];
        if (contentType == null) return this;

        if (contentType
            .where((element) =>
                element.contains('image') ||
                element.contains('application/octet-stream'))
            .isNotEmpty) {
          var result = await _fetchImage(url);
          resizeMetadata?.aspectRatio = result.$1;
          if (getMediaUrl(size: size).contains(size)) {
            dimensions = {
              ...dimensions!,
              size: Dimension(width: result.$2, height: result.$3),
            };
          } else {
            dimensions = {
              MediaSize.original.name:
                  Dimension(width: result.$2, height: result.$3),
            };
          }
        } else if (contentType
            .where((element) => element.contains('image'))
            .isNotEmpty) {
          VideoPlayerController controller =
              VideoPlayerController.networkUrl(Uri.parse(url));
          await controller.initialize();
          resizeMetadata?.aspectRatio = controller.value.aspectRatio;
          dimensions = {
            MediaSize.original.name: Dimension(
                width: controller.value.size.width,
                height: controller.value.size.height),
          };
        }
      }
    } catch (e) {
      // ignore: avoid_print
      print(e);
    }
    return this;
  }

  Future<(double, double, double)> _fetchImage(String url) {
    Completer<(double, double, double)> completer = Completer();
    Image.network(url)
        .image
        .resolve(const ImageConfiguration())
        .addListener(ImageStreamListener((ImageInfo info, bool _) {
      completer.complete((
        info.image.width.toDouble() / info.image.height,
        info.image.width.toDouble(),
        info.image.height.toDouble()
      ));
    }));
    return completer.future;
  }

  ResizeMetadata getImageTransformations() {
    // Extract the scale and translation components from the matrix
    Matrix4 transformMatrix = transformationController!.value;
    double scale = transformMatrix.getMaxScaleOnAxis();
    Vector3 translation = transformMatrix.getTranslation();

    // Get the actual dimensions of the original image and the displayed image
    double originalImageWidth = assetEntity != null
        ? assetEntity!.width.toDouble()
        : getMediaDimensions(size: MediaSize.original.name).width.toDouble();
    double originalImageHeight = assetEntity != null
        ? assetEntity!.height.toDouble()
        : getMediaDimensions(size: MediaSize.original.name).height.toDouble();

    double displayedImageWidth = resizeMetadata!.displayBoxSize!.width;
    double displayedImageHeight = resizeMetadata!.displayBoxSize!.height;

    double boxRatio = displayedImageWidth / displayedImageHeight;

    double left = (translation.x.abs() < 10 ? 0 : translation.x.abs()) / scale;
    double top = (translation.y.abs() < 10 ? 0 : translation.y.abs()) / scale;

    double initialScale = getInitialScale(
        resizeMetadata!.displayBoxSize!, resizeMetadata!.aspectRatio!);
    double scaleFactor = scale / initialScale;
    double originalScaleFactor = (originalImageWidth / displayedImageWidth);

    double width = (displayedImageWidth * originalScaleFactor) / scaleFactor;

    double height = (displayedImageHeight * originalScaleFactor) / scaleFactor;

    final result = ResizeMetadata(
      aspectRatio: boxRatio,
      cropPosition: CropPosition(left: left.floor(), top: top.floor()),
      width: width.floor() >= originalImageWidth - left.floor()
          ? (originalImageWidth.toInt() - left.floor() - 1)
          : width.floor(),
      height: (height.floor() >= originalImageHeight - top.floor()
          ? (originalImageHeight.toInt() - top.floor() - 1)
          : height.floor()),
    );
    return result;
  }

  double getOriginalMediaSizeForInitialScale(
      String biggerSide, double aspectRatio) {
    if (aspectRatio > 1) {
      return dimensions!['original']!.width.toDouble();
    }

    if (aspectRatio < 1) {
      return dimensions!['original']!.height.toDouble();
    }

    return biggerSide == 'width'
        ? dimensions!['original']!.height.toDouble()
        : dimensions!['original']!.width.toDouble();
  }

  double getScreenSizeForInitialScaling(
      String biggerSide, double aspectRatio, Size screenSize) {
    if (aspectRatio > 1) {
      return screenSize.width;
    }
    if (aspectRatio < 1) {
      return screenSize.height;
    }
    return biggerSide == 'width' ? screenSize.height : screenSize.width;
  }

  String getBiggerSide() {
    double originalImageWidth = assetEntity != null
        ? assetEntity!.width.toDouble()
        : getMediaDimensions(size: MediaSize.original.name).width.toDouble();
    double originalImageHeight = assetEntity != null
        ? assetEntity!.height.toDouble()
        : getMediaDimensions(size: MediaSize.original.name).height.toDouble();

    return originalImageWidth > originalImageHeight ? 'width' : 'height';
  }

  double getInitialScale(Size screenSize, double aspectRatio) {
    String biggerSide = getBiggerSide();
    double size =
        getScreenSizeForInitialScaling(biggerSide, aspectRatio, screenSize);
    double originalSize =
        getOriginalMediaSizeForInitialScale(biggerSide, aspectRatio);
    double screenAspectRatio = screenSize.width / screenSize.height;
    double originalMediaAspectRatio =
        getMediaDimensions(size: 'original').width /
            getMediaDimensions(size: 'original').height;
    double aspectRatioScale = screenAspectRatio / originalMediaAspectRatio;

    double initialScale = (size / originalSize) * aspectRatioScale;

    return initialScale;
  }
}

class ResizeMetadata {
  double? aspectRatio;
  int? width;
  int? height;
  CropPosition? cropPosition;
  Size? displayBoxSize;

  ResizeMetadata(
      {this.aspectRatio,
      this.cropPosition,
      this.width = 0,
      this.height = 0,
      this.displayBoxSize});

  Map<String, dynamic> toJson() => {
        "aspectRatio": aspectRatio,
        "cropPosition": cropPosition?.toJson(),
        'width': width?.abs(),
        'height': height?.abs()
      };

  ResizeMetadata copyWith(
      {double? aspectRatio,
      int? width,
      int? height,
      CropPosition? cropPosition,
      Size? displayBoxSize}) {
    return ResizeMetadata(
        aspectRatio: aspectRatio ?? this.aspectRatio,
        cropPosition: cropPosition ?? this.cropPosition,
        height: height ?? this.height,
        width: height ?? this.width,
        displayBoxSize: displayBoxSize ?? this.displayBoxSize);
  }
}

class CropPosition {
  int? left;
  int? top;

  CropPosition({this.left, this.top});

  Map<String, dynamic> toJson() => {
        "left": left,
        "top": top,
      };

  CropPosition copyWith({
    int? left,
    int? top,
  }) {
    return CropPosition(
      left: left ?? this.left,
      top: top ?? this.top,
    );
  }
}

class Dimension {
  Dimension({
    required this.width,
    required this.height,
  });

  num width;
  num height;

  factory Dimension.fromJson(Map<String, dynamic> json) => Dimension(
        width: json["width"] ?? 0,
        height: json["height"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "width": width,
        "height": height,
      };
}

class StoredObject {
  final String key;
  final String publicUrl;
  final String provider;

  StoredObject({
    required this.key,
    required this.publicUrl,
    required this.provider,
  });

  factory StoredObject.fromJson(Map<String, dynamic> json) {
    return StoredObject(
      key: json['key'],
      publicUrl: json['publicUrl'],
      provider: json['provider'],
    );
  }

  Map<String, dynamic> toJson() => {
        'key': key,
        'publicUrl': publicUrl,
        'provider': provider,
      };
}

class StoredObjects {
  final StoredObject original;
  final StoredObject normalized;
  final StoredObject thumbnail1024Outside;
  final StoredObject thumbnail256Outside;

  StoredObjects({
    required this.original,
    required this.normalized,
    required this.thumbnail1024Outside,
    required this.thumbnail256Outside,
  });

  factory StoredObjects.fromJson(Map<String, dynamic> json) {
    return StoredObjects(
      original: StoredObject.fromJson(json['original']),
      normalized: StoredObject.fromJson(json['normalized']),
      thumbnail1024Outside: StoredObject.fromJson(json['thumbnail1024Outside']),
      thumbnail256Outside: StoredObject.fromJson(json['thumbnail256Outside']),
    );
  }

  Map<String, dynamic> toJson() => {
        'original': original.toJson(),
        'normalized': normalized.toJson(),
        'thumbnail1024Outside': thumbnail1024Outside.toJson(),
        'thumbnail256Outside': thumbnail256Outside.toJson(),
      };
}

class MediaEntityRelation {
  MediaEntityRelation({
    required this.entityRelated,
    required this.entityId,
  });

  String entityRelated;
  String entityId;

  factory MediaEntityRelation.fromJson(Map<String, dynamic> json) =>
      MediaEntityRelation(
        entityRelated: json["entity_related"],
        entityId: json["entity_id"],
      );

  Map<String, dynamic> toSnakeCaseJson() => {
        "entity_related": entityRelated,
        "entity_id": entityId,
      };
}

enum MediaSize {
  small,
  original,
  cover,
  smallCover,
  igFit,
}

enum MediaType {
  photo,
  video,
  all,
}
