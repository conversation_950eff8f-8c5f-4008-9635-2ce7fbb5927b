import 'package:malou/shared/services/language/language.provider.dart';

class Translation {
  String? fr;
  String? en;
  String? es;
  String? it;
  String language;

  Translation({
    this.fr,
    this.en,
    this.es,
    this.it,
    required this.language,
  });

  factory Translation.fromJson(Map<String, dynamic> json) => Translation(
        fr: json["fr"],
        en: json["en"],
        es: json["es"],
        it: json["it"],
        language: json["language"],
      );

  Map<String, dynamic> toJson() => {
        "fr": fr,
        "en": en,
        "es": es,
        "it": it,
        "language": language,
      };

  String? getTranslation(AppLocale deviceLanguage) {
    switch (deviceLanguage) {
      case AppLocale.fr:
        return fr;
      case AppLocale.en:
        return en;
      case AppLocale.es:
        return es;
      case AppLocale.it:
        return it;
    }
  }
}
