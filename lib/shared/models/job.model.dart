import 'dart:convert';

class Job {
  String id;
  String name;
  JobData data;
  int priority;
  bool shouldSaveResult;
  dynamic lastModifiedBy;
  dynamic lockedAt;
  DateTime lastRunAt;
  int failCount;
  String failReason;
  DateTime failedAt;
  DateTime lastFinishedAt;
  String type;
  DateTime nextRunAt;

  Job({
    required this.id,
    required this.name,
    required this.data,
    required this.priority,
    required this.shouldSaveResult,
    required this.lastModifiedBy,
    required this.lockedAt,
    required this.lastRunAt,
    required this.failCount,
    required this.failReason,
    required this.failedAt,
    required this.lastFinishedAt,
    required this.type,
    required this.nextRunAt,
  });

  Job copyWith({
    String? id,
    String? name,
    JobData? data,
    int? priority,
    bool? shouldSaveResult,
    dynamic lastModifiedBy,
    dynamic lockedAt,
    DateTime? lastRunAt,
    int? failCount,
    String? failReason,
    DateTime? failedAt,
    DateTime? lastFinishedAt,
    String? type,
    DateTime? nextRunAt,
  }) =>
      Job(
        id: id ?? this.id,
        name: name ?? this.name,
        data: data ?? this.data,
        priority: priority ?? this.priority,
        shouldSaveResult: shouldSaveResult ?? this.shouldSaveResult,
        lastModifiedBy: lastModifiedBy ?? this.lastModifiedBy,
        lockedAt: lockedAt ?? this.lockedAt,
        lastRunAt: lastRunAt ?? this.lastRunAt,
        failCount: failCount ?? this.failCount,
        failReason: failReason ?? this.failReason,
        failedAt: failedAt ?? this.failedAt,
        lastFinishedAt: lastFinishedAt ?? this.lastFinishedAt,
        type: type ?? this.type,
        nextRunAt: nextRunAt ?? this.nextRunAt,
      );

  factory Job.fromRawJson(String str) => Job.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Job.fromJson(Map<String, dynamic> json) => Job(
        id: json["_id"],
        name: json["name"],
        data: JobData.fromJson(json["data"]),
        priority: json["priority"],
        shouldSaveResult: json["shouldSaveResult"],
        lastModifiedBy: json["lastModifiedBy"],
        lockedAt: json["lockedAt"],
        lastRunAt: DateTime.parse(json["lastRunAt"]),
        failCount: json["failCount"],
        failReason: json["failReason"],
        failedAt: DateTime.parse(json["failedAt"]),
        lastFinishedAt: DateTime.parse(json["lastFinishedAt"]),
        type: json["type"],
        nextRunAt: DateTime.parse(json["nextRunAt"]),
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "name": name,
        "data": data.toJson(),
        "priority": priority,
        "shouldSaveResult": shouldSaveResult,
        "lastModifiedBy": lastModifiedBy,
        "lockedAt": lockedAt,
        "lastRunAt": lastRunAt.toIso8601String(),
        "failCount": failCount,
        "failReason": failReason,
        "failedAt": failedAt.toIso8601String(),
        "lastFinishedAt": lastFinishedAt.toIso8601String(),
        "type": type,
        "nextRunAt": nextRunAt.toIso8601String(),
      };
}

class JobData {
  String userId;
  String postId;
  String restaurantId;

  JobData({
    required this.userId,
    required this.postId,
    required this.restaurantId,
  });

  JobData copyWith({
    String? userId,
    String? postId,
    String? restaurantId,
  }) =>
      JobData(
        userId: userId ?? this.userId,
        postId: postId ?? this.postId,
        restaurantId: restaurantId ?? this.restaurantId,
      );

  factory JobData.fromRawJson(String str) => JobData.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory JobData.fromJson(Map<String, dynamic> json) => JobData(
        userId: json["userId"],
        postId: json["postId"],
        restaurantId: json["restaurantId"]!,
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "postId": postId,
        "restaurantId": restaurantId,
      };
}
