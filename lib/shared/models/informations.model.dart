import 'dart:convert';

class Address {
  String? locality;
  String? regionCode;
  String? country;
  String? postalCode;
  String? formattedAddress;
  String? streetNumber;
  String? route;

  Address({
    required this.regionCode,
    required this.country,
    required this.postalCode,
    required this.formattedAddress,
    this.locality,
    this.streetNumber,
    this.route,
  });

  factory Address.fromRawJson(String str) => Address.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Address.fromJson(Map<String, dynamic> json) => Address(
        locality: json["locality"],
        regionCode: json["regionCode"],
        country: json["country"],
        postalCode: json["postalCode"],
        formattedAddress: json["formattedAddress"],
        streetNumber: json["streetNumber"],
        route: json["route"],
      );

  Map<String, dynamic> toJson() => {
        "locality": locality,
        "regionCode": regionCode,
        "country": country,
        "postalCode": postalCode,
        "formattedAddress": formattedAddress,
        "streetNumber": streetNumber,
        "route": route,
      };
}

class BusinessSocialLinks {
  String home;
  String media;

  BusinessSocialLinks({
    required this.home,
    required this.media,
  });

  factory BusinessSocialLinks.fromRawJson(String str) =>
      BusinessSocialLinks.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory BusinessSocialLinks.fromJson(Map<String, dynamic> json) =>
      BusinessSocialLinks(
        home: json["home"],
        media: json["media"],
      );

  Map<String, dynamic> toJson() => {
        "home": home,
        "media": media,
      };
}

class Description {
  String? language;
  String? size;
  String? text;
  bool? active;
  String id;
  DateTime createdAt;
  DateTime updatedAt;

  Description({
    required this.language,
    required this.size,
    required this.text,
    required this.active,
    required this.id,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Description.fromRawJson(String str) =>
      Description.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Description.fromJson(Map<String, dynamic> json) => Description(
        language: json["language"],
        size: json["size"],
        text: json["text"],
        active: json["active"],
        id: json["_id"],
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
      );

  Map<String, dynamic> toJson() => {
        "language": language,
        "size": size,
        "text": text,
        "active": active,
        "_id": id,
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
      };
}

class Latlng {
  double? lat;
  double? lng;

  Latlng({
    this.lat,
    this.lng,
  });

  factory Latlng.fromRawJson(String str) => Latlng.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Latlng.fromJson(Map<String, dynamic> json) => Latlng(
        lat: json["lat"]?.toDouble(),
        lng: json["lng"]?.toDouble(),
      );

  Map<String, dynamic> toJson() => {
        "lat": lat,
        "lng": lng,
      };
}

class Phone {
  int? prefix;
  int? digits;

  Phone({
    this.prefix,
    this.digits,
  });

  factory Phone.fromRawJson(String str) => Phone.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Phone.fromJson(Map<String, dynamic> json) => Phone(
        prefix: json["prefix"],
        digits: json["digits"],
      );

  Map<String, dynamic> toJson() => {
        "prefix": prefix,
        "digits": digits,
      };
}

class RegularHour {
  String openDay;
  String? openTime;
  String? closeDay;
  String? closeTime;
  bool isClosed;

  RegularHour({
    required this.openDay,
    this.openTime,
    this.closeDay,
    this.closeTime,
    required this.isClosed,
  });

  factory RegularHour.fromRawJson(String str) =>
      RegularHour.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory RegularHour.fromJson(Map<String, dynamic> json) => RegularHour(
        openDay: json["openDay"],
        openTime: json["openTime"],
        closeDay: json["closeDay"],
        closeTime: json["closeTime"],
        isClosed: json["isClosed"],
      );

  Map<String, dynamic> toJson() => {
        "openDay": openDay,
        "openTime": openTime,
        "closeDay": closeDay,
        "closeTime": closeTime,
        "isClosed": isClosed
      };
}

class SpecialHour {
  Date? startDate;
  String? openTime;
  Date? endDate;
  String? closeTime;
  bool? isClosed;

  SpecialHour({
    required this.startDate,
    required this.openTime,
    required this.endDate,
    required this.closeTime,
    required this.isClosed,
  });

  factory SpecialHour.fromRawJson(String str) =>
      SpecialHour.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SpecialHour.fromJson(Map<String, dynamic> json) => SpecialHour(
        startDate:
            json["startDate"] != null ? Date.fromJson(json["startDate"]) : null,
        openTime: json["openTime"],
        endDate:
            json["endDate"] != null ? Date.fromJson(json["endDate"]) : null,
        closeTime: json["closeTime"],
        isClosed: json["isClosed"],
      );

  Map<String, dynamic> toJson() => {
        "startDate": startDate?.toJson(),
        "openTime": openTime,
        "endDate": endDate?.toJson(),
        "closeTime": closeTime,
        "isClosed": isClosed,
      };
}

class Date {
  int day;
  int year;
  int month;

  Date({
    required this.day,
    required this.year,
    required this.month,
  });

  factory Date.fromRawJson(String str) => Date.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Date.fromJson(Map<String, dynamic> json) => Date(
        day: json["day"],
        year: json["year"],
        month: json["month"],
      );

  Map<String, dynamic> toJson() => {
        "day": day,
        "year": year,
        "month": month,
      };
}

class WatchedAccount {
  String userName;
  String? profilePicUrl;
  String? name;
  int? followersCount;
  int? mediaCount;
  String? biography;
  String? id;

  WatchedAccount({
    required this.userName,
    required this.profilePicUrl,
    required this.name,
    required this.followersCount,
    required this.mediaCount,
    required this.biography,
    required this.id,
  });

  factory WatchedAccount.fromRawJson(String str) =>
      WatchedAccount.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory WatchedAccount.fromJson(Map<String, dynamic> json) => WatchedAccount(
        userName: json["userName"],
        profilePicUrl: json["profilePicUrl"],
        name: json["name"],
        followersCount: json["followersCount"],
        mediaCount: json["mediaCount"],
        biography: json["biography"],
        id: json["_id"],
      );

  Map<String, dynamic> toJson() => {
        "userName": userName,
        "profilePicUrl": profilePicUrl,
        "name": name,
        "followersCount": followersCount,
        "mediaCount": mediaCount,
        "biography": biography,
        "_id": id,
      };
}
