class Organization {
  Organization({
    required this.id,
    required this.name,
    required this.createdAt,
    required this.updatedAt,
    this.limit,
    this.verifiedEmailsForCampaigns
  });

  String id;
  String name;
  DateTime createdAt;
  DateTime updatedAt;
  int? limit;
  List<String>? verifiedEmailsForCampaigns;

  factory Organization.fromJson(Map<String, dynamic> json) => Organization(
        id: json["_id"],
        name: json["name"],
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
        limit: json["limit"],
        verifiedEmailsForCampaigns: json["verifiedEmailsForCampaigns"] != null ? List<String>.from(json["verifiedEmailsForCampaigns"].map((x) => x)) : null,
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "name": name,
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
        "limit": limit,
        "verifiedEmailsForCampaigns": verifiedEmailsForCampaigns != null ? List<dynamic>.from(verifiedEmailsForCampaigns!.map((x) => x)) : null,
      };
}
