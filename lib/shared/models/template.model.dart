// ignore_for_file: constant_identifier_names

import 'dart:convert';

import 'package:malou/shared/models/reviews.model.dart';

class Template {
  String id;
  String name;
  String restaurantId;
  bool automated;
  TemplateCategory? category;
  DateTime createdAt;
  String language;
  List<int> rating;
  String status;
  String text;
  TemplateType type;
  DateTime updatedAt;
  String? userId;
  WithComment? withComment;
  List<String>? platformsKeys;

  Template({
    required this.id,
    required this.name,
    required this.restaurantId,
    required this.automated,
    this.category,
    required this.createdAt,
    required this.language,
    required this.rating,
    required this.status,
    required this.text,
    required this.type,
    required this.updatedAt,
    required this.userId,
    this.withComment,
    this.platformsKeys,
  });

  Template copyWith({
    String? id,
    String? name,
    int? v,
    bool? automated,
    TemplateCategory? category,
    DateTime? createdAt,
    List<int>? rating,
    String? text,
    TemplateType? type,
    DateTime? updatedAt,
    WithComment? withComment,
  }) =>
      Template(
        id: id ?? this.id,
        name: name ?? this.name,
        restaurantId: restaurantId,
        automated: automated ?? this.automated,
        category: category ?? this.category,
        createdAt: createdAt ?? this.createdAt,
        language: language,
        rating: rating ?? this.rating,
        status: status,
        text: text ?? this.text,
        type: type ?? this.type,
        updatedAt: updatedAt ?? this.updatedAt,
        userId: userId,
        withComment: withComment,
        platformsKeys: platformsKeys,
      );

  factory Template.fromRawJson(String str) =>
      Template.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Template.fromJson(Map<String, dynamic> json) {
    try {
      return Template(
        id: json["_id"],
        name: json["name"] ?? "",
        restaurantId: json["restaurantId"],
        automated: json["automated"] ?? false,
        category: templateCategoryValues.map[json["category"]],
        createdAt: DateTime.parse(json["createdAt"]),
        language: json["language"],
        rating: json["rating"] == null
            ? []
            : List<int>.from(json["rating"].map((x) => x)),
        status: json["status"],
        text: json["text"],
        type: templateTypeValues.map[json["type"]]!,
        updatedAt: DateTime.parse(json["updatedAt"]),
        userId: json["userId"],
        withComment: withCommentValues.map[json["withComment"]],
        platformsKeys: json["platformsKeys"] == null
            ? []
            : List<String>.from(json["platformsKeys"].map((x) => x)),
      );
    } catch (e) {
      throw Exception("[PARSING_TYPE_ERROR] - $json : $e");
    }
  }

  Map<String, dynamic> toJson() => {
        "_id": id,
        "name": name,
        "restaurantId": restaurantId,
        "automated": automated,
        "category": templateCategoryValues.reverse[category],
        "createdAt": createdAt.toIso8601String(),
        "language": language,
        "rating": List<dynamic>.from(rating.map((x) => x)),
        "status": status,
        "text": text,
        "type": templateTypeValues.reverse[type],
        "updatedAt": updatedAt.toIso8601String(),
        "userId": userId,
        "withComment": withCommentValues.reverse[withComment],
        "platformsKeys": platformsKeys == null
            ? []
            : List<dynamic>.from(platformsKeys!.map((x) => x)),
      };

  int compareToFindTheOneThatMatchTheBestForReview(
      Template other, Review review) {
    if (review.rating != null) {
      if (!rating.contains(review.rating!) &&
          !other.rating.contains(review.rating!)) {
        return 0;
      }

      if (rating.contains(review.rating!) &&
          !other.rating.contains(review.rating!)) {
        return -1;
      }

      if (!rating.contains(review.rating!) &&
          other.rating.contains(review.rating!)) {
        return 1;
      }

      if (rating.contains(review.rating!) &&
          other.rating.contains(review.rating!)) {
        if (rating.length > other.rating.length) {
          return -1;
        } else if (rating.length < other.rating.length) {
          return 1;
        } else {
          return 0;
        }
      }
    }
    return 0;
  }
}

enum TemplateType { REVIEW, MESSAGE }

final templateTypeValues = EnumValues(
    {"message": TemplateType.MESSAGE, "review": TemplateType.REVIEW});

enum TemplateCategory { RESTAURANT, TOUTES }

final templateCategoryValues = EnumValues({
  "restaurant": TemplateCategory.RESTAURANT,
  "Toutes": TemplateCategory.TOUTES
});

enum WithComment { WITH, WITH_OR_WITHOUT, WITHOUT }

final withCommentValues = EnumValues({
  "with": WithComment.WITH,
  "without": WithComment.WITHOUT,
  "withOrWithout": WithComment.WITH_OR_WITHOUT
});

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
