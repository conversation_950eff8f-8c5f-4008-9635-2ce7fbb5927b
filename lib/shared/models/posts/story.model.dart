import 'post.model.dart';

class Story extends Post {
  Story(
      {required super.id,
      required super.keys,
      required super.text,
      required super.hashtags,
      required super.published,
      required super.tries,
      required super.restaurantId,
      required super.attachments,
      required super.isReelDisplayedInFeed,
      required super.shouldDuplicateInOtherPlatforms,
      required super.source,
      required super.createdAt,
      required super.socialAttachments,
      required super.userTags,
      required super.updatedAt,
      required super.creationDate,
      super.socialCreatedAt,
      super.postType,
      super.feedback});

  factory Story.fromPost(Post post) {
    return Story(
      id: post.id,
      keys: post.keys,
      text: post.text,
      hashtags: post.hashtags,
      published: post.published,
      tries: post.tries,
      restaurantId: post.restaurantId,
      attachments: post.attachments,
      isReelDisplayedInFeed: post.isReelDisplayedInFeed,
      shouldDuplicateInOtherPlatforms: post.shouldDuplicateInOtherPlatforms,
      source: post.source,
      createdAt: post.createdAt,
      socialAttachments: post.socialAttachments,
      userTags: post.userTags,
      updatedAt: post.updatedAt,
      creationDate: post.creationDate,
      feedback: post.feedback,
      socialCreatedAt: post.socialCreatedAt,
      postType: post.postType,
    );
  }

  bool isActive() {
    if (super.socialCreatedAt == null) return false;
    return (published == PublicationStatus.PUBLISHED &&
        DateTime.now().difference(socialCreatedAt!).inDays < 1);
  }

  String getStoryDuration() {
    if (getAttachments().isEmpty) return '--';
    if (published == PublicationStatus.PUBLISHED) {
      if (socialAttachments[0].type == Type.photo.name) {
        return 'Images 5 Sec.';
      }
      return 'Video 15 Sec.';
    }
    if (attachments[0].type == Type.photo.name) {
      return 'Images 5 Sec.';
    }
    return 'Video 15 Sec.';
  }

  int getHoursSincePublished() {
    if (socialCreatedAt == null) return 24;
    if (published == PublicationStatus.PUBLISHED) {
      return DateTime.now().difference(socialCreatedAt!).inHours;
    }
    return DateTime.now().difference(socialCreatedAt!).inHours;
  }

  @override
  bool canBeDeleted() {
    if (PublicationStatus.PUBLISHED == published) {
      return false;
    }
    return true;
  }
}
