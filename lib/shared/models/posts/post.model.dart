// ignore_for_file: constant_identifier_names

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:malou/shared/helpers/pagination.model.dart';
import 'package:malou/shared/helpers/text_editing_controller_stream.dart';
import 'package:malou/shared/models/keywords/keywords_analysis.model.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/models/posts/post-hashtags.model.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import '../../../generated/l10n.dart';
import '../feedback.model.dart' as feedback_model;

enum PublicationDatePlanned { now, draft, scheduled }

enum MediaPickerType { image, video, all }

class Post {
  String? id;
  List<PlatformKeys> keys;
  String? text;
  PostHashtags hashtags;
  PublicationStatus published;
  int tries;
  DateTime? plannedPublicationDate;
  Author? author;
  String restaurantId;
  List<Media> attachments;
  PostTopic? postTopic;
  PostType? postType;
  bool isReelDisplayedInFeed;
  String? bindingId;
  bool shouldDuplicateInOtherPlatforms;
  List<List<dynamic>>? userTagsList;
  Source source;
  bool? isStory;
  DateTime createdAt;
  List<SocialAttachment> socialAttachments;
  List<AccountTag>? userTags;
  DateTime updatedAt;
  CallToAction? callToAction;
  Event? event;
  Offer? offer;
  PostLocation? location;
  String? attachmentsName;
  DateTime? creationDate;
  PlatformKeys? key;
  Language? language;
  String? platformId;
  String? title;
  String? mapstrCtaButtonType;
  String? mapstrCtaText;
  Source? category;
  DateTime? socialCreatedAt;
  String? socialId;
  String? socialLink;
  DateTime? socialUpdatedAt;
  String? errorData;
  dynamic errorStage;
  String? duplicatedFromRestaurantId;
  feedback_model.Feedback? feedback;
  PublicationDatePlanned? publicationDatePlanned;
  TextEditingControllerStream? postTextController;
  Media? thumbnail;
  String? feedbackId;
  int? thumbnailOffsetTimeInMs;
  KeywordAnalysis? keywordAnalysis;
  DeviceType? createdFromDeviceType;

  Post(
      {this.id,
      required this.keys,
      required this.text,
      required this.hashtags,
      required this.published,
      required this.tries,
      this.plannedPublicationDate,
      this.author,
      required this.restaurantId,
      required this.attachments,
      this.postTopic,
      this.postType,
      required this.isReelDisplayedInFeed,
      this.bindingId,
      required this.shouldDuplicateInOtherPlatforms,
      this.userTagsList,
      required this.source,
      this.isStory = false,
      required this.createdAt,
      required this.socialAttachments,
      required this.userTags,
      required this.updatedAt,
      this.callToAction,
      this.event,
      this.offer,
      this.location,
      this.attachmentsName,
      this.creationDate,
      this.key,
      this.language,
      this.platformId,
      this.title,
      this.mapstrCtaButtonType,
      this.mapstrCtaText,
      this.category,
      this.socialCreatedAt,
      this.socialId,
      this.socialLink,
      this.socialUpdatedAt,
      this.errorData,
      this.errorStage,
      this.duplicatedFromRestaurantId,
      this.feedback,
      this.publicationDatePlanned,
      this.postTextController,
      this.thumbnail,
      this.feedbackId,
      this.thumbnailOffsetTimeInMs,
      this.keywordAnalysis,
      this.createdFromDeviceType});

  Post copyWith({
    String? id,
    List<PlatformKeys>? keys,
    String? text,
    PostHashtags? hashtags,
    PublicationStatus? published,
    int? tries,
    DateTime? plannedPublicationDate,
    Author? author,
    String? restaurantId,
    List<Media>? attachments,
    PostTopic? postTopic,
    PostType? postType,
    bool? isReelDisplayedInFeed,
    String? bindingId,
    bool? hashtagsInFirstComment,
    bool? shouldDuplicateInOtherPlatforms,
    List<List<dynamic>>? userTagsList,
    Source? source,
    bool? isStory,
    DateTime? createdAt,
    List<SocialAttachment>? socialAttachments,
    List<AccountTag>? userTags,
    DateTime? updatedAt,
    int? v,
    CallToAction? callToAction,
    Event? event,
    Offer? offer,
    PostLocation? location,
    String? attachmentsName,
    DateTime? creationDate,
    PlatformKeys? key,
    Language? language,
    String? platformId,
    String? title,
    String? mapstrCtaButtonType,
    String? mapstrCtaText,
    Source? category,
    DateTime? socialCreatedAt,
    String? socialId,
    String? socialLink,
    DateTime? socialUpdatedAt,
    dynamic errorData,
    dynamic errorStage,
    String? duplicatedFromRestaurantId,
    feedback_model.Feedback? feedback,
    PublicationDatePlanned? publicationDatePlanned,
    TextEditingControllerStream? postTextController,
    Media? thumbnail,
    String? feedbackId,
    int? thumbnailOffsetTimeInMs,
    KeywordAnalysis? keywordAnalysis,
    DeviceType? createdFromDeviceType,
  }) =>
      Post(
        id: id ?? this.id,
        keys: keys ?? this.keys,
        text: text ?? this.text,
        hashtags: hashtags ?? this.hashtags,
        published: published ?? this.published,
        tries: tries ?? this.tries,
        plannedPublicationDate:
            plannedPublicationDate ?? this.plannedPublicationDate,
        author: author ?? this.author,
        restaurantId: restaurantId ?? this.restaurantId,
        attachments: attachments ?? this.attachments,
        postTopic: postTopic ?? this.postTopic,
        postType: postType ?? this.postType,
        isReelDisplayedInFeed:
            isReelDisplayedInFeed ?? this.isReelDisplayedInFeed,
        bindingId: bindingId ?? this.bindingId,
        shouldDuplicateInOtherPlatforms: shouldDuplicateInOtherPlatforms ??
            this.shouldDuplicateInOtherPlatforms,
        userTagsList: userTagsList ?? this.userTagsList,
        source: source ?? this.source,
        isStory: isStory ?? this.isStory,
        createdAt: createdAt ?? this.createdAt,
        socialAttachments: socialAttachments ?? this.socialAttachments,
        userTags: userTags ?? this.userTags,
        updatedAt: updatedAt ?? this.updatedAt,
        callToAction: callToAction ?? this.callToAction,
        event: event ?? this.event,
        offer: offer ?? this.offer,
        location: location ?? this.location,
        attachmentsName: attachmentsName ?? this.attachmentsName,
        creationDate: creationDate ?? this.creationDate,
        key: key ?? this.key,
        language: language ?? this.language,
        platformId: platformId ?? this.platformId,
        title: title ?? this.title,
        mapstrCtaButtonType: mapstrCtaButtonType ?? this.mapstrCtaButtonType,
        mapstrCtaText: mapstrCtaText ?? this.mapstrCtaText,
        category: category ?? this.category,
        socialCreatedAt: socialCreatedAt ?? this.socialCreatedAt,
        socialId: socialId ?? this.socialId,
        socialLink: socialLink ?? this.socialLink,
        socialUpdatedAt: socialUpdatedAt ?? this.socialUpdatedAt,
        errorData: errorData ?? this.errorData,
        errorStage: errorStage ?? this.errorStage,
        duplicatedFromRestaurantId:
            duplicatedFromRestaurantId ?? this.duplicatedFromRestaurantId,
        feedback: feedback ?? this.feedback,
        publicationDatePlanned:
            publicationDatePlanned ?? this.publicationDatePlanned,
        postTextController: postTextController ?? this.postTextController,
        thumbnail: thumbnail ?? this.thumbnail,
        feedbackId: feedbackId ?? this.feedbackId,
        thumbnailOffsetTimeInMs:
            thumbnailOffsetTimeInMs ?? this.thumbnailOffsetTimeInMs,
        keywordAnalysis: keywordAnalysis ?? this.keywordAnalysis,
        createdFromDeviceType:
            createdFromDeviceType ?? this.createdFromDeviceType,
      );

  factory Post.fromRawJson(String str) => Post.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Post.fromJson(Map<String, dynamic> json) => Post(
        id: json["_id"],
        keys: List<PlatformKeys>.from(
            json["keys"].map((x) => parsePlatformKey(x))),
        text: json["text"],
        hashtags: json["hashtags"] != null
            ? PostHashtags.fromJson(json["hashtags"])
            : PostHashtags(selected: [], suggested: []),
        published: publishedValues.map[json["published"]]!,
        tries: json["tries"],
        plannedPublicationDate: json["plannedPublicationDate"] == null
            ? null
            : DateTime.parse(json["plannedPublicationDate"]),
        author: json["author"] == null ? null : Author.fromJson(json["author"]),
        restaurantId: json["restaurantId"],
        attachments: List<Media>.from(
            (json["attachments"] ?? []).map((x) => Media.fromJson(x))),
        postTopic: postTopicValues.map[json["postTopic"]],
        postType: postTypeValues.map[json["postType"]],
        isReelDisplayedInFeed: json["isReelDisplayedInFeed"],
        bindingId: json["bindingId"],
        shouldDuplicateInOtherPlatforms:
            json["shouldDuplicateInOtherPlatforms"],
        userTagsList: json["userTagsList"] != null
            ? List<List<dynamic>>.from(json["userTagsList"]
                    ?.map((x) => List<dynamic>.from(x?.map((x) => x) ?? [])) ??
                [])
            : [],
        source: sourceValues.map[json["source"]]!,
        isStory: json["isStory"],
        createdAt: DateTime.parse(json["createdAt"]),
        socialAttachments: List<SocialAttachment>.from(
            json["socialAttachments"].map((x) => SocialAttachment.fromJson(x))),
        userTags: json["userTags"] == null
            ? []
            : List<AccountTag>.from(
                json["userTags"].map((x) => AccountTag.fromJson(x))),
        updatedAt: DateTime.parse(json["updatedAt"]),
        callToAction: json["callToAction"] == null
            ? null
            : CallToAction.fromJson(json["callToAction"]),
        event: json["event"] == null ? null : Event.fromJson(json["event"]),
        offer: json["offer"] == null ? null : Offer.fromJson(json["offer"]),
        location: json["location"] == null
            ? null
            : PostLocation.fromJson(json["location"]),
        attachmentsName: json["attachmentsName"],
        creationDate: json['creationDate'] == null
            ? null
            : DateTime.parse(json["creationDate"]),
        key: parsePlatformKey(json['key'] ?? ''),
        language: languageValues.map[json["language"]],
        platformId: json["platformId"],
        title: json["title"],
        mapstrCtaButtonType: json["mapstrCtaButtonType"],
        mapstrCtaText: json["mapstrCtaText"],
        category: sourceValues.map[json["category"]],
        socialCreatedAt: json["socialCreatedAt"] == null
            ? null
            : DateTime.parse(json["socialCreatedAt"]),
        socialId: json["socialId"],
        socialLink: json["socialLink"],
        socialUpdatedAt: json["socialUpdatedAt"] == null
            ? null
            : DateTime.parse(json["socialUpdatedAt"]),
        errorData: json["errorData"],
        errorStage: json["errorStage"],
        duplicatedFromRestaurantId: json["duplicatedFromRestaurantId"],
        feedback: json["feedback"] == null
            ? null
            : feedback_model.Feedback.fromJson(json["feedback"]),
        thumbnail: json["thumbnail"] == null
            ? null
            : Media.fromJson(json["thumbnail"]),
        feedbackId: json["feedbackId"],
        thumbnailOffsetTimeInMs:
            int.tryParse(json["thumbnailOffsetTimeInMs"].toString()),
        keywordAnalysis: json["keywordAnalysis"] == null
            ? null
            : KeywordAnalysis.fromJson(json["keywordAnalysis"]),
        createdFromDeviceType: json["createdFromDeviceType"] == null
            ? null
            : DeviceType.values.byName(json["createdFromDeviceType"]),
      );

  Map<String, dynamic> toJson() => {
        "keys": List<dynamic>.from(keys.map((x) => x.name)),
        "text": text,
        "hashtags": hashtags.toJson(),
        "published": publishedValues.reverse[published],
        "tries": tries,
        "plannedPublicationDate":
            plannedPublicationDate?.toUtc().toIso8601String(),
        "author": author?.toJson(),
        "restaurantId": restaurantId,
        "attachments": attachments.map((x) => x.id).toList(),
        "postTopic": postTopicValues.reverse[postTopic],
        "postType": postTypeValues.reverse[postType],
        "isReelDisplayedInFeed": isReelDisplayedInFeed,
        "bindingId": bindingId,
        "shouldDuplicateInOtherPlatforms": shouldDuplicateInOtherPlatforms,
        "userTagsList": List<dynamic>.from(
            userTagsList?.map((x) => List<dynamic>.from(x.map((x) => x))) ??
                []),
        "source": sourceValues.reverse[source],
        "isStory": isStory,
        "createdAt": createdAt.toUtc().toIso8601String(),
        "socialAttachments":
            List<dynamic>.from(socialAttachments.map((x) => x.toJson())),
        "userTags": List<dynamic>.from(userTags?.map((x) => x.toJson()) ?? []),
        "updatedAt": updatedAt.toIso8601String(),
        "callToAction": callToAction?.toJson(),
        "event": event?.toJson(),
        "offer": offer?.toJson(),
        "location": location?.toJson(),
        "attachmentsName": attachmentsName,
        "creationDate": creationDate?.toIso8601String(),
        "key": keyValues.reverse[key],
        "language": languageValues.reverse[language],
        "platformId": platformId,
        "title": title,
        "mapstrCtaButtonType": mapstrCtaButtonType,
        "mapstrCtaText": mapstrCtaText,
        "category": sourceValues.reverse[category],
        "socialCreatedAt": socialCreatedAt?.toIso8601String(),
        "socialId": socialId,
        "socialLink": socialLink,
        "socialUpdatedAt": socialUpdatedAt?.toIso8601String(),
        "errorData": errorData,
        "errorStage": errorStage,
        "duplicatedFromRestaurantId": duplicatedFromRestaurantId,
        "feedback": feedback?.toJson(),
        "thumbnail": thumbnail?.id,
        "feedbackId": feedbackId,
        "thumbnailOffsetTimeInMs": thumbnailOffsetTimeInMs,
        "keywordAnalysis": keywordAnalysis?.toJson(),
        "createdFromDeviceType": createdFromDeviceType?.name,
      };

  List<String?> getAttachments({MediaSize size = MediaSize.igFit}) {
    if (socialAttachments.isNotEmpty) {
      // gmb returns 403 for the pictures
      return socialAttachments
          .map((e) => e.getUrls())
          .where((element) => element != null)
          .toList();
    }
    return attachments.isNotEmpty
        ? attachments.map((e) => e.getMediaUrl(size: size.name)).toList()
        : [];
  }

  String? getThumbnail() {
    if (thumbnail != null) {
      return thumbnail?.getMediaUrl(size: MediaSize.igFit.name);
    }
    if (published == PublicationStatus.PUBLISHED) {
      return socialAttachments[0].getThumbnailUrl();
    }
    return attachments.isNotEmpty ? attachments[0].getThumbnailUrl() : null;
  }

  bool hasAttachments() {
    if (attachments.isNotEmpty &&
        attachments[0].urls != null &&
        attachments[0].urls?['original'] != null) {
      return true;
    }
    if (socialAttachments.isNotEmpty &&
        ((socialAttachments[0].urls != null &&
                socialAttachments[0].urls?.original != null) ||
            socialAttachments[0].thumbnailUrl != null)) {
      return true;
    }
    return false;
  }

  bool isVideo() {
    if (published == PublicationStatus.PUBLISHED &&
        socialAttachments.isNotEmpty) {
      return socialAttachments[0].type == Type.video.name;
    }
    return attachments.isNotEmpty && attachments[0].type == Type.video.name;
  }

  bool isReel() {
    return postType == PostType.REEL;
  }

  bool isCarousel() {
    return postType == PostType.CAROUSEL;
  }

  String getPostDate(AppLocale locale) {
    DateTime? date = (published == PublicationStatus.PUBLISHED
            ? socialCreatedAt
            : plannedPublicationDate)
        ?.toLocal();

    return DateFormat(
            locale.name == 'en' ? 'MM.dd - h:mm a' : 'dd.MM.yyyy - HH:mm ',
            locale.name)
        .format(date ?? DateTime.now());
  }

  bool shouldDisplayFeedback() {
    return (feedback != null &&
        feedback!.getFeedbacksCount() > 0 &&
        feedback?.isOpen == true);
  }

  // compare two posts
  bool isEqualTo(Post post) {
    return title == post.title &&
        category == post.category &&
        plannedPublicationDate == post.plannedPublicationDate &&
        location == post.location &&
        key == post.key &&
        mapstrCtaButtonType == post.mapstrCtaButtonType &&
        mapstrCtaText == post.mapstrCtaText &&
        hashtags == post.hashtags &&
        attachments.length == post.attachments.length &&
        text == post.text;
  }

  bool hasError() {
    return [PublicationStatus.ERROR, PublicationStatus.REJECTED]
        .contains(published);
  }

  bool hasFbCheckedAndVideoSelected() {
    return keys.contains(PlatformKeys.facebook) &&
        attachments.any((element) => element.type == MediaType.video.name);
  }

  bool hasFbCheckedAndImageSelected() {
    return keys.contains(PlatformKeys.facebook) &&
        attachments.any((element) => element.type == MediaType.photo.name);
  }

  bool isSeo() {
    return source == Source.seo;
  }

  bool isPublished() {
    return published == PublicationStatus.PUBLISHED;
  }

  static String getPostTopicText(BuildContext context, PostTopic topic) {
    switch (topic) {
      case PostTopic.STANDARD:
        return Translation.of(context).postTopicNew;
      case PostTopic.EVENT:
        return Translation.of(context).postTopicEvent;
      case PostTopic.OFFER:
        return Translation.of(context).postTopicOffer;
      default:
        return Translation.of(context).postTopicNew;
    }
  }

  void setSocialAttachments(List<SocialAttachment> socialAttachments) {
    this.socialAttachments = socialAttachments;
  }

  void setAttachments(List<Media> attachments) {
    this.attachments = attachments;
  }

  bool canBeDeleted() {
    if (published == PublicationStatus.PUBLISHED) {
      if (key == PlatformKeys.facebook) {
        return true;
      }
      if (key == PlatformKeys.gmb) {
        return true;
      }
      if (key == PlatformKeys.instagram) {
        return false;
      }
      if (key == PlatformKeys.mapstr) {
        return false;
      }
    }
    return true;
  }

  bool hasFeedbacks() {
    if (feedback != null) {
      return feedback!.getFeedbacksCount() > 0;
    }
    return false;
  }

  bool canBePosted({required bool arePostPlatformsConnected}) {
    switch (publicationDatePlanned) {
      case PublicationDatePlanned.scheduled:
        return plannedPublicationDate != null &&
            text?.isNotEmpty == true &&
            plannedPublicationDate!.isAfter(DateTime.now()) &&
            attachments.isNotEmpty &&
            (source == Source.seo && postTopic != PostTopic.STANDARD
                ? event?.title?.isNotEmpty == true
                : true) &&
            arePostPlatformsConnected;
      case PublicationDatePlanned.now:
        return text?.isNotEmpty == true &&
            attachments.isNotEmpty &&
            keys.isNotEmpty &&
            (source == Source.seo && postTopic != PostTopic.STANDARD
                ? event?.title?.isNotEmpty == true
                : true) &&
            arePostPlatformsConnected;
      default:
        return keys.isNotEmpty &&
            plannedPublicationDate != null &&
            plannedPublicationDate!.isAfter(DateTime.now());
    }
  }

  bool canBeEdited() {
    return published == PublicationStatus.DRAFT ||
        published == PublicationStatus.PENDING ||
        published == PublicationStatus.ERROR ||
        published == PublicationStatus.REJECTED ||
        (published == PublicationStatus.PUBLISHED && key == PlatformKeys.gmb);
  }

  String getPublishButtonText(BuildContext context) {
    switch (publicationDatePlanned) {
      case PublicationDatePlanned.scheduled:
        return Translation.of(context).schedule;
      case PublicationDatePlanned.now:
        return Translation.of(context).publish;
      default:
        return Translation.of(context).draft;
    }
  }

  bool isPostValid({required bool arePostPlatformsConnected}) {
    return canBeEdited() &&
        canBePosted(arePostPlatformsConnected: arePostPlatformsConnected);
  }

  String getTextWithoutHashtags() {
    String result = text?.replaceAll(RegExp(r'#(\S+)'), '') ?? '';
    return result.trim();
  }

  displayCallToActionName(BuildContext context) {
    switch (callToAction?.actionType) {
      case CallToActionType.BOOK:
        return Translation.of(context).book;
      case CallToActionType.CALL:
        return Translation.of(context).call;
      case CallToActionType.SIGN_UP:
        return Translation.of(context).signup;
      case CallToActionType.LEARN_MORE:
        return Translation.of(context).learnMore;
      case CallToActionType.ORDER:
        return Translation.of(context).order;
      case CallToActionType.BUY:
        return Translation.of(context).buy;
      case CallToActionType.JOIN:
        return Translation.of(context).participate;
      case CallToActionType.SEE:
        return Translation.of(context).see;
      case CallToActionType.MENU:
        return Translation.of(context).seeMenu;
      default:
        return '';
    }
  }

  canBeRescheduledInFeed() {
    return !isPublished() &&
        (plannedPublicationDate == null ||
            plannedPublicationDate!.isAfter(DateTime.now()));
  }
}

class Offer {
  String? couponCode;
  String? onlineUrl;
  String? termsConditions;

  Offer({
    this.couponCode,
    this.onlineUrl,
    this.termsConditions,
  });

  Offer copyWith({
    String? couponCode,
    String? onlineUrl,
    String? termsConditions,
  }) =>
      Offer(
        couponCode: couponCode ?? this.couponCode,
        onlineUrl: onlineUrl ?? this.onlineUrl,
        termsConditions: termsConditions ?? this.termsConditions,
      );

  factory Offer.fromRawJson(String str) => Offer.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Offer.fromJson(Map<String, dynamic> json) => Offer(
        couponCode: json["couponCode"],
        onlineUrl: json["onlineUrl"],
        termsConditions: json["termsConditions"],
      );

  Map<String, dynamic> toJson() => {
        "couponCode": couponCode,
        "onlineUrl": onlineUrl,
        "termsConditions": termsConditions,
      };
}

class Event {
  String? title;
  DateTime? startDate;
  DateTime? endDate;

  Event({
    this.title,
    this.startDate,
    this.endDate,
  });

  Event copyWith({
    String? title,
    DateTime? startDate,
    DateTime? endDate,
  }) =>
      Event(
        title: title ?? this.title,
        startDate: startDate ?? this.startDate,
        endDate: endDate ?? this.endDate,
      );

  factory Event.fromJson(Map<String, dynamic> json) => Event(
        title: json["title"],
        startDate: json["startDate"] == null
            ? null
            : DateTime.parse(json["startDate"]),
        endDate:
            json["endDate"] == null ? null : DateTime.parse(json["endDate"]),
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "startDate": startDate?.toIso8601String(),
        "endDate": endDate?.toIso8601String(),
      };
}

class AccountTag {
  final String username;
  final double x;
  final double y;

  AccountTag(this.username, this.x, this.y);

  AccountTag copyWith() => AccountTag(
        username,
        x,
        y,
      );

  factory AccountTag.fromRawJson(String str) =>
      AccountTag.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AccountTag.fromJson(Map<String, dynamic> json) => AccountTag(
        json["username"],
        json["x"].toDouble(),
        json["y"].toDouble(),
      );

  Map<String, dynamic> toJson() => {
        "username": username,
        "x": x,
        "y": y,
      };
}

enum Category { ADDITIONAL }

final categoryValues = EnumValues({"additional": Category.ADDITIONAL});

class EntityRelatedOn {
  String entityRelated;
  String entityId;

  EntityRelatedOn({
    required this.entityRelated,
    required this.entityId,
  });

  EntityRelatedOn copyWith({
    String? entityRelated,
    String? entityId,
  }) =>
      EntityRelatedOn(
        entityRelated: entityRelated ?? this.entityRelated,
        entityId: entityId ?? this.entityId,
      );

  factory EntityRelatedOn.fromRawJson(String str) =>
      EntityRelatedOn.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory EntityRelatedOn.fromJson(Map<String, dynamic> json) =>
      EntityRelatedOn(
        entityRelated: json["entityRelated"],
        entityId: json["entityId"],
      );

  Map<String, dynamic> toJson() => {
        "entityRelated": entityRelated,
        "entityId": entityId,
      };
}

enum Format { jpeg, jpg, mp4, png }

final formatValues = EnumValues({
  "jpeg": Format.jpeg,
  "jpg": Format.jpg,
  "mp4": Format.mp4,
  "png": Format.png
});

class Sizes {
  dynamic original;
  dynamic small;
  dynamic igFit;

  Sizes({
    required this.original,
    this.small,
    this.igFit,
  });

  Sizes copyWith({
    dynamic original,
    dynamic small,
    dynamic igFit,
  }) =>
      Sizes(
        original: original ?? this.original,
        small: small ?? this.small,
        igFit: igFit ?? this.igFit,
      );

  factory Sizes.fromRawJson(String str) => Sizes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Sizes.fromJson(Map<String, dynamic> json) => Sizes(
        original: json["original"],
        small: json["small"],
        igFit: json["igFit"],
      );

  Map<String, dynamic> toJson() => {
        "original": original,
        "small": small,
        "igFit": igFit,
      };
}

enum Type { photo, video }

final typeValues = EnumValues({"photo": Type.photo, "video": Type.video});

class Author {
  String? id;
  String? name;

  Author({
    required this.id,
    required this.name,
  });

  Author copyWith({
    String? id,
    String? name,
  }) =>
      Author(
        id: id ?? this.id,
        name: name ?? this.name,
      );

  factory Author.fromRawJson(String str) => Author.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Author.fromJson(Map<String, dynamic> json) => Author(
        id: json["_id"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "name": name,
      };
}

enum CallToActionType {
  BOOK,
  ORDER,
  SHOP,
  LEARN_MORE,
  SIGN_UP,
  CALL,
  NONE,
  // BUY is absolutely not mentionned in the gmb api doc but it works
  BUY,

  // only Mapstr
  SEE,
  MENU,
  JOIN,
  MORE,
}

class CallToAction {
  final CallToActionType? actionType;
  final String? url;
  CallToAction({required this.actionType, required this.url});

  CallToAction copyWith({
    CallToActionType? actionType,
    String? url,
  }) =>
      CallToAction(
        actionType: actionType ?? this.actionType,
        url: url ?? this.url,
      );

  factory CallToAction.fromJson(Map<String, dynamic> json) => CallToAction(
        actionType: json["actionType"] == null
            ? null
            : CallToActionType.values.byName(json["actionType"]),
        url: json["url"],
      );

  Map<String, dynamic> toJson() => {
        "actionType": actionType?.name,
        "url": url,
      };
}

enum Source { social, seo }

final sourceValues = EnumValues({"social": Source.social, "seo": Source.seo});

enum Key { facebook, instagram }

final keyValues =
    EnumValues({"facebook": Key.facebook, "instagram": Key.instagram});

enum Language { fr, en, es, it }

enum DeviceType {
  MOBILE,
  DESKTOP,
}

final languageValues = EnumValues({
  "fr": Language.fr,
  "en": Language.en,
  "es": Language.es,
  "it": Language.it
});

class PostLocation {
  String id;
  String name;
  String link;
  LocationAddress? location;

  PostLocation({
    required this.id,
    required this.name,
    required this.link,
    this.location,
  });

  PostLocation copyWith({
    String? id,
    String? name,
    String? link,
    LocationAddress? location,
  }) =>
      PostLocation(
        id: id ?? this.id,
        name: name ?? this.name,
        link: link ?? this.link,
        location: location ?? this.location,
      );

  factory PostLocation.fromRawJson(String str) =>
      PostLocation.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PostLocation.fromJson(Map<String, dynamic> json) => PostLocation(
        id: json["id"],
        name: json["name"],
        link: json["link"],
        location: json["location"] == null
            ? null
            : LocationAddress.fromJson(json["location"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "link": link,
        "location": location?.toJson(),
      };
}

class LocationAddress {
  String? city;
  String? country;
  double? latitude;
  double? longitude;
  String? street;
  String? zip;

  LocationAddress({
    this.city,
    this.country,
    this.latitude,
    this.longitude,
    this.street,
    this.zip,
  });

  LocationAddress copyWith({
    String? city,
    String? country,
    double? latitude,
    double? longitude,
    String? street,
    String? zip,
  }) =>
      LocationAddress(
        city: city ?? this.city,
        country: country ?? this.country,
        latitude: latitude ?? this.latitude,
        longitude: longitude ?? this.longitude,
        street: street ?? this.street,
        zip: zip ?? this.zip,
      );

  factory LocationAddress.fromRawJson(String str) =>
      LocationAddress.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LocationAddress.fromJson(Map<String, dynamic> json) =>
      LocationAddress(
        city: json["city"],
        country: json["country"],
        latitude: json["latitude"]?.toDouble(),
        longitude: json["longitude"]?.toDouble(),
        street: json["street"],
        zip: json["zip"],
      );

  Map<String, dynamic> toJson() => {
        "city": city,
        "country": country,
        "latitude": latitude,
        "longitude": longitude,
        "street": street,
        "zip": zip,
      };

  String getFormattedAddress() {
    return '$street, $city, $country'
        .replaceAll('null,', '')
        .replaceAll(',  null', '');
  }
}

enum PostTopic {
  STANDARD,
  LOCAL_POST_TOPIC_TYPE_UNSPECIFIED,
  EVENT,
  OFFER,
  ALERT,
}

final postTopicValues = EnumValues({
  "STANDARD": PostTopic.STANDARD,
  "EVENT": PostTopic.EVENT,
  "OFFER": PostTopic.OFFER,
  "ALERT": PostTopic.ALERT,
  "LOCAL_POST_TOPIC_TYPE_UNSPECIFIED":
      PostTopic.LOCAL_POST_TOPIC_TYPE_UNSPECIFIED
});

enum PostType { CAROUSEL, IMAGE, REEL, VIDEO }

final postTypeValues = EnumValues({
  "CAROUSEL": PostType.CAROUSEL,
  "IMAGE": PostType.IMAGE,
  "REEL": PostType.REEL,
  "VIDEO": PostType.VIDEO
});

enum PublicationStatus { DRAFT, PENDING, PUBLISHED, ERROR, REJECTED }

final publishedValues = EnumValues({
  "draft": PublicationStatus.DRAFT,
  "pending": PublicationStatus.PENDING,
  "published": PublicationStatus.PUBLISHED,
  "error": PublicationStatus.ERROR,
  "rejected": PublicationStatus.REJECTED
});

class SocialAttachment implements PostAttachment {
  SocialAttachmentUrls? urls;
  String? type;
  String? thumbnailUrl;
  dynamic socialId;

  SocialAttachment({
    this.urls,
    this.type,
    this.thumbnailUrl,
    this.socialId,
  });

  SocialAttachment copyWith({
    SocialAttachmentUrls? urls,
    String? type,
    String? thumbnailUrl,
    dynamic socialId,
  }) =>
      SocialAttachment(
        urls: urls ?? this.urls,
        type: type ?? this.type,
        thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
        socialId: socialId ?? this.socialId,
      );

  factory SocialAttachment.fromRawJson(String str) =>
      SocialAttachment.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SocialAttachment.fromJson(Map<String, dynamic> json) =>
      SocialAttachment(
        urls: json["urls"] == null
            ? null
            : SocialAttachmentUrls.fromJson(json["urls"]!),
        type: json["type"],
        thumbnailUrl: json["thumbnailUrl"],
        socialId: json["socialId"],
      );

  Map<String, dynamic> toJson() => {
        "urls": urls?.toJson(),
        "type": type,
        "thumbnailUrl": thumbnailUrl,
        "socialId": socialId,
      };

  @override
  String? getUrls() {
    return urls?.original;
  }

  @override
  String? getThumbnailUrl() {
    return thumbnailUrl;
  }

  String getFormat() {
    return urls?.original.split('.').last ?? '';
  }
}

class SocialAttachmentUrls {
  String original;

  SocialAttachmentUrls({
    required this.original,
  });

  SocialAttachmentUrls copyWith({
    String? original,
  }) =>
      SocialAttachmentUrls(
        original: original ?? this.original,
      );

  factory SocialAttachmentUrls.fromRawJson(String str) =>
      SocialAttachmentUrls.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SocialAttachmentUrls.fromJson(Map<String, dynamic> json) =>
      SocialAttachmentUrls(
        original: json["original"],
      );

  Map<String, dynamic> toJson() => {
        "original": original,
      };
}

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}

abstract class PostAttachment {
  String? getUrls();
  String? getThumbnailUrl();
}

class PaginatedPosts {
  Pagination pagination;
  List<Post> posts;

  PaginatedPosts({
    required this.pagination,
    required this.posts,
  });
}
