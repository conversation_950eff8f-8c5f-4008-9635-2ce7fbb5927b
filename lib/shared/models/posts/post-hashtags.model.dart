import 'package:malou/shared/models/hashtags/hashtag.model.dart';

class PostHashtags {
  List<Hashtag> selected;
  List<Hashtag> suggested;

  PostHashtags({
    required this.selected,
    required this.suggested,
  });

  factory PostHashtags.fromJson(Map<String, dynamic> json) => PostHashtags(
        selected: List<Hashtag>.from(
            json['selected'].map((x) => Hashtag.fromJson(x))),
        suggested: List<Hashtag>.from(
            json['suggested'].map((x) => Hashtag.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        'selected': List<dynamic>.from(selected.map((x) => x.toJson())),
        'suggested': List<dynamic>.from(suggested.map((x) => x.toJson())),
      };
  copyWith({
    List<Hashtag>? selected,
    List<Hashtag>? suggested,
  }) {
    return PostHashtags(
      selected: selected ?? this.selected,
      suggested: suggested ?? this.suggested,
    );
  }
}
