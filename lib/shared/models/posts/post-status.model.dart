import 'package:malou/shared/models/posts/post.model.dart';

enum StepStatus { pending, success, error }

class PostingStatus {
  String postId;
  String? platformKey;
  Step step;

  PostingStatus({
    required this.postId,
    required this.platformKey,
    required this.step,
  });

  factory PostingStatus.fromPost(Post post) {
    return PostingStatus(
      postId: post.id!,
      platformKey: post.key?.name,
      step: Step(
          status: post.published == PublicationStatus.PENDING
              ? StepStatus.pending
              : post.published == PublicationStatus.PUBLISHED
                  ? StepStatus.success
                  : StepStatus.error,
          errorMessage: post.published == PublicationStatus.ERROR
              ? post.errorData
              : null),
    );
  }
}

class Step {
  StepStatus status;
  String? errorMessage;

  Step({
    required this.status,
    this.errorMessage,
  });
}
