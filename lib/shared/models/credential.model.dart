import 'dart:convert';

class Credential {
  String id;
  String? authId;
  String? accessToken;
  DateTime? createdAt;
  dynamic expiresIn;
  String key;
  DateTime? lastSeenWorking;
  List<String?> organizationIds;
  String? password;
  String? tokenType;
  DateTime? updatedAt;
  String? userId;
  bool? active;
  String? refreshToken;
  String? scope;
  String? organizationId;
  String? name;
  List<PageAccessToken>? pageAccessTokens;
  String? userAccessToken;
  List<IgPageAccessToken>? igPageAccessTokens;
  List<PageAccess>? pageAccess;

  Credential({
    required this.id,
     this.authId,
    this.accessToken,
    this.createdAt,
    this.expiresIn,
    required this.key,
    this.lastSeenWorking,
    required this.organizationIds,
    this.password,
    this.tokenType,
    required this.updatedAt,
    this.userId,
    required this.active,
    this.refreshToken,
    this.scope,
    this.organizationId,
    this.name,
    this.pageAccessTokens,
    this.userAccessToken,
    this.igPageAccessTokens,
    this.pageAccess,
  });

  factory Credential.fromRawJson(String str) =>
      Credential.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Credential.fromJson(Map<String, dynamic> json) => Credential(
        id: json["_id"],
        authId: json["authId"],
        accessToken: json["accessToken"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        expiresIn: json["expiresIn"],
        key: json["key"],
        lastSeenWorking: json["lastSeenWorking"] == null
            ? null
            : DateTime.parse(json["lastSeenWorking"]),
        organizationIds: json["organizationIds"] == null
            ? []
            : List<String?>.from(json["organizationIds"].map((x) => x)),
        password: json["password"],
        tokenType: json["tokenType"],
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        userId: json["userId"],
        active: json["active"],
        refreshToken: json["refreshToken"],
        scope: json["scope"],
        organizationId: json["organizationId"],
        name: json["name"],
        pageAccessTokens: json["pageAccessTokens"] == null
            ? []
            : List<PageAccessToken>.from(json["pageAccessTokens"]!
                .map((x) => PageAccessToken.fromJson(x))),
        userAccessToken: json["userAccessToken"],
        igPageAccessTokens: json["igPageAccessTokens"] == null
            ? []
            : List<IgPageAccessToken>.from(json["igPageAccessTokens"]!
                .map((x) => IgPageAccessToken.fromJson(x))),
        pageAccess: json["pageAccess"] == null
            ? []
            : List<PageAccess>.from(
                json["pageAccess"]!.map((x) => PageAccess.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "authId": authId,
        "accessToken": accessToken,
        "createdAt": createdAt?.toIso8601String(),
        "expiresIn": expiresIn,
        "key": key,
        "lastSeenWorking": lastSeenWorking?.toIso8601String(),
        "organizationIds": List<dynamic>.from(organizationIds.map((x) => x)),
        "password": password,
        "tokenType": tokenType,
        "updatedAt": updatedAt?.toIso8601String(),
        "userId": userId,
        "active": active,
        "refreshToken": refreshToken,
        "scope": scope,
        "organizationId": organizationId,
        "name": name,
        "pageAccessTokens": pageAccessTokens == null
            ? []
            : List<dynamic>.from(pageAccessTokens!.map((x) => x.toJson())),
        "userAccessToken": userAccessToken,
        "igPageAccessTokens": igPageAccessTokens == null
            ? []
            : List<dynamic>.from(igPageAccessTokens!.map((x) => x.toJson())),
        "pageAccess": pageAccess == null
            ? []
            : List<dynamic>.from(pageAccess!.map((x) => x.toJson())),
      };
}

class IgPageAccessToken {
  String pageId;
  String igPageId;
  String pageAccessToken;

  IgPageAccessToken({
    required this.pageId,
    required this.igPageId,
    required this.pageAccessToken,
  });

  factory IgPageAccessToken.fromRawJson(String str) =>
      IgPageAccessToken.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory IgPageAccessToken.fromJson(Map<String, dynamic> json) =>
      IgPageAccessToken(
        pageId: json["pageId"],
        igPageId: json["igPageId"],
        pageAccessToken: json["pageAccessToken"],
      );

  Map<String, dynamic> toJson() => {
        "pageId": pageId,
        "igPageId": igPageId,
        "pageAccessToken": pageAccessToken,
      };
}

class PageAccess {
  String fbPageId;
  String? igPageId;
  String? pageAccessToken;
  DateTime? lastSeenWorking;

  PageAccess({
    required this.fbPageId,
    this.igPageId,
    required this.pageAccessToken,
    this.lastSeenWorking,
  });

  factory PageAccess.fromRawJson(String str) =>
      PageAccess.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PageAccess.fromJson(Map<String, dynamic> json) => PageAccess(
        fbPageId: json["fbPageId"],
        igPageId: json["igPageId"],
        pageAccessToken: json["pageAccessToken"],
        lastSeenWorking: json["lastSeenWorking"] == null
            ? null
            : DateTime.parse(json["lastSeenWorking"]),
      );

  Map<String, dynamic> toJson() => {
        "fbPageId": fbPageId,
        "igPageId": igPageId,
        "pageAccessToken": pageAccessToken,
        "lastSeenWorking": lastSeenWorking?.toIso8601String(),
      };
}

class PageAccessToken {
  String pageId;
  String pageAccessToken;

  PageAccessToken({
    required this.pageId,
    required this.pageAccessToken,
  });

  factory PageAccessToken.fromRawJson(String str) =>
      PageAccessToken.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PageAccessToken.fromJson(Map<String, dynamic> json) =>
      PageAccessToken(
        pageId: json["pageId"],
        pageAccessToken: json["pageAccessToken"],
      );

  Map<String, dynamic> toJson() => {
        "pageId": pageId,
        "pageAccessToken": pageAccessToken,
      };
}
