import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/bottom_navigation_item.dart';
import 'package:malou/shared/helpers/constant.dart';
import 'package:malou/shared/models/brick.model.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/models/platform.model.dart';

class Restaurant {
  String id;
  Address? address;
  String name;
  String updatedAt;
  RestaurantPicture? cover;
  RestaurantPicture? logo;
  String? type;
  CurrentState? currentState;
  List<Brick>? bricks;
  String? website;
  String? menuUrl;
  List<TimePeriod> regularHours;
  Phone? phone;
  String? organizationId;
  List<String?> relatedUrls = [];
  Category? category;
  Ai ai;

  Restaurant({
    required this.id,
    this.address,
    required this.name,
    required this.updatedAt,
    this.cover,
    this.logo,
    this.type,
    this.category,
    required this.currentState,
    required this.bricks,
    this.website,
    this.menuUrl,
    this.regularHours = const [],
    this.phone,
    required this.organizationId,
    this.relatedUrls = const [],
    required this.ai,
  });

  factory Restaurant.fromJson(Map<String, dynamic> json) => Restaurant(
        id: json['_id'],
        category: json['category'] != null
            ? Category.fromJson(json['category'])
            : null,
        address:
            json['address'] != null ? Address.fromJson(json['address']) : null,
        name: json['name'],
        updatedAt: json['updatedAt'],
        cover: json['cover'] != null
            ? RestaurantPicture.fromJson(json['cover'])
            : null,
        logo: json['logo'] != null
            ? RestaurantPicture.fromJson(json['logo'])
            : null,
        type: json['type'],
        currentState: json["currentState"] != null
            ? CurrentState.fromJson(json["currentState"])
            : null,
        website: json['website'],
        menuUrl: json['menuUrl'],
        regularHours: json['regularHours'] != null
            ? List<TimePeriod>.from(
                json["regularHours"].map((x) => TimePeriod.fromJson(x)))
            : [],
        phone: json['phone'] != null ? Phone.fromJson(json['phone']) : null,
        bricks: json['bricks'] != null
            ? List<Brick>.from(json["bricks"].map((x) => Brick.fromJson(x)))
            : null,
        organizationId: json['organizationId'],
        relatedUrls: json['relatedUrls'] != null
            ? List<String?>.from(json["relatedUrls"].map((x) => x))
            : [],
        ai: json['ai'] != null
            ? Ai.fromJson(json['ai'])
            : Ai(monthlyCallCount: 0, callCount: 0),
      );

  Map<String, dynamic> toJson() => {
        '_id': id,
        'address': address?.toJson(),
        'name': name,
        'updatedAt': updatedAt,
        'cover': cover?.toJson(),
        'logo': logo?.toJson(),
        'type': type,
        'currentState': currentState?.toJson(),
        'bricks': List<dynamic>.from(bricks?.map((x) => x.toJson()) ?? []),
        'regularHours': List<dynamic>.from(regularHours.map((x) => x.toJson())),
        'website': website,
        'phone': phone,
        'organizationId': organizationId,
        'relatedUrls': relatedUrls,
      };

  static List<Restaurant> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => Restaurant.fromJson(json)).toList();
  }

  bool isBrandBusiness() {
    return type != 'local_business';
  }

  List<BottomNavigationItem> getBottomNavigationItemsAvailable() {
    if (isBrandBusiness()) {
      // order is important
      return [
        BottomNavigationItem.home,
        BottomNavigationItem.conversations,
        BottomNavigationItem.posts
      ];
    }
    // order is important
    return [
      BottomNavigationItem.home,
      BottomNavigationItem.reviews,
      BottomNavigationItem.conversations,
      BottomNavigationItem.posts
    ];
  }

  String getFullFormattedAddress({String prefix = '', BuildContext? context}) {
    if (address == null) return '';
    if (isBrandBusiness() && context != null) {
      return '$prefix${Translation.of(context).brandAccount}';
    }
    return '$prefix${address?.formattedAddress}, ${address?.postalCode}, ${address?.locality}, ${address?.country}';
  }

  String getRestaurantNameAsBrandHashtag() {
    return '#${name.replaceAll(' ', '').replaceAll('-', '').toLowerCase()}';
  }

  String getRestaurantNameWithFormattedAddress() {
    return '$name - ${getFullFormattedAddress()}';
  }

  Restaurant copyWith({
    String? id,
    Address? address,
    String? name,
    String? updatedAt,
    RestaurantPicture? cover,
    RestaurantPicture? logo,
    String? type,
    CurrentState? currentState,
  }) {
    return Restaurant(
        id: id ?? this.id,
        address: address ?? this.address,
        name: name ?? this.name,
        updatedAt: updatedAt ?? this.updatedAt,
        cover: cover ?? this.cover,
        logo: logo ?? this.logo,
        type: type ?? this.type,
        currentState: currentState ?? this.currentState,
        bricks: bricks,
        organizationId: organizationId,
        ai: ai);
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'address': address?.toJson(),
      'name': name,
      'updatedAt': updatedAt,
      'cover': cover?.toJson(),
      'logo': logo?.toJson(),
      'type': type,
    };
  }
}

class Address {
  String? locality;
  String? regionCode;
  String? country;
  String? postalCode;
  String? formattedAddress;
  String? route;

  Address({
    this.formattedAddress,
    this.regionCode,
    this.postalCode,
    this.locality,
    this.route,
    this.country,
  });

  factory Address.fromJson(Map<String, dynamic> json) => Address(
        locality: json['locality'],
        regionCode: json['regionCode'],
        country: json['country'],
        postalCode: json['postalCode'],
        formattedAddress: json['formattedAddress'],
      );

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['locality'] = locality;
    data['regionCode'] = regionCode;
    data['country'] = country;
    data['postalCode'] = postalCode;
    data['formattedAddress'] = formattedAddress;
    return data;
  }
}

// ---------------------------------------------------------
class RestaurantPicture {
  String? sId;
  Map<String, dynamic>? urls;

  RestaurantPicture({this.sId, this.urls});

  RestaurantPicture.fromJson(Map<String, dynamic> json) {
    sId = json['_id'];
    urls = json['urls'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['_id'] = sId;
    data['urls'] = urls;

    return data;
  }

  String getUrlForSize({MediaSize preferedSize = MediaSize.original}) {
    return urls?[preferedSize] ?? urls?['original'];
  }
}

class CurrentState {
  Reviews? reviews;

  CurrentState({
    required this.reviews,
  });

  factory CurrentState.fromJson(Map<String, dynamic> json) => CurrentState(
        reviews:
            json["reviews"] == null || (json["reviews"] as Map).entries.isEmpty
                ? null
                : Reviews.fromJson(json["reviews"]),
      );

  Map<String, dynamic> toJson() => {
        "reviews": reviews?.toJson(),
      };

  bool done() {
    return reviews!.fetched.values.every((element) =>
        element.status == FetchedStatus.success ||
        element.status == FetchedStatus.error);
  }

  List<String> getPlatformsErrosNames() {
    return reviews?.fetched.entries
            .where((element) => element.value.status == FetchedStatus.error)
            .map((e) {
          PlatformDetails platformDetails = AppConstant.getDetails(e.key);
          return platformDetails.name;
        }).toList() ??
        [];
  }
}

class Reviews {
  Map<PlatformKeys, FetchedState> fetched;

  Reviews({
    required this.fetched,
  });

  factory Reviews.fromJson(Map<String, dynamic> json) => Reviews(
        fetched: Map.from(json["fetched"]).map((k, v) =>
            MapEntry<PlatformKeys, FetchedState>(
                PlatformKeys.values.byName(k), FetchedState.fromJson(v))),
      );

  Map<String, dynamic> toJson() => {
        "fetched": Map.from(fetched).map(
            (k, v) => MapEntry<String, FetchedState>(k.toString(), v.toJson())),
      };
}

enum FetchedStatus { success, error, pending, async }

class FetchedState {
  FetchedStatus status;
  dynamic lastTried;
  dynamic error;

  FetchedState({
    required this.status,
    this.lastTried,
    this.error,
  });

  factory FetchedState.fromJson(Map<String, dynamic> json) => FetchedState(
        status: FetchedStatus.values.byName(json["status"]),
        lastTried: json["lastTried"],
        error: json["error"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "lastTried": lastTried,
        "error": error,
      };
}

class Phone {
  int? prefix;
  int? digits;

  Phone({
    this.prefix,
    this.digits,
  });

  factory Phone.fromJson(Map<String, dynamic> json) => Phone(
        prefix: json['prefix'],
        digits: json['digits'],
      );

  Map<String, dynamic> toJson() => {
        "prefix": prefix,
        "digits": digits,
      };

  @override
  String toString() {
    return '+$prefix$digits';
  }
}

class Period {
  String? openTime;
  String? closeTime;
  bool isClosed = false;

  Period({
    this.openTime,
    this.closeTime,
    this.isClosed = false,
  });

  bool shouldBeClosed() {
    return openTime == '00:00' && closeTime == '00:00' && !isClosed;
  }

  void close() {
    isClosed = true;
    openTime = null;
    closeTime = null;
  }
}

class TimePeriod extends Period {
  String openDay;
  String closeDay;

  TimePeriod({
    this.openDay = '',
    this.closeDay = '',
    String? openTime,
    String? closeTime,
    bool isClosed = false,
  }) : super(openTime: openTime, closeTime: closeTime, isClosed: isClosed);

  factory TimePeriod.fromJson(Map<String, dynamic> json) => TimePeriod(
        openDay: json['openDay'],
        closeDay: json['closeDay'],
        openTime: json['openTime'],
        closeTime: json['closeTime'],
        isClosed: json['isClosed'],
      );

  toJson() => {
        'openDay': openDay,
        'closeDay': closeDay,
        'openTime': openTime,
        'closeTime': closeTime,
        'isClosed': isClosed,
      };

  @override
  String toString() {
    String formattedOpenDay =
        openDay[0].toUpperCase() + openDay.substring(1).toLowerCase();
    return '$formattedOpenDay : (${isClosed ? 'closed' : 'open : $openTime - $closeTime'})';
  }

  bool isFullDay() {
    return openTime == '24-24';
  }

  void cleanFullDay() {
    openTime = '00:00';
    closeTime = '24:00';
  }
}

class SpecialTimePeriod extends Period {
  MyDate startDate;
  MyDate endDate;

  SpecialTimePeriod({
    String? openTime,
    String? closeTime,
    bool isClosed = false,
    MyDate? startDate,
    MyDate? endDate,
  })  : startDate = startDate ?? MyDate(),
        endDate = endDate ?? MyDate(),
        super(openTime: openTime, closeTime: closeTime, isClosed: isClosed);

  void setStartDate(DateTime d) {
    startDate = MyDate()..setDate(d);
  }

  @override
  String toString() {
    return '$startDate : $openTime - $endDate: $closeTime (${isClosed ? 'closed' : 'open'})';
  }
}

class HoursType {
  String hoursType;
  Map<String, String> hoursTypeName;
  String platformKey;

  HoursType({
    this.hoursType = '',
    this.hoursTypeName = const {},
    this.platformKey = '',
  });

  factory HoursType.fromJson(Map<String, dynamic> json) {
    return HoursType(
      hoursType: json['hoursType'],
      hoursTypeName: json['hoursTypeName'] != null
          ? Map<String, String>.from(json['hoursTypeName'])
          : {},
      platformKey: json['platformKey'],
    );
  }

  String getLocaleName(String lang) {
    return hoursTypeName[lang] ?? hoursTypeName['default'] ?? '';
  }
}

abstract class IOtherPeriod {
  HoursType hoursType;
  List<TimePeriod> periods;

  IOtherPeriod({
    required this.hoursType,
    this.periods = const [],
  });
}

class OtherPeriod implements IOtherPeriod {
  @override
  HoursType hoursType;
  @override
  List<TimePeriod> periods;

  OtherPeriod({
    HoursType? hoursType,
    List<TimePeriod>? periods,
  })  : hoursType = hoursType ?? HoursType(),
        periods = periods ?? [];

  factory OtherPeriod.fromJson(Map<String, dynamic> json) {
    return OtherPeriod(
      hoursType: HoursType.fromJson(json['hoursType']),
      periods: (json['periods']).map((u) => TimePeriod.fromJson(u)).toList(),
    );
  }
}

class MyDate {
  int day = DateTime.now().day;
  int month = DateTime.now().month;
  int year = DateTime.now().year;

  MyDate({this.day = 0, this.month = 0, this.year = 0});

  DateTime getDate() {
    return DateTime(year, month, day);
  }

  void setDate(DateTime date) {
    day = date.day;
    month = date.month;
    year = date.year;
  }

  @override
  String toString() {
    return getDate().toLocal().toString();
  }

  bool equals(dynamic o) {
    return toString() == o.toString();
  }
}

class CustomTimePeriod {
  String? openDay;
  String? openTime;
  String? closeDay;
  String? closeTime;
  bool? isClosed;
  String? openTime2;
  String? closeTime2;

  CustomTimePeriod({
    this.openDay,
    this.openTime,
    this.closeDay,
    this.closeTime,
    this.isClosed,
    this.openTime2,
    this.closeTime2,
  });

  factory CustomTimePeriod.fromTimePeriod(TimePeriod timePeriod) {
    return CustomTimePeriod(
        openDay: timePeriod.openDay,
        openTime: timePeriod.openTime,
        closeDay: timePeriod.closeDay,
        closeTime: timePeriod.closeTime,
        isClosed: timePeriod.isClosed);
  }
}

class Ai {
  int monthlyCallCount;
  int? callCount;

  Ai({
    required this.monthlyCallCount,
    required this.callCount,
  });

  factory Ai.fromJson(Map<String, dynamic> json) => Ai(
        monthlyCallCount: json["monthlyCallCount"],
        callCount: json["callCount"],
      );

  Map<String, dynamic> toJson() => {
        "monthlyCallCount": monthlyCallCount,
        "callCount": callCount,
      };
}

class Category {
  String id;
  String platformKey;
  Map<String, String?> categoryName;
  bool? isFood;
  String categoryId;
  String? subcategoryName;
  String? subcategoryId;

  Category({
    required this.id,
    required this.platformKey,
    required this.categoryName,
    this.isFood,
    required this.categoryId,
    this.subcategoryName,
    this.subcategoryId,
  });

  factory Category.fromJson(Map<String, dynamic> json) => Category(
      id: json['_id'],
      platformKey: json['platformKey'],
      categoryName: json['categoryName'] != null
          ? Map<String, String?>.from(json['categoryName'])
          : {},
      isFood: json['isFood'],
      categoryId: json['categoryId'],
      subcategoryName: json['subcategoryName'],
      subcategoryId: json['subcategoryId']);

  Map<String, dynamic> toJson() => {
        '_id': id,
        'platformKey': platformKey,
        'categoryName': categoryName,
        'isFood': isFood,
        'categoryId': categoryId,
        'subcategoryName': subcategoryName,
        'subcategoryId': subcategoryId,
      };

  String getCategoryNameForLang(String lang) {
    return categoryName[lang] ?? categoryName['backup'] ?? '';
  }
}
