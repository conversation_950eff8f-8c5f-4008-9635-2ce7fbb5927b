import 'dart:async';
import 'dart:math';
// ignore: depend_on_referenced_packages
import 'package:http/http.dart' as http;
import 'package:html/parser.dart';

void Function<T>(T?) debounceTime(
  void Function(dynamic) callback, {
  Duration duration = const Duration(milliseconds: 400),
}) {
  Timer? timer;

  void cancelTimer() {
    if (timer?.isActive ?? false) {
      timer!.cancel();
    }
  }

  void handler<T>(T? value) {
    cancelTimer();
    timer = Timer(duration, () => callback(value));
  }

  return handler;
}

String capitalize(String? s) {
  if (s != null && s != '') {
    return s[0].toUpperCase() + s.substring(1);
  }
  return '';
}

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }

  Map<String, T> get values => map;

  T? fromString(String? value) {
    if (value == null) return null;
    return map[value];
  }
}

String formatDuration(Duration duration) {
  String twoDigits(int n) => n.toString().padLeft(2, "0");
  String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
  String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
  return "$twoDigitMinutes:$twoDigitSeconds";
}

Future<String> getMediaTypeFromUrl(String url) async {
  final response = await http.head(Uri.parse(url));
  final contentType = response.headers['content-type'];

  if (response.statusCode != 200) {
    throw Exception('Error getting media type from url');
  }

  if (contentType != null) {
    return contentType.split(';').first.trim();
  }

  return 'image';
}

String getMediaTypeFromExtension(String extension) {
  switch (extension) {
    case 'mp4':
    case 'webm':
    case 'mov':
    case 'avi':
    case 'wmv':
    case 'flv':
      return 'video';
    case 'mp3':
      return 'audio';
    default:
      return 'image';
  }
}

String parseHtmlString(String htmlString) {
  final document = parse(htmlString);
  final String parsedString =
      parse(document.body?.text).documentElement?.text ?? "";

  return parsedString;
}

List<T> take<T>(Iterable<T> iterable, int n) {
  return iterable.take(n).toList();
}

List<T> shuffle<T>(List<T> list) {
  var random = Random();
  for (var i = list.length - 1; i > 0; i--) {
    var j = random.nextInt(i + 1);
    var temp = list[i];
    list[i] = list[j];
    list[j] = temp;
  }
  return list;
}

Map<String, dynamic> removeNullFieldsFromJson(Map<String, dynamic> json) {
  json.removeWhere((key, value) => value == null || value == '');
  return json;
}

extension IterableExtension<T> on Iterable<T> {
  Iterable<T> distinctBy(dynamic Function(T e) getCompareValue) {
    var result = <T>[];
    for (var element in this) {
      if (!result.any((x) => getCompareValue(x) == getCompareValue(element))) {
        result.add(element);
      }
    }

    return result;
  }
}

String normalizeString(String string) {
  return string.toLowerCase().trim();
}
