Map<String, dynamic> objectToSnakeCase(Map<String, dynamic> obj) {
    final newMap = <String, dynamic>{};
    obj.forEach((key, value) {
      final snakeCaseKey = _camelToCase(key);
      newMap[snakeCaseKey] = value;
    });
    return newMap;
  }

  String _camelToCase(String input) {
    final regExp = RegExp(r'(?<=[a-z])[A-Z]');
    return input
        .replaceAllMapped(regExp, (match) => '_${match.group(0)}')
        .toLowerCase();
  }