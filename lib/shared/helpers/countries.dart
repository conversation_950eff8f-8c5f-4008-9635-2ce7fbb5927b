enum CountryCode {
  AF,
  AL,
  DZ,
  AS,
  AD,
  AO,
  AI,
  AQ,
  AG,
  AR,
  AM,
  AW,
  AU,
  AT,
  AZ,
  BS,
  BH,
  BD,
  BB,
  BY,
  BE,
  BZ,
  BJ,
  BM,
  BT,
  BO,
  BA,
  BW,
  BR,
  IO,
  BN,
  BG,
  BF,
  BI,
  KH,
  CM,
  CA,
  CV,
  KY,
  CF,
  TD,
  CL,
  CN,
  CX,
  CC,
  CO,
  KM,
  CG,
  CK,
  CR,
  CI,
  HR,
  CU,
  CY,
  CZ,
  CD,
  DK,
  DJ,
  DM,
  DO,
  EC,
  EG,
  SV,
  GQ,
  ER,
  EE,
  ET,
  FK,
  FO,
  FJ,
  FI,
  FR,
  GF,
  PF,
  GA,
  GM,
  GE,
  DE,
  GH,
  GI,
  GR,
  GL,
  GD,
  GP,
  GU,
  GT,
  GG,
  GN,
  GW,
  GY,
  HT,
  HN,
  HK,
  HU,
  IS,
  IN,
  ID,
  IR,
  IQ,
  IE,
  IM,
  IL,
  IT,
  JM,
  JP,
  JE,
  JO,
  KZ,
  KE,
  KI,
  KW,
  KG,
  AX,
  LA,
  LV,
  LB,
  LS,
  LR,
  LY,
  LI,
  LT,
  LU,
  MO,
  MK,
  MG,
  MW,
  MY,
  MV,
  ML,
  MT,
  MH,
  MQ,
  MR,
  MU,
  YT,
  MX,
  FM,
  MD,
  MC,
  MN,
  ME,
  MS,
  MA,
  MZ,
  MM,
  NA,
  NR,
  NP,
  NL,
  AN,
  NC,
  NZ,
  NI,
  NE,
  NG,
  NU,
  NF,
  KP,
  MP,
  NO,
  OM,
  PK,
  PW,
  PS,
  PA,
  PG,
  PY,
  PE,
  PH,
  PN,
  PL,
  PT,
  PR,
  QA,
  RE,
  RO,
  RU,
  RW,
  BL,
  SH,
  KN,
  LC,
  MF,
  PM,
  VC,
  WS,
  SM,
  ST,
  SA,
  SN,
  RS,
  SC,
  SL,
  SG,
  SK,
  SI,
  SB,
  SO,
  ZA,
  GS,
  KR,
  ES,
  LK,
  SD,
  SR,
  SJ,
  SZ,
  SE,
  CH,
  SY,
  TW,
  TJ,
  TZ,
  TH,
  TL,
  TG,
  TK,
  TO,
  TT,
  TN,
  TR,
  TM,
  TC,
  TV,
  UG,
  UA,
  AE,
  GB,
  US,
  UY,
  UZ,
  VU,
  VA,
  VE,
  VN,
  VG,
  VI,
  WF,
  YE,
  ZM,
  ZW,
}


class Country {
  final String name;
  final String dialCode;
  final CountryCode code;
  final String nameFr;

  Country({
    required this.name,
    required this.dialCode,
    required this.code,
    required this.nameFr,
  });
}

List<Country> COUNTRIES = [
  Country(
    name: 'Afghanistan',
    dialCode: '+93',
    code: CountryCode.AF,
    nameFr: 'Afghanistan',
  ),
  Country(
    name: 'Albania',
    dialCode: '+355',
    code: CountryCode.AL,
    nameFr: 'Albanie',
  ),
  Country(
    name: 'Algeria',
    dialCode: '+213',
    code: CountryCode.DZ,
    nameFr: 'Algérie',
  ),
  Country(
    name: 'American Samoa',
    dialCode: '+1 684',
    code: CountryCode.AS,
    nameFr: 'Samoa américaines',
  ),
  Country(
    name: 'Andorra',
    dialCode: '+376',
    code: CountryCode.AD,
    nameFr: 'Andorre',
  ),
  Country(
    name: 'Angola',
    dialCode: '+244',
    code: CountryCode.AO,
    nameFr: 'Angola',
  ),
  Country(
    name: 'Anguilla',
    dialCode: '+1 264',
    code: CountryCode.AI,
    nameFr: 'Anguilla',
  ),
  Country(
    name: 'Antigua and Barbuda',
    dialCode: '+1268',
    code: CountryCode.AG,
    nameFr: 'Antigue-et-Barbude',
  ),
  Country(
    name: 'Argentina',
    dialCode: '+54',
    code: CountryCode.AR,
    nameFr: 'Argentine',
  ),
  Country(
    name: 'Armenia',
    dialCode: '+374',
    code: CountryCode.AM,
    nameFr: 'Arménie',
  ),
  Country(
    name: 'Aruba',
    dialCode: '+297',
    code: CountryCode.AW,
    nameFr: 'Aruba',
  ),
  Country(
    name: 'Australia',
    dialCode: '+61',
    code: CountryCode.AU,
    nameFr: 'Australie',
  ),
  Country(
    name: 'Austria',
    dialCode: '+43',
    code: CountryCode.AT,
    nameFr: 'Autriche',
  ),
  Country(
    name: 'Azerbaijan',
    dialCode: '+994',
    code: CountryCode.AZ,
    nameFr: 'Azerbaïdjan',
  ),
  Country(
    name: 'Bahamas',
    dialCode: '+1 242',
    code: CountryCode.BS,
    nameFr: 'Bahamas',
  ),
  Country(
    name: 'Bahrain',
    dialCode: '+973',
    code: CountryCode.BH,
    nameFr: 'Bahreïn',
  ),
  Country(
    name: 'Bangladesh',
    dialCode: '+880',
    code: CountryCode.BD,
    nameFr: 'Bangladesh',
  ),
  Country(
    name: 'Barbados',
    dialCode: '+1 246',
    code: CountryCode.BB,
    nameFr: 'Barbade',
  ),
  Country(
    name: 'Belarus',
    dialCode: '+375',
    code: CountryCode.BY,
    nameFr: 'Biélorussie',
  ),
  Country(
    name: 'Belgium',
    dialCode: '+32',
    code: CountryCode.BE,
    nameFr: 'Belgique',
  ),
  Country(
    name: 'Belize',
    dialCode: '+501',
    code: CountryCode.BZ,
    nameFr: 'Belize',
  ),
  Country(
    name: 'Benin',
    dialCode: '+229',
    code: CountryCode.BJ,
    nameFr: 'Benin',
  ),
  Country(
    name: 'Bermuda',
    dialCode: '+1 441',
    code: CountryCode.BM,
    nameFr: 'Bermudes',
  ),
  Country(
    name: 'Bhutan',
    dialCode: '+975',
    code: CountryCode.BT,
    nameFr: 'Bhoutan',
  ),
  Country(
    name: 'Bolivia',
    dialCode: '+591',
    code: CountryCode.BO,
    nameFr: 'Bolivie',
  ),
  Country(
    name: 'Bosnia and Herzegovina',
    dialCode: '+387',
    code: CountryCode.BA,
    nameFr: 'Bosnie-Herzégovine',
  ),
  Country(
    name: 'Botswana',
    dialCode: '+267',
    code: CountryCode.BW,
    nameFr: 'Botswana',
  ),
  Country(
    name: 'Brazil',
    dialCode: '+55',
    code: CountryCode.BR,
    nameFr: 'Brésil',
  ),
  Country(
    name: 'British Indian Ocean Territory',
    dialCode: '+246',
    code: CountryCode.IO,
    nameFr: "Territoire britannique de l'océan Indien",
  ),
  Country(
    name: 'Brunei Darussalam',
    dialCode: '+673',
    code: CountryCode.BN,
    nameFr: 'Brunéi Darussalam',
  ),
  Country(
    name: 'Bulgaria',
    dialCode: '+359',
    code: CountryCode.BG,
    nameFr: 'Bulgarie',
  ),
  Country(
    name: 'Burkina Faso',
    dialCode: '+226',
    code: CountryCode.BF,
    nameFr: 'Burkina Faso',
  ),
  Country(
    name: 'Burundi',
    dialCode: '+257',
    code: CountryCode.BI,
    nameFr: 'Burundi',
  ),
  Country(
    name: 'Cambodia',
    dialCode: '+855',
    code: CountryCode.KH,
    nameFr: 'Cambodge',
  ),
  Country(
    name: 'Cameroon',
    dialCode: '+237',
    code: CountryCode.CM,
    nameFr: 'Cameroun',
  ),
  Country(
    name: 'Canada',
    dialCode: '+1',
    code: CountryCode.CA,
    nameFr: 'Canada',
  ),
  Country(
    name: 'Cape Verde',
    dialCode: '+238',
    code: CountryCode.CV,
    nameFr: 'Cap-Vert',
  ),
  Country(
    name: 'Cayman Islands',
    dialCode: '+345',
    code: CountryCode.KY,
    nameFr: 'Îles Caïmanes',
  ),
  Country(
    name: 'Central African Republic',
    dialCode: '+236',
    code: CountryCode.CF,
    nameFr: 'Centrafricaine, république',
  ),
  Country(
    name: 'Chad',
    dialCode: '+235',
    code: CountryCode.TD,
    nameFr: 'Tchad',
  ),
  Country(
    name: 'Chile',
    dialCode: '+56',
    code: CountryCode.CL,
    nameFr: 'Chili',
  ),
  Country(
    name: 'China',
    dialCode: '+86',
    code: CountryCode.CN,
    nameFr: 'Chine',
  ),
  Country(
    name: 'Christmas Island',
    dialCode: '+61',
    code: CountryCode.CX,
    nameFr: 'île Christmas',
  ),
  Country(
    name: 'Cocos (Keeling) Islands',
    dialCode: '+61',
    code: CountryCode.CC,
    nameFr: 'îles cocos',
  ),
  Country(
    name: 'Colombia',
    dialCode: '+57',
    code: CountryCode.CO,
    nameFr: 'Colombie',
  ),
  Country(
    name: 'Comoros',
    dialCode: '+269',
    code: CountryCode.KM,
    nameFr: 'Comores',
  ),
  Country(
    name: 'Congo',
    dialCode: '+242',
    code: CountryCode.CG,
    nameFr: 'République du Congo',
  ),
  Country(
    name: 'Democratic Republic of the Congo',
    dialCode: '+243',
    code: CountryCode.CD,
    nameFr: 'République démocratique du congo',
  ),
  Country(
    name: "Cote d'Ivoire",
    dialCode: '+225',
    code: CountryCode.CI,
    nameFr: "Côte d'Ivoire",
  ),
  Country(
    name: 'Cook Islands',
    dialCode: '+682',
    code: CountryCode.CK,
    nameFr: 'Îles Cook',
  ),
  Country(
    name: 'Costa Rica',
    dialCode: '+506',
    code: CountryCode.CR,
    nameFr: 'Costa Rica',
  ),
  Country(
    name: 'Croatia',
    dialCode: '+385',
    code: CountryCode.HR,
    nameFr: 'Croatie',
  ),
  Country(
    name: 'Cuba',
    dialCode: '+53',
    code: CountryCode.CU,
    nameFr: 'Cuba',
  ),
  Country(
    name: 'Cyprus',
    dialCode: '+537',
    code: CountryCode.CY,
    nameFr: 'Chypre',
  ),
  Country(
    name: 'Czech Republic',
    dialCode: '+420',
    code: CountryCode.CZ,
    nameFr: 'République tchèque',
  ),
  Country(
    name: 'Denmark',
    dialCode: '+45',
    code: CountryCode.DK,
    nameFr: 'Danemark',
  ),
  Country(
    name: 'Djibouti',
    dialCode: '+253',
    code: CountryCode.DJ,
    nameFr: 'Djibouti',
  ),
  Country(
    name: 'Dominica',
    dialCode: '+1 767',
    code: CountryCode.DM,
    nameFr: 'Dominique',
  ),
  Country(
    name: 'Dominican Republic',
    dialCode: '+1 849',
    code: CountryCode.DO,
    nameFr: 'République Dominicaine',
  ),
  Country(
    name: 'Ecuador',
    dialCode: '+593',
    code: CountryCode.EC,
    nameFr: 'Équateur',
  ),
  Country(
    name: 'Egypt',
    dialCode: '+20',
    code: CountryCode.EG,
    nameFr: 'Égypte',
  ),
  Country(
    name: 'El Salvador',
    dialCode: '+503',
    code: CountryCode.SV,
    nameFr: 'El Salvador',
  ),
  Country(
    name: 'Equatorial Guinea',
    dialCode: '+240',
    code: CountryCode.GQ,
    nameFr: 'Guinée équatoriale',
  ),
  Country(
    name: 'Eritrea',
    dialCode: '+291',
    code: CountryCode.ER,
    nameFr: 'Érythrée',
  ),
  Country(
    name: 'Estonia',
    dialCode: '+372',
    code: CountryCode.EE,
    nameFr: 'Estonie',
  ),
  Country(
    name: 'Ethiopia',
    dialCode: '+251',
    code: CountryCode.ET,
    nameFr: 'Éthiopie',
  ),
  Country(
    name: 'Falkland Islands (Malvinas)',
    dialCode: '+500',
    code: CountryCode.FK,
    nameFr: 'îles falkland',
  ),
  Country(
    name: 'Faroe Islands',
    dialCode: '+298',
    code: CountryCode.FO,
    nameFr: 'Îles Féroé',
  ),
  Country(
    name: 'Fiji',
    dialCode: '+679',
    code: CountryCode.FJ,
    nameFr: 'Fidji',
  ),
  Country(
    name: 'Finland',
    dialCode: '+358',
    code: CountryCode.FI,
    nameFr: 'Finlande',
  ),
  Country(
    name: 'France',
    dialCode: '+33',
    code: CountryCode.FR,
    nameFr: 'France',
  ),
  Country(
    name: 'French Guiana',
    dialCode: '+594',
    code: CountryCode.GF,
    nameFr: 'Guyane française',
  ),
  Country(
    name: 'French Polynesia',
    dialCode: '+689',
    code: CountryCode.PF,
    nameFr: 'Polynésie Française',
  ),
  Country(
    name: 'Gabon',
    dialCode: '+241',
    code: CountryCode.GA,
    nameFr: 'Gabon',
  ),
  Country(
    name: 'Gambia',
    dialCode: '+220',
    code: CountryCode.GM,
    nameFr: 'Gambie',
  ),
  Country(
    name: 'Georgia',
    dialCode: '+995',
    code: CountryCode.GE,
    nameFr: 'Géorgie',
  ),
  Country(
    name: 'Germany',
    dialCode: '+49',
    code: CountryCode.DE,
    nameFr: 'Allemagne',
  ),
  Country(
    name: 'Ghana',
    dialCode: '+233',
    code: CountryCode.GH,
    nameFr: 'Ghana',
  ),
  Country(
    name: 'Gibraltar',
    dialCode: '+350',
    code: CountryCode.GI,
    nameFr: 'Gibraltar',
  ),
  Country(
    name: 'Greece',
    dialCode: '+30',
    code: CountryCode.GR,
    nameFr: 'Grèce',
  ),
  Country(
    name: 'Greenland',
    dialCode: '+299',
    code: CountryCode.GL,
    nameFr: 'Groenland',
  ),
  Country(
    name: 'Grenada',
    dialCode: '+1 473',
    code: CountryCode.GD,
    nameFr: 'Grenade',
  ),
  Country(
    name: 'Guadeloupe',
    dialCode: '+590',
    code: CountryCode.GP,
    nameFr: 'Guadeloupe',
  ),
  Country(
    name: 'Guam',
    dialCode: '+1 671',
    code: CountryCode.GU,
    nameFr: 'Guam',
  ),
  Country(
    name: 'Guatemala',
    dialCode: '+502',
    code: CountryCode.GT,
    nameFr: 'Guatemala',
  ),
  Country(
    name: 'Guernsey',
    dialCode: '+44',
    code: CountryCode.GG,
    nameFr: 'Guernesey',
  ),
  Country(
    name: 'Guinea',
    dialCode: '+224',
    code: CountryCode.GN,
    nameFr: 'Guinée',
  ),
  Country(
    name: 'Guinea-Bissau',
    dialCode: '+245',
    code: CountryCode.GW,
    nameFr: 'Guinée-Bissau',
  ),

    Country(
    name: 'Guyana',
    dialCode: '+595',
    code: CountryCode.GY,
    nameFr: 'Guyana',
  ),
  Country(
    name: 'Haiti',
    dialCode: '+509',
    code: CountryCode.HT,
    nameFr: 'Haïti',
  ),
  Country(
    name: 'Honduras',
    dialCode: '+504',
    code: CountryCode.HN,
    nameFr: 'Honduras',
  ),
  Country(
    name: 'Hong Kong',
    dialCode: '+852',
    code: CountryCode.HK,
    nameFr: 'Hong Kong',
  ),
  Country(
    name: 'Hungary',
    dialCode: '+36',
    code: CountryCode.HU,
    nameFr: 'Hongrie',
  ),
  Country(
    name: 'Iceland',
    dialCode: '+354',
    code: CountryCode.IS,
    nameFr: 'Islande',
  ),
  Country(
    name: 'India',
    dialCode: '+91',
    code: CountryCode.IN,
    nameFr: 'Inde',
  ),
  Country(
    name: 'Indonesia',
    dialCode: '+62',
    code: CountryCode.ID,
    nameFr: 'Indonésie',
  ),
  Country(
    name: 'Iran',
    dialCode: '+98',
    code: CountryCode.IR,
    nameFr: 'Iran',
  ),
  Country(
    name: 'Iraq',
    dialCode: '+964',
    code: CountryCode.IQ,
    nameFr: 'Irak',
  ),
  Country(
    name: 'Ireland',
    dialCode: '+353',
    code: CountryCode.IE,
    nameFr: 'Irlande',
  ),
  Country(
    name: 'Isle of Man',
    dialCode: '+44',
    code: CountryCode.IM,
    nameFr: 'Île de Man',
  ),
  Country(
    name: 'Israel',
    dialCode: '+972',
    code: CountryCode.IL,
    nameFr: 'Israël',
  ),
  Country(
    name: 'Italy',
    dialCode: '+39',
    code: CountryCode.IT,
    nameFr: 'Italie',
  ),
  Country(
    name: 'Jamaica',
    dialCode: '+1 876',
    code: CountryCode.JM,
    nameFr: 'Jamaïque',
  ),
  Country(
    name: 'Japan',
    dialCode: '+81',
    code: CountryCode.JP,
    nameFr: 'Japon',
  ),
  Country(
    name: 'Jersey',
    dialCode: '+44',
    code: CountryCode.JE,
    nameFr: 'Jersey',
  ),
  Country(
    name: 'Jordan',
    dialCode: '+962',
    code: CountryCode.JO,
    nameFr: 'Jordanie',
  ),
  Country(
    name: 'Kazakhstan',
    dialCode: '+7 7',
    code: CountryCode.KZ,
    nameFr: 'Kazakhstan',
  ),
  Country(
    name: 'Kenya',
    dialCode: '+254',
    code: CountryCode.KE,
    nameFr: 'Kenya',
  ),
  Country(
    name: 'Kiribati',
    dialCode: '+686',
    code: CountryCode.KI,
    nameFr: 'Kiribati',
  ),
  Country(
    name: 'Kuwait',
    dialCode: '+965',
    code: CountryCode.KW,
    nameFr: 'Koweït',
  ),
  Country(
    name: 'Kyrgyzstan',
    dialCode: '+996',
    code: CountryCode.KG,
    nameFr: 'Kirghizistan',
  ),
  Country(
    name: 'Laos',
    dialCode: '+856',
    code: CountryCode.LA,
    nameFr: 'Laos',
  ),
  Country(
    name: 'Latvia',
    dialCode: '+371',
    code: CountryCode.LV,
    nameFr: 'Lettonie',
  ),
  Country(
    name: 'Lebanon',
    dialCode: '+961',
    code: CountryCode.LB,
    nameFr: 'Liban',
  ),
  Country(
    name: 'Lesotho',
    dialCode: '+266',
    code: CountryCode.LS,
    nameFr: 'Lesotho',
  ),
  Country(
    name: 'Liberia',
    dialCode: '+231',
    code: CountryCode.LR,
    nameFr: 'Liberia',
  ),
  Country(
    name: 'Libya',
    dialCode: '+218',
    code: CountryCode.LY,
    nameFr: 'Libye',
  ),
  Country(
    name: 'Liechtenstein',
    dialCode: '+423',
    code: CountryCode.LI,
    nameFr: 'Liechtenstein',
  ),
  Country(
    name: 'Lithuania',
    dialCode: '+370',
    code: CountryCode.LT,
    nameFr: 'Lituanie',
  ),
  Country(
    name: 'Luxembourg',
    dialCode: '+352',
    code: CountryCode.LU,
    nameFr: 'Luxembourg',
  ),
  Country(
    name: 'Macao',
    dialCode: '+853',
    code: CountryCode.MO,
    nameFr: 'Macao',
  ),
  Country(
    name: 'Madagascar',
    dialCode: '+261',
    code: CountryCode.MG,
    nameFr: 'Madagascar',
  ),
  Country(
    name: 'Malawi',
    dialCode: '+265',
    code: CountryCode.MW,
    nameFr: 'Malawi',
  ),
  Country(
    name: 'Malaysia',
    dialCode: '+60',
    code: CountryCode.MY,
    nameFr: 'Malaisie',
  ),
  Country(
    name: 'Maldives',
    dialCode: '+960',
    code: CountryCode.MV,
    nameFr: 'Maldives',
  ),
  Country(
    name: 'Mali',
    dialCode: '+223',
    code: CountryCode.ML,
    nameFr: 'Mali',
  ),
  Country(
    name: 'Malta',
    dialCode: '+356',
    code: CountryCode.MT,
    nameFr: 'Malte',
  ),
  Country(
    name: 'Marshall Islands',
    dialCode: '+692',
    code: CountryCode.MH,
    nameFr: 'Îles Marshall',
  ),
  Country(
    name: 'Martinique',
    dialCode: '+596',
    code: CountryCode.MQ,
    nameFr: 'Martinique',
  ),
  Country(
    name: 'Mauritania',
    dialCode: '+222',
    code: CountryCode.MR,
    nameFr: 'Mauritanie',
  ),
  Country(
    name: 'Mauritius',
    dialCode: '+230',
    code: CountryCode.MU,
    nameFr: 'Île Maurice',
  ),
  Country(
    name: 'Mayotte',
    dialCode: '+262',
    code: CountryCode.YT,
    nameFr: 'Mayotte',
  ),
  Country(
    name: 'Mexico',
    dialCode: '+52',
    code: CountryCode.MX,
    nameFr: 'Mexique',
  ),
  Country(
    name: 'Micronesia',
    dialCode: '+691',
    code: CountryCode.FM,
    nameFr: 'Micronésie',
  ),
  Country(
    name: 'Moldova',
    dialCode: '+373',
    code: CountryCode.MD,
    nameFr: 'Moldavie',
  ),
  Country(
    name: 'Monaco',
    dialCode: '+377',
    code: CountryCode.MC,
    nameFr: 'Monaco',
  ),
  Country(
    name: 'Mongolia',
    dialCode: '+976',
    code: CountryCode.MN,
    nameFr: 'Mongolie',
  ),
  Country(
    name: 'Montenegro',
    dialCode: '+382',
    code: CountryCode.ME,
    nameFr: 'Monténégro',
  ),
  Country(
    name: 'Montserrat',
    dialCode: '+1664',
    code: CountryCode.MS,
    nameFr: 'Montserrat',
  ),
  Country(
    name: 'Morocco',
    dialCode: '+212',
    code: CountryCode.MA,
    nameFr: 'Maroc',
  ),
  Country(
    name: 'Mozambique',
    dialCode: '+258',
    code: CountryCode.MZ,
    nameFr: 'Mozambique',
  ),
  Country(
    name: 'Myanmar',
    dialCode: '+95',
    code: CountryCode.MM,
    nameFr: 'Myanmar',
  ),
    Country(
    name: 'Namibia',
    dialCode: '+264',
    code: CountryCode.NA,
    nameFr: 'Namibie',
  ),
  Country(
    name: 'Nauru',
    dialCode: '+674',
    code: CountryCode.NR,
    nameFr: 'Nauru',
  ),
  Country(
    name: 'Nepal',
    dialCode: '+977',
    code: CountryCode.NP,
    nameFr: 'Népal',
  ),
  Country(
    name: 'Netherlands',
    dialCode: '+31',
    code: CountryCode.NL,
    nameFr: 'Pays-Bas',
  ),
  Country(
    name: 'Netherlands Antilles',
    dialCode: '+599',
    code: CountryCode.AN,
    nameFr: 'Antilles néerlandaises',
  ),
  Country(
    name: 'New Caledonia',
    dialCode: '+687',
    code: CountryCode.NC,
    nameFr: 'Nouvelle-Calédonie',
  ),
  Country(
    name: 'New Zealand',
    dialCode: '+64',
    code: CountryCode.NZ,
    nameFr: 'Nouvelle-Zélande',
  ),
  Country(
    name: 'Nicaragua',
    dialCode: '+505',
    code: CountryCode.NI,
    nameFr: 'Nicaragua',
  ),
  Country(
    name: 'Niger',
    dialCode: '+227',
    code: CountryCode.NE,
    nameFr: 'Niger',
  ),
  Country(
    name: 'Nigeria',
    dialCode: '+234',
    code: CountryCode.NG,
    nameFr: 'Nigéria',
  ),
  Country(
    name: 'Niue',
    dialCode: '+683',
    code: CountryCode.NU,
    nameFr: 'Nioué',
  ),
  Country(
    name: 'Norfolk Island',
    dialCode: '+672',
    code: CountryCode.NF,
    nameFr: 'Iles Norfolk',
  ),
  Country(
    name: 'North Korea',
    dialCode: '+850',
    code: CountryCode.KP,
    nameFr: 'Corée du nord',
  ),
  Country(
    name: 'Northern Mariana Islands',
    dialCode: '+1 670',
    code: CountryCode.MP,
    nameFr: 'Îles Mariannes du Nord',
  ),
  Country(
    name: 'Norway',
    dialCode: '+47',
    code: CountryCode.NO,
    nameFr: 'Norvège',
  ),
  Country(
    name: 'Oman',
    dialCode: '+968',
    code: CountryCode.OM,
    nameFr: 'Oman',
  ),
  Country(
    name: 'Pakistan',
    dialCode: '+92',
    code: CountryCode.PK,
    nameFr: 'Pakistan',
  ),
  Country(
    name: 'Palau',
    dialCode: '+680',
    code: CountryCode.PW,
    nameFr: 'Palaos',
  ),
  Country(
    name: 'Palestinian',
    dialCode: '+970',
    code: CountryCode.PS,
    nameFr: 'Palestine',
  ),
  Country(
    name: 'Panama',
    dialCode: '+507',
    code: CountryCode.PA,
    nameFr: 'Panama',
  ),
  Country(
    name: 'Papua New Guinea',
    dialCode: '+675',
    code: CountryCode.PG,
    nameFr: 'Papouasie-Nouvelle-Guinée',
  ),
  Country(
    name: 'Paraguay',
    dialCode: '+595',
    code: CountryCode.PY,
    nameFr: 'Paraguay',
  ),
  Country(
    name: 'Peru',
    dialCode: '+51',
    code: CountryCode.PE,
    nameFr: 'Pérou',
  ),
  Country(
    name: 'Philippines',
    dialCode: '+63',
    code: CountryCode.PH,
    nameFr: 'Philippines',
  ),
  Country(
    name: 'Pitcairn',
    dialCode: '+872',
    code: CountryCode.PN,
    nameFr: 'Îles Pitcairn',
  ),
  Country(
    name: 'Poland',
    dialCode: '+48',
    code: CountryCode.PL,
    nameFr: 'Pologne',
  ),
  Country(
    name: 'Portugal',
    dialCode: '+351',
    code: CountryCode.PT,
    nameFr: 'Portugal',
  ),
  Country(
    name: 'Puerto Rico',
    dialCode: '+1 939',
    code: CountryCode.PR,
    nameFr: 'Porto Rico',
  ),
  Country(
    name: 'Qatar',
    dialCode: '+974',
    code: CountryCode.QA,
    nameFr: 'Qatar',
  ),
  Country(
    name: 'Réunion',
    dialCode: '+262',
    code: CountryCode.RE,
    nameFr: 'Réunion',
  ),
  Country(
    name: 'Romania',
    dialCode: '+40',
    code: CountryCode.RO,
    nameFr: 'Roumanie',
  ),
  Country(
    name: 'Russia',
    dialCode: '+7',
    code: CountryCode.RU,
    nameFr: 'Russie',
  ),
  Country(
    name: 'Rwanda',
    dialCode: '+250',
    code: CountryCode.RW,
    nameFr: 'Rwanda',
  ),
  Country(
    name: 'Saint Barthélemy',
    dialCode: '+590',
    code: CountryCode.BL,
    nameFr: 'Saint Barthélemy',
  ),
  Country(
    name: 'Saint Helena',
    dialCode: '+290',
    code: CountryCode.SH,
    nameFr: 'Sainte-Hélène',
  ),
  Country(
    name: 'Saint Kitts and Nevis',
    dialCode: '+1 869',
    code: CountryCode.KN,
    nameFr: 'Saint-Christophe-et-Niévès',
  ),
  Country(
    name: 'Saint Lucia',
    dialCode: '+1 758',
    code: CountryCode.LC,
    nameFr: 'Sainte-Lucie',
  ),
  Country(
    name: 'Saint Martin',
    dialCode: '+590',
    code: CountryCode.MF,
    nameFr: 'Saint-Martin',
  ),
  Country(
    name: 'Saint Pierre and Miquelon',
    dialCode: '+508',
    code: CountryCode.PM,
    nameFr: 'Saint-Pierre et Miquelon',
  ),
  Country(
    name: 'Saint Vincent and the Grenadines',
    dialCode: '+1 784',
    code: CountryCode.VC,
    nameFr: 'Saint-Vincent-et-les Grenadines',
  ),
  Country(
    name: 'Samoa',
    dialCode: '+685',
    code: CountryCode.WS,
    nameFr: 'Samoa',
  ),
  Country(
    name: 'San Marino',
    dialCode: '+378',
    code: CountryCode.SM,
    nameFr: 'Saint-Marin',
  ),
  Country(
    name: 'Sao Tome and Principe',
    dialCode: '+239',
    code: CountryCode.ST,
    nameFr: 'Sao Tomé-et-Principe',
  ),
  Country(
    name: 'Saudi Arabia',
    dialCode: '+966',
    code: CountryCode.SA,
    nameFr: 'Arabie saoudite',
  ),
  Country(
    name: 'Senegal',
    dialCode: '+221',
    code: CountryCode.SN,
    nameFr: 'Sénégal',
  ),
  Country(
    name: 'Serbia',
    dialCode: '+381',
    code: CountryCode.RS,
    nameFr: 'Serbie',
  ),
  Country(
    name: 'Seychelles',
    dialCode: '+248',
    code: CountryCode.SC,
    nameFr: 'Seychelles',
  ),
  Country(
    name: 'Sierra Leone',
    dialCode: '+232',
    code: CountryCode.SL,
    nameFr: 'Sierra Leone',
  ),
  Country(
    name: 'Singapore',
    dialCode: '+65',
    code: CountryCode.SG,
    nameFr: 'Singapour',
  ),
  Country(
    name: 'Slovakia',
    dialCode: '+421',
    code: CountryCode.SK,
    nameFr: 'Slovaquie',
  ),
  Country(
    name: 'Slovenia',
    dialCode: '+386',
    code: CountryCode.SI,
    nameFr: 'Slovénie',
  ),
  Country(
    name: 'Solomon Islands',
    dialCode: '+677',
    code: CountryCode.SB,
    nameFr: 'Îles Salomon',
  ),
  Country(
    name: 'Somalia',
    dialCode: '+252',
    code: CountryCode.SO,
    nameFr: 'Somalie',
  ),
  Country(
    name: 'South Africa',
    dialCode: '+27',
    code: CountryCode.ZA,
    nameFr: 'Afrique du Sud',
  ),
    Country(
    name: 'South Georgia and the South Sandwich Islands',
    dialCode: '+500',
    code: CountryCode.GS,
    nameFr: 'Géorgie du Sud-et-les îles Sandwich du Sud',
  ),
  Country(
    name: 'South Korea',
    dialCode: '+82',
    code: CountryCode.KR,
    nameFr: 'Corée du sud',
  ),
  Country(
    name: 'Spain',
    dialCode: '+34',
    code: CountryCode.ES,
    nameFr: 'Espagne',
  ),
  Country(
    name: 'Sri Lanka',
    dialCode: '+94',
    code: CountryCode.LK,
    nameFr: 'Sri Lanka',
  ),
  Country(
    name: 'Sudan',
    dialCode: '+249',
    code: CountryCode.SD,
    nameFr: 'Soudan',
  ),
  Country(
    name: 'Suriname',
    dialCode: '+597',
    code: CountryCode.SR,
    nameFr: 'Suriname',
  ),
  Country(
    name: 'Svalbard and Jan Mayen',
    dialCode: '+47',
    code: CountryCode.SJ,
    nameFr: 'Svalbard et Jan Mayen',
  ),
  Country(
    name: 'Swaziland',
    dialCode: '+268',
    code: CountryCode.SZ,
    nameFr: 'Swaziland',
  ),
  Country(
    name: 'Sweden',
    dialCode: '+46',
    code: CountryCode.SE,
    nameFr: 'Suède',
  ),
  Country(
    name: 'Switzerland',
    dialCode: '+41',
    code: CountryCode.CH,
    nameFr: 'Suisse',
  ),
  Country(
    name: 'Syria',
    dialCode: '+963',
    code: CountryCode.SY,
    nameFr: 'Syrie',
  ),
  Country(
    name: 'Taiwan',
    dialCode: '+886',
    code: CountryCode.TW,
    nameFr: 'Taiwan',
  ),
  Country(
    name: 'Tajikistan',
    dialCode: '+992',
    code: CountryCode.TJ,
    nameFr: 'Tadjikistan',
  ),
  Country(
    name: 'Tanzania',
    dialCode: '+255',
    code: CountryCode.TZ,
    nameFr: 'Tanzanie',
  ),
  Country(
    name: 'Thailand',
    dialCode: '+66',
    code: CountryCode.TH,
    nameFr: 'Thaïlande',
  ),
  Country(
    name: 'Timor-Leste',
    dialCode: '+670',
    code: CountryCode.TL,
    nameFr: 'Timor-Leste',
  ),
  Country(
    name: 'Togo',
    dialCode: '+228',
    code: CountryCode.TG,
    nameFr: 'Togo',
  ),
  Country(
    name: 'Tokelau',
    dialCode: '+690',
    code: CountryCode.TK,
    nameFr: 'Tokelau',
  ),
  Country(
    name: 'Tonga',
    dialCode: '+676',
    code: CountryCode.TO,
    nameFr: 'Tonga',
  ),
  Country(
    name: 'Trinidad and Tobago',
    dialCode: '+1 868',
    code: CountryCode.TT,
    nameFr: 'Trinité-et-Tobago',
  ),
  Country(
    name: 'Tunisia',
    dialCode: '+216',
    code: CountryCode.TN,
    nameFr: 'Tunisie',
  ),
  Country(
    name: 'Turkey',
    dialCode: '+90',
    code: CountryCode.TR,
    nameFr: 'Turquie',
  ),
  Country(
    name: 'Turkmenistan',
    dialCode: '+993',
    code: CountryCode.TM,
    nameFr: 'Turkmenistan',
  ),
  Country(
    name: 'Turks and Caicos Islands',
    dialCode: '+1 649',
    code: CountryCode.TC,
    nameFr: 'Îles Turks et Caïques',
  ),
  Country(
    name: 'Tuvalu',
    dialCode: '+688',
    code: CountryCode.TV,
    nameFr: 'Tuvalu',
  ),
  Country(
    name: 'Uganda',
    dialCode: '+256',
    code: CountryCode.UG,
    nameFr: 'Ouganda',
  ),
  Country(
    name: 'Ukraine',
    dialCode: '+380',
    code: CountryCode.UA,
    nameFr: 'Ukraine',
  ),
  Country(
    name: 'United Arab Emirates',
    dialCode: '+971',
    code: CountryCode.AE,
    nameFr: 'Émirats Arabes Unis',
  ),
  Country(
    name: 'United Kingdom',
    dialCode: '+44',
    code: CountryCode.GB,
    nameFr: 'Grande-Bretagne',
  ),
  Country(
    name: 'United States',
    dialCode: '+1',
    code: CountryCode.US,
    nameFr: 'États-Unis',
  ),
  Country(
    name: 'Uruguay',
    dialCode: '+598',
    code: CountryCode.UY,
    nameFr: 'Uruguay',
  ),
  Country(
    name: 'Uzbekistan',
    dialCode: '+998',
    code: CountryCode.UZ,
    nameFr: 'Ouzbékistan',
  ),
  Country(
    name: 'Vanuatu',
    dialCode: '+678',
    code: CountryCode.VU,
    nameFr: 'Vanuatu',
  ),
  Country(
    name: 'Vatican',
    dialCode: '+379',
    code: CountryCode.VA,
    nameFr: 'Vatican',
  ),
  Country(
    name: 'Venezuela',
    dialCode: '+58',
    code: CountryCode.VE,
    nameFr: 'Venezuela',
  ),
  Country(
    name: 'Vietnam',
    dialCode: '+84',
    code: CountryCode.VN,
    nameFr: 'Vietnam',
  ),
  Country(
    name: 'Wallis and Futuna',
    dialCode: '+681',
    code: CountryCode.WF,
    nameFr: 'Wallis et Futuna',
  ),
  Country(
    name: 'Yemen',
    dialCode: '+967',
    code: CountryCode.YE,
    nameFr: 'Yémen',
  ),
  Country(
    name: 'Zambia',
    dialCode: '+260',
    code: CountryCode.ZM,
    nameFr: 'Zambie',
  ),
  Country(
    name: 'Zimbabwe',
    dialCode: '+263',
    code: CountryCode.ZW,
    nameFr: 'Zimbabwe',
  ),
];

