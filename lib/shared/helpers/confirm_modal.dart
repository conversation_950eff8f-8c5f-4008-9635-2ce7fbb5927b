
import 'package:flutter/material.dart';
import 'package:malou/shared/helpers/modal_bottom_sheet.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

showConfirmModal({
  required BuildContext context,
  required String title,
  required String subtitle,
  required Function onConfirm,
  required Function onCancel,
  required String primaryButtonTitle,
  required String secondaryButtonTitle,
}) async {
  AppBottomModal.showTinyModalBottomSheet(
      context,
      Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            title,
            style: MalouTextStyles(context)
                .text13Semibold
                .copyWith(color: MalouColors.textTitle),
          ),
          const SizedBox(height: 10),
          Text(subtitle, textAlign: TextAlign.center),
          const Divider(
            color: MalouColors.borderPrimary,
            height: 1,
          ),
          TextButton(
            onPressed: () {
              onConfirm();
            },
            child: Text(
              primaryButtonTitle,
              style: MalouTextStyles(context)
                  .text13Semibold
                  .copyWith(color: MalouColors.pinkAccent),
            ),
          ),
          const Divider(
            color: MalouColors.borderPrimary,
            height: 1,
          ),
          TextButton(
            onPressed: () {
              onCancel();
            },
            child: Text(
              secondaryButtonTitle,
              style: MalouTextStyles(context)
                  .text13Semibold
                  .copyWith(color: MalouColors.textBody),
            ),
          ),
        ],
      ));
}
