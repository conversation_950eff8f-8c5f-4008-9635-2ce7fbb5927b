import 'dart:io';

import 'package:flutter/material.dart';
import 'package:heap_flutter_bridge/heap_flutter_bridge.dart';
import 'package:malou/error_logger.dart';
import 'package:malou/pages/router.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:provider/provider.dart';

class HeapService {
  UserProvider? userProvider;
  HeapService._(this.userProvider);

  static final HeapService instance = HeapService._(null);

  void init(UserProvider userProvider) {
    if (const String.fromEnvironment('IS_RUNNING_ON_MY_COMPUTER') == 'true') {
      return;
    }
    this.userProvider = userProvider;

    Heap().startRecording(const String.fromEnvironment("HEAP_ID"), {
      "environment": const String.fromEnvironment("CURRENT_ENVIRONNEMENT"),
      "userId": userProvider.currentUser!.id,
      "userEmail": userProvider.currentUser!.email,
      "userName": userProvider.currentUser!.name,
      "role": userProvider.currentUser!.role,
      "userCaslRole": userProvider.currentUser!.caslRole,
      "platform": Platform.operatingSystem,
      "mobileAppSession": true
    }).then((value) {
      Heap().setLogLevel(HeapLogLevel.debug);

      Heap().identify(userProvider.currentUser!.id);
      Heap().addUserProperties({
        "environment": const String.fromEnvironment("CURRENT_ENVIRONNEMENT"),
        "userId": userProvider.currentUser!.id,
        "userEmail": userProvider.currentUser!.email,
        "userName": userProvider.currentUser!.name,
        "role": userProvider.currentUser!.role,
        "userCaslRole": userProvider.currentUser!.caslRole,
        "platform": Platform.operatingSystem,
        "mobileAppSession": true
      });
      Heap().track("App Launched", {"userId": userProvider.currentUser!.id});
    });
  }

  void track(String eventName, Map<String, dynamic> properties,
      {BuildContext? context}) {
    if (const String.fromEnvironment('IS_RUNNING_ON_MY_COMPUTER') == 'true') {
      return;
    }
    if (context == null) {
      Heap().track(eventName, {
        "environment": const String.fromEnvironment("CURRENT_ENVIRONNEMENT"),
        "userId": userProvider?.currentUser!.id,
        "userEmail": userProvider?.currentUser!.email,
        "userName": userProvider?.currentUser!.name,
        "role": userProvider?.currentUser!.role,
        "userCaslRole": userProvider?.currentUser!.caslRole,
        "platform": Platform.operatingSystem,
        "mobileAppSession": true,
        ...properties,
      }).then((value) {
        print('Heap event $eventName tracked');
      }).catchError((err) {
        log('Heap event $eventName error: $err');
      });
      return;
    }
    // check if context is still valid
    if (!context.mounted) {
      context = rootNavigatorKey.currentContext!;
    }

    RestaurantProvider restaurantProvider =
        Provider.of<RestaurantProvider>(context, listen: false);
    UserProvider? userProvider2 =
        Provider.of<UserProvider>(context, listen: false);
    var organization = userProvider2.getOrganizationById(
        restaurantProvider.currentRestaurant?.organizationId ?? '');
    Heap().track(eventName, {
      "environment": const String.fromEnvironment("CURRENT_ENVIRONNEMENT"),
      "userId": userProvider2.currentUser!.id,
      "userEmail": userProvider2.currentUser!.email,
      "userName": userProvider2.currentUser!.name,
      "role": userProvider2.currentUser!.role,
      "userCaslRole": userProvider2.currentUser!.caslRole,
      "platform": Platform.operatingSystem,
      "mobileAppSession": true,
      'restaurantOrganisation': organization?.name,
      'restaurantId': restaurantProvider.currentRestaurant?.id,
      'restaurant':
          '${restaurantProvider.currentRestaurant?.name} - ${restaurantProvider.currentRestaurant?.getFullFormattedAddress()}',
      ...properties,
    });
    return;
  }
}
