import 'package:dio/dio.dart';
import 'package:malou/shared/helpers/sentry.service.dart';

class ApiResult<T> {
  final String message;
  final T? data;
  final bool error;
  final int? statusCode;
  final List<String>? errors;

  ApiResult(
      {required this.message,
      this.data,
      this.statusCode,
      this.error = false,
      this.errors});

  factory ApiResult.fromJson(Map<String, dynamic> json,
      {T Function(dynamic)? fromJsonT}) {
    return ApiResult(
      message: json['msg'] ?? json['message'] ?? '',
      data: fromJsonT == null ? null : fromJsonT(json['data']),
      error: false,
    );
  }

  factory ApiResult.error(dynamic error, {StackTrace? stackTrace}) {
    print(error);
    print(stackTrace);
    if (error.runtimeType == DioException) {
      DioException dioError = error as DioException;
      if (dioError.error == 'No internet connection') {
        return ApiResult(message: 'No Internet', error: true, statusCode: 503);
      }
      if (dioError.response != null) {
        return ApiResult(
            message: dioError.response!.data.runtimeType == String
                ? dioError.response?.data
                : dioError.response?.data?['message'],
            error: true,
            statusCode: dioError.response!.statusCode);
      }
    }
    if (error.toString().contains('is not a subtype of')) {
      SentryService.instance.addError(error, StackTrace.current);
      return ApiResult(
          message:
              'A type error occured, please contact the support for more informations.',
          error: true,
          statusCode: 500);
    }
    return ApiResult(
        message: error.toString() + stackTrace.toString(),
        data: null,
        error: true);
  }
}
