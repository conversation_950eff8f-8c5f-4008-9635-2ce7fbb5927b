import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/models/quick_text.model.dart';

enum TemplateVariable {
  BUSINESS_NAME,
  MY_FIRSTNAME,
  CLIENT_NAME,
  PHONE,
  ADDRESS,
  MENU_LINK,
  WEBSIT<PERSON>,
  REGULAR_HOURS,
}

const AVAILABLE_REVIEW_TEMPLATE_VARIABLES = [
  TemplateVariable.CLIENT_NAME,
  TemplateVariable.MY_FIRSTNAME,
  TemplateVariable.BUSINESS_NAME,
];

const AVAILABLE_MESSAGE_TEMPLATE_VARIABLES = [
  TemplateVariable.CLIENT_NAME,
  TemplateVariable.ADDRESS,
  TemplateVariable.MENU_LINK,
  TemplateVariable.PHONE,
  TemplateVariable.REGULAR_HOURS,
  TemplateVariable.WEBSITE,
];

enum TemplateReplacerType {
  CHIP_TEXT,
  VARIABLE_TEXT,
  READABLE_TEXT,
}

class TemplateReplacer {
  static String replacePreview(
    String template,
    BuildContext context,
  ) {
    List<QuickText> quickTexts = [
      {
        'key': 'phone',
        'displayKey': '@${Translation.of(context).restaurantPhone}',
        'content': ''
      },
      {
        'key': 'address',
        'displayKey': '@${Translation.of(context).restaurantAddress}',
        'content': ''
      },
      {
        'key': 'regular_hours',
        'displayKey': '@${Translation.of(context).restaurantOpeningHours}',
        'content': ''
      },
      {
        'key': 'website',
        'displayKey': '@${Translation.of(context).restaurantWebsite}',
        'content': ''
      },
      {
        'key': 'menuUrl',
        'displayKey': '@${Translation.of(context).restaurantMenu}',
        'content': ''
      },
      {
        'key': 'client_name',
        'displayKey': '@${Translation.of(context).clientName}',
        'content': ''
      },
      {
        'key': 'business_name',
        'displayKey': '@${Translation.of(context).businessName}',
        'content': ''
      },
      {
        'key': 'my_firstname',
        'displayKey': '@${Translation.of(context).myFirstName}',
        'content': ''
      },
    ]
        .map((e) => QuickText(
            key: e['key'] ?? '',
            displayKey: e['displayKey'] ?? '',
            content: e['content'] ?? ''))
        .toList(growable: false);

    return template.replaceAllMapped(
      RegExp(r'\{([a-zA-Z_]+)\}'),
      (match) {
        final variable = match.group(1)!;
        final quickText = quickTexts.firstWhere(
          (element) => element.key == variable,
          orElse: () => QuickText(key: variable, content: '', displayKey: ''),
        );

        return quickText.displayKey;
      },
    );
  }
}
