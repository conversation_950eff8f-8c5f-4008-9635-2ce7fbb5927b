import 'package:malou/shared/helpers/object_to_snake_case.dart';

class Pagination {
  Pagination({
    required this.pageNumber,
    required this.total,
    required this.pageSize,
    this.skip,
  });

  int pageNumber;
  int pageSize;
  dynamic total;
  int? skip;

  factory Pagination.fromJson(Map<String, dynamic> json) => Pagination(
        pageNumber: json["pageNumber"],
        pageSize: json["pageSize"],
        total: json["total"],
        skip: json["skip"],
      );

  Map<String, dynamic> toJson() => {
        "pageNumber": pageNumber,
        "pageSize": pageSize,
        "total": total,
        "skip": skip,
      };

  Map<String, dynamic> toSnakeCaseJson() => objectToSnakeCase(toJson());
}

class PaginationResult {
  int totalDocs;
  int limit;
  int totalPages;
  int page;
  int pagingCounter;
  bool hasPrevPage;
  bool hasNextPage;
  int prevPage;
  int nextPage;

  PaginationResult({
    required this.totalDocs,
    required this.limit,
    required this.totalPages,
    required this.page,
    required this.pagingCounter,
    required this.hasPrevPage,
    required this.hasNextPage,
    required this.prevPage,
    required this.nextPage,
  });

  factory PaginationResult.fromJson(Map<String, dynamic> json) =>
      PaginationResult(
        totalDocs: json["totalDocs"],
        limit: json["limit"],
        totalPages: json["totalPages"],
        page: json["page"],
        pagingCounter: json["pagingCounter"],
        hasPrevPage: json["hasPrevPage"],
        hasNextPage: json["hasNextPage"],
        prevPage: json["prevPage"],
        nextPage: json["nextPage"],
      );

  Map<String, dynamic> toJson() => {
        "totalDocs": totalDocs,
        "limit": limit,
        "totalPages": totalPages,
        "page": page,
        "pagingCounter": pagingCounter,
        "hasPrevPage": hasPrevPage,
        "hasNextPage": hasNextPage,
        "prevPage": prevPage,
        "nextPage": nextPage,
      };
}

class SortedPagination extends Pagination {
  SortedPagination({
    required this.sortBy,
    this.sortOrder = -1,
    required int pageNumber,
    dynamic total,
    required int pageSize,
    int? skip,
  }) : super(
          pageNumber: pageNumber,
          total: total,
          pageSize: pageSize,
          skip: skip,
        );

  String sortBy;
  int sortOrder;

  factory SortedPagination.fromJson(Map<String, dynamic> json) =>
      SortedPagination(
        sortBy: json["sort"],
        pageNumber: json["pageNumber"],
        pageSize: json["pageSize"],
        total: json["total"],
        skip: json["skip"],
        sortOrder: json["sortOrder"],
      );

  @override
  Map<String, dynamic> toJson() => {
        "sortBy": sortBy,
        "sortOrder": sortOrder,
        "pageNumber": pageNumber,
        "pageSize": pageSize,
        "total": total,
        "skip": skip,
      };

  @override
  Map<String, dynamic> toSnakeCaseJson() => {
        "sort_by": sortBy,
        "sort_order": sortOrder,
        "page_number": pageNumber,
        "page_size": pageSize,
        "total": total,
        "skip": skip,
      };
}
