import 'package:growthbook_sdk_flutter/growthbook_sdk_flutter.dart';
import 'package:malou/shared/models/user.model.dart';

class ExperimentationService {
  GrowthBookSDK? _gbinstance;
  ExperimentationService._();

  static final ExperimentationService instance = ExperimentationService._();

  Future<GrowthBookSDK> initialize(
      User? userProps, String? currentRestaurantId) async {
    _gbinstance = await GBSDKBuilderApp(
      apiKey: const String.fromEnvironment('GROWTHBOOK_API_KEY'),
      hostURL: const String.fromEnvironment('GROWTHBOOK_HOST_URL'),
      backgroundSync: true,
      growthBookTrackingCallBack: (_) {},
      attributes: {
        'userId': userProps?.id,
        'email': userProps?.email,
        'name': userProps?.name,
        'role': userProps?.role,
        'restaurantId': currentRestaurantId
      },
    ).initialize();
    await _gbinstance!.refresh();
    return _gbinstance!;
  }

  bool isOn(String experimentKey) {
    return _gbinstance!.isOn(experimentKey);
  }
}
