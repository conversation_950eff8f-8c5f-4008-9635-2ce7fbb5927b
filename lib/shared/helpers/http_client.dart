import 'package:dio/dio.dart';
import 'package:malou/error_logger.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/router.dart';
import 'package:malou/shared/services/connectivity/connectivity.service.dart';
import 'package:malou/shared/widgets/toasts/toast.widget.dart';
import 'package:malou/shared/widgets/toasts/toasts.service.dart';
import 'package:rxdart/rxdart.dart';
import 'package:shared_preferences/shared_preferences.dart';

class DioClient {
  Dio client;
  DioClient({required this.client}) {
    showNoInternetConnectionToast$
        .debounceTime(
      const Duration(milliseconds: 50),
    )
        .listen((_) {
      ToastsService.instance.add(Toast(
          type: ToastType.error,
          // ignore: use_build_context_synchronously
          message: Translation.of(rootNavigatorKey.currentContext!)
              .noInternetConnection));
    });
  }

  static final DioClient _instance = DioClient.create();

  BehaviorSubject<void> showNoInternetConnectionToast$ = BehaviorSubject();

  factory DioClient.create({Dio? client}) {
    Dio dio = client ?? Dio();
    addAuthTokenInterceptor(dio);
    addConnectivityInterceptor(dio);

    return DioClient(client: dio);
  }

  static void addAuthTokenInterceptor(Dio dio) {
    dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        SharedPreferences preferences = await SharedPreferences.getInstance();
        String? authToken = preferences.getString('auth_token');
        if (authToken != null) {
          options.headers['Authorization'] = 'Bearer $authToken';
        }
        // Add custom header to identify mobile app requests
        options.headers['X-Client-Type'] = 'mobile-app';
        return handler.next(options);
      },
      onError: (e, handler) {
        if (e.response?.statusCode == 403 && e.response?.data['casl'] == true) {
          // show dialog
          ToastsService.instance.add(Toast(
              type: ToastType.error,
              message: Translation.of(rootNavigatorKey.currentContext!)
                  .unauthorized));
        }
        final String requestLog = '''
[HTTP_REQUEST_ERROR_LOG]
- Method: ${e.requestOptions.method}
- URL: ${e.requestOptions.baseUrl}${e.requestOptions.path}
- Query Parameters: ${e.requestOptions.queryParameters.isNotEmpty ? e.requestOptions.queryParameters : 'None'}
- Headers: ${e.requestOptions.headers}
- Data: ${e.requestOptions.data ?? 'None'}
    ''';
        log(requestLog, isError: false);
        handler.next(e);
      },
    ));
  }

  static DioClient get instance => _instance;

  static addConnectivityInterceptor(Dio dio) {
    dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        if (!await ConnectivityService().isConnectedToInternet()) {
          instance.showNoInternetConnectionToast$.add(null);
          return handler.reject(DioException(
            requestOptions: options,
            error: 'No internet connection',
          ));
        }
        return handler.next(options);
      },
    ));
  }
}
