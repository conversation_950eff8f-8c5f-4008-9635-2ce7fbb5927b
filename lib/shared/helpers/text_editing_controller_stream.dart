import 'package:flutter/material.dart';
import 'package:rxdart/rxdart.dart';

class TextEditingControllerStream extends TextEditingController {
  final BehaviorSubject<String> textObs$;
  TextEditingControllerStream({String text = ''})
      : textObs$ = BehaviorSubject<String>.seeded(text),
        super(text: text);

  @override
  set text(String newText) {
    super.text = newText;
    textObs$.add(newText);
  }

  @override
  set value(TextEditingValue newValue) {
    super.value = newValue;
    textObs$.add(newValue.text);
  }

  insertFromCursor(String textToAppend) {
    num textLength = text.length;
    TextSelection textSelection = selection;

    if (textLength == textSelection.start &&
        textLength == textSelection.end) {
      textToAppend = " $textToAppend";
    } else {
      textToAppend = textToAppend;
    }

    if (textSelection.start == -1 && textSelection.end == -1) {
      String newText = text.replaceRange(0, 0, textToAppend);
      final textToAppendLength = textToAppend.length;
      text = newText;
      selection = textSelection.copyWith(
        baseOffset: 0 + textToAppendLength,
        extentOffset: 0 + textToAppendLength,
      );
    } else {
      String newText = text.replaceRange(
          textSelection.start, textSelection.end, textToAppend);
      final textToAppendLength = textToAppend.length;
      text = newText;
      selection = textSelection.copyWith(
        baseOffset: textSelection.start + textToAppendLength,
        extentOffset: textSelection.start + textToAppendLength,
      );
    }
  }

  @override
  void dispose() {
    textObs$.close();
    super.dispose();
  }
}
