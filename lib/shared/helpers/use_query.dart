import 'package:dio/dio.dart';
import 'package:malou/shared/helpers/clarify_error.dart';
import 'package:rxdart/rxdart.dart';

class ErrorFetching {
  bool isError;
  // intentionally use a String and not an Enum to avoid type checking errors
  String? malouErrorCode;
  Map<String, String>? cause;

  ErrorFetching({required this.isError, this.cause, this.malouErrorCode});

  hasMalouError(MalouErrorCode code) {
    return malouErrorCode == code.name;
  }

  @override
  String toString() {
    return 'ErrorFetching{isError: $isError, cause: $cause}';
  }
}

class QueryResult<ResultQuery, QueryParams> {
  late Future<ResultQuery?> Function(QueryParams) execute;
  late void Function() close;
  late ValueStream<bool> isFetching;
  late ValueStream<ErrorFetching> errorFetching;
  late BehaviorSubject<ResultQuery?> data;
}

QueryResult<ResultQuery, QueryParams>
    useQuery<ResultQuery, QueryParams, OnProcessResult>(
        Future<ResultQuery> Function(QueryParams) fetchData,
        {OnProcessResult Function(QueryParams)? onProcess,
        Function(ResultQuery, QueryParams, OnProcessResult?)? onSuccess,
        Function(ErrorFetching, QueryParams, OnProcessResult?)? onError}) {
  final isFetchingController = BehaviorSubject<bool>.seeded(false);
  final errorFetchingController =
      BehaviorSubject<ErrorFetching>.seeded(ErrorFetching(isError: false));
  final dataController = BehaviorSubject<ResultQuery?>.seeded(null);

  Future<ResultQuery?> execute(QueryParams parameter) async {
    isFetchingController.add(true);
    errorFetchingController.add(ErrorFetching(isError: false));

    OnProcessResult? onProcessResult;

    if (onProcess != null) {
      onProcessResult = onProcess(parameter);
    }

    try {
      final result = await fetchData(parameter);
      dataController.add(result);
      if (onSuccess != null) {
        onSuccess(result, parameter, onProcessResult);
      }
      return result;
    } catch (error, stackTrace) {
      // log stack trace
      print('Error from execute in useQuery ...');
      print(error);
      print(stackTrace);

      ErrorFetching errorFetching = ErrorFetching(isError: true);

      // http request
      if (error is DioException) {
        errorFetching = ErrorFetching(
            isError: true,
            malouErrorCode: error.response?.data['malouErrorCode'],
            cause: {'message': error.response?.data['message']});
      } else if (error is Exception) {
        errorFetching = ErrorFetching(isError: true, cause: {
          'message': error.toString(),
        });
      }

      errorFetchingController.add(errorFetching);
      if (onError != null) {
        onError(errorFetching, parameter, onProcessResult);
      }
      return null;
    } finally {
      isFetchingController.add(false); // Indiquer que la requête est terminée
    }
  }

  final result = QueryResult<ResultQuery, QueryParams>();
  result.execute = execute;
  result.isFetching = isFetchingController.stream;
  result.errorFetching = errorFetchingController.stream;
  result.data = dataController;

  void close() {
    isFetchingController.close();
    errorFetchingController.close();
    dataController.close();
  }

  result.close = close;

  return result;
}
