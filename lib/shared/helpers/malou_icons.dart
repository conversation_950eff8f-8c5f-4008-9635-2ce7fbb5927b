import 'package:flutter/material.dart';

class MalouIcons {
  MalouIcons._();
  static const _maloufont = 'maloufont';

  // some icons are missing

  static const IconData conversation = IconData(0xe801, fontFamily: _maloufont);
  static const IconData clock = IconData(0xe802, fontFamily: _maloufont);
  static const IconData ellipsis = IconData(0xe803, fontFamily: _maloufont);
  static const IconData post = IconData(0xe804, fontFamily: _maloufont);
  static const IconData calendar = IconData(0xe805, fontFamily: _maloufont);
  static const IconData plusTwo = IconData(0xe806, fontFamily: _maloufont);
  static const IconData eyeClosed = IconData(0xe807, fontFamily: _maloufont);
  static const IconData check = IconData(0xe808, fontFamily: _maloufont);
  static const IconData setting = IconData(0xe809, fontFamily: _maloufont);
  static const IconData reviews = IconData(0xe80A, fontFamily: _maloufont);
  static const IconData send = IconData(0xe80B, fontFamily: _maloufont);
  static const IconData images = IconData(0xe80C, fontFamily: _maloufont);
  static const IconData chevronLeft = IconData(0xe80D, fontFamily: _maloufont);
  static const IconData eye = IconData(0xe80E, fontFamily: _maloufont);
  static const IconData brouillon = IconData(0xe80F, fontFamily: _maloufont);
  static const IconData convers = IconData(0xe810, fontFamily: _maloufont);
  static const IconData plus = IconData(0xe811, fontFamily: _maloufont);
  static const IconData spinner = IconData(0xe812, fontFamily: _maloufont);
  static const IconData attention = IconData(0xe8013, fontFamily: _maloufont);
  static const IconData user = IconData(0xe814, fontFamily: _maloufont);
  static const IconData modele = IconData(0xe815, fontFamily: _maloufont);
  static const IconData soundOn = IconData(0xe816, fontFamily: _maloufont);
  static const IconData arrowRight = IconData(0xe817, fontFamily: _maloufont);
  static const IconData arrowLeft = IconData(0xe818, fontFamily: _maloufont);
  static const IconData fav = IconData(0xe819, fontFamily: _maloufont);
  static const IconData eclair = IconData(0xe81A, fontFamily: _maloufont);
  static const IconData filter = IconData(0xe81B, fontFamily: _maloufont);
  static const IconData arrowDown = IconData(0xe81C, fontFamily: _maloufont);

  static const IconData sync = IconData(0xe820, fontFamily: _maloufont);
  static const IconData favStroke = IconData(0xe821, fontFamily: _maloufont);
  static const IconData soundOff = IconData(0xe822, fontFamily: _maloufont);

  static const IconData haut = IconData(0xe824, fontFamily: _maloufont);
  static const IconData cross = IconData(0xe825, fontFamily: _maloufont);
  static const IconData starFull = IconData(0xe826, fontFamily: _maloufont);
  static const IconData search = IconData(0xe827, fontFamily: _maloufont);
  static const IconData trash = IconData(0xe828, fontFamily: _maloufont);
  static const IconData copy = IconData(0xe829, fontFamily: _maloufont);
  static const IconData ia = IconData(0xe82A, fontFamily: _maloufont);
  static const IconData play = IconData(0xe82B, fontFamily: _maloufont);
  static const IconData home = IconData(0xe82D, fontFamily: _maloufont);
  static const IconData starOutline = IconData(0xe82E, fontFamily: _maloufont);
  static const IconData recadrage = IconData(0xe82F, fontFamily: _maloufont);
  static const IconData baisse = IconData(0xe830, fontFamily: _maloufont);
  static const IconData photographie = IconData(0xe831, fontFamily: _maloufont);
  static const IconData edit = IconData(0xe833, fontFamily: _maloufont);
  static const IconData warning = IconData(0xe834, fontFamily: _maloufont);
  static const IconData info = IconData(0xe800, fontFamily: _maloufont);
}
