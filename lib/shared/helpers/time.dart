// ignore_for_file: constant_identifier_names

import 'package:intl/intl.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/models/restaurant.model.dart';

enum Day {
  MONDAY,
  TUESDAY,
  WEDNESDAY,
  THURSDAY,
  FRIDAY,
  SATURDAY,
  SUNDAY,
}

final Map<String, Map<String, dynamic>> days = {
  Day.MONDAY.name: {
    'digit': 1,
    'fr': 'lundi',
    'en': 'monday',
    'es': 'Lunes',
    'it': 'Lunedì',
  },
  Day.TUESDAY.name: {
    'digit': 2,
    'fr': 'mardi',
    'en': 'tuesday',
    'es': 'Mart<PERSON>',
    'it': '<PERSON><PERSON><PERSON>',
  },
  Day.WEDNESDAY.name: {
    'digit': 3,
    'fr': 'mercredi',
    'en': 'wednesday',
    'es': 'Miércoles',
    'it': 'Mercoledì',
  },
  Day.THURSDAY.name: {
    'digit': 4,
    'fr': 'jeudi',
    'en': 'thursday',
    'es': 'Jueves',
    'it': '<PERSON><PERSON><PERSON><PERSON>',
  },
  Day.FRIDAY.name: {
    'digit': 5,
    'fr': 'vendredi',
    'en': 'friday',
    'es': 'Viernes',
    'it': 'Venerdì',
  },
  Day.SATURDAY.name: {
    'digit': 6,
    'fr': 'samedi',
    'en': 'saturday',
    'es': 'Sábado',
    'it': 'Sabato',
  },
  Day.SUNDAY.name: {
    'digit': 7,
    'fr': 'dimanche',
    'en': 'sunday',
    'es': 'Domingo',
    'it': 'Domenica',
  },
};

String _formatTime(String time, String lang) {
  switch (lang) {
    case 'en':
      int hours = int.parse(time.split(':')[0]);
      int minutes = int.parse(time.split(':')[1]);
      DateTime date = DateTime.now();
      date = DateTime(date.year, date.month, date.day, hours, minutes, 0, 0);
      return DateFormat('hh:mm a').format(date).replaceAll(':00', '');
    default:
      return time;
  }
}

bool _isSameHours(CustomTimePeriod h1, CustomTimePeriod h2) {
  if (h1.isClosed != h2.isClosed) {
    return false;
  }
  if (h1.isClosed == true) {
    return true;
  }
  return h1.openTime == h2.openTime &&
      h1.closeTime == h2.closeTime &&
      h1.openTime2 == h2.openTime2 &&
      h1.closeTime2 == h2.closeTime2;
}

List<CustomTimePeriod> _sortHours(List<TimePeriod> hours) {
  List<String> keys = days.keys.toList();
  List<CustomTimePeriod> res = [];
  for (int i = 0; i < keys.length; i++) {
    List<TimePeriod> sameDayHours =
        hours.where((d) => d.openDay == keys[i]).toList();
    if (sameDayHours.length > 1) {
      res.add(
        CustomTimePeriod(
          openDay: sameDayHours[0].openDay,
          openTime: sameDayHours[0].openTime,
          closeTime: sameDayHours[0].closeTime,
          openTime2: sameDayHours[1].openTime,
          closeTime2: sameDayHours[1].closeTime,
        ),
      );
    } else {
      res.addAll(sameDayHours.map((e) => CustomTimePeriod.fromTimePeriod(e)));
    }
  }
  return res;
}

List<List<CustomTimePeriod>> _mergeHours(List<TimePeriod> hours) {
  List<CustomTimePeriod> sortedHours = _sortHours(hours);
  int len = sortedHours.length;
  int j = 0;
  List<List<CustomTimePeriod>> res = [];

  while (j < len) {
    List<CustomTimePeriod> range = [];
    range.add(sortedHours[j]);
    for (int i = j; i < len - 1; i++) {
      if (_isSameHours(sortedHours[i], sortedHours[i + 1])) {
        range.add(sortedHours[i + 1]);
        j++;
      } else {
        break;
      }
    }
    j++;
    res.add(range);
  }
  return res;
}

String generateTextFromHours(List<TimePeriod> hours, String lang, Translation translation) {
  if (hours.isEmpty) {
    return "";
  }

  List<List<CustomTimePeriod>> mergedHours = _mergeHours(hours);
  String text = translation.we_are;
  for (int i = 0; i < mergedHours.length; i++) {
    List<CustomTimePeriod> range = mergedHours[i];

    if (range.length == 1) {
      if (range[0].isClosed == true) {
        text += translation.closed_day(days[range[0].openDay]?[lang]!);
      } else {
        text += translation.open_day(
          _formatTime(range[0].closeTime!, lang),
          days[range[0].openDay]![lang]!,
          _formatTime(range[0].openTime!, lang),
        );
        if (range[0].openTime2 != null) {
          text += translation.open_day_more(
            _formatTime(range[0].closeTime2!, lang),
            _formatTime(range[0].openTime2!, lang),
          );
        }
      }
    } else {
      CustomTimePeriod firstDay = range[0];
      CustomTimePeriod lastDay = range[range.length - 1];
      if (firstDay.isClosed == true) {
        text += translation.closed_days(
          days[firstDay.openDay]![lang]!,
          days[lastDay.openDay]![lang]!,
        );
      } else {
        text += translation.open_days(
          _formatTime(firstDay.closeTime ?? '', lang),
          days[lastDay.openDay]![lang]!,
          _formatTime(firstDay.openTime ?? '', lang),
          days[firstDay.openDay]![lang]!,
        );
        if (firstDay.openTime2 != null) {
          text += translation.open_day_more(
            _formatTime(firstDay.openTime2!, lang),
            _formatTime(firstDay.closeTime2!, lang),
          );
        }
      }
    }
  }

  return text
      .replaceAll(', et', translation.and)
      .replaceAll('24:00h', translation.midnight)
      .replaceAll('00:00h', translation.midnight)
      .replaceAll('12:00h', translation.noon)
      .replaceAll(':00', '')
      .replaceAll(RegExp(r',([^,]*)$'), '');
}
