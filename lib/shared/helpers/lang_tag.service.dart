import 'package:malou/generated/l10n.dart';

class LangTagService {
  Translation translationService;

  LangTagService(this.translationService);

  String getLangTag(String langCode) {
    switch (langCode) {
      case 'af':
        return translationService.af;
      case 'am':
        return translationService.am;
      case 'ar':
        return translationService.ar;
      case 'az':
        return translationService.az;
      case 'be':
        return translationService.be;
      case 'bg':
        return translationService.bg;
      case 'bn':
        return translationService.bn;
      case 'bs':
        return translationService.bs;
      case 'ca':
        return translationService.ca;
      case 'ceb':
        return translationService.ceb;
      case 'co':
        return translationService.co;
      case 'cs':
        return translationService.cs;
      case 'cy':
        return translationService.cy;
      case 'da':
        return translationService.da;
      case 'de':
        return translationService.de;
      case 'el':
        return translationService.el;
      case 'en':
        return translationService.en;
      case 'eo':
        return translationService.eo;
      case 'es':
        return translationService.es;
      case 'et':
        return translationService.et;
      case 'eu':
        return translationService.eu;
      case 'fa':
        return translationService.fa;
      case 'fi':
        return translationService.fi;
      case 'fr':
        return translationService.fr;
      case 'fy':
        return translationService.fy;
      case 'ga':
        return translationService.ga;
      case 'gd':
        return translationService.gd;
      case 'gl':
        return translationService.gl;
      case 'gu':
        return translationService.gu;
      case 'ha':
        return translationService.ha;
      case 'haw':
        return translationService.haw;
      case 'he':
        return translationService.he;
      case 'hi':
        return translationService.hi;
      case 'hmn':
        return translationService.hmn;
      case 'hr':
        return translationService.hr;
      case 'ht':
        return translationService.ht;
      case 'hu':
        return translationService.hu;
      case 'hy':
        return translationService.hy;
      case 'id':
        return translationService.id;
      case 'ig':
        return translationService.ig;
      case 'it':
        return translationService.it;
      case 'iw':
        return translationService.iw;
      case 'ja':
        return translationService.ja;
      case 'jw':
        return translationService.jw;
      case 'ka':
        return translationService.ka;
      case 'kk':
        return translationService.kk;
      case 'km':
        return translationService.km;
      case 'kn':
        return translationService.kn;
      case 'ko':
        return translationService.ko;
      case 'ku':
        return translationService.ku;
      case 'ky':
        return translationService.ky;
      case 'la':
        return translationService.la;
      case 'lb':
        return translationService.lb;
      case 'lo':
        return translationService.lo;
      case 'lt':
        return translationService.lt;
      case 'lv':
        return translationService.lv;
      case 'mg':
        return translationService.mg;
      case 'mi':
        return translationService.mi;
      case 'mk':
        return translationService.mk;
      case 'ml':
        return translationService.ml;
      case 'mn':
        return translationService.mn;
      case 'mr':
        return translationService.mr;
      case 'ms':
        return translationService.ms;
      case 'mt':
        return translationService.mt;
      case 'my':
        return translationService.my;
      case 'ne':
        return translationService.ne;
      case 'nl':
        return translationService.nl;
      case 'no':
        return translationService.no;
      case 'ny':
        return translationService.ny;
      case 'or':
        return translationService.or;
      case 'pa':
        return translationService.pa;
      case 'pl':
        return translationService.pl;
      case 'ps':
        return translationService.ps;
      case 'pt':
        return translationService.pt;
      case 'ro':
        return translationService.ro;
      case 'ru':
        return translationService.ru;
      case 'sd':
        return translationService.sd;
      case 'si':
        return translationService.si;
      case 'sk':
        return translationService.sk;
      case 'sl':
        return translationService.sl;
      case 'sm':
        return translationService.sm;
      case 'sn':
        return translationService.sn;
      case 'so':
        return translationService.so;
      case 'sq':
        return translationService.sq;
      case 'sr':
        return translationService.sr;
      case 'st':
        return translationService.st;
      case 'su':
        return translationService.su;
      case 'sv':
        return translationService.sv;
      case 'sw':
        return translationService.sw;
      case 'ta':
        return translationService.ta;
      case 'te':
        return translationService.te;
      case 'tg':
        return translationService.tg;
      case 'th':
        return translationService.th;
      case 'tl':
        return translationService.tl;
      case 'tr':
        return translationService.tr;
      case 'ug':
        return translationService.ug;
      case 'uk':
        return translationService.uk;
      case 'ur':
        return translationService.ur;
      case 'uz':
        return translationService.uz;
      case 'vi':
        return translationService.vi;
      case 'xh':
        return translationService.xh;
      case 'yi':
        return translationService.yi;
      case 'yo':
        return translationService.yo;
      case 'zu':
        return translationService.zu;
      default:
        return langCode;
    }
  }
}