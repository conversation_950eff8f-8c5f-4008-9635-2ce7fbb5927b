import 'package:malou/shared/services/language/language.provider.dart';

String getOrderCountText(int order, {AppLocale lang = AppLocale.fr}) {
  if (lang == AppLocale.fr) {
    return order == 1 ? "1er" : "$orderème";
  } else if (lang == AppLocale.it || lang == AppLocale.es) {
    return "$orderº";
  } else if (lang == AppLocale.en) {
    List<String> suffixes = ["th", "st", "nd", "rd"];
    int suffixIndex = order % 100;
    if (suffixIndex > 3) {
      suffixIndex = 0;
    }
    return "$order${suffixes[suffixIndex]}";
  }
  return "$order";
}