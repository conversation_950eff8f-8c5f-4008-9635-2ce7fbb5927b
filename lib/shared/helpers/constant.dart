import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/theme/theme_colors.dart';

class AppConstant {
  static PlatformDetails getDetails(PlatformKeys key) {
    List<PlatformDetails> platformDetails = getPlatformDetailsList();

    dynamic pDetails = platformDetails.firstWhere(
      (element) => element.key == key,
      // in case we add new platforms to the app, firstWhere can cause no element found issue
      orElse: () => PlatformDetails(
        key: PlatformKeys.malou,
        name: '',
        borderColor: MalouColors.purpleAccent,
        hasRating: false,
      ),
    );
    return pDetails;
  }

  static List<PlatformDetails> getPlatformDetailsList() => [
        PlatformDetails(
          key: PlatformKeys.gmb,
          name: 'Google',
          borderColor: MalouColors.googleBorder,
          hasRating: true,
        ),
        PlatformDetails(
          key: PlatformKeys.facebook,
          name: 'Facebook',
          borderColor: MalouColors.facebookBorder,
          hasRating: false,
        ),
        PlatformDetails(
            key: PlatformKeys.yelp,
            name: 'Yelp',
            borderColor: MalouColors.yelpBorder,
            hasRating: true,
            domainMap: Map.from({
              'fr': 'www.biz.yelp.fr',
              'en': 'www.biz.yelp.com',
              'es': 'www.biz.yelp.es',
              'it': 'www.biz.yelp.it',
            })),
        PlatformDetails(
          key: PlatformKeys.foursquare,
          name: 'Foursquare',
          borderColor: MalouColors.foursquareBorder,
          hasRating: true,
        ),
        PlatformDetails(
          key: PlatformKeys.tripadvisor,
          name: 'Tripadvisor',
          borderColor: MalouColors.tripadvisorBorder,
          hasRating: true,
        ),
        PlatformDetails(
          key: PlatformKeys.lafourchette,
          name: 'The Fork',
          borderColor: MalouColors.lafourchetteBorder,
          hasRating: true,
        ),
        PlatformDetails(
          key: PlatformKeys.instagram,
          name: 'Instagram',
          borderColor: MalouColors.instagramBorder,
          hasRating: false,
        ),
        PlatformDetails(
          key: PlatformKeys.mapstr,
          name: 'Mapstr',
          borderColor: MalouColors.mapstrBorder,
          hasRating: false,
        ),
        PlatformDetails(
          key: PlatformKeys.zenchef,
          name: 'Zenchef',
          borderColor: MalouColors.zenchefBorder,
          hasRating: false,
        ),
        PlatformDetails(
          key: PlatformKeys.deliveroo,
          name: 'Deliveroo',
          borderColor: MalouColors.deliverooBorder,
          hasRating: false,
        ),
        PlatformDetails(
          key: PlatformKeys.ubereats,
          name: 'Uber Eats',
          borderColor: MalouColors.ubereatsBorder,
          hasRating: false,
        ),
        PlatformDetails(
          key: PlatformKeys.malouPrivate,
          name: 'Malou Private',
          borderColor: MalouColors.malouPrivateBorder,
          hasRating: false,
        ),
        PlatformDetails(
          key: PlatformKeys.malou,
          name: 'Malou Private',
          borderColor: MalouColors.primary,
          hasRating: false,
        ),
        PlatformDetails(
          key: PlatformKeys.manual,
          name: 'Malou Private',
          borderColor: MalouColors.purpleAccent,
          hasRating: false,
        ),
        PlatformDetails(
          key: PlatformKeys.manual,
          name: 'Malou Private',
          borderColor: MalouColors.purpleAccent,
          hasRating: false,
        ),
        PlatformDetails(
          key: PlatformKeys.opentable,
          name: 'OpenTable',
          borderColor: MalouColors.opentableBorder,
          hasRating: true,
        ),
        PlatformDetails(
          key: PlatformKeys.resy,
          name: 'Resy',
          borderColor: MalouColors.resyBorder,
          hasRating: true,
        ),
        PlatformDetails(
          key: PlatformKeys.sevenrooms,
          name: 'SevenRooms',
          borderColor: MalouColors.sevenroomsBorder,
          hasRating: true,
        ),
        PlatformDetails(
          key: PlatformKeys.doordash,
          name: 'DoorDash',
          borderColor: MalouColors.doordashBorder,
          hasRating: true,
        ),
      ];

  static const APP_DEFAULT_LANGUAGE = 'fr';

  static const PLATFORMS_THAT_HAVE_REVIEWS = [
    PlatformKeys.abc,
    PlatformKeys.malouPrivate,
    PlatformKeys.deliveroo,
    PlatformKeys.facebook,
    PlatformKeys.foursquare,
    PlatformKeys.gmb,
    PlatformKeys.instagram,
    PlatformKeys.lafourchette,
    PlatformKeys.mapstr,
    PlatformKeys.opentable,
    PlatformKeys.malouPrivate,
    PlatformKeys.resy,
    PlatformKeys.tripadvisor,
    PlatformKeys.ubereats,
    PlatformKeys.yelp,
    PlatformKeys.zenchef,
  ];
}
