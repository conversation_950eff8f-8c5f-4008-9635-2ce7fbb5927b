import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/helpers/use_query.dart';
import 'package:malou/shared/widgets/snackbars/snackbar.dart';

enum MalouErrorCode {
  // ----- From flutter -----
  // Posts Hashtags
  TEXT_IS_REQUIRED_FOR_AI_GENERATION,
  HASHTAGS_ARE_REQUIRED_FOR_AI_GENERATION,

  // ----- From backend -----
  // AI
  NOT_ENOUGH_CREDIT_TO_MAKE_AI_API_CALL,
  AI_FEATURE_NOT_ACTIVATED,
  CREATE_COMPLETION_ERROR,
  RESTAURANT_NOT_FOUND,

  // AUTOMATIONS
  INCONSISTENT_RESTAURANT_ID,

  // CREDENTIALS
  CREDENTIAL_NOT_FOUND,
  MAPSTR_NO_DATA,

  // JOBS
  NO_POST_TO_PREPARE,
  // MESSAGES
  MISSING_AWS_SOURCE_KEY,

  // MISC
  CREATION_ERROR,
  NOT_FOUND,

  // PLATFORM
  PLATFORM_NOT_FOUND,

  // POSTS
  MAPSTR_MAPPING_ERROR,

  // PUBSUB
  NO_SUBSCRIPTION_NAME_OR_ID,

  // REVIEW
  REVIEWS_UPDATE_ERROR,
  REVIEW_NOT_FOUND,

  // AUTOMATIONS
  AUTO_GENERATE_REVIEW_REPLY_ERROR,

  POST_NOT_FOUND,
}

List<String> clarifyError(BuildContext context, String error) {
  if (error.contains(
      MalouErrorCode.NOT_ENOUGH_CREDIT_TO_MAKE_AI_API_CALL.name.toString())) {
    return [
      Translation.of(context).notEnoughCreditToMakeAiApiCall,
      Translation.of(context).aIcreditsBackSoon
    ];
  }
  if (error
      .contains("has exceeded the maximum number of calls to the AI API")) {
    return [
      Translation.of(context).notEnoughCreditToMakeAiApiCall,
      Translation.of(context).aIcreditsBackSoon
    ];
  }
  if (error.contains(MalouErrorCode.CREATE_COMPLETION_ERROR.name.toString())) {
    return [Translation.of(context).createCompletionError];
  }
  if (error.contains(
      MalouErrorCode.TEXT_IS_REQUIRED_FOR_AI_GENERATION.name.toString())) {
    return [Translation.of(context).completeCaptionToGenerateHashtagsFromAi];
  }
  if (error.contains(
      MalouErrorCode.HASHTAGS_ARE_REQUIRED_FOR_AI_GENERATION.name.toString())) {
    return [
      Translation.of(context).hashtagsRequiredForAI,
      Translation.of(context).pleaseCreateHashtagsFromYourSettings
    ];
  }
  if (error.contains(
      MalouErrorCode.HASHTAGS_ARE_REQUIRED_FOR_AI_GENERATION.name.toString())) {
    return [
      Translation.of(context).hashtagsRequiredForAI,
      Translation.of(context).pleaseCreateHashtagsFromYourSettings
    ];
  }
  if (error.contains(MalouErrorCode.POST_NOT_FOUND.name.toString())) {
    return [
      Translation.of(context).postNotFound,
      Translation.of(context).probablyDeleted
    ];
  }

  // todo add other errors

  return [
    Translation.of(context).somethingWentWrong,
    Translation.of(context).tryAgainLater
  ];
}

void displaySnackbarWithClarifiedError(
    BuildContext context, ErrorFetching result) {
  String errorFromRequest = '';
  if (result.cause != null) {
    errorFromRequest = result.cause?['message'] ?? '';
  }
  List<String> clarifiedErrors = clarifyError(context, errorFromRequest);
  MalouSnackbars.showInfoSnackbar(
      context: context,
      label: clarifiedErrors[0],
      subLabel: clarifiedErrors.length > 1 ? clarifiedErrors[1] : null);
}

class ClarifyErrorWidget extends StatelessWidget {
  final String error;
  const ClarifyErrorWidget({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    List<String> clarifiedErrors = clarifyError(context, error);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
              fit: BoxFit.cover,
              IllustrationsPathResolver.resolve(Illustrations.cook)),
          const SizedBox(height: 20),
          Text(clarifiedErrors[0]),
          if (clarifiedErrors.length > 1) ...[
            const SizedBox(height: 8),
            Text(clarifyError(context, error)[1])
          ]
        ],
      ),
    );
  }
}
