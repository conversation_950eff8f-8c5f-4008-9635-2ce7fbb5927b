import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/router.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

typedef OnSheetClosedCallback<T> = void Function(T result);

class AppBottomModal {
  AppBottomModal._();

  static Future<T?> showAppBottomModal<T>(BuildContext context, Widget child,
      {bool initExpanded = false,
      OnSheetClosedCallback<T>? afterClosed,
      bool maxSizeMin = false,
      bool isDismissible = true,
      bool enableDrag = false,
      double minSize = 0.5,
      double maxSize = 0.8}) {
    return showModalBottomSheet<T>(
      useRootNavigator: true,
      elevation: 0,
      context: context,
      barrierColor:
          isDismissible ? Colors.black.withOpacity(0.5) : Colors.transparent,
      backgroundColor: Colors.white,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      constraints: BoxConstraints(
        minWidth: MediaQuery.of(context).size.width,
      ),
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20), topRight: Radius.circular(20))),
      builder: (BuildContext context) => Padding(
        padding:
            EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
        child: DraggableScrollableSheet(
          minChildSize: minSize,
          initialChildSize: initExpanded ? maxSize : minSize,
          maxChildSize: maxSizeMin ? minSize : maxSize,
          expand: false,
          builder: (context, scrollController) => SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            controller: maxSizeMin ? null : scrollController,
            child: Container(
                height: MediaQuery.of(context).size.height,
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20)),
                  color: Colors.white,
                ),
                padding: const EdgeInsets.only(top: 15),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          Container(
                            width: 30,
                            height: 5,
                            decoration: const BoxDecoration(
                                color: MalouColors.purpleLight,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(12.0))),
                          ),
                        ],
                      ),
                      Expanded(
                        child: child,
                      )
                    ])),
          ),
        ),
      ),
    ).then((value) {
      if (afterClosed != null && value != null) {
        afterClosed(value);
      }
      return null;
    });
  }

  static Future<T?> showTinyModalBottomSheet<T extends dynamic>(
      BuildContext context, Widget child,
      {Color? barrierColor, OnSheetClosedCallback<T>? afterClosed}) {
    return showModalBottomSheet<T>(
      useSafeArea: true,
      elevation: 0,
      context: context,
      isScrollControlled: true,
      barrierColor: barrierColor ?? Colors.black.withOpacity(0.5),
      backgroundColor: Colors.white,
      constraints: BoxConstraints(
        minWidth: MediaQuery.of(context).size.width,
      ),
      builder: (context) => SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                ),
                margin: const EdgeInsets.symmetric(vertical: 10),
                height: 30,
                child: Center(
                  child: Container(
                    width: 30,
                    height: 5,
                    decoration: const BoxDecoration(
                        color: MalouColors.purpleLight,
                        borderRadius: BorderRadius.all(Radius.circular(12.0))),
                  ),
                )),
            Container(
                padding: const EdgeInsets.only(left: 18, right: 18, bottom: 30),
                child: child)
          ],
        ),
      ),
    ).then((value) {
      if (afterClosed != null && value != null) {
        afterClosed(value);
      }
      return null;
    });
  }

  static Future<T?> showAppBottomModalV2<T>(
    BuildContext context,
    Widget child, {
    bool initExpanded = false,
    OnSheetClosedCallback<T>? afterClosed,
    bool maxSizeMin = false,
    bool isDismissible = true,
    bool enableDrag = false,
    bool scrollable = true,
    double minSize = 0.5,
    double maxSize = 0.8,
    double initialChildSize = 0.5,
  }) {
    return showModalBottomSheet(
        useSafeArea: true,
        elevation: 0,
        context: context,
        useRootNavigator: true,
        barrierColor: Colors.black.withOpacity(0.5),
        backgroundColor: Colors.white,
        constraints: BoxConstraints(
          minWidth: MediaQuery.of(context).size.width,
        ),
        isDismissible: isDismissible,
        isScrollControlled: true,
        enableDrag: enableDrag,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20), topRight: Radius.circular(20))),
        builder: (context) => DraggableScrollableSheet(
              expand: false,
              initialChildSize: initialChildSize,
              maxChildSize: maxSize,
              minChildSize: minSize,
              builder: (_, controller) => Container(
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                ),
                child: Stack(
                  children: [
                    CustomScrollView(
                      controller: controller,
                      physics: scrollable
                          ? const BouncingScrollPhysics()
                          : const NeverScrollableScrollPhysics(),
                      slivers: [
                        SliverToBoxAdapter(
                            child: Center(
                          child: Container(
                            width: 30,
                            height: 5,
                            margin: const EdgeInsets.symmetric(vertical: 10),
                            decoration: const BoxDecoration(
                                color: MalouColors.purpleLight,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(12.0))),
                          ),
                        )),
                        SliverToBoxAdapter(
                          child: Container(
                              color: Colors.transparent,
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 20),
                              child: child),
                        )
                      ],
                    ),
                  ],
                ),
              ),
            )).then((value) {
      if (afterClosed != null && value != null) {
        afterClosed(value);
      }
      return null;
    });
  }

  static void updateDeleteActionsDialog(BuildContext refContext,
      {Function? onConfirmDelete,
      Function? onClickEditButton,
      String? deleteTitle,
      String? deleteSubTitle}) {
    AppBottomModal.showTinyModalBottomSheet(
      refContext,
      Column(
        children: [
          Material(
            borderRadius: BorderRadius.circular(10),
            clipBehavior: Clip.hardEdge,
            color: Colors.transparent,
            child: ListTile(
              title: Text(Translation.of(refContext).edit,
                  style: MalouTextStyles(refContext).text15Semibold),
              onTap: () {
                refContext.pop();
                if (onClickEditButton != null) {
                  onClickEditButton();
                }
              },
            ),
          ),
          Material(
            borderRadius: BorderRadius.circular(10),
            clipBehavior: Clip.hardEdge,
            color: Colors.transparent,
            child: ListTile(
              title: Text(Translation.of(refContext).delete,
                  style: MalouTextStyles(refContext)
                      .text15Semibold
                      .copyWith(color: MalouColors.error)),
              onTap: () {
                refContext.pop();
                // Need new context to avoid ancestor error on context.pop() !!
                // because we are opening a dialog into another dialog so we lose the context of the first one
                showConfirmModal(
                  context: rootNavigatorKey.currentContext!,
                  title: deleteTitle ?? Translation.of(refContext).pursue,
                  subtitle: deleteSubTitle ?? '',
                  onConfirm: () {
                    refContext.pop();
                    if (onConfirmDelete != null) {
                      onConfirmDelete();
                    }
                  },
                  onCancel: () {
                    refContext.pop();
                  },
                  primaryButtonTitle: Translation.of(refContext).delete,
                  secondaryButtonTitle: Translation.of(refContext).cancel,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  static void showConfirmModal({
    required BuildContext context,
    required String title,
    required String subtitle,
    required Function onConfirm,
    required Function onCancel,
    required String primaryButtonTitle,
    required String secondaryButtonTitle,
  }) async {
    AppBottomModal.showTinyModalBottomSheet(
        context,
        Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              title,
              style: MalouTextStyles(context)
                  .text13Semibold
                  .copyWith(color: MalouColors.textTitle),
            ),
            const SizedBox(height: 10),
            Text(subtitle, textAlign: TextAlign.center),
            const SizedBox(height: 20),
            const Divider(
              color: MalouColors.borderPrimary,
              height: 1,
            ),
            TextButton(
              onPressed: () {
                onConfirm();
              },
              child: Text(
                primaryButtonTitle,
                style: MalouTextStyles(context)
                    .text13Semibold
                    .copyWith(color: MalouColors.pinkAccent),
              ),
            ),
            const Divider(
              color: MalouColors.borderPrimary,
              height: 1,
            ),
            TextButton(
              onPressed: () {
                onCancel();
              },
              child: Text(
                secondaryButtonTitle,
                style: MalouTextStyles(context)
                    .text13Semibold
                    .copyWith(color: MalouColors.textBody),
              ),
            ),
          ],
        ));
  }
}
