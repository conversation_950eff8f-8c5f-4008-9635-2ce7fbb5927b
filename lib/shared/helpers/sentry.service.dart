import 'package:flutter/material.dart';
import 'package:malou/pages/router.dart';
import 'package:malou/shared/models/restaurant.model.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SentryService {
  SentryService._();

  static final SentryService instance = SentryService._();

  void addUserInfos(
      UserProvider userProvider, RestaurantProvider restaurantProvider) {
    if (const String.fromEnvironment('IS_RUNNING_ON_MY_COMPUTER') == 'true' ||
        const String.fromEnvironment('CURRENT_ENVIRONNEMENT') ==
            'development') {
      return;
    }

    Sentry.configureScope((scope) {
      scope.setUser(SentryUser(
          id: userProvider.currentUser!.id,
          username: userProvider.currentUser!.name,
          email: userProvider.currentUser!.email,
          data: {
            "restaurantId":
                restaurantProvider.currentRestaurant?.id ?? 'unknown',
            "restaurantName":
                restaurantProvider.currentRestaurant?.name ?? 'unknown',
            "role": userProvider.currentUser!.role,
            "caslRole": userProvider.currentUser!.caslRole,
            "env": const String.fromEnvironment("CURRENT_ENVIRONNEMENT"),
          }));
    });
  }

  void addRestaurantInfos(Restaurant? restaurant) async {
    final preferences = await SharedPreferences.getInstance();
    final user = preferences.getString('user');

    if (const String.fromEnvironment('IS_RUNNING_ON_MY_COMPUTER') == 'true') {
      return;
    }

    Sentry.configureScope((scope) {
      scope.setExtra("restaurantId", restaurant?.id ?? 'not selected');
      scope.setExtra("restaurantName", restaurant?.name ?? 'not selected');
      scope.setExtra("user", user);
      // get current route
      scope.setExtra(
          "page",
          ModalRoute.of(rootNavigatorKey.currentContext!)?.settings.name ??
              'unknown');
    });
  }

  void addError(dynamic error, dynamic stackTrace) {
    if (const String.fromEnvironment('IS_RUNNING_ON_MY_COMPUTER') == 'true' &&
        const String.fromEnvironment('CURRENT_ENVIRONNEMENT') ==
            'development') {
      return;
    }

    Sentry.captureException(error, stackTrace: stackTrace);
  }
}
