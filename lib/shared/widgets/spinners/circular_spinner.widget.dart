import 'package:flutter/material.dart';
import 'package:malou/shared/helpers/malou_icons.dart';

class CircularSpinner extends StatefulWidget {
  final double size;
  final Color? color;
  const CircularSpinner({Key? key, this.size = 30.0, this.color = Colors.white})
      : super(key: key);

  @override
  State<CircularSpinner> createState() => _CircularSpinnerState();
}

class _CircularSpinnerState extends State<CircularSpinner>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(
        seconds: 1,
      ),
    )..repeat(period: const Duration(milliseconds: 1000));
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        SizedBox(
          height: widget.size,
          width: widget.size,
        ),
        Positioned.fill(
          child: RotationTransition(
            turns: Tween(begin: 0.0, end: 1.0).animate(CurvedAnimation(
              parent: _animationController,
              curve: const Interval(0.0, 1.0, curve: Curves.easeInOut),
            )),
            child: Icon(
              MalouIcons.spinner,
              color: widget.color,
              size: widget.size,
            ),
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
}
