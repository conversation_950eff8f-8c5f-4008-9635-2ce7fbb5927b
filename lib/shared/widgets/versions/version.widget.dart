import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';

class Version extends StatelessWidget {
  const Version({super.key});

  Future<String> _getVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    return packageInfo.version;
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: _getVersion(),
      builder: (BuildContext context, AsyncSnapshot<String> snapshot) {
        if (snapshot.hasData) {
          return Text('v ${snapshot.data}');
        } else {
          return const Text('...');
        }
      },
    );
  }
}
