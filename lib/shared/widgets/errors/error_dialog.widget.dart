import 'package:flutter/material.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

class ErrorDialog extends StatelessWidget {
  final String errorMessage;
  const ErrorDialog({super.key, required this.errorMessage});

  @override
  Widget build(BuildContext context) {
    var dissmissKey = GlobalKey();
    var size = MediaQuery.of(context).size;
    return Container(
      padding: const EdgeInsets.all(17.0),
      height: size.height,
      width: size.width,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Dismissible(
              key: dissmissKey,
              direction: DismissDirection.down,
              onDismissed: (direction) => Navigator.pop(context),
              child: Container(
                height: 220,
                width: size.width,
                padding: const EdgeInsets.all(13),
                margin: const EdgeInsets.only(bottom: 40),
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20)),
                child: Column(
                  children: [
                    Container(
                      width: 34,
                      height: 4,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4),
                        color: MalouColors.borderSecondary,
                      ),
                    ),
                    const SizedBox(
                      height: 30,
                    ),
                    Image.asset(
                        height: 80,
                        IllustrationsPathResolver.resolve(Illustrations.cook)),
                    const SizedBox(
                      height: 15,
                    ),
                    Text(
                      errorMessage,
                      textAlign: TextAlign.center,
                      style: MalouTextStyles(context)
                          .text13Bold
                          .copyWith(color: MalouColors.textTitle, height: 1.5),
                    )
                  ],
                ),
              ))
        ],
      ),
    );
  }
}
