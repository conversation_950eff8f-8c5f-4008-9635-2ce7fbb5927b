import 'package:flutter/material.dart';
import 'package:malou/app_config.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/buttons/styles.dart';
import 'package:url_launcher/url_launcher.dart';

class ErroNoPlatfromsConnected extends StatelessWidget {
  final String restaurantId;
  const ErroNoPlatfromsConnected({
    super.key,
    required this.restaurantId,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        height: MediaQuery.of(context).size.height * 0.6,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              IllustrationsPathResolver.resolve(Illustrations.cook),
              height: 80,
            ),
            const SizedBox(
              height: 20,
            ),
            Text(
              Translation.of(context).platformsDisconnected,
              style: MalouTextStyles(context).text14Italic,
            ),
            const <PERSON><PERSON><PERSON><PERSON>(
              height: 20,
            ),
            PrimaryButtonContainer(
                child: ElevatedButton(
              onPressed: onConnectPlatforms,
              child: Text(Translation.of(context).connectMyPlatforms),
            ))
          ],
        ),
      )
    ;
  }

  void onConnectPlatforms() async {
    Uri uri = Uri.parse(
        '${AppConfig.shared.webAppUri}/restaurants/$restaurantId/settings/platforms/connection');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }
}
