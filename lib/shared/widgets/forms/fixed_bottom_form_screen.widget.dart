import 'package:flutter/material.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/widgets/inputs/text_field.widget.dart';
import 'package:malou/shared/widgets/buttons/styles.dart';
import 'package:malou/shared/widgets/chips/quick_text_chips_list.widget.dart';

class FixedBottomFormScreen extends StatefulWidget {
  final Icon? leftIcon;

  // controller
  final TextEditingController? controller;
  final int? maxLines;
  final bool? enabled;
  final String? initialValue;
  final int? maxLength;
  final String? hint;
  final String? label;
  final bool showInfoIcon;
  final bool showAiButtonInsideField;
  final bool showAiButtonNextToLabel;
  final TextStyle? style;
  final void Function(String)? onChanged;
  final void Function()? onAiPressed;
  final String? tooltipMessage;
  final bool showBorders;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final int? minLines;
  final void Function()? onTap;
  final bool? onAiProcessing;
  final void Function()? onSubmit;
  final void Function()? leftIconButtonOnPressed;
  final QuickTextsList? quickTextsList;
  final int? remainingAICredits;
  final Widget? leftCustomIcon;
  final bool keepKeyboardOpenOnTapOutside;

  FixedBottomFormScreen({
    super.key,
    this.controller,
    this.maxLines = 1,
    this.enabled = true,
    this.initialValue,
    this.maxLength,
    this.hint,
    this.label,
    this.showInfoIcon = false,
    this.showAiButtonNextToLabel = false,
    this.showAiButtonInsideField = false,
    this.onAiPressed,
    this.onChanged,
    this.style,
    this.tooltipMessage,
    this.showBorders = false,
    this.validator,
    this.keyboardType,
    this.minLines,
    this.onTap,
    this.leftIcon,
    this.onAiProcessing,
    this.onSubmit,
    this.quickTextsList,
    this.remainingAICredits,
    this.leftCustomIcon,
    this.leftIconButtonOnPressed,
    this.keepKeyboardOpenOnTapOutside = false,
  }) {
    if (leftIcon != null && leftCustomIcon != null) {
      throw Exception('You can only use one of leftIcon or leftCustomIcon');
    }
  }

  @override
  State<FixedBottomFormScreen> createState() => _FixedBottomFormScreenState();
}

class _FixedBottomFormScreenState extends State<FixedBottomFormScreen> {
  final ScrollController scrollController = ScrollController();

  @override
  initState() {
    super.initState();
    widget.controller?.addListener(() {
      if (scrollController.hasClients) {
        scrollController.animateTo(scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 200), curve: Curves.easeIn);
      }
    });
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: const EdgeInsets.only(left: 15, right: 15, bottom: 8),
        child: Column(
          children: [
            if (widget.quickTextsList != null) ...[
              const SizedBox(
                height: 10,
              ),
              widget.quickTextsList!,
              const SizedBox(
                height: 10,
              )
            ],
            Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
              // left icon
              if (widget.leftIcon != null) ...[
                IconButton(
                  onPressed: widget.leftIconButtonOnPressed,
                  icon: widget.leftIcon!,
                ),
              ],

              if (widget.leftCustomIcon != null) ...[
                widget.leftCustomIcon!,
              ],

              const SizedBox(
                width: 8,
              ),

              // Text field
              Expanded(
                child: MalouTextFormField(
                    controller: widget.controller,
                    maxLines: widget.maxLines,
                    enabled: widget.enabled,
                    initialValue: widget.initialValue,
                    maxLength: widget.maxLength,
                    hint: widget.hint,
                    label: widget.label,
                    showInfoIcon: widget.showInfoIcon,
                    showAiButtonInsideField: widget.showAiButtonInsideField,
                    showAiButtonNextToLabel: widget.showAiButtonNextToLabel,
                    showAiButtonOnlyIcon: widget.showAiButtonInsideField,
                    onAiPressed: widget.onAiPressed,
                    onChanged: widget.onChanged,
                    style: widget.style,
                    tooltipMessage: widget.tooltipMessage,
                    showBorders: widget.showBorders,
                    validator: widget.validator,
                    keyboardType: widget.keyboardType,
                    minLines: widget.minLines,
                    onTap: widget.onTap,
                    remainingAICredits: widget.remainingAICredits,
                    contentPadding: const EdgeInsets.all(10),
                    keepKeyboardOpenOnTapOutside:
                        widget.keepKeyboardOpenOnTapOutside,
                    onAiProcessing: widget.onAiProcessing ?? false,

                    scrollController: scrollController),
              ),

              const SizedBox(
                width: 8,
              ),

              // Submit button
              PrimaryIconButtonContainer(
                  disabled: widget.onSubmit != null ? false : true,
                  height: 50,
                  width: 50,
                  child: IconButton(
                    iconSize: 20,
                    disabledColor: Colors.white,
                    onPressed: widget.onSubmit,
                    icon: const Icon(
                      MalouIcons.send,
                    ),
                  ))
            ]),
          ],
        ));
  }
}
