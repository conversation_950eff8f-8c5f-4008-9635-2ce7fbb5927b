import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:malou/shared/models/message.model.dart';
import 'package:malou/shared/widgets/image/cached_image.widget.dart';
import 'package:malou/shared/widgets/pages/medias_preview.widget.dart';

class FullscreenCarouselSlider extends StatelessWidget {
  final List<Attachment> attachments;
  final int initialPage;

  const FullscreenCarouselSlider(
      {super.key, required this.attachments, this.initialPage = 0});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Builder(
        builder: (context) {
          final double height = MediaQuery.of(context).size.height;
          return SizedBox(
            height: height,
            child: CarouselSlider(
              options: CarouselOptions(
                aspectRatio: 1.0,
                enlargeCenterPage: true,
                enableInfiniteScroll: false,
                initialPage: initialPage,
                // autoPlay: false,
              ),
              items: attachments.map((attachment) {
                if (attachment.type == 'image' || attachment.type == 'photo') {
                  return CachedImage(url: attachment.url, height: height);
                }
                return VideoMedia(url: attachment.url);
              }).toList(),
            ),
          );
        },
      ),
    );
  }
}
