import 'dart:io';

import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:malou/shared/widgets/appbar/appbar_back.widget.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/buttons/styles.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:malou/generated/l10n.dart';

class CameraPreviewPage extends StatefulWidget {
  final Function(XFile file) onSelectImage;
  const CameraPreviewPage({
    super.key,
    required this.onSelectImage,
  });

  @override
  State<CameraPreviewPage> createState() => _CameraPreviewPageState();
}

class _CameraPreviewPageState extends State<CameraPreviewPage> {
  late CameraDescription camera;
  late CameraController controller;
  late Future<void> _initializeControllerFuture;
  bool cameraInitialized = false;

  @override
  void didChangeDependencies() {
    availableCameras().then((value) {
      if (value.isEmpty) {
        return;
      }
      camera = value.first;
      controller = CameraController(camera, ResolutionPreset.high);
      _initializeControllerFuture = controller.initialize();
      setState(() {
        cameraInitialized = true;
      });
    });

    super.didChangeDependencies();
  }

  @override
  void dispose() {
    // Dispose of the controller when the widget is disposed.
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppBarArrowBack(),
      body: SizedBox(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          child: !cameraInitialized
              ? const CircularSpinner(
                  size: 30,
                )
              : FutureBuilder<void>(
                  future: _initializeControllerFuture,
                  builder: (context, snapshot) {
                    if (snapshot.hasError) {
                      return Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            IllustrationsPathResolver.resolve(
                                Illustrations.icecream),
                            height: 80,
                          ),
                          const SizedBox(height: 20),
                          SizedBox(
                            width: 250,
                            child: Text(
                              Translation.of(context).cameraNoAccessTitle,
                              style: MalouTextStyles(context)
                                  .text13Semibold
                                  .copyWith(color: MalouColors.textTitle),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          SizedBox(
                            width: 300,
                            child: Text(
                              Translation.of(context).cameraNoAccessDescription,
                              style: MalouTextStyles(context).text12,
                              textAlign: TextAlign.center,
                            ),
                          ),
                          TextButton(
                              onPressed: () => PhotoManager.openSetting(),
                              child: Text(
                                  Translation.of(context).authorizeCamera,
                                  style: MalouTextStyles(context)
                                      .text12Semibold
                                      .copyWith(color: MalouColors.primary)))
                        ],
                      );
                    }
                    if (snapshot.connectionState == ConnectionState.done) {
                      // If the Future is complete, display the preview.
                      return CameraPreview(controller);
                    } else {
                      // Otherwise, display a loading indicator.
                      return const Center(child: CircularProgressIndicator());
                    }
                  },
                )),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: GestureDetector(
        onTap: () async {
          try {
            // Ensure that the camera is initialized.
            await _initializeControllerFuture;

            // Attempt to take a picture and get the file `image`
            // where it was saved.
            final image = await controller.takePicture();
            // ignore: use_build_context_synchronously
            Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                    builder: (context) => PicturePreviewPage(
                        imageFile: image,
                        onSelectImage: widget.onSelectImage)));
          } catch (e) {
            rethrow;
          }
        },
        child: Container(
          padding: const EdgeInsets.all(4.0),
          width: 70,
          height: 70,
          decoration: BoxDecoration(
            color: Colors.transparent,
            shape: BoxShape.circle,
            border: Border.all(
              color: Colors.white,
              width: 2,
            ),
          ),
          child: Container(
            width: 60,
            height: 60,
            decoration: const BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
          ),
        ),
      ),
    );
  }
}

class PicturePreviewPage extends StatelessWidget {
  final Function(XFile file) onSelectImage;
  final XFile imageFile;
  const PicturePreviewPage(
      {super.key, required this.imageFile, required this.onSelectImage});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppBarArrowBack(),
      body: Stack(
        children: [
          SizedBox(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            child: Image.file(
              File(imageFile.path),
              fit: BoxFit.cover,
            ),
          ),
          Positioned(
              bottom: 50,
              right: 20,
              height: 60,
              width: 60,
              child: PrimaryIconButtonContainer(
                  isCircular: true,
                  child: IconButton(
                    onPressed: () {
                      onSelectImage(imageFile);
                      Navigator.pop(context, imageFile);
                    },
                    icon: const Icon(
                      Icons.check,
                    ),
                  )))
        ],
      ),
    );
  }
}
