import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:malou/shared/widgets/appbar/appbar_back.widget.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/models/message.model.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/widgets/pages/carousel_slider.widget.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';
import 'package:video_player/video_player.dart';

class MediasPreviewPage extends StatelessWidget {
  final List<Attachment> attachments;
  final int initialPage;
  final String tag;
  const MediasPreviewPage(
      {super.key,
      required this.attachments,
      required this.tag,
      this.initialPage = 0});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: const AppBarArrowBack(
          centerTitle: false,
          title: 'Aperçu',
        ),
        body: Builder(
          builder: (context) {
            if (attachments.length == 1) {
              if (attachments.first.type == 'image' ||
                  attachments.first.type == 'photo') {
                return Hero(
                  tag: tag,
                  child: Container(
                    width: double.infinity,
                    height: double.infinity,
                    decoration: BoxDecoration(
                        image: DecorationImage(
                            image: CachedNetworkImageProvider(
                                attachments.first.url),
                            fit: BoxFit.contain)),
                  ),
                );
              } else {
                return VideoMedia(url: attachments.first.url);
              }
            } else {
              return FullscreenCarouselSlider(
                  attachments: attachments, initialPage: initialPage);
            }
          },
        ));
  }
}

class VideoMedia extends StatefulWidget {
  final String url;
  final bool autoplay;
  final bool displayControls;
  final BorderRadius borderRadius;
  final bool displayPlayPauseButton;
  final bool displayMuteButton;
  final bool displayLoader;
  const VideoMedia(
      {super.key,
      required this.url,
      this.autoplay = true,
      this.displayControls = true,
      this.borderRadius = const BorderRadius.all(Radius.circular(0)),
      this.displayMuteButton = false,
      this.displayPlayPauseButton = false,
      this.displayLoader = false});

  @override
  State<VideoMedia> createState() => _VideoMediaState();
}

class _VideoMediaState extends State<VideoMedia> {
  late VideoPlayerController _controller;
  bool isLoading = true;
  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.networkUrl(Uri.parse(widget.url))
      ..initialize().then((_) {
        setState(() {
          isLoading = false;
          if (widget.autoplay) {
            _controller.setLooping(true);

            _controller.play();
          }
        });
      }).catchError((error) {
        isLoading = false;
      });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        GestureDetector(
          onTap: () {
            if (widget.displayControls || widget.displayPlayPauseButton) {
              setState(() {
                if (_controller.value.isPlaying) {
                  _controller.pause();
                } else {
                  _controller.play();
                }
              });
            }
          },
          child: ClipRRect(
            borderRadius: widget.borderRadius,
            child: SizedBox(
              width: double.infinity,
              height: double.infinity,
              child: isLoading
                  ? const Center(
                      child: CircularSpinner(
                        size: 50,
                        color: MalouColors.primary,
                      ),
                    )
                  : VideoPlayer(_controller),
            ),
          ),
        ),
        if (!_controller.value.isPlaying &&
            (widget.displayControls || widget.displayPlayPauseButton) &&
            !isLoading)
          Center(
            child: Container(
              width: 80,
              decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.4), shape: BoxShape.circle),
              child: Center(
                child: IconButton(
                  padding: EdgeInsets.zero,
                  icon: const Icon(
                    MalouIcons.play,
                    color: Colors.white,
                    size: 50,
                  ),
                  onPressed: () {
                    setState(() {
                      _controller.play();
                    });
                  },
                ),
              ),
            ),
          ),
        if (widget.displayControls && !isLoading)
          Positioned(
            bottom: 0,
            child: Column(
              children: [
                SizedBox(
                    width: MediaQuery.of(context).size.width,
                    height: 5,
                    child: VideoProgressIndicator(_controller,
                        padding: EdgeInsets.zero,
                        colors: VideoProgressColors(
                          backgroundColor: Colors.white.withOpacity(0.5),
                          bufferedColor: Colors.white.withOpacity(0.5),
                          playedColor: MalouColors.primary,
                        ),
                        allowScrubbing: true)),
                Container(
                  width: MediaQuery.of(context).size.width,
                  height: 50,
                  color: Colors.black.withOpacity(0.5),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        '${_controller.value.position.toString().split('.')[0]} / ${_controller.value.duration.toString().split('.')[0]}',
                        style: const TextStyle(color: Colors.white),
                      ),
                      IconButton(
                        icon: Icon(
                          _controller.value.isPlaying
                              ? Icons.pause
                              : MalouIcons.play,
                          color: Colors.white,
                          size: 20,
                        ),
                        onPressed: () {
                          _controller.value.isPlaying
                              ? _controller.pause()
                              : _controller.play();
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  @override
  void dispose() {
    super.dispose();
    _controller.dispose();
  }
}
