import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/services/templates/templates.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:provider/provider.dart';

class ListPicker extends StatefulWidget {
  final List<String> data;
  final String title;
  const ListPicker({super.key, required this.data, required this.title});

  @override
  State<ListPicker> createState() => _ListPickerState();
}

class _ListPickerState extends State<ListPicker> {
  @override
  Widget build(BuildContext context) {
    return Consumer<TemplatesProvider>(builder: (context, provider, child) {
      return Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.bookmarks_outlined,
                    size: 30,
                    color: MalouColors.primary,
                  ),
                  const SizedBox(width: 10),
                  Text(
                    widget.title,
                    style: MalouTextStyles(context).text20Bold,
                  ),
                ],
              ),
              const SizedBox(
                height: 30,
              ),
              if (widget.data.isEmpty) ...[
                const ListNoResults(),
              ] else ...[
                for (var element in widget.data)
                    Container(
                      margin: const EdgeInsets.only(bottom: 5),
                      decoration: BoxDecoration(
                          border: Border.all(
                              color: MalouColors.backgroundDark, width: 1),
                          borderRadius: BorderRadius.circular(10)),
                      child: InkWell(
                        onTap: () => Navigator.pop(context, element),
                        child: Row(
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(20),
                              child: Text(element, style: MalouTextStyles(context).text15))
                          ],
                        ),
                      ),
                    ),
              ]
            ],
          ));
    });
  }
}

class ListNoResults extends StatelessWidget {
  const ListNoResults({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 300,
      child: Column(
        children: [
          Image.asset(
            IllustrationsPathResolver.resolve(Illustrations.cook),
            height: 80,
          ),
          const SizedBox(height: 20),
          Text(Translation.of(context).noResults,
              style: MalouTextStyles(context)
                  .text13Bold
                  .copyWith(color: MalouColors.textTitle)),
          const SizedBox(height: 10),
        ],
      ),
    );
  }
}
