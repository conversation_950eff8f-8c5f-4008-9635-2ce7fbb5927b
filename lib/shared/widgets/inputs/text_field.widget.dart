import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';

class MalouTextFormField extends StatelessWidget {
  final TextEditingController? controller;
  final int? maxLines;
  final bool? enabled;
  final String? initialValue;
  final int? maxLength;
  final String? hint;
  final String? label;
  final bool showInfoIcon;
  final bool showAiButtonInsideField;
  final bool showAiButtonNextToLabel;
  final bool showAiButtonOnlyIcon;
  final TextStyle? style;
  final void Function(String)? onChanged;
  final void Function()? onAiPressed;
  final String? tooltipMessage;
  final bool showBorders;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final int? minLines;
  final void Function()? onTap;
  final EdgeInsetsGeometry? contentPadding;
  final List<TextInputFormatter>? inputFormatters;
  final bool onAiProcessing;
  final int? remainingAICredits;
  final ScrollController? scrollController;
  final Widget? prefixIcon;
  final bool? autocorrect;
  final TextCapitalization? textCapitalization;
  final bool? showAiProcessingSpinner;
  final bool keepKeyboardOpenOnTapOutside;
  const MalouTextFormField(
      {super.key,
      this.controller,
      this.maxLines = 1,
      this.enabled = true,
      this.initialValue,
      this.maxLength,
      this.hint,
      this.label,
      this.showInfoIcon = false,
      this.showAiButtonInsideField = false,
      this.showAiButtonNextToLabel = false,
      this.showAiButtonOnlyIcon = false,
      this.onAiPressed,
      this.onChanged,
      this.style,
      this.tooltipMessage,
      this.showBorders = false,
      this.validator,
      this.keyboardType,
      this.minLines,
      this.onTap,
      this.contentPadding,
      this.inputFormatters,
      this.remainingAICredits,
      this.onAiProcessing = false,
      this.prefixIcon,
      this.scrollController,
      this.textCapitalization,
      this.autocorrect,
      this.keepKeyboardOpenOnTapOutside = false,
      this.showAiProcessingSpinner});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (label != null) ...[
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    label ?? '',
                    style: MalouTextStyles(context)
                        .text12Bold
                        .copyWith(color: MalouColors.textTitle),
                  ),
                  if (showAiButtonNextToLabel)
                    InkWell(
                      onTap: !onAiProcessing ? onAiPressed : null,
                      child: showAiProcessingSpinner == true
                          ? const CircularSpinner(
                              size: 20,
                              color: MalouColors.purpleAccent,
                            )
                          : Icon(MalouIcons.ia,
                              size: 26,
                              color: !onAiProcessing
                                  ? MalouColors.purpleAccent
                                  : MalouColors.purpleAccent.withOpacity(0.5)),
                    )
                ],
              ),
            ],
            TextFormField(
              onTapOutside: (event) => keepKeyboardOpenOnTapOutside
                  ? null
                  : FocusScope.of(context).unfocus(),
              textCapitalization:
                  textCapitalization ?? TextCapitalization.sentences,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              onTap: onTap,
              autofocus: false,
              validator: validator,
              onChanged: onChanged,
              keyboardType: keyboardType,
              minLines: minLines,
              autocorrect: autocorrect ?? true,
              controller: controller,
              scrollController: scrollController,
              maxLines: maxLines,
              enabled: enabled,
              initialValue: initialValue,
              inputFormatters: inputFormatters,
              maxLength: maxLength,
              readOnly: onAiProcessing,
              style: style ??
                  MalouTextStyles(context).text12.copyWith(
                        color: MalouColors.textTitle,
                      ),
              decoration: InputDecoration(
                  errorStyle: MalouTextStyles(context).text12.copyWith(
                        color: MalouColors.error,
                      ),
                  errorMaxLines: 1,
                  hintText: !onAiProcessing ? hint : '',
                  hintStyle: MalouTextStyles(context).text12Italic,
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  contentPadding: contentPadding ?? EdgeInsets.zero,
                  prefix: prefixIcon != null
                      ? Padding(
                          padding: const EdgeInsets.only(right: 10),
                          child: prefixIcon)
                      : null,
                  suffixIcon: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (showAiButtonInsideField) ...[
                        TextButton(
                          onPressed: !onAiProcessing ? onAiPressed : null,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(MalouIcons.ia,
                                  size: 24,
                                  color: !onAiProcessing
                                      ? MalouColors.purpleAccent
                                      : MalouColors.purpleAccent
                                          .withOpacity(0.5)),
                              const SizedBox(width: 10),
                              if (!showAiButtonOnlyIcon)
                                Text(Translation.of(context).writePostWithAi,
                                    style: MalouTextStyles(context)
                                        .text12Semibold
                                        .copyWith(
                                            color: !onAiProcessing
                                                ? MalouColors.purpleAccent
                                                : MalouColors.purpleAccent
                                                    .withOpacity(0.5)))
                            ],
                          ),
                        )
                      ],
                      if (showInfoIcon) ...[
                        Tooltip(
                          textStyle: MalouTextStyles(context).text10.copyWith(
                                color: Colors.white,
                              ),
                          triggerMode: TooltipTriggerMode.tap,
                          decoration: BoxDecoration(
                            color: MalouColors.textTitle,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          message: tooltipMessage ?? '',
                          child: const Icon(
                            Icons.info_outline,
                            size: 25,
                            color: MalouColors.primary,
                          ),
                        )
                      ]
                    ],
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: BorderSide(
                        color: MalouColors.borderPrimary,
                        width: 1,
                        // better to do like this to keep a gap between the input and the label, otherwise the label is too close to the input
                        style:
                            showBorders ? BorderStyle.solid : BorderStyle.none),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: BorderSide(
                        color: MalouColors.error,
                        width: 1,
                        // better to do like this to keep a gap between the input and the label, otherwise the label is too close to the input
                        style:
                            showBorders ? BorderStyle.solid : BorderStyle.none),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: BorderSide(
                        color: MalouColors.borderPrimary,
                        width: 1,
                        // better to do like this to keep a gap between the input and the label, otherwise the label is too close to the input
                        style:
                            showBorders ? BorderStyle.solid : BorderStyle.none),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: BorderSide(
                        color: MalouColors.error,
                        width: 1,
                        // better to do like this to keep a gap between the input and the label, otherwise the label is too close to the input
                        style:
                            showBorders ? BorderStyle.solid : BorderStyle.none),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: BorderSide(
                        color: MalouColors.borderPrimary,
                        width: 1,
                        // better to do like this to keep a gap between the input and the label, otherwise the label is too close to the input
                        style:
                            showBorders ? BorderStyle.solid : BorderStyle.none),
                  )),
            ),
          ],
        ),
        if (onAiProcessing) ...[
          Positioned(
            left: 10,
            top: 2,
            child: AnimatedTextKit(
              animatedTexts: [
                RotateAnimatedText(Translation.of(context).processing,
                    duration: const Duration(milliseconds: 800),
                    textStyle: MalouTextStyles(context)
                        .text12Bold
                        .copyWith(color: MalouColors.textTitle)),
                RotateAnimatedText(Translation.of(context).processing,
                    duration: const Duration(milliseconds: 800),
                    textStyle: MalouTextStyles(context)
                        .text12Bold
                        .copyWith(color: MalouColors.textTitle)),
                RotateAnimatedText(
                  Translation.of(context).processing,
                  duration: const Duration(milliseconds: 800),
                  textStyle: MalouTextStyles(context)
                      .text12Bold
                      .copyWith(color: MalouColors.textTitle),
                ),
              ],
              repeatForever: true,
              isRepeatingAnimation: true,
              pause: const Duration(seconds: 0),
            ),
          )
        ]
      ],
    );
  }
}
