import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';
import 'package:rxdart/rxdart.dart';

class TextSearch extends StatefulWidget {
  final ValueChanged<String>? onChanged;
  final VoidCallback onClearText;
  final VoidCallback? onFocus;
  final TextEditingController controller;
  final String? hint;
  final BehaviorSubject<bool>? showLoader;
  const TextSearch({
    super.key,
    this.onChanged,
    required this.controller,
    required this.onClearText,
    this.hint,
    this.showLoader,
    this.onFocus,
  });

  @override
  State<TextSearch> createState() => _TextSearchState();
}

class _TextSearchState extends State<TextSearch> {
  BehaviorSubject<String> text$ = BehaviorSubject.seeded('');
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    widget.controller.removeListener(() {
      text$.add(widget.controller.value.text);
    });
    text$.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
        stream: widget.showLoader ?? text$,
        builder: (context, snapshot) {
          return TextField(
            onTap: widget.onFocus,
            onTapOutside: (_) => FocusScope.of(context).unfocus(),
            style: MalouTextStyles(context).text13Medium,
            controller: widget.controller,
            onChanged: widget.onChanged,
            decoration: InputDecoration(
                suffixIcon: widget.showLoader != null && snapshot.data == true
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularSpinner(
                            size: 20, color: MalouColors.primary))
                    : widget.controller.value.text.isEmpty
                        ? null
                        : IconButton(
                            splashRadius: 20,
                            onPressed: widget.onClearText,
                            icon: const Icon(MalouIcons.cross,
                                size: 18, color: MalouColors.primary)),
                fillColor: Colors.white,
                filled: true,
                hintText: widget.hint ?? Translation.of(context).search,
                hintStyle: MalouTextStyles(context).text13Italic,
                prefixIcon: const Icon(MalouIcons.search,
                    size: 22, color: MalouColors.textTitle),
                focusColor: MalouColors.backgroundDark,
                focusedBorder: const OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(10)),
                    borderSide: BorderSide(
                        color: MalouColors.backgroundDark, width: 1)),
                enabledBorder: const OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(10)),
                    borderSide: BorderSide(
                        color: MalouColors.backgroundDark, width: 1)),
                contentPadding: const EdgeInsets.all(5.0)),
          );
        });
  }
}
