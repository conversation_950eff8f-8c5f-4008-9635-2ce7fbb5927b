import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_medias/widgets/post_media_picker/widgets/widgets/widgets/video_media_square.widget.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/helpers/utils.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/services/medias/medias.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/buttons/styles.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:photo_manager_image_provider/photo_manager_image_provider.dart';
import 'package:provider/provider.dart';
import 'package:skeleton_loader/skeleton_loader.dart';

class MediaPickerModal extends StatefulWidget {
  final double maxSize;
  const MediaPickerModal({super.key, this.maxSize = 0.7});

  @override
  State<MediaPickerModal> createState() => _MediaPickerModalState();
}

class _MediaPickerModalState extends State<MediaPickerModal>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    _tabController = TabController(length: 2, vsync: this, initialIndex: 0);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TabBar(
          indicatorColor: MalouColors.primary,
          indicatorSize: TabBarIndicatorSize.tab,
          dividerColor: MalouColors.primary.withValues(alpha: 0.1),
          tabs: [
            Tab(
              child: Text(
                Translation.of(context).malouGallery,
                style: MalouTextStyles(context).text15Semibold,
              ),
            ),
            Tab(
              child: Text(
                Translation.of(context).myMedias,
                style: MalouTextStyles(context).text15Semibold,
              ),
            ),
          ],
          controller: _tabController,
        ),
        SizedBox(
          height: MediaQuery.of(context).size.height * widget.maxSize,
          child: TabBarView(
              controller: _tabController,
              viewportFraction: 1.0,
              children: const [
                MalouGalleryGrid(),
                DeviceMediaPicker(),
              ]),
        )
      ],
    );
  }
}

class MalouGalleryGrid extends StatefulWidget {
  const MalouGalleryGrid({
    super.key,
  });

  @override
  State<MalouGalleryGrid> createState() => _MalouGalleryGridState();
}

class _MalouGalleryGridState extends State<MalouGalleryGrid> {
  late MediasProvider _mediasProvider;
  PagingController<int, Media> pagingController =
      PagingController(firstPageKey: 0);

  List<Media> selectedMedias = [];

  updateMediasSelection(Media media) {
    setState(() {
      if (selectedMedias.contains(media)) {
        selectedMedias.remove(media);
      } else {
        selectedMedias.add(media);
      }
    });
  }

  @override
  void initState() {
    _mediasProvider = Provider.of<MediasProvider>(context, listen: false);
    pagingController.addPageRequestListener((pageKey) {
      _fetchPage(pageKey);
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      fit: StackFit.expand,
      children: [
        PagedGridView(
          shrinkWrap: true,
          pagingController: pagingController,
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 4,
            mainAxisSpacing: 0,
            crossAxisSpacing: 0,
          ),
          builderDelegate: PagedChildBuilderDelegate<Media>(
              noItemsFoundIndicatorBuilder: (context) => Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        IllustrationsPathResolver.resolve(Illustrations.cook),
                        height: 80,
                      ),
                      const SizedBox(height: 20),
                      SizedBox(
                        width: 200,
                        child: Text(
                          Translation.of(context).noMediaInGallery,
                          style: MalouTextStyles(context).text14Italic,
                        ),
                      )
                    ],
                  ),
              firstPageProgressIndicatorBuilder: (context) =>
                  SkeletonGridLoader(
                    items: 12,
                    highlightColor: MalouColors.borderPrimary,
                    baseColor: MalouColors.backgroundLight,
                    direction: SkeletonDirection.ltr,
                    itemsPerRow: 4,
                    mainAxisSpacing: 2,
                    crossAxisSpacing: 2,
                    builder: Container(),
                  ),
              itemBuilder: (context, item, index) => GestureDetector(
                  onTap: () => updateMediasSelection(item),
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      item.type == MediaType.photo.name
                          ? CachedNetworkImage(
                              imageUrl:
                                  item.getMediaUrl(size: MediaSize.igFit.name),
                              fit: BoxFit.cover,
                            )
                          : VideoMediaSquare(url: item.urls!['original']!),
                      if (selectedMedias.contains(item))
                        Container(
                          color: MalouColors.textTitle.withOpacity(0.5),
                          child: Center(
                              child: Text(
                            (selectedMedias.indexOf(item) + 1).toString(),
                            style: MalouTextStyles(context)
                                .text20Bold
                                .copyWith(color: Colors.white),
                          )),
                        ),
                    ],
                  ))),
        ),
        if (selectedMedias.isNotEmpty)
          Positioned(
              bottom: 50,
              right: 20,
              height: 60,
              width: 60,
              child: PrimaryIconButtonContainer(
                  isCircular: true,
                  child: IconButton(
                    onPressed: () {
                      Navigator.pop(context, selectedMedias);
                    },
                    icon: const Icon(Icons.check),
                  )))
      ],
    );
  }

  void _fetchPage(int pageKey) async {
    try {
      final newItems = await _mediasProvider.getRestaurantMedias(pageKey);
      final isLastPage = newItems.length < 10;
      if (isLastPage) {
        pagingController.appendLastPage(newItems);
      } else {
        final nextPageKey = pageKey + 1;
        pagingController.appendPage(newItems, nextPageKey);
      }
    } catch (error) {
      pagingController.error = error;
    }
  }
}

class DeviceMediaPicker extends StatefulWidget {
  const DeviceMediaPicker({
    super.key,
  });

  @override
  State<DeviceMediaPicker> createState() => _DeviceMediaPickerState();
}

class _DeviceMediaPickerState extends State<DeviceMediaPicker> {
  PermissionState _ps = PermissionState.denied;
  @override
  void initState() {
    PhotoManager.requestPermissionExtend().then((value) {
      setState(() {
        _ps = value;
      });
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return _ps == PermissionState.authorized || _ps == PermissionState.limited
        ? const DeviceMediaGrid()
        : Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                IllustrationsPathResolver.resolve(Illustrations.icecream),
                height: 80,
              ),
              const SizedBox(height: 20),
              SizedBox(
                width: 250,
                child: Text(
                  Translation.of(context).noAccessToMedias,
                  style: MalouTextStyles(context)
                      .text13Semibold
                      .copyWith(color: MalouColors.textTitle),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(
                height: 20,
              ),
              SizedBox(
                width: 300,
                child: Text(
                  Translation.of(context).mediasAccessDescription,
                  style: MalouTextStyles(context).text12,
                  textAlign: TextAlign.center,
                ),
              ),
              TextButton(
                  onPressed: () => PhotoManager.openSetting(),
                  child: Text(Translation.of(context).authorizeMedias,
                      style: MalouTextStyles(context)
                          .text12Semibold
                          .copyWith(color: MalouColors.primary)))
            ],
          );
  }
}

class DeviceMediaGrid extends StatefulWidget {
  const DeviceMediaGrid({super.key});

  @override
  State<DeviceMediaGrid> createState() => _DeviceMediaGridState();
}

class _DeviceMediaGridState extends State<DeviceMediaGrid> {
  ValueNotifier<List<AssetEntity>> selectedMedias = ValueNotifier([]);
  late PagingController<int, AssetEntity> _pagingController;
  final pageSize = 20;

  @override
  void initState() {
    _pagingController = PagingController(firstPageKey: 0);
    _pagingController.addPageRequestListener((pageKey) {
      _fetchPage(pageKey);
    });
    super.initState();
  }

  _fetchPage(int pageKey) async {
    try {
      final newItems = await _getDeviceMedias(pageKey);
      final isLastPage = newItems.length < pageSize;
      if (isLastPage) {
        _pagingController.appendLastPage(newItems);
      } else {
        final nextPageKey = pageKey + 1;
        _pagingController.appendPage(newItems, nextPageKey);
      }
    } catch (error) {
      _pagingController.error = error;
    }
  }

  Future<List<AssetEntity>> _getDeviceMedias(int pageKey) async {
    final List<AssetPathEntity> albums = await PhotoManager.getAssetPathList(
        filterOption: FilterOptionGroup()
          ..setOption(
              AssetType.video,
              const FilterOption(
                  durationConstraint: DurationConstraint(
                      min: Duration(seconds: 3),
                      max: Duration(minutes: 1, seconds: 30)))));
    final List<AssetEntity> assets = await albums[0]
        .getAssetListPaged(
          page: pageKey,
          size: pageSize,
        )
        .then((value) => value);

    return assets;
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        PagedGridView<int, AssetEntity>(
            shrinkWrap: true,
            pagingController: _pagingController,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              mainAxisSpacing: 2,
              crossAxisSpacing: 2,
            ),
            builderDelegate: PagedChildBuilderDelegate<AssetEntity>(
                noItemsFoundIndicatorBuilder: (context) => Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          IllustrationsPathResolver.resolve(Illustrations.cook),
                          height: 80,
                        ),
                        const SizedBox(height: 20),
                        SizedBox(
                          width: 200,
                          child: Text(
                            Translation.of(context).emptyDeviceMedias,
                            style: MalouTextStyles(context).text14Italic,
                            textAlign: TextAlign.center,
                          ),
                        )
                      ],
                    ),
                firstPageProgressIndicatorBuilder: (context) =>
                    SkeletonGridLoader(
                      items: 12,
                      highlightColor: MalouColors.borderPrimary,
                      baseColor: MalouColors.backgroundLight,
                      direction: SkeletonDirection.ltr,
                      itemsPerRow: 4,
                      mainAxisSpacing: 2,
                      crossAxisSpacing: 2,
                      builder: Container(),
                    ),
                itemBuilder: (context, item, index) => GestureDetector(
                    onTap: () => _updateMediasSelection(item),
                    child: Stack(
                      fit: StackFit.expand,
                      children: [
                        AssetEntityImage(
                          item,
                          fit: BoxFit.cover,
                          isOriginal: false,
                          thumbnailSize: const ThumbnailSize.square(150),
                          thumbnailFormat: ThumbnailFormat.jpeg,
                        ),
                        if (item.type == AssetType.video)
                          Align(
                            alignment: Alignment.bottomRight,
                            child: Container(
                              margin: const EdgeInsets.all(5),
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: Colors.black.withOpacity(0.5),
                              ),
                              child: Text(formatDuration(item.videoDuration),
                                  style: MalouTextStyles(context)
                                      .text10Semibold
                                      .copyWith(color: Colors.white)),
                            ),
                          ),
                        if (item.type == AssetType.video)
                          Align(
                              alignment: Alignment.center,
                              child: Icon(
                                MalouIcons.play,
                                color: Colors.white.withOpacity(0.8),
                                size: 25,
                              )),
                        ValueListenableBuilder<List<AssetEntity>>(
                          valueListenable: selectedMedias,
                          builder: (context, selectedItems, child) {
                            return selectedItems.contains(item)
                                ? Container(
                                    color:
                                        MalouColors.textTitle.withOpacity(0.5),
                                    child: const Center(
                                      child: Icon(
                                        Icons.check,
                                        color: Colors.white,
                                      ),
                                    ),
                                  )
                                : const SizedBox.shrink();
                          },
                        ),
                      ],
                    )))),
        ValueListenableBuilder<List<AssetEntity>>(
          valueListenable: selectedMedias,
          builder: (context, selectedItems, child) {
            return selectedItems.isNotEmpty
                ? Positioned(
                    bottom: 50,
                    right: 20,
                    height: 60,
                    width: 60,
                    child: PrimaryIconButtonContainer(
                        isCircular: true,
                        child: IconButton(
                          onPressed: () async {
                            // convert selected medias to Media model
                            List<Media> medias = [];
                            for (AssetEntity media in selectedItems) {
                              medias.add(Media(
                                  isDeviceMedia: true,
                                  category: '',
                                  userId: '',
                                  restaurantId: '',
                                  id: media.id,
                                  type: media.type == AssetType.image
                                      ? 'image'
                                      : MediaType.video.name,
                                  urls: {'original': ''},
                                  createdAt: DateTime.now(),
                                  updatedAt: DateTime.now(),
                                  format: '',
                                  postIds: [],
                                  sizes: {},
                                  socialId: '',
                                  assetEntity: media,
                                  tagIds: []));
                            }
                            // ignore: use_build_context_synchronously
                            context.pop(medias);
                          },
                          icon: const Icon(Icons.check),
                        )))
                : const SizedBox.shrink();
          },
        )
      ],
    );
  }

  void _updateMediasSelection(AssetEntity media) {
    selectedMedias.value = List.from(selectedMedias.value)..toggle(media);
  }
}

extension ListExtension<T> on List<T> {
  void toggle(T item) {
    contains(item) ? remove(item) : add(item);
  }
}
