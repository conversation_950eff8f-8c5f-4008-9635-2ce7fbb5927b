import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/services/messaging/messaging.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/review/review.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';


class BottomNavigation extends StatefulWidget {
  final RestaurantProvider restaurantProvider;
  final Function(int index) onItemTapped;
  const BottomNavigation(
      {super.key,
      required this.onItemTapped,
      required this.restaurantProvider});

  @override
  State<BottomNavigation> createState() => _BottomNavigationState();
}

class _BottomNavigationState extends State<BottomNavigation> {
  @override
  Widget build(BuildContext context) {
    return Consumer2<ReviewProvider, MessagingProvider>(
        builder: (context, reviewProvider, messagingProvider, child) {
      return Container(
        decoration: const BoxDecoration(
            borderRadius: BorderRadius.only(
                topRight: Radius.circular(20), topLeft: Radius.circular(20)),
            boxShadow: [
              MalouColors.boxShadow,
              MalouColors.boxShadow,
            ]),
        child: Theme(
          data: ThemeData(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
          ),
          child: StreamBuilder(
              stream: CombineLatestStream.list([
                reviewProvider.unAnswered,
                messagingProvider.unreadConversationsCount$
              ]),
              builder: (context, snapshot) {
                var reviewsCount = snapshot.data?[0] ?? 0;
                var messagesCount = snapshot.data?[1] ?? 0;
                return BottomNavigationBar(
                    type: BottomNavigationBarType.fixed,
                    selectedItemColor: MalouColors.defaultChipColor,
                    unselectedItemColor: MalouColors.purpleLight,
                    iconSize: 0,
                    showSelectedLabels: false,
                    selectedFontSize: 0,
                    unselectedFontSize: 0,
                    enableFeedback: false,
                    elevation: 0,
                    items: [
                      BottomNavigationBarItem(
                        label: '',
                        icon: InactiveIconContainer(
                          label: Translation.of(context).home,
                          icon: MalouIcons.home,
                        ),
                        activeIcon: ActiveIconContainer(
                            label: Translation.of(context).home,
                            icon: MalouIcons.home),
                      ),
                      if (!widget.restaurantProvider.currentRestaurant!
                          .isBrandBusiness()) ...[
                        BottomNavigationBarItem(
                          label: '',
                          activeIcon: ActiveIconContainer(
                              icon: MalouIcons.reviews,
                              label: Translation.of(context).reviews,
                              shouldShowBadge:
                                  snapshot.hasData ? reviewsCount > 0 : false),
                          icon: InactiveIconContainer(
                              icon: MalouIcons.reviews,
                              label: Translation.of(context).reviews,
                              shouldShowBadge:
                                  snapshot.hasData ? reviewsCount > 0 : false),
                        ),
                      ],
                      BottomNavigationBarItem(
                        label: '',
                        activeIcon: ActiveIconContainer(
                          icon: MalouIcons.convers,
                          label: Translation.of(context).messages,
                          shouldShowBadge: messagesCount > 0,
                        ),
                        icon: InactiveIconContainer(
                          icon: MalouIcons.convers,
                          label: Translation.of(context).messages,
                          shouldShowBadge: messagesCount > 0,
                        ),
                      ),
                      BottomNavigationBarItem(
                        label: '',
                        activeIcon: ActiveIconContainer(
                          icon: MalouIcons.post,
                          label: Translation.of(context).posts,
                          shouldShowBadge: false,
                        ),
                        icon: InactiveIconContainer(
                          icon: MalouIcons.post,
                          label: Translation.of(context).posts,
                          shouldShowBadge: false,
                        ),
                      )
                    ],
                    currentIndex:
                        widget.restaurantProvider.selectedBottomAppBarIndex,
                    onTap: widget.onItemTapped);
              }),
        ),
      );
    });
  }
}

class ActiveIconContainer extends StatelessWidget {
  final bool shouldShowBadge;
  final IconData icon;
  final String label;
  const ActiveIconContainer(
      {super.key,
      required this.icon,
      this.shouldShowBadge = false,
      required this.label});

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        SizedBox(
          width: 70,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Stack(
                clipBehavior: Clip.none,
                children: [
                  Center(
                    child: Icon(
                      icon,
                      color: MalouColors.primary,
                      size: 24,
                    ),
                  ),
                  !shouldShowBadge
                      ? Container()
                      : Positioned(
                          top: 0,
                          right: 20,
                          child: Container(
                              height: 12,
                              width: 12,
                              decoration: const BoxDecoration(
                                color: MalouColors.primary,
                                shape: BoxShape.circle,
                              ))),
                ],
              ),
              const SizedBox(
                height: 5,
              ),
              Text(
                label,
                style: MalouTextStyles(context)
                    .text10Medium
                    .copyWith(color: MalouColors.primary),
              )
            ],
          ),
        ),
      ],
    );
  }
}

class InactiveIconContainer extends StatelessWidget {
  final bool shouldShowBadge;
  final IconData icon;
  final String label;
  const InactiveIconContainer(
      {super.key,
      required this.icon,
      this.shouldShowBadge = false,
      required this.label});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(top: 0),
      decoration: const BoxDecoration(
        // add border top
        border: Border(
          top: BorderSide(
            color: Colors.transparent,
            width: 3.0,
          ),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Stack(clipBehavior: Clip.none, children: [
            Center(
              child: Icon(
                icon,
                color: MalouColors.purpleLight,
                size: 24,
              ),
            ),
            !shouldShowBadge
                ? Container()
                : Center(
                    child: Container(
                        height: 12,
                        width: 12,
                        margin: const EdgeInsets.only(left: 20),
                        decoration: const BoxDecoration(
                          color: MalouColors.primary,
                          shape: BoxShape.circle,
                        )),
                  )
          ]),
          const SizedBox(
            height: 5,
          ),
          Text(
            label,
            style: MalouTextStyles(context).text10Medium.copyWith(
                  color: MalouColors.purpleLight,
                ),
          )
        ],
      ),
    );
  }
}
