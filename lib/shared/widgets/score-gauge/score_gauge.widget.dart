import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

class ScoreGauge extends StatelessWidget {
  final num score;
  final bool displayLabel;
  final bool onWhiteBackground;
  const ScoreGauge(
      {super.key,
      required this.score,
      this.displayLabel = true,
      this.onWhiteBackground = false});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 22,
      child: Row(
        children: [
          Text(displayLabel ? Translation.of(context).keywords_score : "",
              style: MalouTextStyles(context).text12Medium),
          const SizedBox(width: 5),
          ListView.separated(
              scrollDirection: Axis.horizontal,
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: 5,
              itemBuilder: (context, index) {
                return Container(
                  width: 9,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: index + 1 <= score
                          ? score < 2
                              ? MalouColors.pinkAccent
                              : score <= 3
                                  ? MalouColors.warning
                                  : MalouColors.success
                          : MalouColors.backgroundDark,
                      width: 1,
                    ),
                    color: index + 1 <= score
                        ? score < 2
                            ? MalouColors.pinkAccent
                            : score <= 3
                                ? MalouColors.warning
                                : MalouColors.success
                        : onWhiteBackground
                            ? MalouColors.backgroundDark
                            : Colors.white,
                  ),
                );
              },
              separatorBuilder: (context, index) => const SizedBox(
                    width: 1.5,
                  )),
        ],
      ),
    );
  }
}
