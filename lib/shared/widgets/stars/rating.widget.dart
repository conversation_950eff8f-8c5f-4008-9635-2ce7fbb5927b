import 'package:flutter/material.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

class RatingStar extends StatelessWidget {
  final int stars;
  final double size;
  const RatingStar({super.key, required this.stars, this.size = 18});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: MalouColors.defaultChipColor,),
      alignment: Alignment.center,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
        child: Row(children: [
          Text(stars.toString(),
              style: MalouTextStyles(context)
                  .text15
                  .copyWith(color: MalouColors.primary)),
          Icon(
            MalouIcons.starOutline,
            color: MalouColors.primary,
            size: size,
          )
        ]),
      ),
    );
  }
}
