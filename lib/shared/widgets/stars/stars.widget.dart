import 'package:flutter/material.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/theme/theme_colors.dart';



class StarsWidget extends StatelessWidget {
  final int stars;
  final int starFilled;
  final double size;
  const StarsWidget({super.key, required this.stars, required this.starFilled, this.size = 20});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 20,
      child: ListView.separated(
        shrinkWrap: true,
        scrollDirection: Axis.horizontal,
        itemCount: stars,
        itemBuilder: (context, index) {
          return Icon(
            index < starFilled ? MalouIcons.starFull : MalouIcons.starOutline,
            color: index < starFilled ? MalouColors.starFilled : MalouColors.starUnfilled,
            size: size,
          );
        },
        separatorBuilder: (context, index) {
          return const SizedBox(width: 10);
        },
      ),
    );
  }
}