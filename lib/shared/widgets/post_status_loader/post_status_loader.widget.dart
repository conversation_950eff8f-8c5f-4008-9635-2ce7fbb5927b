import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';

class PostStatusLoader extends StatelessWidget {
  const PostStatusLoader({
    super.key,
    required this.size,
  });

  final Size size;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size.width,
      padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 20),
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: MalouColors.textTitle,
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Row(
        children: [
          const CircularSpinner(
            color: Colors.white,
            size: 20,
          ),
          const SizedBox(width: 20),
          Text(Translation.of(context).publishing,
              style: MalouTextStyles(context)
                  .text13Medium
                  .copyWith(color: Colors.white))
        ],
      ),
    );
  }
}
