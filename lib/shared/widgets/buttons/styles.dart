import 'package:flutter/material.dart';
import 'package:malou/shared/theme/theme_colors.dart';

class PrimaryButtonContainer extends StatelessWidget {
  final Widget child;
  final bool fullWidth;
  const PrimaryButtonContainer(
      {super.key, required this.child, this.fullWidth = false});

  @override
  Widget build(BuildContext context) {
    return fullWidth
        ? Container(
            width: double.infinity,
            decoration: BoxDecoration(
                gradient: MalouColors.gradientFiveReversed,
                borderRadius: BorderRadius.circular(15)),
            child: child,
          )
        : Container(
            decoration: BoxDecoration(
                gradient: MalouColors.gradientFiveReversed,
                borderRadius: BorderRadius.circular(15)),
            child: child,
          );
  }
}

class PrimaryIconButtonContainer extends StatelessWidget {
  final Widget child;
  final bool isCircular;
  final bool disabled;
  final double? width;
  final double? height;
  const PrimaryIconButtonContainer(
      {super.key,
      required this.child,
      this.isCircular = false,
      this.disabled = false, this.width, this.height});

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: disabled ? 0.5 : 1,
      child: Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
            gradient: MalouColors.gradientFiveReversed,
            borderRadius: isCircular
                ? BorderRadius.circular(100)
                : BorderRadius.circular(10)),
        child: child,
      ),
    );
  }
}

class SecondaryButtonContainer extends StatelessWidget {
  final Widget child;
  final bool isCircular;
  final Color? color;
  final bool fullWidth;
  final bool disabled;
  final Color borderColor;

  const SecondaryButtonContainer(
      {super.key,
      required this.child,
      this.isCircular = false,
      this.color,
      this.disabled = false,
      this.fullWidth = false,
      this.borderColor = MalouColors.backgroundDark});
  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: disabled ? 0.5 : 1,
      child: Container(
        width: fullWidth ? double.infinity : null,
        decoration: BoxDecoration(
            color: color ?? MalouColors.backgroundLight,
            border: Border.all(color: borderColor),
            borderRadius: isCircular
                ? BorderRadius.circular(100)
                : BorderRadius.circular(15)),
        child: child,
      ),
    );
  }
}

class ButtonPaddings {
  static const bigElevatedButtonPadding =
      EdgeInsets.symmetric(horizontal: 26, vertical: 18.5);

  static const bigIconButtonPadding = EdgeInsets.all(12);
}

class ToggleButton extends StatelessWidget {
  final bool isSelected;
  final Widget child;
  final Function() onTap;
  final Size size;
  const ToggleButton(
      {super.key,
      required this.isSelected,
      required this.child,
      required this.onTap,
      required this.size});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size.width,
        height: size.height,
        decoration: BoxDecoration(
            color: isSelected ? MalouColors.purple : MalouColors.backgroundDark,
            borderRadius: BorderRadius.circular(10)),
        child: Center(child: child),
      ),
    );
  }
}
