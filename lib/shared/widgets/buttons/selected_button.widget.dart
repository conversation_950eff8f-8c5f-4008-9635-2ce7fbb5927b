import 'package:flutter/material.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

class SelectedButton extends StatelessWidget {
  final bool selected;
  final String buttonText;
  final bool isLongText;
  final EdgeInsets margin;
  final Function()? onPressed;
  final Alignment alignment;

  const SelectedButton(
      {super.key,
      required this.selected,
      required this.buttonText,
      this.isLongText = false,
      this.margin = const EdgeInsets.all(0),
      this.alignment = Alignment.center,
      this.onPressed});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      width: double.infinity,
      height: isLongText ? null : 50,
      child: OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          alignment: alignment,
          backgroundColor:
              selected ? MalouColors.backgroundLight : Colors.white,
          side: selected
              ? const BorderSide(color: MalouColors.primary, width: 1)
              : const BorderSide(color: MalouColors.backgroundDark, width: 1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Padding(
          padding: isLongText
              ? const EdgeInsets.symmetric(vertical: 12.0)
              : const EdgeInsets.all(0),
          child: Text(buttonText,
              style: MalouTextStyles(context).text14Semibold.copyWith(
                  height: 1.8,
                  color: isLongText
                      ? MalouColors.textBody
                      : MalouColors.textTitle)),
        ),
      ),
    );
  }
}
