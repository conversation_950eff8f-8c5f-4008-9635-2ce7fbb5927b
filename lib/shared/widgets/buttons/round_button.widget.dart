import 'package:flutter/material.dart';
import 'package:malou/shared/theme/theme_colors.dart';

class RoundButton extends StatelessWidget {
  const RoundButton({
    super.key,
    required this.icon,
    required this.onPressed,
    this.width = 70,
    this.height = 70,
    this.isSubmitting = false,
    this.isFormValid = true,
  });

  final IconData icon;
  final void Function() onPressed;
  final bool isSubmitting;
  final bool isFormValid;
  final double width;
  final double height;

  @override
  Widget build(BuildContext context) {
      return Opacity(
          opacity: isFormValid && !isSubmitting ? 1 : 0.5,
          child: Container(
            height: height,
            width: width,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(100),
            ),
            child: IconButton(
              onPressed: !isFormValid || isSubmitting ? null : onPressed,
              icon: Icon(
                icon,
                size: width * 0.5,
                color: MalouColors.primary
                    .withOpacity(isFormValid && !isSubmitting ? 1 : 0.5),
              ),
              color: MalouColors.primary,
            ),
          )
      );
  }
}
