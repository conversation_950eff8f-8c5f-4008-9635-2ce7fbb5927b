import 'package:flutter/material.dart';
import 'package:malou/shared/theme/theme_colors.dart';

class AiButton extends StatelessWidget {
  final void Function()? onPressed;
  final Widget child;
  final EdgeInsetsGeometry? padding;
  const AiButton(
      {super.key, this.onPressed, required this.child, this.padding});

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: onPressed == null ? 0.5 : 1.0,
      child: FilledButton(
        style: FilledButton.styleFrom(
          disabledBackgroundColor: MalouColors.backgroundDark,
          backgroundColor: MalouColors.backgroundDark,
          disabledForegroundColor: MalouColors.purpleAccent,
          padding: padding ??
              const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          foregroundColor: MalouColors.purpleAccent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(5),
          ),
          minimumSize: const Size(0, 0),
        ),
        onPressed: onPressed,
        child: child,
      ),
    );
  }
}
