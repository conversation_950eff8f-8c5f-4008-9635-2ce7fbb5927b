import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_svg/svg.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';

class CachedImage extends StatelessWidget {
  const CachedImage({
    super.key,
    required this.url,
    this.radius = BorderRadius.zero,
    this.height = 100,
    this.width = double.infinity,
    this.fit = BoxFit.cover,
    this.fallbackUrl,
  });

  final String? url;
  final double height;
  final double width;
  final BorderRadius radius;
  final BoxFit fit;
  final String? fallbackUrl;

  @override
  Widget build(BuildContext context) {
    final isValidUrl = url != null && url!.trim().isNotEmpty;
    return SizedBox(
      height: height,
      width: width,
      child: isValidUrl
          ? ClipRRect(
              borderRadius: radius,
              child: CachedNetworkImage(
                imageUrl: url!,
                height: height,
                width: width,
                fit: fit,
                alignment: Alignment.center,
                progressIndicatorBuilder: (context, url, progress) =>
                    const Center(
                  child: CircularSpinner(
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                errorWidget: (context, url, error) => fallbackUrl == null
                    ? Image.asset(
                        fit: BoxFit.cover,
                        IllustrationsPathResolver.resolve(
                            Illustrations.defaultMalou))
                    : SvgPicture.network(
                        fallbackUrl!,
                        fit: BoxFit.cover,
                      ),
              ))
          : ClipRRect(
              borderRadius: radius,
              child: Image.asset(
                IllustrationsPathResolver.resolve(Illustrations.defaultMalou),
                fit: BoxFit.cover,
              ),
            ),
    );
  }
}
