import 'package:flutter/material.dart';
import 'package:malou/shared/models/quick_text.model.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

class QuickTextsList extends StatelessWidget {
  const QuickTextsList(
      {super.key,
      required this.quickTexts,
      required TextEditingController textEditingController})
      : _textEditingController = textEditingController;

  final List<QuickText> quickTexts;
  final TextEditingController _textEditingController;

  void addTextToInput(String textToAdd) {
    final text = _textEditingController.text;
    final selection = _textEditingController.selection;

    String newText = '';
    String previousChar = selection.start > 0 ? text[selection.start - 1] : '';
    String nextChar = selection.end >= 0 && selection.end < text.length
        ? text[selection.end]
        : '';

    if (previousChar.isNotEmpty && previousChar != ' ') {
      textToAdd = " $textToAdd";
    }
    if (nextChar.isNotEmpty && nextChar != ' ') {
      textToAdd = "$textToAdd ";
    }

    if (selection.start >= 0 && selection.end >= 0) {
      newText = text.replaceRange(
        selection.start,
        selection.end,
        textToAdd,
      );
    } else {
      newText = "$text$textToAdd";
    }

    final newSelection = TextSelection.collapsed(
      offset: selection.start + textToAdd.length,
    );

    _textEditingController.value = _textEditingController.value.copyWith(
      text: newText,
      selection: newSelection,
      composing: TextRange.empty,
    );

    _textEditingController.selection = newSelection;
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40,
      child: ListView.builder(
        padding: const EdgeInsets.only(left: 12),
        scrollDirection: Axis.horizontal,
        itemCount: quickTexts.length,
        itemBuilder: (BuildContext context, int index) {
          // quick text chips
          return GestureDetector(
            onTap: () => addTextToInput(quickTexts[index].content),
            child: Container(
              margin: const EdgeInsets.only(right: 10),
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              decoration: BoxDecoration(
                  color: MalouColors.defaultChipColor,
                  borderRadius: BorderRadius.circular(4)),
              child: Center(
                child: Text(
                  quickTexts[index].displayKey,
                  style: MalouTextStyles(context).text13,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
