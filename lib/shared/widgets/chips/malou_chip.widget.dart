import 'package:flutter/material.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/helpers/malou_icons.dart';

class MalouChip extends StatelessWidget {
  const MalouChip(
      {super.key,
      this.color = MalouColors.backgroundDark,
      required this.text,
      this.withDefaultMargin = true,
      this.onClosed});

  final Color color;
  final String text;
  final void Function()? onClosed;
  final bool withDefaultMargin;
  @override
  Widget build(BuildContext context) {
    return Chip(
        deleteIcon: const Icon(Icons.cancel_outlined,
            size: 15, color: MalouColors.primary),
        onDeleted: onClosed,
        side: BorderSide.none,
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 7),
        materialTapTargetSize: withDefaultMargin
            ? MaterialTapTargetSize.padded
            : MaterialTapTargetSize.shrinkWrap,
        backgroundColor: color,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
        label: Text(
          text,
          style: MalouTextStyles(context).text12,
        ));
  }
}

class MalouChipSelectable extends StatelessWidget {
  const MalouChipSelectable({
    super.key,
    this.color = Colors.white,
    this.selectedBackgroundColor = MalouColors.defaultChipColor,
    this.selectedForegroundColor = Colors.white,
    this.selected = false,
    required this.text,
    this.onTap,
  });

  final Color color;
  final Color selectedBackgroundColor;
  final Color selectedForegroundColor;
  final bool selected;
  final String text;
  final Function? onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onTap?.call(),
      child: Chip(
          side: BorderSide(
              color: selected
                  ? selectedBackgroundColor
                  : MalouColors.borderSecondary),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
          backgroundColor: selected ? selectedBackgroundColor : color,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          label: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (selected) ...[
                const Icon(
                  MalouIcons.check,
                  color: MalouColors.success,
                  size: 20,
                )
              ] else...[
              const Icon(
                Icons.add_circle_outline,
                color: MalouColors.primary,
                size: 20,
              ),
              ],
              const SizedBox(
                width: 10,
              ),
              Text(text,
                  style: MalouTextStyles(context).text12Bold.copyWith(
                      color: MalouColors.textBody)),
            ],
          )),
    );
  }
}
