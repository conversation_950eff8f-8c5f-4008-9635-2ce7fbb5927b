import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/buttons/styles.dart';
import 'package:malou/shared/widgets/inputs/text_field.widget.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';
import 'package:provider/provider.dart';

class TranslationLanguageModal extends StatefulWidget {
  final Function(String selectedLang) onValidate;
  const TranslationLanguageModal({super.key, required this.onValidate});

  @override
  State<TranslationLanguageModal> createState() =>
      _TranslationLanguageModalState();
}

class _TranslationLanguageModalState extends State<TranslationLanguageModal> {
  String? selectedLang = AppLocale.fr.name;
  bool loading = false;
  final TextEditingController controller = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        MalouTextFormField(
          onTap: () => setState(() {
            if (controller.text.isNotEmpty) return;
            selectedLang = null;
          }),
          showBorders: true,
          controller: controller,
          onChanged: (value) => setState(() {
            selectedLang = value;
          }),
          hint: Translation.of(context).typeYourLang,
          contentPadding: const EdgeInsets.symmetric(horizontal: 20),
        ),
        const SizedBox(
          height: 20,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
          child: Column(
            children: context
                .read<LanguageProvider>()
                .getAvailableLanguages(context)
                .entries
                .map((e) => ListTile(
                      onTap: () {
                        setState(() {
                          controller.clear();
                          if (selectedLang == e.key.name) {
                            selectedLang = null;
                            return;
                          }
                          selectedLang = e.key.name;
                        });
                      },
                      title: Text(e.value,
                          style: MalouTextStyles(context).text15Semibold),
                      trailing: selectedLang == e.key.name
                          ? const Icon(Icons.check,
                              color: MalouColors.primary, size: 20.0)
                          : const SizedBox(),
                    ))
                .toList(),
          ),
        ),
        const SizedBox(
          height: 20,
        ),
        Opacity(
          opacity: loading || selectedLang == null ? 0.5 : 1.0,
          child: PrimaryButtonContainer(
              fullWidth: true,
              child: ElevatedButton(
                onPressed: loading || selectedLang == null
                    ? null
                    : () async {
                        setState(() {
                          loading = true;
                        });
                        await widget.onValidate(selectedLang!);
                        setState(() {
                          loading = false;
                        });
                      },
                child: loading
                    ? const CircularSpinner(
                        color: Colors.white,
                        size: 20.0,
                      )
                    : Text(Translation.of(context).validate),
              )),
        )
      ],
    );
  }
}
