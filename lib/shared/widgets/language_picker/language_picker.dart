import 'package:flutter/material.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

class LanguagePicker extends StatelessWidget {
  final AppLocale language;
  final Function(AppLocale) onClickLanguage;
  final LanguageProvider languageProvider;

  const LanguagePicker(
      {super.key,
      required this.onClickLanguage,
      required this.language,
      required this.languageProvider});

  _createBody(Map<AppLocale, String> mappedLanguages, BuildContext context) {
    return Column(
      children: [
        for (var entry in mappedLanguages.entries)
          Row(
            children: [
              Expanded(
                child: _languageItem(entry.key, context, entry.value),
              ),
              const SizedBox(height: 5),
            ],
          ),
      ],
    );
  }

  _languageItem(AppLocale langKey, BuildContext context, String langValue) {
    return GestureDetector(
      onTap: () async {
        onClickLanguage(langKey);
      },
      child: Container(
        height: 48,
        color: Colors.transparent,
        width: double.infinity,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(langValue,
                style: MalouTextStyles(context)
                    .text15Semibold
                    .copyWith(color: MalouColors.textBody)),
            SizedBox(
              child: language == langKey
                  ? const Icon(
                      MalouIcons.check,
                      color: MalouColors.primary,
                    )
                  : null,
            )
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final mappedLanguages = languageProvider.getAvailableLanguages(context);

    return _createBody(mappedLanguages, context);
  }
}
