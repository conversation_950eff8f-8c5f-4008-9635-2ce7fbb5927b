import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/toasts/toasts.service.dart';

enum ToastType { error, success }

class Toast extends StatefulWidget {
  final String toastId = UniqueKey().toString();
  final ToastType type;
  final String message;
  final Illustrations illustration;
  Toast(
      {Key? key,
      this.message = '',
      this.illustration = Illustrations.icecream,
      required this.type})
      : super(key: key);

  @override
  State<Toast> createState() => _ToastState();
}

class _ToastState extends State<Toast> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  late Illustrations illustration;

  bool isDissmied = false;

  @override
  void initState() {
    illustration = widget.illustration;
    if (widget.type == ToastType.success &&
        illustration == Illustrations.icecream) {
      illustration = Illustrations.gogoolOk;
    }
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 3500),
    );

    _animation = Tween<double>(begin: 1, end: 0)
        .animate(CurvedAnimation(parent: _controller, curve: Curves.linear));

    _controller.forward();

    Future.delayed(const Duration(milliseconds: 3500), () {
      // close the dialog
      if (!isDissmied) {
        ToastsService.instance.remove(widget);
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    return Dismissible(
      key: UniqueKey(),
      direction: DismissDirection.vertical,
      dismissThresholds: const {
        DismissDirection.vertical: 1,
      },
      movementDuration: const Duration(milliseconds: 500),
      onDismissed: (direction) {
        setState(() {
          isDissmied = true;
        });
        ToastsService.instance.remove(widget);
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            width: MediaQuery.of(context).size.width - 40,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              boxShadow: const [MalouColors.boxShadow],
            ),
            child: Column(
              children: [
                Stack(
                  children: [
                    Container(
                        height: 5,
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                          color: MalouColors.backgroundDark,
                          borderRadius: BorderRadius.circular(10),
                        )),
                    AnimatedBuilder(
                        animation: _animation,
                        builder: (context, child) {
                          return Container(
                              height: 5,
                              width: _animation.value *
                                  MediaQuery.of(context).size.width,
                              decoration: BoxDecoration(
                                color: widget.type == ToastType.error
                                    ? MalouColors.error
                                    : MalouColors.success,
                                borderRadius: BorderRadius.circular(10),
                              ));
                        }),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.all(24.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Image.asset(
                          height: 45,
                          IllustrationsPathResolver.resolve(illustration)),
                      const SizedBox(
                        width: 15,
                      ),
                      SizedBox(
                        width: size.width * 0.5,
                        child: Text(
                          widget.message.isEmpty
                              ? Translation.of(context).anErrorOccured
                              : widget.message,
                          style: MalouTextStyles(context).text13Medium.copyWith(
                              color: MalouColors.textTitle, height: 1.5),
                        ),
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
