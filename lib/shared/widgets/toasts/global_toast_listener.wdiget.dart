import 'package:flutter/material.dart';
import 'package:malou/shared/widgets/toasts/toasts.service.dart';

class GlobalToastListener extends StatelessWidget {
  const GlobalToastListener({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 50,
      left: 0,
      child: StreamBuilder(
        stream: ToastsService.instance.toasts,
        builder: (context, snapshot) {
          if (snapshot.hasData && snapshot.data!.isNotEmpty) {
            return SizedBox(
              width: MediaQuery.of(context).size.width,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: snapshot.data!
                    .map((e) => Padding(
                          padding: const EdgeInsets.symmetric(vertical: 10.0),
                          child: e,
                        ))
                    .toList(),
              ),
            );
          }
          return Container();
        },
      ),
    );
  }
}
