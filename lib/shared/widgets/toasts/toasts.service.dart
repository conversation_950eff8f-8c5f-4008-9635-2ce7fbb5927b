import 'package:malou/shared/widgets/toasts/toast.widget.dart';
import 'package:rxdart/rxdart.dart';

// signleton
class ToastsService {
  static final ToastsService instance = ToastsService._internal();

  factory ToastsService() {
    return instance;
  }

  ToastsService._internal();

  final _toasts = BehaviorSubject<List<Toast>>.seeded([]);

  Stream<List<Toast>> get toasts => _toasts.stream;

  void add(Toast toast) {
    _toasts.add([..._toasts.value, toast]);
  }

  void remove(Toast toast) {
    _toasts.add(_toasts.value
        .where((event) => event.toastId != toast.toastId)
        .toList());
  }

  void dispose() {
    _toasts.close();
  }
}
