import 'package:flutter/material.dart';
import 'package:malou/pages/router.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/buttons/styles.dart';

import '../../../generated/l10n.dart';

class FrequencyResult {
  final bool realtime;
  final List<Map<String, int>> days;

  FrequencyResult({required this.realtime, required this.days});
}

class NotificationsFrequency {
  final bool realtime;
  final List<int> days;

  NotificationsFrequency({required this.realtime, required this.days});
}

class FrequencyPicker extends StatefulWidget {
  final bool isForMessages;
  final NotificationsFrequency frequency;
  const FrequencyPicker(
      {super.key, required this.isForMessages, required this.frequency});

  @override
  State<FrequencyPicker> createState() => _FrequencyPickerState();
}

class _FrequencyPickerState extends State<FrequencyPicker> {
  bool realtime = true;

  List<Map<String, int>> selectedDays = [];
  late final List<Map<String, int>> days;
  @override
  void initState() {
    realtime = widget.frequency.realtime;
    days = [
      {Translation.of(rootNavigatorKey.currentContext!).monday: 1},
      {Translation.of(rootNavigatorKey.currentContext!).tuesday: 2},
      {Translation.of(rootNavigatorKey.currentContext!).wednesday: 3},
      {Translation.of(rootNavigatorKey.currentContext!).thursday: 4},
      {Translation.of(rootNavigatorKey.currentContext!).friday: 5},
      {Translation.of(rootNavigatorKey.currentContext!).saturday: 6},
      {Translation.of(rootNavigatorKey.currentContext!).sunday: 7},
    ];
    selectedDays = days
        .where(
            (element) => widget.frequency.days.contains(element.values.first))
        .toList();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Translation.of(context).frequency,
          style: MalouTextStyles(context).text20Semibold,
        ),
        const SizedBox(height: 10),
        Container(
          constraints: BoxConstraints(maxHeight: realtime ? 30 : 120),
          child: ListTile(
            titleAlignment: ListTileTitleAlignment.top,
            contentPadding: EdgeInsets.zero,
            title: RichText(
              text: TextSpan(
                text: Translation.of(context).customized,
                style: MalouTextStyles(context).text15Medium,
                children: [
                  TextSpan(
                      text: Translation.of(context).onceADayWithParenthesis,
                      style: MalouTextStyles(context)
                          .text15Italic
                          .copyWith(color: MalouColors.textBody)),
                ],
              ),
            ),
            subtitle: !realtime
                ? Column(
                    children: [
                      Container(
                        margin: const EdgeInsets.symmetric(vertical: 5),
                        child: Text(
                          Translation.of(context).concernsOnlyNegativeReviews,
                          style: MalouTextStyles(context)
                              .text10Italic
                              .copyWith(color: MalouColors.textBody),
                        ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          for (var day in days)
                            ToggleButton(
                              size: Size(size.width * 0.09, size.width * 0.09),
                              isSelected: selectedDays.contains(day),
                              onTap: () {
                                setState(() {
                                  if (selectedDays.contains(day)) {
                                    selectedDays.remove(day);
                                  } else {
                                    selectedDays.add(day);
                                  }
                                });
                              },
                              child: Text(day.keys.first[0],
                                  style: MalouTextStyles(context)
                                      .text15Semibold
                                      .copyWith(
                                          color: selectedDays.contains(day)
                                              ? Colors.white
                                              : MalouColors.textTitle)),
                            )
                        ],
                      ),
                    ],
                  )
                : Container(),
            trailing: Radio(
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              visualDensity: !realtime
                  ? const VisualDensity(horizontal: 0, vertical: -4)
                  : const VisualDensity(horizontal: 0, vertical: 0),
              value: false,
              groupValue: realtime,
              onChanged: (value) {
                setState(() {
                  realtime = value as bool;
                });
              },
            ),
          ),
        ),
        ListTile(
          contentPadding: EdgeInsets.zero,
          title: RichText(
            text: TextSpan(
              text: Translation.of(context).eachTime,
              style: MalouTextStyles(context).text15Medium,
            ),
          ),
          subtitle: Column(
            children: [
              !widget.isForMessages
                  ? Container(
                      margin: const EdgeInsets.only(top: 5),
                      child: Text(
                        Translation.of(context).onlyForGoogleReviews,
                        style: MalouTextStyles(context)
                            .text10Italic
                            .copyWith(color: MalouColors.textBody),
                      ),
                    )
                  : Container(),
              realtime
                  ? Container(
                      margin: const EdgeInsets.only(top: 20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          for (var day in days)
                            ToggleButton(
                              size: Size(size.width * 0.09, size.width * 0.09),
                              isSelected: selectedDays.contains(day),
                              onTap: () {
                                setState(() {
                                  if (selectedDays.contains(day)) {
                                    selectedDays.remove(day);
                                  } else {
                                    selectedDays.add(day);
                                  }
                                });
                              },
                              child: Text(day.keys.first[0],
                                  style: MalouTextStyles(context)
                                      .text15Semibold
                                      .copyWith(
                                          color: selectedDays.contains(day)
                                              ? Colors.white
                                              : MalouColors.textTitle)),
                            )
                        ],
                      ))
                  : Container(),
            ],
          ),
          titleAlignment: ListTileTitleAlignment.top,
          trailing: Radio(
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            value: true,
            groupValue: realtime,
            onChanged: (value) {
              setState(() {
                realtime = value as bool;
              });
            },
          ),
        ),
        const SizedBox(height: 20),
        Container(
          alignment: Alignment.bottomCenter,
          child: PrimaryButtonContainer(
              fullWidth: true,
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context,
                    FrequencyResult(realtime: realtime, days: selectedDays)),
                style: ElevatedButton.styleFrom(
                    shadowColor: Colors.transparent,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10))),
                child: Text(
                  Translation.of(context).validate,
                  style: MalouTextStyles(context)
                      .text15Semibold
                      .copyWith(color: Colors.white),
                ),
              )),
        ),
      ],
    );
  }
}
