import 'package:flutter/material.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

class DateFormatter extends StatelessWidget {
  final DateTime date;
  final String format;
  final TextStyle? style;
  final String? prefix;

  const DateFormatter(
      {super.key,
      required this.date,
      this.style,
      this.format = 'dd MMMM yyyy',
      this.prefix});

  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageProvider>(
        builder: (context, languageProvider, child) {
      return Text(
        "${prefix != null ? "$prefix " : ""}${formatDate(date, languageProvider.language.name, format)}",
        style: style,
      );
    });
  }

  String formatDate(DateTime? dateTime, String lang, String format) {
    if (dateTime == null) {
      return '';
    }
    DateFormat formatter = DateFormat(format, lang);
    return formatter.format(dateTime);
  }
}
