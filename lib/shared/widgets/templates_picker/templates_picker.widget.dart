import 'package:flutter/material.dart';
import 'package:malou/app_config.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/helpers/template_replacer.dart';
import 'package:malou/shared/models/reviews.model.dart';
import 'package:malou/shared/models/template.model.dart';
import 'package:malou/shared/services/templates/templates.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/stars/rating.widget.dart';
import 'package:provider/provider.dart';
import 'package:skeleton_loader/skeleton_loader.dart';
import 'package:url_launcher/url_launcher.dart';

class TemplatesPicker extends StatefulWidget {
  final TemplateType type;
  final String restaurantId;
  final TemplateMatcher? templateMatcher;
  const TemplatesPicker(
      {super.key,
      required this.type,
      required this.restaurantId,
      this.templateMatcher});

  @override
  State<TemplatesPicker> createState() => _TemplatesPickerState();
}

class _TemplatesPickerState extends State<TemplatesPicker> {
  @override
  Widget build(BuildContext context) {
    return Consumer<TemplatesProvider>(builder: (context, provider, child) {
      return FutureBuilder(
          future: provider.getTemplates(widget.restaurantId, widget.type),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const TemplatesListSkeleton();
            }

            if (snapshot.hasError) {
              return Center(
                  child: Text(
                'Error: ${snapshot.error.toString()}',
              ));
            }

            List<Template> templates = snapshot.data as List<Template>;

            templates = templates
                .where((element) => element.status == 'active')
                .toList();

            if (widget.templateMatcher != null) {
              templates = widget.templateMatcher!.sort(templates);
            }

            return Column(
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.bookmarks_outlined,
                      size: 30,
                      color: MalouColors.primary,
                    ),
                    const SizedBox(width: 10),
                    Text(
                      Translation.of(context).myTemplates,
                      style: MalouTextStyles(context).text20Bold,
                    ),
                  ],
                ),
                const SizedBox(
                  height: 30,
                ),
                if (templates.isEmpty) ...[
                  TemplateNoResults(
                    restaurantId: widget.restaurantId,
                    type: widget.type,
                  ),
                ] else ...[
                  for (var template in templates)
                    if (widget.type == TemplateType.REVIEW) ...[
                      ReviewTemplateTile(template: template)
                    ] else ...[
                      MessageTemplateTile(template: template)
                    ],
                ]
              ],
            );
          });
    });
  }
}

class MessageTemplateTile extends StatelessWidget {
  const MessageTemplateTile({
    super.key,
    required this.template,
  });

  final Template template;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 15),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
          border: Border.all(color: MalouColors.backgroundDark, width: 1),
          borderRadius: BorderRadius.circular(10)),
      child: ListTile(
          title: Text(template.name),
          subtitle:
              Text(TemplateReplacer.replacePreview(template.text, context)),
          onTap: () {
            Navigator.pop(context, template);
          }),
    );
  }
}

class ReviewTemplateTile extends StatelessWidget {
  const ReviewTemplateTile({
    super.key,
    required this.template,
  });

  final Template template;

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    return Container(
      margin: const EdgeInsets.only(bottom: 15),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
          border: Border.all(color: MalouColors.backgroundDark, width: 1),
          borderRadius: BorderRadius.circular(10)),
      child: ListTile(
          title:
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            SizedBox(
              width: size.width * 0.4,
              child: Text(template.name,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  softWrap: false),
            ),
            Row(
              children: [
                for (var rating in template.rating)
                  Padding(
                    padding: const EdgeInsets.only(right: 5),
                    child: RatingStar(
                      stars: rating,
                    ),
                  ),
              ],
            )
          ]),
          subtitle:
              Text(TemplateReplacer.replacePreview(template.text, context)),
          onTap: () {
            Navigator.pop(context, template);
          }),
    );
  }
}

class TemplatesListSkeleton extends StatelessWidget {
  const TemplatesListSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return SkeletonLoader(
      builder: Container(
          margin: const EdgeInsets.only(bottom: 15),
          width: double.infinity,
          height: 100,
          decoration: BoxDecoration(
            color: MalouColors.backgroundDark,
            borderRadius: BorderRadius.circular(5),
          )),
      items: 10,
      highlightColor: MalouColors.borderPrimary,
      baseColor: MalouColors.backgroundDark,
      direction: SkeletonDirection.ltr,
    );
  }
}

class TemplateNoResults extends StatelessWidget {
  const TemplateNoResults(
      {super.key, required this.restaurantId, required this.type});
  final String restaurantId;
  final TemplateType type;

  void _onRedirectToTemplates(TemplateType type) async {
    String url =
        '${AppConfig.shared.webAppUri}/restaurants/$restaurantId/resources';

    switch (type) {
      case TemplateType.REVIEW:
        url += '/review-templates';
        break;
      case TemplateType.MESSAGE:
        url += '/message-templates';
        break;
      default:
        break;
    }

    Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 300,
      child: Column(
        children: [
          Image.asset(
            IllustrationsPathResolver.resolve(Illustrations.cook),
            height: 80,
          ),
          const SizedBox(height: 20),
          Text(Translation.of(context).noTemplates,
              style: MalouTextStyles(context)
                  .text13Bold
                  .copyWith(color: MalouColors.textTitle)),
          const SizedBox(height: 10),
          Text(
              Translation.of(context).noTemplatesDetails(
                  type == TemplateType.REVIEW
                      ? Translation.of(context).reviews.toLowerCase()
                      : Translation.of(context).messages),
              textAlign: TextAlign.center,
              style: MalouTextStyles(context)
                  .text12Italic
                  .copyWith(color: MalouColors.textBody)),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              TextButton(
                child: Text(Translation.of(context).later,
                    style: MalouTextStyles(context)
                        .text13Bold
                        .copyWith(color: MalouColors.textBody)),
                onPressed: () {
                  Navigator.pop(context, null);
                },
              ),
              TextButton(
                child: Text(Translation.of(context).addNow,
                    style: MalouTextStyles(context)
                        .text13Bold
                        .copyWith(color: MalouColors.primary)),
                onPressed: () {
                  _onRedirectToTemplates(type);
                },
              )
            ],
          )
        ],
      ),
    );
  }
}

abstract class TemplateMatcher<T extends Object?> {
  final T entity;

  TemplateMatcher(this.entity);

  List<Template> sort(List<Template> templates);
}

class TemplateMatcherReview implements TemplateMatcher<Review> {
  @override
  final Review entity;

  TemplateMatcherReview(this.entity);

  @override
  List<Template> sort(List<Template> templates) {
    templates.sort((tA, tB) =>
        tA.compareToFindTheOneThatMatchTheBestForReview(tB, entity));
    return templates;
  }
}
