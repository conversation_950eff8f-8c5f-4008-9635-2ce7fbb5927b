import 'package:flutter/material.dart';
import 'package:malou/shared/helpers/constant.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/widgets/image/cached_image.widget.dart';

class Avatar extends StatelessWidget {
  final String url;
  final Widget? badge;
  final Color? borderColor;
  final double factorSize;
  final String? fallbackUrl;
  const Avatar(
      {super.key,
      required this.url,
      this.badge,
      this.borderColor,
      this.fallbackUrl,
      this.factorSize = 1});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        CircleAvatar(
            backgroundColor: borderColor ?? Colors.white,
            radius: 28 * factorSize,
            child: CachedImage(
              height: 50 * factorSize,
              width: 50 * factorSize,
              radius: BorderRadius.circular(30),
              url: url,
              fallbackUrl: fallbackUrl,
            )),
        Positioned(
          bottom: 0,
          right: 0,
          child: Container(
            width: 18,
            height: 18,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
            ),
            child: badge ?? Container(),
          ),
        ),
      ],
    );
  }
}

class AvatarPlatform extends StatelessWidget {
  final String url;
  final PlatformKeys platformKey;
  final String? fallbackUrl;
  final double factorSize;

  const AvatarPlatform(
      {super.key,
      required this.platformKey,
      required this.url,
      this.fallbackUrl,
      this.factorSize = 1});

  @override
  Widget build(BuildContext context) {
    PlatformDetails platformDetails = AppConstant.getDetails(platformKey);
    return Avatar(
        url: url,
        borderColor: platformDetails.borderColor,
        fallbackUrl: fallbackUrl,
        factorSize: factorSize,
        badge: Image.asset(
          height: 20,
          platformDetails.resolveLogo(),
        ));
  }
}
