import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

class Dropdown<T> extends StatelessWidget {
  final List<T> options;
  final String Function(T value) displayValue;
  final void Function(T?) onChanged;
  final DropdownMenuItem<T> Function(BuildContext context, T value,
      String Function(T value) displayValue, T selectedValue)? itemWidget;
  final T selectedValue;

  Dropdown({
    super.key,
    required this.options,
    required this.displayValue,
    required this.onChanged,
    required this.selectedValue,
    final DropdownMenuItem<T> Function(BuildContext context, T value,
            String Function(T value) displayValue, T selectedValue)?
        itemWidget,
  }) : itemWidget = itemWidget ??
            ((BuildContext context, T value,
                String Function(T value) displayValue, selectedValue) {
              return DropdownMenuItem<T>(
                  value: value,
                  child: Row(
                    children: [
                      Text(
                        displayValue(value),
                        style: MalouTextStyles(context).text12Semibold,
                      ),
                    ],
                  ));
            });

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: const EdgeInsets.only(bottom: 20),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: MalouColors.borderPrimary, width: 1),
          borderRadius: const BorderRadius.all(Radius.circular(10.0)),
        ),
        child: DropdownButtonHideUnderline(
          child: DropdownButton2<T>(
              style: MalouTextStyles(context).text12Semibold,
              value: selectedValue,
              isExpanded: true,
              items: options
                  .map((T value) =>
                      itemWidget!(context, value, displayValue, selectedValue))
                  .toList(),
              onChanged: onChanged,
              dropdownStyleData: DropdownStyleData(
                elevation: 0,
                decoration: BoxDecoration(
                  color: Colors.white,
                  border:
                      Border.all(color: MalouColors.borderPrimary, width: 1),
                  borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                ),
              ),
              iconStyleData: const IconStyleData(
                icon: Padding(
                  padding: EdgeInsets.only(right: 15.0),
                  child: Icon(Icons.expand_more, color: MalouColors.primary),
                ),
              ),
              menuItemStyleData: MenuItemStyleData(
                selectedMenuItemBuilder: (BuildContext context, Widget child) {
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      child,
                      const Padding(
                        padding: EdgeInsets.only(right: 15.0),
                        child: Icon(
                          Icons.check,
                          color: MalouColors.primary,
                          size: 20.0,
                        ),
                      ),
                    ],
                  );
                },
              )),
        ));
  }
}
