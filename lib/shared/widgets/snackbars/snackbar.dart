import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

class MalouSnackbars {
  static showInfoSnackbar(
          {required BuildContext context,
          undoFunction,
          Duration? duration,
          required String label,
          String? subLabel}) =>
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        duration: duration ?? const Duration(seconds: 3),
        content: Column(
          children: [
            Text(
              textAlign: TextAlign.center,
              label,
              style: MalouTextStyles(context).text12Semibold.copyWith(
                    color: Colors.white,
                  ),
            ),
            subLabel != null
                ? Text(
                  textAlign: TextAlign.center,
                  subLabel,
                  style: MalouTextStyles(context).text12.copyWith(
                        color: Colors.white,
                      ),
                )
                : const SizedBox()
          ],
        ),
        backgroundColor: MalouColors.textTitle,
        behavior: SnackBarBehavior.floating,
        action: undoFunction?.toString().isNotEmpty ?? false
            ? SnackBarAction(
                label: Translation.of(context).cancel,
                textColor: MalouColors.backgroundDark,
                onPressed: undoFunction)
            : null,
      ));
}
