import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_medias/widgets/post_media_picker/widgets/widgets/widgets/video_thumbnail.widget.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:photo_manager_image_provider/photo_manager_image_provider.dart';

class PostMediaCover extends StatefulWidget {
  final Post post;
  final bool fullHeight;
  final BorderRadius borderRadius;
  final bool showPlayIcon;
  const PostMediaCover(
      {super.key,
      required this.post,
      this.showPlayIcon = true,
      this.fullHeight = false,
      this.borderRadius = const BorderRadius.all(Radius.circular(0))});

  @override
  State<PostMediaCover> createState() => _PostMediaCoverState();
}

class _PostMediaCoverState extends State<PostMediaCover> {
  bool _errorLoadingImage = false;

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    if (widget.post.isReel() || widget.post.isVideo()) {
      if (widget.post.getThumbnail() != null) {
        return buildCover(true);
      }
      if (widget.post.attachments.isNotEmpty &&
          widget.post.attachments.first.assetEntity != null) {
        return ClipRRect(
          borderRadius: widget.borderRadius,
          child: AssetEntityImage(
            widget.post.attachments.first.assetEntity!,
            fit: BoxFit.cover,
            isOriginal: false,
            width: size.width,
            thumbnailSize: const ThumbnailSize.square(300),
            thumbnailFormat: ThumbnailFormat.jpeg,
          ),
        );
      }
      if (widget.post.getAttachments().isNotEmpty) {
        return VideoMediaThumbnail(
          thumbnailurl: widget.post.attachments.first.thumbnail,
          borderRadius: widget.borderRadius,
          url: widget.post.getAttachments()[0]!,
          fit: BoxFit.cover,
          height: size.width,
          width: size.width,
          showPlayIcon: widget.showPlayIcon || !widget.post.isCarousel(),
        );
      }
      return buildCover(true);
    } else {
      return buildCover(false);
    }
  }

  Container buildCover(bool isVideo) {
    String? imageUrl;
    if (widget.post.hasAttachments()) {
      if (isVideo) {
        imageUrl = widget.post.getThumbnail();
      } else if (widget.post.hasAttachments() && !_errorLoadingImage) {
        imageUrl = widget.post.getAttachments(size: MediaSize.small)[0];
      }
    }
    return Container(
      height: double.infinity,
      decoration: BoxDecoration(
          borderRadius: widget.borderRadius,
          image: imageUrl != null
              ? DecorationImage(
                  image: CachedNetworkImageProvider(imageUrl,
                      errorListener: (e) =>
                          setState(() => _errorLoadingImage = true)),
                  fit: BoxFit.cover)
              : DecorationImage(
                  image: AssetImage(
                      IllustrationsPathResolver.resolve(Illustrations.burger)),
                  fit: BoxFit.cover)),
      child: Center(
          child: isVideo && widget.showPlayIcon
              ? Icon(
                  MalouIcons.play,
                  color: Colors.white.withOpacity(0.8),
                  size: 25,
                )
              : const SizedBox()),
    );
  }
}
