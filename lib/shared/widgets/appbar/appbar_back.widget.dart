import 'package:flutter/material.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

class AppBarArrowBack extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final Icon icon;
  final double? height;
  final bool? centerTitle;
  final List<Widget> actions;
  final Future<bool> Function()? onPop;

  const AppBarArrowBack({
    super.key,
    this.title = '',
    this.icon = const Icon(
      MalouIcons.arrowLeft,
      color: MalouColors.primary,
    ),
    this.actions = const [],
    this.height = 50,
    this.centerTitle = true,
    this.onPop,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      title: Align(
        alignment: Alignment.centerLeft,
        child: Text(title,
            style: MalouTextStyles(context)
                .text20Semibold
                .copyWith(color: MalouColors.textTitle)),
      ),
      elevation: 0,
      leading: IconButton(
        icon: icon,
        onPressed: () {
          if (onPop != null) {
            onPop?.call().then((value) {
              if (value == true) {
                Navigator.of(context).pop();
              }
            });
          } else {
            Navigator.of(context).pop();
          }
        },
      ),
      actions: [
        ...actions,
        const SizedBox(
          width: 10,
        )
      ],
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(height!);
}
