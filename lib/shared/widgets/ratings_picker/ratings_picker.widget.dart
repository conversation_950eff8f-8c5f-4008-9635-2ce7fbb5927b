import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/buttons/styles.dart';

class RatingsPicker extends StatefulWidget {
  final List<int> initialRatings;
  const RatingsPicker({
    super.key,
    required this.initialRatings,
  });

  @override
  State<RatingsPicker> createState() => _RatingsPickerState();
}

class _RatingsPickerState extends State<RatingsPicker> {
  List<int> selectedRatings = [];

  @override
  void initState() {
    super.initState();
    selectedRatings = widget.initialRatings;
  }

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Translation.of(context).concernedRatings,
          style: MalouTextStyles(context).text20Semibold,
        ),
        const SizedBox(
          height: 10,
        ),
        Center(
          child: SizedBox(
            width: size.width * 0.75,
            child: Wrap(
              alignment: WrapAlignment.center,
              runAlignment: WrapAlignment.spaceAround,
              runSpacing: 10,
              spacing: 10,
              children: [
                for (var i = 1; i <= 5; i++)
                  GestureDetector(
                    onTap: () {
                      if (selectedRatings.contains(i)) {
                        setState(() {
                          selectedRatings.remove(i);
                        });
                      } else {
                        setState(() {
                          selectedRatings.add(i);
                        });
                      }
                    },
                    child: RatingStarContainer(
                      selected: selectedRatings.contains(i),
                      numberOfStars: i,
                    ),
                  ),
              ],
            ),
          ),
        ),
        const SizedBox(
          height: 20,
        ),
        Container(
          alignment: Alignment.bottomCenter,
          child: PrimaryButtonContainer(
              fullWidth: true,
              child: ElevatedButton(
                onPressed: () {
                  selectedRatings.sort();
                  Navigator.pop(context, selectedRatings);
                },
                style: ElevatedButton.styleFrom(
                    shadowColor: Colors.transparent,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10))),
                child: Text(
                  Translation.of(context).validate,
                  style: MalouTextStyles(context)
                      .text15Semibold
                      .copyWith(color: Colors.white),
                ),
              )),
        ),
      ],
    );
  }
}

class RatingStarContainer extends StatelessWidget {
  final bool selected;
  final int numberOfStars;
  const RatingStarContainer(
      {super.key, required this.selected, required this.numberOfStars});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(6.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: selected ? MalouColors.purple : MalouColors.backgroundDark,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          for (var i = 0; i < numberOfStars; i++)
            selected
                ? const Icon(
                    MalouIcons.starFull,
                    color: Colors.white,
                    size: 26,
                  )
                : const Icon(
                    MalouIcons.starOutline,
                    color: MalouColors.purpleLight,
                    size: 26,
                  ),
        ],
      ),
    );
  }
}
