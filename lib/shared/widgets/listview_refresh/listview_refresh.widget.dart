import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class ListviewRefresh extends StatelessWidget {
  final RefreshController refreshController;
  final void Function()? onRefresh;
  final Widget child;
  const ListviewRefresh(
      {super.key,
      required this.refreshController,
      required this.onRefresh,
      required this.child});

  @override
  Widget build(BuildContext context) {
    return SmartRefresher(
        controller: refreshController,
        enablePullDown: true,
        header: WaterDropHeader(
            complete: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Image.asset(
                  IllustrationsPathResolver.resolve(Illustrations.gogoolOk),
                  width: 25,
                ),
                const SizedBox(
                  width: 10,
                ),
                Text(Translation.of(context).postsUpdated,
                    style: MalouTextStyles(context).text12Italic),
              ],
            ),
            waterDropColor: Colors.transparent,
            idleIcon: const Icon(MalouIcons.spinner,
                color: MalouColors.primary, size: 20),
            refresh:
                const CircularSpinner(color: MalouColors.primary, size: 20)),
        onRefresh: onRefresh,
        child: child);
  }
}
