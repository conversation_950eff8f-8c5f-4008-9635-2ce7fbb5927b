import 'package:flutter/material.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

Widget Function(BuildContext, Widget?)? datePickerThemeBuilder =
    (context, child) => Theme(
          data: ThemeData.light().copyWith(
              datePickerTheme: DatePickerTheme.of(context).copyWith(
                surfaceTintColor: MalouColors.backgroundDark,
                elevation: 0,
                headerHelpStyle: const TextStyle(fontSize: 0),
                shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(Radius.circular(20))),
                headerHeadlineStyle: MalouTextStyles(context).text20Bold,
              ),
              colorScheme: const ColorScheme.dark(
                  surface: MalouColors.backgroundDark,
                  onPrimary: Colors.white, // selected text color
                  onSurface: Colors.black, // default text color
                  primary: MalouColors.purple // circle color

                  ),
              dialogBackgroundColor: Colors.white,
              textButtonTheme: TextButtonThemeData(
                  style: TextButton.styleFrom(
                textStyle: MalouTextStyles(context).text13Semibold.copyWith(
                      color: MalouColors.primary,
                    ),
                foregroundColor:
                    MalouColors.primary, // color of button's letters
              ))),
          child: child!,
        );
