import 'package:flutter/material.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:provider/provider.dart';

Widget Function(BuildContext, Widget?)? timePickerThemeBuilder =
    (context, child) => MediaQuery(
        data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: false),
        child: Theme(
          data: ThemeData.light().copyWith(
              timePickerTheme: TimePickerTheme.of(context).copyWith(
                dayPeriodButtonStyle: TextButton.styleFrom(
                    textStyle: MalouTextStyles(context).text13Semibold,
                    foregroundColor: MalouColors.textBody),
                dayPeriodColor: MalouColors.borderSecondary,
                shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(Radius.circular(20))),
                hourMinuteTextColor: MalouColors.textTitle,
                dayPeriodTextColor: MalouColors.textTitle,
                hourMinuteColor: Colors.white,
                dialBackgroundColor: MalouColors.backgroundDark,
                dialTextColor: MalouColors.textTitle,
                entryModeIconColor: MalouColors.textTitle,
                helpTextStyle: const TextStyle(fontSize: 0),
                dayPeriodBorderSide:
                    const BorderSide(color: Colors.transparent),
                dialHandColor: MalouColors.borderSecondary,
                cancelButtonStyle: TextButton.styleFrom(
                    textStyle: MalouTextStyles(context).text13Semibold,
                    foregroundColor: MalouColors.textBody),
              ),
              colorScheme: const ColorScheme.light(
                  surface: Colors.white,
                  onPrimary: Colors.white, // selected text color
                  onSurface: Colors.black, // default text color
                  primary: MalouColors.purple // circle color

                  ),
              dialogBackgroundColor: Colors.white,
              textButtonTheme: TextButtonThemeData(
                  style: TextButton.styleFrom(
                textStyle: MalouTextStyles(context).text13Semibold.copyWith(
                      color: MalouColors.primary,
                    ),
                foregroundColor:
                    MalouColors.primary, // color of button's letters
              ))),
          child: Localizations.override(
              context: context,
              locale: Locale(context.read<LanguageProvider>().language.name),
              child: child!),
        ));
