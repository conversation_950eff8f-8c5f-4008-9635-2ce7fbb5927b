import 'package:flutter/material.dart';
import 'package:malou/shared/pages/ai_generation/pages/ai_generation_edit_pannel/ai_generation_edit_pannel.dart';
import 'package:malou/shared/pages/ai_generation/pages/ai_generation_edit_pannel/pages/models/translate_from_ai_params.model.dart';
import 'package:malou/shared/pages/ai_generation/pages/ai_generation_prompt.page.dart';
import 'package:malou/shared/pages/ai_generation/pages/models/generate_from_ai_params.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';

class AIGeneration extends StatelessWidget {
  final Future<String> Function(GenerateFromAIParams) generateFromAI;
  final Future<List<String>> Function(GenerateFromAIParams)?
      generateFromAIAdvancedSettings;
  final Future<String> Function(TranslateFromAIParams)? translateFromAI;
  final Future<String> Function(String)? optimizeFromAI;
  final void Function()? onAiValidateSuggestion;
  final void Function(String) setValueFromAI;
  final String text;
  final bool showPromptStep;
  final bool showEditPannelGeneration;
  final String label;
  final String placeholder;
  final PostsProvider? postProvider;
  const AIGeneration(
      {super.key,
      required this.text,
      required this.generateFromAI,
      this.generateFromAIAdvancedSettings,
      this.translateFromAI,
      this.optimizeFromAI,
      required this.setValueFromAI,
      this.showPromptStep = true,
      this.showEditPannelGeneration = false,
      required this.label,
      required this.placeholder,
      this.postProvider,
      this.onAiValidateSuggestion});

  @override
  Widget build(BuildContext context) {
    if (showEditPannelGeneration) {
      if (text.isEmpty) {
        return AIPromptGeneration(
            label: label,
            placeholder: placeholder,
            generateFromAI: generateFromAI,
            generateFromAIAdvancedSettings: generateFromAIAdvancedSettings,
            setValueFromAI: setValueFromAI,
            onAiValidateSuggestion: onAiValidateSuggestion,
            postProvider: postProvider);
      }
      return AIEditPannelGeneration(
          label: label,
          placeholder: placeholder,
          text: text,
          generateFromAI: generateFromAI,
          translateFromAI:
              translateFromAI ?? (TranslateFromAIParams params) async => '',
          optimizeFromAI: optimizeFromAI ?? (String text) async => '',
          setValueFromAI: setValueFromAI);
    }
    return AIPromptGeneration(
        label: label,
        placeholder: placeholder,
        generateFromAI: generateFromAI,
        generateFromAIAdvancedSettings: generateFromAIAdvancedSettings,
        setValueFromAI: setValueFromAI,
        onAiValidateSuggestion: onAiValidateSuggestion,
        postProvider: postProvider);
  }
}
