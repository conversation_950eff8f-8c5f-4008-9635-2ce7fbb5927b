import 'dart:async';

import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/services/medias/medias.service.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AIImageAnalysisController {
  final BuildContext context;
  final MediasService mediaService = MediasService();
  final PostsProvider? postsProvider;
  final void Function(void Function()) setState;
  bool? mounted;

  final String FAILED_MEDIA_AI_DESCRIPTION =
      'Not Available'; // this is a constant received from the lambda in case of failure

  bool shouldUseImageAnalysis = true;
  bool isImageAnalysisToggleChecked = true;

  String? usableAiDescriptionForImageAnalysis;
  bool hasFailedToGetAiDescription = false;
  String? previousUsableImageId;

  Timer? _waitForIntervalTimer;

  String getDisableImageAnalysisTooltipText() {
    if (hasFailedToGetAiDescription) {
      return Translation.of(context).cantAnalyzeImage;
    }
    bool isPostWithoutAttachments =
        postsProvider?.postToEdit.attachments.isEmpty ?? true;
    if (isPostWithoutAttachments) {
      return Translation.of(context).addImageToUse;
    }
    var usablePhotoForImageAnalysis =
        postsProvider?.usablePhotoForImageAnalysis$.value;
    if (usablePhotoForImageAnalysis == null) {
      return Translation.of(context).cantAnalyzeVideos;
    }
    if (usableAiDescriptionForImageAnalysis == null) {
      return Translation.of(context).waitingForImageAnalysis;
    }
    if (usableAiDescriptionForImageAnalysis == FAILED_MEDIA_AI_DESCRIPTION) {
      return Translation.of(context).cantAnalyzeImage;
    }
    return '';
  }

  Future<void> checkMediaForAnalysis() async {
    final media = postsProvider?.usablePhotoForImageAnalysis$.value;

    if (media == null) {
      setState(() {
        usableAiDescriptionForImageAnalysis = null;
      });
      return;
    }

    if (media.aiDescription != null) {
      setState(() {
        usableAiDescriptionForImageAnalysis = media.aiDescription;
      });
      return;
    }

    final isSameMediaAsBefore = previousUsableImageId == media.id;
    if (!isSameMediaAsBefore) {
      setState(() {
        previousUsableImageId = media.id;
        usableAiDescriptionForImageAnalysis = null;
      });
    }

    await _waitForAiDescription(media.id!);
  }

  void _cancelExistingTimer() {
    if (_waitForIntervalTimer != null && _waitForIntervalTimer!.isActive) {
      _waitForIntervalTimer?.cancel();
      _waitForIntervalTimer = null;
    }
  }

  void _finalizeTimer(Timer? timer) {
    if (timer != null) {
      timer.cancel();
    }
  }

  void _getUpdatedMediaWithDescription(String mediaId, Timer? timer) async {
    mediaService.getMediumById(mediaId).then((response) {
      var fetchedMedia = response.data;

      if (fetchedMedia != null && fetchedMedia.aiDescription != null) {
        setState(() {
          usableAiDescriptionForImageAnalysis = fetchedMedia.aiDescription;
          hasFailedToGetAiDescription = false;
          updateImageAnalysisToggleChecked();
        });
        _finalizeTimer(timer);
      }
    }).catchError((error) {
      setState(() {
        usableAiDescriptionForImageAnalysis = null;
        hasFailedToGetAiDescription = false;
      });
      _finalizeTimer(timer);
    });
  }

  Future<void> _waitForAiDescription(String mediaId) async {
    const MAX_TIME = 30;
    const CHECK_INTERVAL = 2;
    _cancelExistingTimer();

    DateTime startTime = DateTime.now();

    _getUpdatedMediaWithDescription(mediaId, null);

    _waitForIntervalTimer =
        Timer.periodic(const Duration(seconds: CHECK_INTERVAL), (timer) {
      if (DateTime.now().difference(startTime).inSeconds >= MAX_TIME) {
        _finalizeTimer(timer);
        if (usableAiDescriptionForImageAnalysis == null) {
          setState(() {
            hasFailedToGetAiDescription = true;
          });
        }
        return;
      }

      _getUpdatedMediaWithDescription(mediaId, timer);
    });
  }

  void firstUpdateShouldUseImageAnalysis() async {
    final SharedPreferences preferences = await SharedPreferences.getInstance();
    shouldUseImageAnalysis =
        preferences.getBool('shouldUseImageAnalysisInPostsByDefault') ?? true;
    updateImageAnalysisToggleChecked();
  }

  void updateImageAnalysisToggleChecked() {
    isImageAnalysisToggleChecked =
        getDisableImageAnalysisTooltipText().isEmpty && shouldUseImageAnalysis;
  }

  void dispose() {
    final timer = _waitForIntervalTimer;
    if (timer != null) {
      _finalizeTimer(timer);
      _waitForIntervalTimer = null;
    }
  }

  AIImageAnalysisController(
      {required this.context,
      required this.setState,
      required this.mounted,
      this.postsProvider}) {
    firstUpdateShouldUseImageAnalysis();

    postsProvider?.usablePhotoForImageAnalysis$.listen((value) {
      if (mounted != null && mounted!) {
        checkMediaForAnalysis();
        updateImageAnalysisToggleChecked();
      }
    });
  }
}
