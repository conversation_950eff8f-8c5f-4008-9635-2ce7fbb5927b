import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/widgets/appbar/appbar_back.widget.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/pages/ai_generation/pages/ai_generation_edit_pannel/pages/ai_generation_choose_language.controller.dart';
import 'package:malou/shared/pages/ai_generation/pages/ai_generation_edit_pannel/pages/models/selected_button.model.dart';
import 'package:malou/shared/pages/ai_generation/pages/ai_generation_edit_pannel/pages/models/translate_from_ai_params.model.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/buttons/selected_button.widget.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';
import 'package:provider/provider.dart';

class AIGenerationChooseLanguage extends StatefulWidget {
  final Map<Language, String> languages;
  final Future<String> Function(TranslateFromAIParams) translateFromAI;
  final Function(String) setValueFromAI;
  final String textToTranslate;
  const AIGenerationChooseLanguage(
      {super.key,
      required this.textToTranslate,
      required this.languages,
      required this.translateFromAI,
      required this.setValueFromAI});

  @override
  State<AIGenerationChooseLanguage> createState() =>
      _AIGenerationChooseLanguageState();
}

class _AIGenerationChooseLanguageState
    extends State<AIGenerationChooseLanguage> {
  late AIGenerationChooseLanguageController
      aIGenerationChooseLanguageController;

  @override
  void initState() {
    aIGenerationChooseLanguageController = AIGenerationChooseLanguageController(
      context: context,
      setState: setState,
      languages: widget.languages,
      translateFromAI: widget.translateFromAI,
      setValueFromAI: widget.setValueFromAI,
      textToTranslate: widget.textToTranslate,
      languageProvider: Provider.of<LanguageProvider>(context, listen: false),
      restaurantProvider:
          Provider.of<RestaurantProvider>(context, listen: false),
    );
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarArrowBack(
        title: Translation.of(context).translate,
        icon: const Icon(
          MalouIcons.arrowLeft,
          color: MalouColors.primary,
        ),
        actions: [
          StreamBuilder<bool>(
              stream: aIGenerationChooseLanguageController
                  .translateFromAIQuery.isFetching,
              builder: (context, snapshot) {
                if (snapshot.hasData && snapshot.data!) {
                  return const Center(
                      child: CircularSpinner(
                    color: MalouColors.primary,
                    size: 20,
                  ));
                }
                return IconButton(
                    onPressed: aIGenerationChooseLanguageController
                            .selectedLang.isNotEmpty
                        ? () {
                            aIGenerationChooseLanguageController
                                .translateFromAIQuery
                                .execute(TranslateFromAIParams(
                                    lang: aIGenerationChooseLanguageController
                                        .selectedLang,
                                    text: widget.textToTranslate));
                          }
                        : null,
                    icon: Icon(
                      Icons.check,
                      color: aIGenerationChooseLanguageController
                              .selectedLang.isNotEmpty
                          ? MalouColors.primary
                          : MalouColors.backgroundDark,
                    ));
              })
        ],
      ),
      body: StreamBuilder<List<SelectedButtonState<Language>>>(
          stream: aIGenerationChooseLanguageController.languageButtons$,
          builder: (context, snapshot) {
            if (!snapshot.hasData || snapshot.data!.isEmpty) {
              return const SizedBox();
            }
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  for (var btnState in snapshot.data!) ...[
                    SelectedButton(
                        selected: btnState.selected,
                        buttonText: btnState.buttonText,
                        onPressed: () {
                          aIGenerationChooseLanguageController
                              .setSelectedButton(btnState);
                        }),
                    const SizedBox(
                      height: 10,
                    )
                  ],
                  const SizedBox(
                    height: 10,
                  ),
                  Text(Translation.of(context).cannotFindYourLang,
                      style: MalouTextStyles(context)
                          .text12Semibold
                          .copyWith(color: MalouColors.textTitle)),
                  const SizedBox(
                    height: 10,
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                            color: MalouColors.backgroundDark, width: 1)),
                    child: TextFormField(
                      onTap: () {
                        aIGenerationChooseLanguageController
                            .setSelectedButton(null);
                        setState(() {
                          aIGenerationChooseLanguageController.selectedLang =
                              aIGenerationChooseLanguageController
                                  .textEditingController.text;
                        });
                      },
                      controller: aIGenerationChooseLanguageController
                          .textEditingController,
                      textCapitalization: TextCapitalization.sentences,
                      keyboardType: TextInputType.multiline,
                      maxLines: 1,
                      maxLength: 50,
                      style: MalouTextStyles(context)
                          .text13
                          .copyWith(color: MalouColors.textTitle),
                      decoration: InputDecoration(
                          border: InputBorder.none,
                          counterText: "",
                          hintStyle: MalouTextStyles(context).text13Italic,
                          hintText: Translation.of(context).typeYourLang),
                    ),
                  )
                ],
              ),
            );
          }),
    );
  }
}
