import 'package:flutter/material.dart';
import 'package:malou/shared/helpers/clarify_error.dart';
import 'package:malou/shared/helpers/use_query.dart';
import 'package:malou/shared/pages/ai_generation/pages/ai_generation_edit_pannel/pages/models/selected_button.model.dart';
import 'package:malou/shared/pages/ai_generation/pages/ai_generation_edit_pannel/pages/models/translate_from_ai_params.model.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:rxdart/subjects.dart';

class AIGenerationChooseLanguageController {
  final BuildContext context;
  final Function setState;
  final LanguageProvider languageProvider;
  final RestaurantProvider restaurantProvider;
  final TextEditingController textEditingController = TextEditingController();
  final Map<Language, String> languages;
  final Future<String> Function(TranslateFromAIParams) translateFromAI;
  final Function(String) setValueFromAI;
  final String textToTranslate;
  late QueryResult<String, TranslateFromAIParams> translateFromAIQuery;

  BehaviorSubject<List<SelectedButtonState<Language>>> languageButtons$ =
      BehaviorSubject<List<SelectedButtonState<Language>>>.seeded([]);

  String selectedLang = '';

  AIGenerationChooseLanguageController(
      {required this.context,
      required this.setState,
      required this.languages,
      required this.translateFromAI,
      required this.setValueFromAI,
      required this.textToTranslate,
      required this.languageProvider,
      required this.restaurantProvider}) {
    List<SelectedButtonState<Language>> SelectedButtonStates = languages.entries
        .map((entry) => SelectedButtonState<Language>(
            buttonText: entry.value, value: entry.key, selected: false))
        .toList();

    languageButtons$.add(SelectedButtonStates);

    textEditingController.addListener(() {
      setState(() {
        selectedLang = textEditingController.text;
      });
    });

    translateFromAIQuery =
        useQuery(translateFromAI, onSuccess: (result, params, onProcessResult) {
      restaurantProvider.aiRemainingCredits$
          .add(restaurantProvider.aiRemainingCredits$.value - 1);

      setValueFromAI(result);
      // pop 2 times to get back to the post
      Navigator.of(context).pop();
      Navigator.of(context).pop();
    }, onError: (result, params, onProcessResult) {
      displaySnackbarWithClarifiedError(context, result);
    });
  }

  void setSelectedButton(SelectedButtonState<Language>? currentBtnState) {
    final List<SelectedButtonState<Language>> languageButtons =
        languageButtons$.value.map((state) {
      return SelectedButtonState(
          selected: false, buttonText: state.buttonText, value: state.value);
    }).toList();

    if (currentBtnState == null) {
      languageButtons$.add(languageButtons);
      return;
    }

    final index = languageButtons$.value.indexOf(currentBtnState);
    var newSelectedButtonState = SelectedButtonState<Language>(
        selected: true,
        buttonText: currentBtnState.buttonText,
        value: currentBtnState.value);
    languageButtons[index] = newSelectedButtonState;

    setState(() => selectedLang = currentBtnState.value.name);
    languageButtons$.add(languageButtons);
  }
}
