import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/widgets/appbar/appbar_back.widget.dart';
import 'package:malou/shared/helpers/clarify_error.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/helpers/use_query.dart';
import 'package:malou/shared/pages/ai_generation/pages/ai_generation_edit_pannel/pages/ai_generation_choose_language.dart';
import 'package:malou/shared/pages/ai_generation/pages/ai_generation_edit_pannel/pages/models/translate_from_ai_params.model.dart';
import 'package:malou/shared/pages/ai_generation/pages/models/generate_from_ai_params.model.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';

class AIEditPannelGeneration extends StatelessWidget {
  final Future<String> Function(GenerateFromAIParams) generateFromAI;
  final Future<String> Function(TranslateFromAIParams) translateFromAI;
  final Future<String> Function(String) optimizeFromAI;
  final void Function(String) setValueFromAI;
  final String text;
  final bool showPromptStep;
  final void Function()? onAiValidateSuggestion;
  final String label;
  final String placeholder;

  const AIEditPannelGeneration(
      {super.key,
      required this.text,
      required this.generateFromAI,
      required this.translateFromAI,
      required this.optimizeFromAI,
      required this.setValueFromAI,
      this.showPromptStep = true,
      required this.label,
      required this.placeholder,
      this.onAiValidateSuggestion});

  @override
  Widget build(BuildContext context) {
    return Consumer2<LanguageProvider, RestaurantProvider>(
        builder: (context, languageProvider, restaurantProvider, child) {
      QueryResult<String, String> optimizeFromAiQuery = useQuery(optimizeFromAI,
          onSuccess: (result, params, onProcessResult) {
        restaurantProvider.aiRemainingCredits$
            .add(restaurantProvider.aiRemainingCredits$.value - 1);
        setValueFromAI(result);
        Navigator.pop(context);
      }, onError: (result, params, onProcessResult) {
        displaySnackbarWithClarifiedError(context, result);
      });

      QueryResult<String, GenerateFromAIParams> generateFromAIQuery = useQuery(
          generateFromAI, onSuccess: (result, params, onProcessResult) {
        restaurantProvider.aiRemainingCredits$
            .add(restaurantProvider.aiRemainingCredits$.value - 1);
        setValueFromAI(result);
        Navigator.pop(context);
      }, onError: (result, params, onProcessResult) {
        displaySnackbarWithClarifiedError(context, result);
      });

      return Scaffold(
          appBar: AppBarArrowBack(
            title: Translation.of(context).editWithAI,
            icon: const Icon(
              MalouIcons.cross,
              color: MalouColors.primary,
            ),
            actions: [
              StreamBuilder<bool>(
                  // optimizeQuery or generateQuery
                  stream: CombineLatestStream([
                    optimizeFromAiQuery.isFetching,
                    generateFromAIQuery.isFetching
                  ], (values) => values[0] || values[1]),
                  builder: (context, snapshot) {
                    if (snapshot.hasData && snapshot.data!) {
                      return const Center(
                          child: CircularSpinner(
                        color: MalouColors.primary,
                        size: 20,
                      ));
                    }
                    return const SizedBox();
                  })
            ],
          ),
          body: SafeArea(
              child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Column(
              children: [
                const SizedBox(
                  height: 10,
                ),
                ListTile(
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8)),
                  onTap: () {
                    optimizeFromAiQuery.execute(text);
                  },
                  leading: Text(Translation.of(context).optimize,
                      style: MalouTextStyles(context)
                          .text15Bold
                          .copyWith(color: MalouColors.textBody)),
                ),
                ListTile(
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8)),
                    onTap: () {
                      Navigator.push(context,
                          MaterialPageRoute(builder: (context) {
                        return AIGenerationChooseLanguage(
                            languages: languageProvider
                                .getAvailableLanguagesForAI(context),
                            textToTranslate: text,
                            translateFromAI: translateFromAI,
                            setValueFromAI: setValueFromAI);
                      }));
                    },
                    leading: Text(Translation.of(context).translate,
                        style: MalouTextStyles(context)
                            .text15Bold
                            .copyWith(color: MalouColors.textBody)),
                    trailing: const Icon(
                      Icons.chevron_right,
                      color: MalouColors.primary,
                      size: 26,
                    )),
              ],
            ),
          )));
    });
  }
}
