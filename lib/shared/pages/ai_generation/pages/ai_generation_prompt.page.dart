import 'dart:async';

import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/pages/ai_generation/pages/ai_image_analysis.controller.dart';
import 'package:malou/shared/pages/ai_generation/pages/ai_image_analysis_toggle.widget.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/widgets/appbar/appbar_back.widget.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/pages/ai_generation/pages/ai_generation_prompt.controller.dart';
import 'package:malou/shared/pages/ai_generation/pages/models/generate_from_ai_params.model.dart';
import 'package:malou/shared/pages/ai_generation/pages/models/help_idea_button_state.model.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/buttons/selected_button.widget.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';
import 'package:provider/provider.dart';
import 'package:malou/shared/widgets/buttons/styles.dart';

class AIPromptGeneration extends StatefulWidget {
  final Future<String> Function(GenerateFromAIParams) generateFromAI;
  final Future<List<String>> Function(GenerateFromAIParams)?
      generateFromAIAdvancedSettings;
  final void Function(String) setValueFromAI;
  final void Function()? onAiValidateSuggestion;
  final String label;
  final String placeholder;
  final PostsProvider? postProvider;
  const AIPromptGeneration(
      {super.key,
      required this.generateFromAI,
      required this.generateFromAIAdvancedSettings,
      required this.setValueFromAI,
      required this.label,
      required this.placeholder,
      this.onAiValidateSuggestion,
      this.postProvider});

  @override
  State<AIPromptGeneration> createState() => _AIPromptGenerationState();
}

class _AIPromptGenerationState extends State<AIPromptGeneration> {
  late AIGenerationPromptController aIGenerationPromptController;
  late AIImageAnalysisController aiImageAnalysisController;
  bool shouldHideImageAnalysisToggle = true;

  @override
  void initState() {
    super.initState();

    aiImageAnalysisController = AIImageAnalysisController(
        postsProvider: widget.postProvider,
        context: context,
        mounted: mounted,
        setState: (fn) => {if (mounted) setState(fn)});

    aIGenerationPromptController = AIGenerationPromptController(
      widget.generateFromAI,
      widget.generateFromAIAdvancedSettings,
      widget.setValueFromAI,
      context,
      languageProvider: Provider.of<LanguageProvider>(context, listen: false),
      restaurantProvider:
          Provider.of<RestaurantProvider>(context, listen: false),
      postsProvider: widget.postProvider,
    );

    shouldHideImageAnalysisToggle =
        aIGenerationPromptController.postsProvider?.postToEdit.source ==
                Source.social &&
            aIGenerationPromptController.postsProvider?.postToEdit.postType ==
                PostType.REEL;
  }

  @override
  void dispose() {
    aiImageAnalysisController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final double width = MediaQuery.of(context).size.width;
    final double height = MediaQuery.of(context).size.height;

    return StreamBuilder<bool>(
      stream: aIGenerationPromptController
          .generateFromAIAdvancedSettingsQuery.isFetching,
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const SizedBox();
        }
        bool isFetching = snapshot.data!;
        return Scaffold(
          resizeToAvoidBottomInset: false,
          appBar: AppBarArrowBack(
            title: Translation.of(context).artificialIntelligence,
            icon: const Icon(
              MalouIcons.cross,
              color: MalouColors.primary,
            ),
            actions: const [],
          ),
          body: SafeArea(
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const SizedBox(height: 10),
                          Container(
                            alignment: Alignment.centerLeft,
                            child: Padding(
                              padding: const EdgeInsets.only(left: 5),
                              child: Text(
                                widget.label,
                                style: MalouTextStyles(context).text12Semibold,
                              ),
                            ),
                          ),
                          const SizedBox(height: 10),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                  color: MalouColors.backgroundDark, width: 1),
                            ),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(
                                  width: width * 0.85,
                                  child: TextFormField(
                                    onTapOutside: (event) =>
                                        aIGenerationPromptController
                                            .focusTextFieldController
                                            .unfocus(),
                                    scrollController:
                                        aIGenerationPromptController
                                            .textFieldScrollController,
                                    focusNode: aIGenerationPromptController
                                        .focusTextFieldController,
                                    controller: aIGenerationPromptController
                                        .textEditingController,
                                    textCapitalization:
                                        TextCapitalization.sentences,
                                    keyboardType: TextInputType.multiline,
                                    minLines: 6,
                                    maxLines: 6,
                                    maxLength: 300,
                                    textAlignVertical: TextAlignVertical.top,
                                    textAlign: TextAlign.start,
                                    style: MalouTextStyles(context)
                                        .text13
                                        .copyWith(color: MalouColors.textTitle),
                                    decoration: InputDecoration(
                                      border: InputBorder.none,
                                      hintStyle:
                                          MalouTextStyles(context).text13Italic,
                                      hintText: widget.placeholder,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          if (!shouldHideImageAnalysisToggle)
                            aiImageAnalysisController
                                    .getDisableImageAnalysisTooltipText()
                                    .isNotEmpty
                                ? Tooltip(
                                    triggerMode: TooltipTriggerMode.tap,
                                    enableFeedback: true,
                                    message: aiImageAnalysisController
                                        .getDisableImageAnalysisTooltipText(),
                                    textStyle: MalouTextStyles(context)
                                        .text10
                                        .copyWith(color: Colors.white),
                                    child: AiImageAnalysisToggle(
                                      getDisableImageAnalysisTooltipText:
                                          aiImageAnalysisController
                                              .getDisableImageAnalysisTooltipText,
                                      isFetching: isFetching,
                                      isImageAnalysisToggleChecked:
                                          aiImageAnalysisController
                                              .isImageAnalysisToggleChecked,
                                      setIsImageAnalysisToggleChecked:
                                          (bool value) {
                                        setState(() {
                                          aiImageAnalysisController
                                                  .isImageAnalysisToggleChecked =
                                              value;
                                        });
                                      },
                                    ),
                                  )
                                : AiImageAnalysisToggle(
                                    getDisableImageAnalysisTooltipText:
                                        aiImageAnalysisController
                                            .getDisableImageAnalysisTooltipText,
                                    isFetching: isFetching,
                                    isImageAnalysisToggleChecked:
                                        aiImageAnalysisController
                                            .isImageAnalysisToggleChecked,
                                    setIsImageAnalysisToggleChecked:
                                        (bool value) {
                                      setState(() {
                                        aiImageAnalysisController
                                                .isImageAnalysisToggleChecked =
                                            value;
                                      });
                                    },
                                  ),
                          SizedBox(height: height * 0.02),
                          _buildCaptionsProposalButtons(
                            context,
                            aIGenerationPromptController,
                            widget.postProvider,
                            width,
                            height,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                // The button is outside the scrollable area, fixed at the bottom
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: StreamBuilder<String>(
                    stream: aIGenerationPromptController.text$,
                    builder: (context, snapshot) {
                      bool isDisabled =
                          ((!snapshot.hasData || snapshot.data!.isEmpty) &&
                                  !aiImageAnalysisController
                                      .isImageAnalysisToggleChecked) ||
                              isFetching;
                      return SecondaryButtonContainer(
                        fullWidth: true,
                        disabled: isDisabled,
                        child: ElevatedButton(
                          onPressed: () {
                            widget.postProvider?.captionsProposalButtons$
                                .add([]);
                            aIGenerationPromptController
                                .generateFromAIAdvancedSettingsQuery
                                .execute(GenerateFromAIParams(
                              description:
                                  aIGenerationPromptController.text$.value,
                              lang: aIGenerationPromptController
                                  .languageProvider.language.name,
                              shouldUseImageAnalysis: aiImageAnalysisController
                                  .isImageAnalysisToggleChecked,
                            ));
                          },
                          child: isFetching
                              ? const Center(
                                  child: CircularSpinner(
                                    color: MalouColors.purpleAccent,
                                    size: 25,
                                  ),
                                )
                              : Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(
                                      MalouIcons.ia,
                                      color: MalouColors.purpleAccent,
                                    ),
                                    const SizedBox(width: 10),
                                    StreamBuilder<
                                        List<CaptionProposalButtonState>>(
                                      stream: widget.postProvider
                                          ?.captionsProposalButtons$,
                                      builder: (context, snapshot) {
                                        return Text(
                                          snapshot.hasData &&
                                                  snapshot.data!.isNotEmpty
                                              ? Translation.of(context).retry
                                              : Translation.of(context)
                                                  .generate,
                                          style: MalouTextStyles(context)
                                              .text12Semibold
                                              .copyWith(
                                                  color:
                                                      MalouColors.purpleAccent),
                                        );
                                      },
                                    ),
                                  ],
                                ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCaptionsProposalButtons(
      BuildContext context,
      AIGenerationPromptController aIGenerationPromptController,
      PostsProvider? postProvider,
      double width,
      double height) {
    return StreamBuilder<List<CaptionProposalButtonState>>(
      stream: postProvider?.captionsProposalButtons$,
      builder: (context, snapshot) {
        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const SizedBox();
        }
        return Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  Translation.of(context).proposals,
                  style: MalouTextStyles(context)
                      .text13Bold
                      .copyWith(color: MalouColors.textTitle),
                ),
              ],
            ),
            SizedBox(
              height: height * 0.01,
            ),
            Column(
              children: snapshot.data!
                  .map((buttonState) => SelectedButton(
                        margin: const EdgeInsets.only(bottom: 10),
                        isLongText: true,
                        alignment: Alignment.centerLeft,
                        buttonText: buttonState.buttonText,
                        selected: buttonState.selected,
                        onPressed: () => aIGenerationPromptController
                            .setSelectedButton(buttonState),
                      ))
                  .toList(),
            )
          ],
        );
      },
    );
  }
}
