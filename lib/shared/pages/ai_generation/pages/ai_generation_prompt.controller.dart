import 'package:flutter/material.dart';
import 'package:malou/shared/helpers/clarify_error.dart';
import 'package:malou/shared/helpers/use_query.dart';
import 'package:malou/shared/pages/ai_generation/pages/models/generate_from_ai_params.model.dart';
import 'package:malou/shared/pages/ai_generation/pages/models/help_idea_button_state.model.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:rxdart/rxdart.dart';

class AIGenerationPromptController {
  final BuildContext context;
  final LanguageProvider languageProvider;
  final RestaurantProvider restaurantProvider;
  final PostsProvider? postsProvider;
  final ScrollController textFieldScrollController = ScrollController();
  final TextEditingController textEditingController = TextEditingController();
  final FocusNode focusTextFieldController = FocusNode();
  final Future<String> Function(GenerateFromAIParams) generateFromAI;
  final Future<List<String>> Function(GenerateFromAIParams)?
      generateFromAIAdvancedSettings;
  final void Function(String) setValueFromAI;

  late QueryResult<List<String>, GenerateFromAIParams>
      generateFromAIAdvancedSettingsQuery;
  late QueryResult<String, GenerateFromAIParams> generateFromAIQuery;

  BehaviorSubject<String> text$ = BehaviorSubject<String>.seeded('');

  AIGenerationPromptController(
    this.generateFromAI,
    this.generateFromAIAdvancedSettings,
    this.setValueFromAI,
    this.context, {
    required this.languageProvider,
    required this.restaurantProvider,
    this.postsProvider,
  }) {
    textEditingController.addListener(() {
      text$.add(textEditingController.text);
    });

    generateFromAIQuery =
        useQuery(generateFromAI, onSuccess: (result, params, onProcessResult) {
      restaurantProvider.aiRemainingCredits$
          .add(restaurantProvider.aiRemainingCredits$.value - 1);
      setValueFromAI(result);
      Navigator.pop(context);
    }, onError: (result, params, onProcessResult) {
      displaySnackbarWithClarifiedError(context, result);
    });

    generateFromAIAdvancedSettingsQuery =
        useQuery(generateFromAIAdvancedSettings ?? (params) async => [],
            onSuccess: (result, params, onProcessResult) {
      restaurantProvider.aiRemainingCredits$
          .add(restaurantProvider.aiRemainingCredits$.value - 1);
      List<String> captionsWithoutHashtags = result
          .map((caption) =>
              caption.replaceAllMapped(RegExp(r'#[a-zA-Z0-9]+'), (match) => ''))
          .toList();
      List<CaptionProposalButtonState> captionsButtons = captionsWithoutHashtags
          .map((caption) => CaptionProposalButtonState(
              selected: false, buttonText: caption, content: caption))
          .toList();
      postsProvider?.captionsProposalButtons$.add(captionsButtons);
    }, onError: (result, params, onProcessResult) {
      displaySnackbarWithClarifiedError(context, result);
    });
  }

  String _removeExistingCaptionsFromText(
      List<CaptionProposalButtonState> captions) {
    String currentText =
        postsProvider?.postToEdit.postTextController!.value.text ?? '';
    List<String> captionTexts = captions.map((e) => e.content).toList();
    for (var captionText in captionTexts) {
      currentText = currentText
          .replaceAll('\n$captionText', '')
          .replaceAll(captionText, '');
    }
    return currentText;
  }

  String _concatTextWithCaption(String? text, String caption) {
    return text != null && text.isNotEmpty ? '$text\n$caption' : caption;
  }

  void _setValueFromAIWithCaption(
      List<CaptionProposalButtonState> captions, int index) {
    String cleanedText = _removeExistingCaptionsFromText(captions);
    String newText =
        _concatTextWithCaption(cleanedText, captions[index].content);
    setValueFromAI(newText);
  }

  void setSelectedButton(CaptionProposalButtonState buttonState) {
    final currentListValue = postsProvider?.captionsProposalButtons$.value;
    if (currentListValue == null || currentListValue.isEmpty) {
      return;
    }
    final index = currentListValue.indexOf(buttonState);
    final List<CaptionProposalButtonState> newSelectedButtonState =
        currentListValue.map((state) {
      return CaptionProposalButtonState(
          selected: false,
          buttonText: state.buttonText,
          content: state.content);
    }).toList();
    newSelectedButtonState[index] = CaptionProposalButtonState(
        selected: true,
        buttonText: buttonState.buttonText,
        content: buttonState.content);

    postsProvider?.captionsProposalButtons$.add(newSelectedButtonState);
    _setValueFromAIWithCaption(newSelectedButtonState, index);
    Navigator.pop(context);
  }
}
