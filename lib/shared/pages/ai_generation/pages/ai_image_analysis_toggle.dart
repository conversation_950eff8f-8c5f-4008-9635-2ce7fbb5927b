import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AiImageAnalysisToggle extends StatefulWidget {
  final String Function() getDisableImageAnalysisTooltipText;
  final bool isFetching;
  final bool isImageAnalysisToggleChecked;
  final void Function(bool) setIsImageAnalysisToggleChecked;
  const AiImageAnalysisToggle(
      {super.key,
      required this.getDisableImageAnalysisTooltipText,
      required this.isFetching,
      required this.isImageAnalysisToggleChecked,
      required this.setIsImageAnalysisToggleChecked});

  @override
  State<AiImageAnalysisToggle> createState() => _AiImageAnalysisToggleState();
}

class _AiImageAnalysisToggleState extends State<AiImageAnalysisToggle> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        children: [
          Text(
            Translation.of(context).useImageForPostGeneration,
            style: MalouTextStyles(context).text12.copyWith(
                  color: MalouColors.textTitle,
                ),
          ),
          const Spacer(),
          SizedBox(
            width: 40,
            child: Opacity(
              opacity: widget.getDisableImageAnalysisTooltipText().isNotEmpty ||
                      widget.isFetching
                  ? 0.5
                  : 1,
              child: Switch(
                  value: widget.isImageAnalysisToggleChecked,
                  onChanged: (bool value) async {
                    bool isDisabled = widget
                            .getDisableImageAnalysisTooltipText()
                            .isNotEmpty ||
                        widget.isFetching;
                    if (isDisabled) {
                      return;
                    }
                    final SharedPreferences preferences =
                        await SharedPreferences.getInstance();
                    preferences.setBool(
                        'shouldUseImageAnalysisInPostsByDefault', value);
                    widget.setIsImageAnalysisToggleChecked(value);
                  }),
            ),
          ),
        ],
      ),
    );
  }
}
