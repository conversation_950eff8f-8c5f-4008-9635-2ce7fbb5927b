import 'package:malou/pages/router.dart';

class NotificationPayload {
  final String? title;
  final String? body;
  final NotificationPayloadData? data;

  NotificationPayload({this.title, this.body, this.data});

  factory NotificationPayload.fromJson(Map<String, dynamic> json) {
    NotificationPayloadData? data =
        NotificationPayloadData.fromJson(json['data']);
    if (data.isTranslationRequired == 'true') {
      return NotificationPayload(
          title: json['title'], body: json['body'], data: data);
    }

    return NotificationPayload(
        title: json['title'], body: json['body'], data: data);
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'body': body,
      'data': data,
    };
  }

  PageToPushWithArgs? getPageToPushWithArguments(String restaurantId) {
    switch (data?.type) {
      case NotificationType.NEW_MESSAGE:
        return PageToPushWithArgs(
            pageToPush:
                '/restaurant/$restaurantId/conversations/conversation/${data?.conversationId}',
            pageBottomAppBarIndex: CONVERSATIONS_TAB_INDEX,
            arguments: {'conversationId': data?.conversationId});
      case NotificationType.REVIEW:
        return PageToPushWithArgs(
            pageToPush: '/restaurant/$restaurantId/reviews',
            pageBottomAppBarIndex: REVIEWS_TAB_INDEX,
            arguments: data);

      case NotificationType.DAILY_UNREAD_MESSAGES_NOTIFICATION:
        return PageToPushWithArgs(
            pageToPush: '/restaurant/$restaurantId/conversations',
            pageBottomAppBarIndex: CONVERSATIONS_TAB_INDEX,
            arguments: null);

      case NotificationType.REVIEW_REPLY_REMINDER:
        return PageToPushWithArgs(
            pageToPush: '/restaurant/$restaurantId/reviews',
            pageBottomAppBarIndex: REVIEWS_TAB_INDEX,
            arguments: null);
      case NotificationType.NO_MORE_SCHEDULED_POSTS:
        return PageToPushWithArgs(
            pageToPush: '/restaurant/$restaurantId/posts',
            pageBottomAppBarIndex: POSTS_TAB_INDEX,
            arguments: null);
      default:
        return null;
    }
  }
}

class PageToPushWithArgs {
  final String? pageToPush;
  final int? pageBottomAppBarIndex;
  final Object? arguments;

  PageToPushWithArgs(
      {required this.pageToPush,
      required this.pageBottomAppBarIndex,
      required this.arguments});
}

class NotificationPayloadData {
  NotificationType? type;
  // it's a boolean as a string because we cannot send other data types in a notification payload data
  String? isTranslationRequired;
  String? conversationId;
  String? messageId;
  String? restaurantId;
  String? reviewId;
  String? unreadMessagesCount;
  String? reviewRating;
  String? unansweredReviewsCount;
  String? language;
  String userId;
  String notificationId;

  NotificationPayloadData(
      {required this.notificationId,
      this.isTranslationRequired,
      this.conversationId,
      this.messageId,
      this.restaurantId,
      this.reviewId,
      this.unreadMessagesCount,
      this.reviewRating,
      this.unansweredReviewsCount,
      this.type,
      this.language,
      this.userId = ''});

  factory NotificationPayloadData.fromJson(Map<String, dynamic> json) {
    return NotificationPayloadData(
        notificationId: json['notificationId'],
        isTranslationRequired: json['isTranslationRequired'],
        conversationId: json['conversationId'],
        messageId: json['messageId'],
        restaurantId: json['restaurantId'],
        reviewId: json['reviewId'],
        unreadMessagesCount: json['unreadMessagesCount'],
        reviewRating: json['reviewRating'],
        unansweredReviewsCount: json['unansweredReviewsCount'],
        type: NotificationType.values
            .byName(json['type']?.toString().toUpperCase() ?? 'DEFAULT'),
        language: json['language'],
        userId: json['userId'] ?? '');
  }
}

enum NotificationType {
  REVIEW_REPLY_REMINDER,
  NEW_MESSAGE,
  REVIEW,
  DAILY_UNREAD_MESSAGES_NOTIFICATION,
  NO_MORE_SCHEDULED_POSTS,
  DEFAULT,
}
