import 'dart:convert';

import 'package:firebase_messaging/firebase_messaging.dart' as fcm;
import 'package:flutter_app_badge/flutter_app_badge.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:malou/app_config.dart';
import 'package:malou/error_logger.dart';

import 'package:malou/notifications/notification_payload.dart';
import 'package:malou/pages/router.dart';
import 'package:malou/shared/helpers/heap.service.dart';
import 'package:malou/shared/models/user.model.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:malou/shared/helpers/http_client.dart';

void onDidReceiveBackgroundNotification(NotificationResponse response) {
  NotificationService.instance.showNotification(response);
}

void onBackgroundNotificationTap(NotificationPayload payload,
    RestaurantProvider restaurantProvider, UserProvider userProvider) async {
  FlutterAppBadge.count(0);
  var restaurant = restaurantProvider.getUserRestaurantById(
      restaurantId: payload.data?.restaurantId ?? '');
  restaurant ??= await restaurantProvider
      .getRestaurantById(payload.data?.restaurantId ?? '');
  var organization =
      userProvider.getOrganizationById(restaurant?.organizationId ?? '');
  HeapService.instance.track('notification_tapped', {
    'type': payload.data?.type,
    'restaurantId': payload.data?.restaurantId,
    'restaurant':
        '${restaurant?.name} - ${restaurant?.getFullFormattedAddress()}',
    'restaurantOrganisation': organization?.name
  });
  if (payload.data?.notificationId != null) {
    NotificationService.instance
        .markNotificationAsRead(payload.data!.notificationId);
  }
  restaurantProvider.currentRestaurant = restaurantProvider
      .getUserRestaurantById(restaurantId: payload.data?.restaurantId);

  PageToPushWithArgs? pageToPushWithArgs = payload
      .getPageToPushWithArguments(restaurantProvider.currentRestaurant!.id);

  restaurantProvider.selectedBottomAppBarIndex =
      pageToPushWithArgs?.pageBottomAppBarIndex ?? 0;

  router.go(pageToPushWithArgs!.pageToPush!,
      extra: pageToPushWithArgs.arguments);
}

void onNotificationTap(NotificationPayload payload,
    RestaurantProvider restaurantProvider, UserProvider userProvider) async {
  FlutterAppBadge.count(0);
  var restaurant = restaurantProvider.getUserRestaurantById(
      restaurantId: payload.data?.restaurantId ?? '');

  restaurant ??= await restaurantProvider
      .getRestaurantById(payload.data?.restaurantId ?? '');

  var organization =
      userProvider.getOrganizationById(restaurant?.organizationId ?? '');

  HeapService.instance.track('notification_tapped', {
    'type': payload.data?.type,
    'restaurantId': payload.data?.restaurantId,
    'restaurant':
        '${restaurant?.name} - ${restaurant?.getFullFormattedAddress()}',
    'restaurantOrganisation': organization?.name
  });

  if (payload.data?.notificationId != null) {
    NotificationService.instance
        .markNotificationAsRead(payload.data!.notificationId);
  }
  if (restaurantProvider.currentRestaurant?.id != payload.data?.restaurantId) {
    restaurantProvider.currentRestaurant = restaurantProvider
        .getUserRestaurantById(restaurantId: payload.data?.restaurantId);
  }

  PageToPushWithArgs? pageToPushWithArgs =
      payload.getPageToPushWithArguments(payload.data!.restaurantId!);

  restaurantProvider.selectedBottomAppBarIndex =
      pageToPushWithArgs?.pageBottomAppBarIndex ?? 0;

  router.go(
    pageToPushWithArgs!.pageToPush!,
    extra: pageToPushWithArgs.arguments,
  );
}

class NotificationService {
  NotificationService._();
  bool isAlreadyInitialized = false;
  NotificationPayload? payload;
  String? currentRoute = '/restaurants';

  static final NotificationService instance = NotificationService._();

  final FlutterLocalNotificationsPlugin flutterNotification =
      FlutterLocalNotificationsPlugin();
  final fcm.FirebaseMessaging _fcm = fcm.FirebaseMessaging.instance;

  showNotification(NotificationResponse message) async {
    payload = NotificationPayload.fromJson(jsonDecode(message.payload!));
    var prefs = await SharedPreferences.getInstance();
    if (!prefs.containsKey('user')) {
      return;
    }
    final user = User.fromJson(jsonDecode(prefs.getString('user')!));
    if (user.id != payload?.data?.userId) {
      return;
    }
    FlutterAppBadge.count(1);
    var androidDetails = const AndroidNotificationDetails(
        "channelId", "channelName",
        importance: Importance.max,
        priority: Priority.max,
        enableVibration: true);
    var iosDetails = const DarwinNotificationDetails();
    var generalNotificationDetails =
        NotificationDetails(android: androidDetails, iOS: iosDetails);

    await flutterNotification.show(
        0, payload?.title, payload?.body, generalNotificationDetails);
  }

  getAndSetUserDeviceToken(bool mounted, UserProvider userProvider) async {
    String? token = await _fcm.getToken();
    if (mounted) {
      userProvider.addDeviceToken(token!);
    }
  }

  Future requestPermission(UserProvider userProvider) async {
    Future<fcm.NotificationSettings> requestPermissions() {
      return _fcm.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: false,
      );
    }

    return requestPermissions().then((value) async {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      bool firstTimeAskForNotifications =
          prefs.getBool('first_time_ask_for_notifications') ?? false;
      if (!firstTimeAskForNotifications &&
          value.authorizationStatus == fcm.AuthorizationStatus.authorized) {
        prefs.setBool('first_time_ask_for_notifications', true);
        var user = userProvider.currentUser!.copyWith(
            settings: userProvider.currentUser!.settings.copyWith(
                notificationSettings: userProvider
                    .currentUser!.settings.notificationSettings!
                    .copyWith(active: true)));

        userProvider.currentUser = user;
        userProvider.updateUser({
          'settings': {
            ...user.settings.toJson(),
            'notificationSettings': {
              ...user.settings.notificationSettings!.toJson(),
              'active': true,
            }
          }
        });
        return null;
      } else if (!firstTimeAskForNotifications &&
          value.authorizationStatus == fcm.AuthorizationStatus.denied) {
        prefs.setBool('first_time_ask_for_notifications', true);
        var user = userProvider.currentUser!.copyWith(
            settings: userProvider.currentUser!.settings.copyWith(
                notificationSettings: userProvider
                    .currentUser!.settings.notificationSettings!
                    .copyWith(active: false)));
        userProvider.currentUser = user;
        userProvider.updateUser({
          'settings': {
            ...user.settings.toJson(),
            'notificationSettings': {
              ...user.settings.notificationSettings!.toJson(),
              'active': false,
            }
          }
        });
        return null;
      }
    });
  }

  Future<bool> areNotificationsAuthorized() {
    return _fcm.getNotificationSettings().then((value) =>
        value.authorizationStatus == fcm.AuthorizationStatus.authorized);
  }

  Future<void> deleteDeviceToken(UserProvider userProvider) async {
    final token = await fcm.FirebaseMessaging.instance.getToken();
    if (token != null) {
      fcm.FirebaseMessaging.instance.deleteToken();
    }
  }

  initialize(
      bool mounted,
      UserProvider userProvider,
      RestaurantProvider restaurantProvider,
      void Function(NotificationPayload, RestaurantProvider, UserProvider)
          onNotificationTap) {
    if (isAlreadyInitialized) {
      return;
    }
    isAlreadyInitialized = true;

    NotificationService.instance.requestPermission(userProvider).then((value) =>
        {NotificationService.instance.requestPermission(userProvider)});

    var androidInit =
        const AndroidInitializationSettings('@mipmap/ic_launcher');
    var iosInit = const DarwinInitializationSettings();

    var initSettings =
        InitializationSettings(android: androidInit, iOS: iosInit);
    flutterNotification.initialize(initSettings,
        onDidReceiveNotificationResponse: (_) =>
            onNotificationTap(payload!, restaurantProvider, userProvider),
        onDidReceiveBackgroundNotificationResponse:
            onDidReceiveBackgroundNotification);

    fcm.FirebaseMessaging.onMessage.listen((fcm.RemoteMessage message) {
      Map<String, dynamic> payload = {
        'title': message.notification?.title,
        'body': message.notification?.body,
        'data': message.data
      };
      showNotification(NotificationResponse(
          notificationResponseType:
              NotificationResponseType.selectedNotification,
          payload: jsonEncode(payload)));
    });

    fcm.FirebaseMessaging.onMessageOpenedApp
        .listen((fcm.RemoteMessage message) {
      FlutterAppBadge.count(0);
      Map<String, dynamic> payload = {
        'title': message.notification?.title,
        'body': message.notification?.body,
        'data': message.data
      };
      onNotificationTap(
          NotificationPayload.fromJson(jsonDecode(jsonEncode(payload))),
          restaurantProvider,
          userProvider);
    });

    fcm.FirebaseMessaging.instance.getInitialMessage().then((message) {
      if (message != null) {
        FlutterAppBadge.count(0);
        Map<String, dynamic> payload = {
          'title': message.notification?.title,
          'body': message.notification?.body,
          'data': message.data
        };

        onBackgroundNotificationTap(
            NotificationPayload.fromJson(jsonDecode(jsonEncode(payload))),
            restaurantProvider,
            userProvider);
      }
    });

    _fcm.onTokenRefresh.listen((event) {
      // add new token
      getAndSetUserDeviceToken(mounted, userProvider);
    });

    getAndSetUserDeviceToken(
      mounted,
      userProvider,
    );
  }

  markNotificationAsRead(String notificationId) {
    final apiUrl = '${AppConfig.shared.baseUrl}/';
    final httpClient = DioClient.instance.client;
    httpClient.put(
      '$apiUrl/notifications',
      data: {
        "readAt": DateTime.now().toIso8601String(),
        "completedAt": DateTime.now().toIso8601String(),
        "notificationIds": [notificationId]
      },
    ).catchError((error) {
      log('Error marking notification as read: $error');
    });
  }
}
