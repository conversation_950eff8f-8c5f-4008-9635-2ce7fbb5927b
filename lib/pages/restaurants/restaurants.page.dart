import 'package:flutter/material.dart';
import 'package:flutter_app_badge/flutter_app_badge.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/notifications/notifications.service.dart';
import 'package:malou/pages/restaurants/widgets/restaurants_list.widget.dart';
import 'package:malou/shared/helpers/sentry.service.dart';
import 'package:malou/shared/services/organization/organization.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/inputs/text_search.widget.dart';
import 'package:provider/provider.dart';

class RestaurantsPage extends StatefulWidget {
  const RestaurantsPage({super.key});

  @override
  State<RestaurantsPage> createState() => RestaurantsPageState();
}

@visibleForTesting
class RestaurantsPageState extends State<RestaurantsPage> {
  bool loadingRestaurants = true;
  String? errorMessage;
  late RestaurantProvider restaurantProvider;
  late UserProvider userProvider;
  late Future<void> _restaurantsAndUser;
  final TextEditingController searchController = TextEditingController();

  Future<void> _onRefresh() async {
    await restaurantProvider
        .setDefaultRestaurantSelected(userProvider.currentUser!.id);
    // need to refetch user in the case where we modify the restaurants we manage in the admin deskop section
    // because model User contains a list of userRestaurants where there is settings for each restaurant
    await userProvider.refetchUser();
  }

  @override
  void initState() {
    super.initState();
    restaurantProvider =
        Provider.of<RestaurantProvider>(context, listen: false);
    userProvider = Provider.of<UserProvider>(context, listen: false);
    _restaurantsAndUser = _onRefresh();

    FlutterAppBadge.count(0);
    NotificationService.instance.initialize(
        mounted, userProvider, restaurantProvider, onNotificationTap);

    SentryService.instance.addUserInfos(userProvider, restaurantProvider);
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: Scaffold(
          appBar: AppBar(
            backgroundColor: Colors.white,
            elevation: 0,
            toolbarHeight: 10,
          ),
          backgroundColor: Colors.white,
          // The safe area will make sure that the content is not behind the status bar
          body: SafeArea(
            child: Consumer3<UserProvider, RestaurantProvider,
                    OrganizationProvider>(
                builder: (context, userProvider, restaurantProvider,
                    organizationProvider, child) {
              return Container(
                color: MalouColors.backgroundLight,
                child: GestureDetector(
                  onTap: () => FocusScope.of(context).unfocus(),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: CustomScrollView(
                      slivers: <Widget>[
                        SliverToBoxAdapter(
                            child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  IconButton(
                                      constraints: const BoxConstraints(),
                                      padding: EdgeInsets.zero,
                                      onPressed: () =>
                                          context.push('/settings'),
                                      icon: const Icon(
                                        Icons.settings_outlined,
                                        color: MalouColors.primary,
                                      ))
                                ],
                              ),
                            ),
                            Text(
                              'Hello ${userProvider.currentUser!.name} !',
                              style: MalouTextStyles(context)
                                  .text20Semibold
                                  .copyWith(fontSize: 33),
                            ),
                            const SizedBox(height: 20),
                            TextSearch(
                              controller: searchController,
                              onChanged: (val) => setState(() {
                                onSearchTextChanged(val, restaurantProvider);
                              }),
                              onClearText: () => setState(() {
                                onSearchTextChanged('', restaurantProvider);
                              }),
                            ),
                            const SizedBox(height: 20),
                          ],
                        )),
                        SliverList(
                          delegate: SliverChildListDelegate([
                            FutureBuilder(
                                future: _restaurantsAndUser,
                                builder: (context, snapshot) {
                                  if (snapshot.connectionState ==
                                      ConnectionState.waiting) {
                                    return const Center(
                                        child: CircularProgressIndicator());
                                  }
                                  if (snapshot.hasError) {
                                    return Center(
                                      child: Text(snapshot.error.toString()),
                                    );
                                  }
                                  return RefreshIndicator(
                                    onRefresh: _onRefresh,
                                    child: RestaurantsList(
                                      filterText: searchController.text,
                                      restaurantProvider: restaurantProvider,
                                      organizationProvider:
                                          organizationProvider,
                                    ),
                                  );
                                }),
                          ]),
                        )
                      ],
                    ),
                  ),
                ),
              );
            }),
          )),
    );
  }

  @visibleForTesting
  void onSearchTextChanged(String text, RestaurantProvider restaurantProvider) {
    if (text.isEmpty) {
      searchController.clear();
      restaurantProvider.filteredUserRestaurants
          .add(restaurantProvider.userRestaurants);
      return;
    }
    restaurantProvider.filteredUserRestaurants.add(restaurantProvider
        .userRestaurants
        .where((element) =>
            element.name.toLowerCase().contains(text.toLowerCase()))
        .toList());
  }
}
