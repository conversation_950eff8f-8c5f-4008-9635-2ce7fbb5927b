import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/models/restaurant.model.dart';
import 'package:malou/shared/widgets/image/cached_image.widget.dart';

class RestaurantCard extends StatelessWidget {
  const RestaurantCard({
    super.key,
    required this.restaurant,
  });

  final Restaurant restaurant;

  @override
  Widget build(BuildContext context) {
    String? restaurantCover = restaurant.cover?.getUrlForSize(
      preferedSize: MediaSize.igFit,
    );
    String? restaurantLogo =
        restaurant.logo?.getUrlForSize(preferedSize: MediaSize.igFit);

    return Container(
      height: 240,
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 20),
      padding: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: MalouColors.primary.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Stack(
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              restaurantCover == null
                  ? Container(
                      height: 100,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(15),
                          topRight: Radius.circular(15),
                        ),
                        image: DecorationImage(
                          image: AssetImage(
                            IllustrationsPathResolver.resolve(
                                Illustrations.defaultMalou),
                          ),
                          fit: BoxFit.cover,
                        ),
                      ))
                  : Container(
                      height: 100,
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(15),
                          topRight: Radius.circular(15),
                        ),
                        image: DecorationImage(
                          image: CachedNetworkImageProvider(restaurantCover),
                          fit: BoxFit.cover,
                        ),
                      )),
              Container(
                margin: const EdgeInsets.only(left: 20),
                child: Text(
                  restaurant.isBrandBusiness()
                      ? Translation.of(context).brandAccount
                      : restaurant.getFullFormattedAddress(context: context),
                  style: MalouTextStyles(context).text13,
                ),
              ),
            ],
          ),
          Container(
              margin: const EdgeInsets.only(left: 20, top: 20),
              alignment: Alignment.centerLeft,
              child: Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.white,
                    ),
                    child: restaurantLogo == null
                        ? Image.asset(
                            height: 100,
                            width: 100,
                            fit: BoxFit.cover,
                            IllustrationsPathResolver.resolve(
                                Illustrations.defaultMalou),
                            frameBuilder: (context, child, frame,
                                wasSynchronouslyLoaded) {
                              return ClipRRect(
                                borderRadius: BorderRadius.circular(10),
                                child: child,
                              );
                            },
                          )
                        : CachedImage(
                            radius: BorderRadius.circular(10),
                            url: restaurantLogo,
                            height: 100,
                            width: 100,
                            fit: BoxFit.cover,
                          ),
                  ),
                  Container(
                    width: 160,
                    margin: const EdgeInsets.only(left: 10, top: 30),
                    child: Text(
                      restaurant.name,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                      style: MalouTextStyles(context).text20Bold,
                    ),
                  ),
                ],
              )),
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              margin: const EdgeInsets.only(top: 20, right: 20),
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                  color: restaurant.isBrandBusiness()
                      ? MalouColors.warning
                      : MalouColors.primary,
                  borderRadius: BorderRadius.circular(100)),
              child: Center(
                child: Icon(
                  restaurant.isBrandBusiness()
                      ? Icons.favorite_border
                      : Icons.location_on_outlined,
                  color: Colors.white,
                  size: 18,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
