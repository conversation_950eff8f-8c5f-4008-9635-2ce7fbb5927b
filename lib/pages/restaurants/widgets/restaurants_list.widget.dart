import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurants/widgets/restaurant_card.widget.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/models/restaurant.model.dart';
import 'package:malou/shared/services/organization/organization.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/buttons/styles.dart';
import 'package:url_launcher/url_launcher.dart';

class RestaurantsList extends StatelessWidget {
  final RestaurantProvider restaurantProvider;
  final OrganizationProvider organizationProvider;
  final String filterText;
  const RestaurantsList({
    super.key,
    required this.restaurantProvider,
    required this.organizationProvider,
    required this.filterText,
  });

  void onTapRestaurant(Restaurant restaurant, BuildContext context) {
    restaurantProvider.currentRestaurant = restaurant;
    restaurantProvider.filteredUserRestaurants
        .add(restaurantProvider.userRestaurants);
    context.go('/restaurant/${restaurant.id}/home');
  }

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    var filteredRestaurants = restaurantProvider.filteredUserRestaurants;
    return SizedBox(
      height: size.height * 0.7,
      child: StreamBuilder(
          stream: filteredRestaurants,
          builder: (context, snapshot) {
            if (snapshot.hasData && snapshot.data!.isNotEmpty) {
              return ListView.builder(
                  cacheExtent: 1,
                  primary: true,
                  shrinkWrap: true,
                  itemCount: snapshot.data!.length,
                  itemBuilder: (context, index) {
                    final restaurant = snapshot.data![index];

                    return GestureDetector(
                        onTap: () {
                          onTapRestaurant(restaurant, context);
                        },
                        child: RestaurantCard(restaurant: restaurant));
                  });
            }
            return RestaurantsListEmpty(size: size, filterText: filterText);
          }),
    );
  }
}

class RestaurantsListEmpty extends StatelessWidget {
  const RestaurantsListEmpty({
    super.key,
    required this.size,
    required this.filterText,
  });

  final Size size;
  final String filterText;

  @override
  Widget build(BuildContext context) {
    return Center(
        child: Column(
      children: [
        SizedBox(height: size.height * 0.1),
        Image.asset(
            height: 100,
            IllustrationsPathResolver.resolve(filterText.isEmpty
                ? Illustrations.burgerFlag
                : Illustrations.cook)),
        const SizedBox(height: 20),
        Text(
            filterText.isEmpty
                ? Translation.of(context).noRestaurantQuestion
                : Translation.of(context).oupsSeeNothing,
            style: MalouTextStyles(context).text14Bold),
        const SizedBox(height: 10),
        Text(
            filterText.isEmpty
                ? Translation.of(context).addYourFirstRestaurant
                : Translation.of(context).noRestaurantForYourResearch,
            textAlign: TextAlign.center,
            style: MalouTextStyles(context).text12Italic),
        const SizedBox(height: 20),
        filterText.isEmpty
            ? PrimaryButtonContainer(
                child: ElevatedButton(
                    onPressed: redirectToAppDesktop,
                    child: Text(Translation.of(context).addARestaurant)))
            : Container()
      ],
    ));
  }

  void redirectToAppDesktop() async {
    Uri url = Uri.parse("https://v3.app.malou.io/restaurants/list");
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }
}
