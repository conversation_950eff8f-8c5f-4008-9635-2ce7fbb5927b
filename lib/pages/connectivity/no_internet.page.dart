import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/buttons/styles.dart';

class NoInternetPage extends StatelessWidget {
  const NoInternetPage({super.key});

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;

    return Scaffold(
      body: Container(
        padding: const EdgeInsets.all(20),
        width: double.infinity,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(IllustrationsPathResolver.resolve(Illustrations.cook)),
            const SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Text(
                Translation.of(context).youreDisconnectedFromInternet,
                textAlign: TextAlign.center,
                style: MalouTextStyles(context).text13Semibold.copyWith(
                      color: MalouColors.textTitle,
                    ),
              ),
            ),
              const SizedBox(height: 20),
            SizedBox(
              width: size.width * .6,
              child: Text(
                Translation.of(context).reconnectToInternet,
                style: MalouTextStyles(context).text12Italic,
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 20),
            PrimaryButtonContainer(
                fullWidth: true,
                child: ElevatedButton(
                  child: Text(Translation.of(context).refreshPage),
                  onPressed: () {
                    context.go('/login');
                  },
                )),
          ],
        ),
      ),
    );
  }
}
