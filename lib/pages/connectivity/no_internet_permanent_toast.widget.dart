import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/services/connectivity/connectivity.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:provider/provider.dart';

class NoInternetPermanentToast extends StatelessWidget {
  const NoInternetPermanentToast({super.key, required this.shouldShow});

  final bool shouldShow;

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    if (shouldShow) return Container();
    return StreamBuilder(
      stream: context.read<ConnectivityProvider>().isConnected$,
      builder: (context, snapshot) {
        if (snapshot.hasData && snapshot.data! == false) {
          return Material(
              type: MaterialType.transparency,
              child: Container(
                  color: Colors.black26,
                  height: size.height,
                  width: size.width,
                  child: Align(
                      alignment: Alignment.center,
                      child: Center(
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 15),
                          height: 150,
                          width: MediaQuery.of(context).size.width * 0.9,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10),
                            boxShadow: const [MalouColors.boxShadow],
                          ),
                          child: Row(
                            children: [
                              Image.asset(
                                  IllustrationsPathResolver.resolve(
                                      Illustrations.cook),
                                  height: 50),
                              const SizedBox(width: 10),
                              SizedBox(
                                width: MediaQuery.of(context).size.width * 0.6,
                                child: Text(
                                  Translation.of(context)
                                      .youreDisconnectedFromInternet,
                                  style: MalouTextStyles(context).text12Medium,
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ))));
        }
        return Container();
      },
    );
  }
}
