import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/pages/connectivity/no_internet.page.dart';
import 'package:malou/pages/router.dart';

GoRoute buildConnectivityRoutes() {
  return GoRoute(
      parentNavigatorKey: rootShellNavigatorKey,
      path: 'no-internet',
      pageBuilder: (context, state) => CustomTransitionPage(
            child: const NoInternetPage(),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) =>
                    FadeTransition(
              opacity: animation,
              child: child,
            ),
          ));
}
