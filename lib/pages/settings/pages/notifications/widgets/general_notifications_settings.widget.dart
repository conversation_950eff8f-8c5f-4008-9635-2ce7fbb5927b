import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/settings/pages/notifications/notifications_page.controller.dart';
import 'package:malou/pages/settings/pages/notifications/widgets/notification_toggle.widget.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

class GeneralNotificationsSettings extends StatelessWidget {
  final bool notificationsEnabled;
  final UserProvider userProvider;
  final NotificationsPageController controller;

  const GeneralNotificationsSettings(
      {super.key,
      required this.notificationsEnabled,
      required this.userProvider,
      required this.controller});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(Translation.of(context).general,
            style: MalouTextStyles(context)
                .text15Semibold
                .copyWith(color: MalouColors.textTitle)),
        const SizedBox(height: 10),
        Container(
          height: 59,
          width: double.infinity,
          decoration: BoxDecoration(
              color: MalouColors.backgroundLight,
              border: Border.all(color: MalouColors.borderPrimary, width: 1),
              borderRadius: const BorderRadius.all(Radius.circular(10.0))),
          child: Padding(
              padding: const EdgeInsets.only(left: 19, right: 19),
              child: NotificationToggle(
                title: Translation.of(context).enableNotifications,
                value: notificationsEnabled,
                onToggle: (bool value) => controller.toggleAllNotifications(
                    value, userProvider, context),
              )),
        ),
      ],
    );
  }
}
