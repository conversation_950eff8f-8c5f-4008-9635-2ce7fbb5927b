import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/settings/pages/notifications/notifications_page.controller.dart';
import 'package:malou/pages/settings/pages/notifications/widgets/notification_toggle.widget.dart';
import 'package:malou/shared/models/user.model.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

class ReviewsNotificationsSettings extends StatelessWidget {
  final bool notificationsEnabled;
  final UserProvider userProvider;
  final ReviewsSettings reviewsSettings;
  final NotificationsPageController controller;

  const ReviewsNotificationsSettings(
      {super.key,
      required this.notificationsEnabled,
      required this.userProvider,
      required this.reviewsSettings,
      required this.controller});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(Translation.of(context).reviews,
            style: MalouTextStyles(context)
                .text15Semibold
                .copyWith(color: MalouColors.textTitle)),
        const SizedBox(height: 10),
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
              border: Border.all(color: MalouColors.borderPrimary, width: 1),
              borderRadius: const BorderRadius.all(Radius.circular(10.0))),
          child: Column(
            children: [
              Container(
                color: MalouColors.backgroundLight,
                child: Padding(
                  padding: const EdgeInsets.only(left: 19, right: 19),
                  child: SizedBox(
                      height: 48,
                      child: NotificationToggle(
                          title: Translation.of(context).notAnsweredReviews,
                          value: notificationsEnabled == false
                              ? false
                              : reviewsSettings.active,
                          onToggle: !notificationsEnabled
                              ? null
                              : (bool value) =>
                                  controller.toggleReviewsNotifications(
                                      value, userProvider, context))),
                ),
              ),
              Container(
                color: MalouColors.backgroundLight,
                child: InkWell(
                  borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                  radius: 10,
                  onTap: () => controller.openReviewsFrequencyPicker(
                      userProvider, context),
                  child: Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: 4, horizontal: 19),
                    child: ConstrainedBox(
                      constraints: const BoxConstraints(minHeight: 40),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(children: [
                            Container(
                              width: 8,
                              height: 8,
                              margin: const EdgeInsets.only(right: 10),
                              decoration: const BoxDecoration(
                                  color: MalouColors.primary,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(10.0))),
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(Translation.of(context).frequency,
                                    style: MalouTextStyles(context)
                                        .text12Semibold
                                        .copyWith(
                                            color: MalouColors.textTitle)),
                                SizedBox(
                                  width: size.width * 0.6,
                                  child: Text(
                                    reviewsSettings.realtime
                                        ? Translation.of(context).eachTime
                                        : Translation.of(context).onceADay,
                                    style: MalouTextStyles(context)
                                        .text12Italic
                                        .copyWith(color: MalouColors.textBody),
                                  ),
                                )
                              ],
                            )
                          ]),
                          const Icon(
                            Icons.chevron_right,
                            color: MalouColors.primary,
                            size: 26,
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              // Commenting this just in case we want it back
              // Container(
              //   color: MalouColors.backgroundLight,
              //   child: InkWell(
              //     borderRadius: const BorderRadius.all(Radius.circular(10.0)),
              //     radius: 10,
              //     onTap: () => controller.openConcernedRatingsPicker(
              //         userProvider, context),
              //     child: Padding(
              //       padding:
              //           const EdgeInsets.symmetric(vertical: 4, horizontal: 19),
              //       child: ConstrainedBox(
              //         constraints: const BoxConstraints(minHeight: 40),
              //         child: Row(
              //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //           children: [
              //             Row(children: [
              //               Container(
              //                 width: 8,
              //                 height: 8,
              //                 margin: const EdgeInsets.only(right: 10),
              //                 decoration: const BoxDecoration(
              //                     color: MalouColors.primary,
              //                     borderRadius:
              //                         BorderRadius.all(Radius.circular(10.0))),
              //               ),
              //               Column(
              //                 crossAxisAlignment: CrossAxisAlignment.start,
              //                 mainAxisAlignment: MainAxisAlignment.center,
              //                 children: [
              //                   Text(Translation.of(context).reviewScores,
              //                       style: MalouTextStyles(context)
              //                           .text12Semibold
              //                           .copyWith(
              //                               color: MalouColors.textTitle)),
              //                   SizedBox(
              //                     width: size.width * 0.6,
              //                     child: Text(
              //                       '${reviewsSettings.concernedRatings.map((e) => e.toString()).join('-')} étoiles',
              //                       style: MalouTextStyles(context)
              //                           .text12Italic
              //                           .copyWith(color: MalouColors.textBody),
              //                     ),
              //                   )
              //                 ],
              //               )
              //             ]),
              //             const Icon(
              //               Icons.chevron_right,
              //               color: MalouColors.primary,
              //               size: 26,
              //             )
              //           ],
              //         ),
              //       ),
              //     ),
              //   ),
              // ),
              Container(
                color: MalouColors.backgroundLight,
                child: Padding(
                  padding: const EdgeInsets.only(left: 19, right: 19),
                  child: SizedBox(
                    height: 48,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Container(
                              width: 8,
                              height: 8,
                              margin: const EdgeInsets.only(right: 10),
                              decoration: const BoxDecoration(
                                  color: MalouColors.primary,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(10.0))),
                            ),
                            SizedBox(
                              width: size.width * 0.55,
                              child: Text(
                                  Translation.of(context)
                                      .includeReviewsWithAutoAnswer,
                                  style: MalouTextStyles(context)
                                      .text12Semibold
                                      .copyWith(color: MalouColors.textTitle)),
                            )
                          ],
                        ),
                        Switch(
                            // This bool value toggles the switch.
                            value: notificationsEnabled == false
                                ? false
                                : reviewsSettings.includeAutoRepliedReviews,
                            onChanged: !notificationsEnabled
                                ? null
                                : (bool value) =>
                                    controller.toggleIncludeAutoRepliedReviews(
                                        value, userProvider, context))
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
