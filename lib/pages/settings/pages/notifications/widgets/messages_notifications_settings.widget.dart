import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/settings/pages/notifications/widgets/notification_toggle.widget.dart';
import 'package:malou/shared/models/user.model.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

import '../notifications_page.controller.dart';

class MessagesNotificationsSettings extends StatelessWidget {
  final bool notificationsEnabled;
  final NotificationsPageController controller;
  final UserProvider userProvider;
  final MessagesSettings messagesSettings;

  const MessagesNotificationsSettings(
      {super.key,
      required this.notificationsEnabled,
      required this.userProvider,
      required this.messagesSettings,
      required this.controller});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(Translation.of(context).conversation,
            style: MalouTextStyles(context)
                .text15Semibold
                .copyWith(color: MalouColors.textTitle)),
        const SizedBox(height: 10),
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
              border: Border.all(color: MalouColors.borderPrimary, width: 1),
              borderRadius: const BorderRadius.all(Radius.circular(10.0))),
          child: Column(
            children: [
              Container(
                color: MalouColors.backgroundLight,
                child: Padding(
                  padding: const EdgeInsets.only(left: 19, right: 19),
                  child: SizedBox(
                      height: 48,
                      child: NotificationToggle(
                          title: Translation.of(context).notAnsweredMessages,
                          value: notificationsEnabled == false
                              ? false
                              : messagesSettings.active,
                          onToggle: !notificationsEnabled
                              ? null
                              : (bool value) =>
                                  controller.toggleMessagesNotifications(
                                      value, userProvider, context))),
                ),
              ),
              Container(
                color: MalouColors.backgroundLight,
                child: InkWell(
                  borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                  radius: 10,
                  onTap: () => controller.openMessagesFrequencyPicker(
                      userProvider, context),
                  child: Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: 4, horizontal: 19),
                    child: ConstrainedBox(
                      constraints: const BoxConstraints(minHeight: 40),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(children: [
                            Container(
                              width: 8,
                              height: 8,
                              margin: const EdgeInsets.only(right: 10),
                              decoration: const BoxDecoration(
                                  color: MalouColors.primary,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(10.0))),
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(Translation.of(context).frequency,
                                    style: MalouTextStyles(context)
                                        .text12Semibold
                                        .copyWith(
                                            color: MalouColors.textTitle)),
                                SizedBox(
                                  width: size.width * 0.6,
                                  child: Text(
                                    userProvider
                                            .currentUser!
                                            .settings
                                            .newNotifications
                                            .mobile
                                            .newMessage
                                            .realtime
                                        ? Translation.of(context).eachTime
                                        : Translation.of(context).onceADay,
                                    style: MalouTextStyles(context)
                                        .text12Italic
                                        .copyWith(color: MalouColors.textBody),
                                  ),
                                )
                              ],
                            )
                          ]),
                          const Icon(
                            Icons.chevron_right,
                            color: MalouColors.primary,
                            size: 26,
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        )
      ],
    );
  }
}
