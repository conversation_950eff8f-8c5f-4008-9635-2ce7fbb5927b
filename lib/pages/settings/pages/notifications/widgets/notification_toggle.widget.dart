import 'package:flutter/material.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

class NotificationToggle extends StatelessWidget {
  const NotificationToggle({
    super.key,
    required this.value,
    required this.onToggle,
    required this.title,
  });
  final String title;
  final bool value;
  final void Function(bool value)? onToggle;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(title,
            style: MalouTextStyles(context)
                .text13Semibold
                .copyWith(color: MalouColors.textTitle)),
        Switch(value: value, onChanged: onToggle)
      ],
    );
  }
}
