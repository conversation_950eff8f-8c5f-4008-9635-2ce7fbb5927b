import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/settings/pages/notifications/notifications_page.controller.dart';
import 'package:malou/shared/models/user.model.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

import 'notification_toggle.widget.dart';

class PostsNotificationsSettings extends StatelessWidget {
  final bool notificationsEnabled;
  final PostsSettings postsSettings;
  final UserProvider userProvider;
  final NotificationsPageController controller;

  const PostsNotificationsSettings(
      {super.key,
      required this.notificationsEnabled,
      required this.userProvider,
      required this.controller,
      required this.postsSettings});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(Translation.of(context).posts,
            style: MalouTextStyles(context)
                .text15Semibold
                .copyWith(color: MalouColors.textTitle)),
        const SizedBox(height: 10),
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
              color: MalouColors.backgroundLight,
              border: Border.all(color: MalouColors.borderPrimary, width: 1),
              borderRadius: const BorderRadius.all(Radius.circular(10.0))),
          child: Padding(
            padding: const EdgeInsets.only(left: 19, right: 19),
            child: Column(
              children: [
                NotificationToggle(
                    title: Translation.of(context).noMoreScheduledPosts,
                    value: notificationsEnabled == false
                        ? false
                        : postsSettings.noMoreScheduledPosts.active,
                    onToggle: !notificationsEnabled
                        ? null
                        : (bool value) => controller
                            .togglePostsNoMoreScheduledPostsNotifications(
                                value, userProvider, context)),
                NotificationToggle(
                    title: Translation.of(context).errorPublishing,
                    value: notificationsEnabled == false
                        ? false
                        : postsSettings.publishError.active,
                    onToggle: !notificationsEnabled
                        ? null
                        : (bool value) =>
                            controller.togglePostsPublishErrorNotifications(
                                value, userProvider, context)),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
