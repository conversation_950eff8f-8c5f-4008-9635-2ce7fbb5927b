import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/settings/pages/notifications/notifications_page.controller.dart';
import 'package:malou/pages/settings/pages/notifications/widgets/posts_notifications_settings.widget.dart';
import 'package:malou/shared/widgets/appbar/appbar_back.widget.dart';
import 'package:malou/shared/models/user.model.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:provider/provider.dart';

import 'widgets/general_notifications_settings.widget.dart';
import 'widgets/messages_notifications_settings.widget.dart';
import 'widgets/reviews_notifications_settings.widget.dart';

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({
    super.key,
  });

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> {
  late NotificationsPageController controller;

  @override
  void initState() {
    controller = NotificationsPageController(setState);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarArrowBack(title: Translation.of(context).notifCenter),
      body: Consumer<UserProvider>(builder: (context, userProvider, child) {
        bool notificationsEnabled =
            userProvider.currentUser!.settings.notificationSettings!.active;
        ReviewsSettings reviewsSettings = userProvider
            .currentUser!.settings.newNotifications.mobile.newReviews;
        MessagesSettings messagesSettings = userProvider
            .currentUser!.settings.newNotifications.mobile.newMessage;
        PostsSettings postsSettings =
            userProvider.currentUser!.settings.notificationSettings!.posts!;
        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.only(left: 16, right: 16, top: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                GeneralNotificationsSettings(
                  notificationsEnabled: notificationsEnabled,
                  userProvider: userProvider,
                  controller: controller,
                ),
                const SizedBox(height: 25),
                PostsNotificationsSettings(
                    postsSettings: postsSettings,
                    notificationsEnabled: notificationsEnabled,
                    userProvider: userProvider,
                    controller: controller),
                const SizedBox(height: 25),
                ReviewsNotificationsSettings(
                  notificationsEnabled: notificationsEnabled,
                  reviewsSettings: reviewsSettings,
                  userProvider: userProvider,
                  controller: controller,
                ),
                const SizedBox(height: 25),
                MessagesNotificationsSettings(
                  notificationsEnabled: notificationsEnabled,
                  userProvider: userProvider,
                  messagesSettings: messagesSettings,
                  controller: controller,
                ),
              ],
            ),
          ),
        );
      }),
    );
  }
}
