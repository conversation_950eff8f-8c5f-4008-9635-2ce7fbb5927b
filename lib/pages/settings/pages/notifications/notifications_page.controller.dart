import 'package:app_settings/app_settings.dart';
import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/notifications/notifications.service.dart';
import 'package:malou/shared/helpers/heap.service.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/helpers/modal_bottom_sheet.dart';
import 'package:malou/shared/widgets/ratings_picker/ratings_picker.widget.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/frequency_picker/frequency_picker.widget.dart';
import 'package:shared_preferences/shared_preferences.dart';

class NotificationsPageController {
  void Function(void Function()) setState;
  NotificationsPageController(
    this.setState,
  );

  void toggleAllNotifications(
      bool value, UserProvider userProvider, BuildContext context) async {
    HeapService.instance
        .track('toggle_all_notifications', {'value': value}, context: context);
    SharedPreferences preferences = await SharedPreferences.getInstance();
    NotificationService.instance
        .areNotificationsAuthorized()
        .then((authorized) {
      if (authorized) {
        preferences.setBool('first_time_ask_for_notifications', true);
        setState(() {
          userProvider.currentUser!.settings.newNotifications.mobile.active =
              value;
          userProvider.currentUser!.settings.notificationSettings!.active =
              value;

          userProvider.updateUser(
              {'settings': userProvider.currentUser!.settings.toJson()});
        });
      } else {
        AppBottomModal.showTinyModalBottomSheet(
            context,
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                children: [
                  Image.asset(
                    IllustrationsPathResolver.resolve(Illustrations.cook),
                    height: 60,
                  ),
                  const SizedBox(height: 10),
                  // Title
                  Text(Translation.of(context).notificationsDisabledTitle,
                      style: MalouTextStyles(context).text13Semibold),

                  Text(
                      Translation.of(context).notificationsDisabledDescription,
                      style: MalouTextStyles(context).text12Italic,
                      textAlign: TextAlign.center),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      TextButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          child: Text(Translation.of(context).later,
                              style: MalouTextStyles(context)
                                  .text13Semibold
                                  .copyWith(color: MalouColors.textBody))),
                      TextButton(
                          onPressed: () {
                            AppSettings.openAppSettings(
                                type: AppSettingsType.notification);
                            Navigator.pop(context);
                          },
                          child: Text(Translation.of(context).activate,
                              style: MalouTextStyles(context)
                                  .text13Semibold
                                  .copyWith(color: MalouColors.primary))),
                    ],
                  )
                ],
              ),
            ));
      }
    });
  }

  void toggleReviewsNotifications(
      bool value, UserProvider userProvider, BuildContext context) {
    HeapService.instance.track('toggle_reviews_notifications', {'value': value},
        context: context);
    setState(() {
      userProvider.currentUser!.settings.newNotifications.mobile.newReviews
          .active = value;
      userProvider.updateUser(
          {'settings': userProvider.currentUser!.settings.toJson()});
    });
  }

  void toggleMessagesNotifications(
      bool value, UserProvider userProvider, BuildContext context) {
    HeapService.instance.track(
        'toggle_messages_notifications', {'value': value},
        context: context);
    setState(() {
      userProvider.currentUser!.settings.newNotifications.mobile.newMessage
          .active = value;
      userProvider.updateUser(
          {'settings': userProvider.currentUser!.settings.toJson()});
    });
  }

  void toggleIncludeAutoRepliedReviews(
      bool value, UserProvider userProvider, BuildContext context) {
    HeapService.instance.track(
        'toggle_include_auto_replied_reviews', {'value': value},
        context: context);
    setState(() {
      userProvider.currentUser!.settings.newNotifications.mobile.newReviews
          .includeAutoRepliedReviews = value;
      userProvider.updateUser(
          {'settings': userProvider.currentUser!.settings.toJson()});
    });
  }

  void updateReviewsNotificationsFrequency(
      FrequencyResult frequencyResult, UserProvider userProvider) {
    setState(() {
      userProvider.currentUser!.settings.newNotifications.mobile.newReviews =
          userProvider.currentUser!.settings.newNotifications.mobile.newReviews
              .copyWith(
        realtime: frequencyResult.realtime,
        receivingWeekDays:
            frequencyResult.days.map((e) => e.values.first).toList(),
      );

      userProvider.updateUser(
          {'settings': userProvider.currentUser!.settings.toJson()});
    });
  }

  void updateMessagesNotificiationsFrequency(
      FrequencyResult frequencyResult, UserProvider userProvider) {
    setState(() {
      userProvider.currentUser!.settings.newNotifications.mobile.newMessage =
          userProvider.currentUser!.settings.newNotifications.mobile.newMessage
              .copyWith(
        realtime: frequencyResult.realtime,
        receivingWeekDays:
            frequencyResult.days.map((e) => e.values.first).toList(),
      );
      userProvider.updateUser(
          {'settings': userProvider.currentUser!.settings.toJson()});
    });
  }

  void updateConcernedRatings(List<int> ratings, UserProvider userProvider) {
    setState(() {
      userProvider.currentUser!.settings.newNotifications.mobile.newReviews =
          userProvider.currentUser!.settings.newNotifications.mobile.newReviews
              .copyWith(concernedRatings: ratings);
      userProvider.updateUser(
          {'settings': userProvider.currentUser!.settings.toJson()});
    });
  }

  void openReviewsFrequencyPicker(
      UserProvider userProvider, BuildContext context) {
    AppBottomModal.showTinyModalBottomSheet<FrequencyResult>(
      context,
      FrequencyPicker(
        isForMessages: false,
        frequency: NotificationsFrequency(
          realtime: userProvider.currentUser!.settings.newNotifications.mobile
              .newReviews.realtime,
          days: userProvider.currentUser!.settings.newNotifications.mobile
              .newReviews.receivingWeekDays,
        ),
      ),
      afterClosed: (result) =>
          updateReviewsNotificationsFrequency(result, userProvider),
    );
  }

  void openMessagesFrequencyPicker(
      UserProvider userProvider, BuildContext context) {
    AppBottomModal.showTinyModalBottomSheet<FrequencyResult>(
      context,
      FrequencyPicker(
        isForMessages: true,
        frequency: NotificationsFrequency(
          realtime: userProvider.currentUser!.settings.newNotifications.mobile
              .newMessage.realtime,
          days: userProvider.currentUser!.settings.newNotifications.mobile
              .newMessage.receivingWeekDays,
        ),
      ),
      afterClosed: (result) =>
          updateMessagesNotificiationsFrequency(result, userProvider),
    );
  }

  void openConcernedRatingsPicker(
      UserProvider userProvider, BuildContext context) {
    AppBottomModal.showTinyModalBottomSheet<List<int>>(
      context,
      RatingsPicker(
        initialRatings: userProvider.currentUser!.settings.notificationSettings!
            .reviews!.concernedRatings,
      ),
      afterClosed: (result) => updateConcernedRatings(result, userProvider),
    );
  }

  void togglePostsNoMoreScheduledPostsNotifications(
      bool value, UserProvider userProvider, BuildContext context) {
    HeapService.instance.track(
        'toggle_posts_no_more_scheduled_posts_notifications', {'value': value},
        context: context);
    setState(() {
      userProvider.currentUser!.settings.notificationSettings!.posts!
          .noMoreScheduledPosts.active = value;
      userProvider.currentUser!.settings.newNotifications.mobile
          .noMoreScheduledPosts.active = value;
      userProvider.updateUser(
          {'settings': userProvider.currentUser!.settings.toJson()});
    });
  }

  void togglePostsPublishErrorNotifications(
      bool value, UserProvider userProvider, BuildContext context) {
    HeapService.instance.track(
        'toggle_posts_publish_error_notifications', {'value': value},
        context: context);
    setState(() {
      userProvider.currentUser!.settings.notificationSettings!.posts!
          .publishError.active = value;
      userProvider.updateUser(
          {'settings': userProvider.currentUser!.settings.toJson()});
    });
  }
}
