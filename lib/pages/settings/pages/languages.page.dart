import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/widgets/appbar/appbar_back.widget.dart';
import 'package:malou/shared/widgets/language_picker/language_picker.dart';
import 'package:provider/provider.dart';

class LanguagesPage extends StatefulWidget {
  const LanguagesPage({super.key});

  @override
  State<LanguagesPage> createState() => _LanguagesPageState();
}

class _LanguagesPageState extends State<LanguagesPage> {
  late LanguageProvider _languageProvider;
  late UserProvider _userProvider;

  @override
  void initState() {
    super.initState();
    _languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    _userProvider = Provider.of<UserProvider>(context, listen: false);
  }

  changeLanguage(AppLocale langKey) async {
    _languageProvider.changeLanguage(langKey);
    await _userProvider.updateUser({
      'defaultLanguage': langKey.name,
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarArrowBack(title: Translation.of(context).languages),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 10),
        child: LanguagePicker(
            onClickLanguage: (AppLocale langKey) {
              changeLanguage(langKey);
              Navigator.of(context).pop();
            },
            language: _languageProvider.language,
            languageProvider: _languageProvider),
      ),
    );
  }
}
