import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/widgets/appbar/appbar_back.widget.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

// This widget will be used in the future, for now we just redirect to the web app
class LegalMentions extends StatefulWidget {
  const LegalMentions({super.key});

  @override
  State<LegalMentions> createState() => _LegalMentionsState();
}

class _LegalMentionsState extends State<LegalMentions> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarArrowBack(title: Translation.of(context).legalMentions),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(Translation.of(context).legalMentions1,
                  style: MalouTextStyles(context)
                      .text13Bold
                      .copyWith(color: MalouColors.textTitle)),
              const SizedBox(height: 16),
              Text(Translation.of(context).legalMentions1Details,
                  style: MalouTextStyles(context)
                      .text10
                      .copyWith(color: MalouColors.textBody)),
              const SizedBox(height: 16),
              Text(Translation.of(context).legalMentionsA,
                  style: MalouTextStyles(context)
                      .text13Bold
                      .copyWith(color: MalouColors.textTitle)),
              const SizedBox(height: 16),
              Text(Translation.of(context).legalMentionsADetails,
                  style: MalouTextStyles(context)
                      .text10
                      .copyWith(color: MalouColors.textBody)),
            ],
          ),
        ),
      ),
    );
  }
}
