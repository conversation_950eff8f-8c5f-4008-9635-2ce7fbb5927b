// ignore_for_file: non_constant_identifier_names

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/notifications/notifications.service.dart';
import 'package:malou/shared/widgets/appbar/appbar_back.widget.dart';
import 'package:malou/shared/helpers/heap.service.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/helpers/modal_bottom_sheet.dart';
import 'package:malou/shared/services/auth/auth.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/versions/version.widget.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

final Uri URL_QA = Uri.parse(
    'https://welcomehomemalou.notion.site/Foire-aux-questions-MalouApp-257b1ca546b747efaca1b63345da9603');
final Uri URL_LEGAL_MENTIONS =
    Uri.parse('https://v3.omni.malou.io/assets/cgu_fr.pdf');

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  late AuthProvider authProvider;

  @override
  void initState() {
    super.initState();
    authProvider = Provider.of<AuthProvider>(context, listen: false);
  }

  Future<void> _launchUrl(Uri uri) async {
    if (await canLaunchUrl(uri)) {
      launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    return Scaffold(
      appBar: AppBarArrowBack(
        title: Translation.of(context).settings,
        icon: const Icon(
          MalouIcons.cross,
          color: MalouColors.primary,
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, top: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                children: [
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                        border: Border.all(
                            color: MalouColors.borderPrimary, width: 1),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(10.0))),
                    child: Column(
                      children: [
                        Material(
                          color: MalouColors.backgroundLight,
                          child: InkWell(
                            onTapUp: (d) {
                              HeapService.instance.track(
                                  'notif_center_clicked', {},
                                  context: context);
                              context.push('/settings/notifications');
                            },
                            borderRadius:
                                const BorderRadius.all(Radius.circular(10.0)),
                            radius: 10,
                            onTap: () => {},
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 22, horizontal: 19),
                              child: ConstrainedBox(
                                constraints:
                                    const BoxConstraints(minHeight: 40),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(children: [
                                      Image.asset(
                                          height: size.height * 0.075,
                                          IllustrationsPathResolver.resolve(
                                              Illustrations.gogoolOk)),
                                      SizedBox(width: size.width * 0.05),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                              Translation.of(context)
                                                  .notifCenter,
                                              style:
                                                  MalouTextStyles(context)
                                                      .text15Semibold
                                                      .copyWith(
                                                          color: MalouColors
                                                              .textTitle,
                                                          height: 2)),
                                          Text(
                                            Translation.of(context)
                                                .tooManyNotif,
                                            style: MalouTextStyles(context)
                                                .text10Semibold
                                                .copyWith(
                                                    color: MalouColors.textBody,
                                                    height: 1.5),
                                          ),
                                          SizedBox(
                                            width: size.width * 0.5,
                                            child: Text(
                                              Translation.of(context)
                                                  .manageNotifs,
                                              style: MalouTextStyles(context)
                                                  .text10
                                                  .copyWith(
                                                      color:
                                                          MalouColors.textBody),
                                            ),
                                          )
                                        ],
                                      )
                                    ]),
                                    const Icon(
                                      Icons.chevron_right,
                                      color: MalouColors.primary,
                                      size: 26,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 13),
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                        border: Border.all(
                            color: MalouColors.borderPrimary, width: 1),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(10.0))),
                    child: Column(
                      children: [
                        Material(
                          color: MalouColors.backgroundLight,
                          child: InkWell(
                            onTapUp: (d) {
                              HeapService.instance.track('language_clicked', {},
                                  context: context);
                              context.push('/settings/languages');
                            },
                            borderRadius:
                                const BorderRadius.all(Radius.circular(10.0)),
                            radius: 10,
                            onTap: () => {},
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 4, horizontal: 19),
                              child: ConstrainedBox(
                                constraints:
                                    const BoxConstraints(minHeight: 40),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(children: [
                                      Text(
                                          Translation.of(context)
                                              .changeLanguage,
                                          style: MalouTextStyles(context)
                                              .text15Semibold
                                              .copyWith(
                                                  color: MalouColors.textTitle))
                                    ]),
                                    const Icon(
                                      Icons.chevron_right,
                                      color: MalouColors.primary,
                                      size: 26,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                        Material(
                          color: MalouColors.backgroundLight,
                          child: InkWell(
                            borderRadius:
                                const BorderRadius.all(Radius.circular(10.0)),
                            radius: 10,
                            onTap: () {
                              HeapService.instance.track('legal_mentions', {},
                                  context: context);
                              _launchUrl(URL_LEGAL_MENTIONS);
                            },
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 4, horizontal: 19),
                              child: ConstrainedBox(
                                constraints:
                                    const BoxConstraints(minHeight: 40),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(children: [
                                      Text(
                                          Translation.of(context).legalMentions,
                                          style: MalouTextStyles(context)
                                              .text15Semibold
                                              .copyWith(
                                                  color: MalouColors.textTitle))
                                    ]),
                                    const Icon(
                                      Icons.chevron_right,
                                      color: MalouColors.primary,
                                      size: 26,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 13),
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                        border: Border.all(
                            color: MalouColors.borderPrimary, width: 1),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(10.0))),
                    child: Column(
                      children: [
                        Material(
                          color: MalouColors.backgroundLight,
                          child: InkWell(
                            borderRadius:
                                const BorderRadius.all(Radius.circular(10.0)),
                            radius: 10,
                            onTap: () {
                              HeapService.instance
                                  .track('faq_clicked', {}, context: context);
                              _launchUrl(URL_QA);
                            },
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 22, horizontal: 19),
                              child: ConstrainedBox(
                                constraints:
                                    const BoxConstraints(minHeight: 40),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(children: [
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                              Translation.of(context)
                                                  .questionsAnswers,
                                              style: MalouTextStyles(context)
                                                  .text15Semibold
                                                  .copyWith(
                                                      color:
                                                          MalouColors.textTitle,
                                                      height: 2)),
                                          Text(
                                            Translation.of(context).question,
                                            style: MalouTextStyles(context)
                                                .text10Semibold
                                                .copyWith(
                                                    color: MalouColors.textBody,
                                                    height: 1.5),
                                          ),
                                          SizedBox(
                                            width: 250,
                                            child: Text(
                                              Translation.of(context)
                                                  .contactMalouOps,
                                              style: MalouTextStyles(context)
                                                  .text10
                                                  .copyWith(
                                                      color:
                                                          MalouColors.textBody),
                                            ),
                                          )
                                        ],
                                      )
                                    ]),
                                    const Icon(
                                      Icons.chevron_right,
                                      color: MalouColors.primary,
                                      size: 26,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 150,
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(child: Container()),
                  Row(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 20),
                        child: TextButton(
                          child: Text(Translation.of(context).signOut,
                              style: MalouTextStyles(context)
                                  .text15Medium
                                  .copyWith(color: MalouColors.pinkAccent)),
                          onPressed: () => {
                            AppBottomModal.showTinyModalBottomSheet(
                                context,
                                Container(
                                  padding: const EdgeInsets.only(bottom: 5),
                                  width: double.infinity,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Image.asset(
                                          height: 60,
                                          IllustrationsPathResolver.resolve(
                                              Illustrations.cook)),
                                      const SizedBox(height: 10),
                                      Text(
                                          Translation.of(context)
                                              .aboutToSignOut,
                                          style: MalouTextStyles(context)
                                              .text13Semibold
                                              .copyWith(
                                                  color:
                                                      MalouColors.textTitle)),
                                      const SizedBox(height: 20),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceAround,
                                        children: [
                                          TextButton(
                                              onPressed: () => {
                                                    Navigator.pop(context),
                                                  },
                                              child: Text(
                                                  Translation.of(context)
                                                      .cancel,
                                                  style:
                                                      MalouTextStyles(context)
                                                          .text13Semibold
                                                          .copyWith(
                                                              color: MalouColors
                                                                  .textBody))),
                                          TextButton(
                                              onPressed: () {
                                                UserProvider userProvider =
                                                    Provider.of<UserProvider>(
                                                        context,
                                                        listen: false);
                                                NotificationService.instance
                                                    .deleteDeviceToken(
                                                        userProvider);
                                                authProvider.logout();
                                                // go login
                                                context.pop();
                                                context.go('/login');
                                              },
                                              child: Text(
                                                  Translation.of(context)
                                                      .signOut,
                                                  style:
                                                      MalouTextStyles(context)
                                                          .text13Semibold
                                                          .copyWith(
                                                              color: MalouColors
                                                                  .primary))),
                                        ],
                                      )
                                    ],
                                  ),
                                ))
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(bottom: 10),
                        child: Version(),
                      ),
                    ],
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
