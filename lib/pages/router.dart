import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/notifications/notification_payload.dart';
import 'package:malou/pages/connectivity/connectivity.router.dart';
import 'package:malou/pages/connectivity/no_internet_permanent_toast.widget.dart';
import 'package:malou/pages/restaurant/pages/home/<USER>';
import 'package:malou/pages/authentication/forgot_password/forgot_password.page.dart';
import 'package:malou/pages/authentication/forgot_password/forgot_password_email_sent.page.dart';
import 'package:malou/pages/authentication/login/login.page.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/post_creation.page.dart';
import 'package:malou/pages/restaurant/pages/posts/posts.page.dart';
import 'package:malou/pages/restaurant/pages/messages/conversation.page.dart';
import 'package:malou/pages/restaurant/pages/messages/conversations_root.page.dart';
import 'package:malou/pages/restaurant/pages/posts/tabs/stories/widgets/story-preview.page.dart';
import 'package:malou/pages/restaurant/pages/reviews/pages/reply_review_page/reply_review.page.dart';
import 'package:malou/pages/restaurant/pages/reviews/pages/reviewReplyByMail.page.dart';
import 'package:malou/pages/restaurant/pages/reviews/reviews.page.dart';
import 'package:malou/pages/restaurant/restaurant.page.dart';
import 'package:malou/pages/restaurants/restaurants.page.dart';
import 'package:malou/pages/settings/pages/languages.page.dart';
import 'package:malou/pages/settings/pages/legal_mentions.page.dart';
import 'package:malou/pages/settings/pages/notifications/notifications.page.dart';
import 'package:malou/pages/settings/settings.page.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/models/reviews.model.dart';
import 'package:malou/shared/services/messaging/messaging.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/widgets/toasts/global_toast_listener.wdiget.dart';

import 'package:provider/provider.dart';

import '../shared/models/posts/story.model.dart';

// private navigators
final rootNavigatorKey = GlobalKey<NavigatorState>();
final rootShellNavigatorKey = GlobalKey<NavigatorState>();
final shellNavigatorKey = GlobalKey<NavigatorState>();
final settingsNavigatorKey = GlobalKey<NavigatorState>();

// tab indexes
const int POSTS_TAB_INDEX = 3;
const int REVIEWS_TAB_INDEX = 1;
const int CONVERSATIONS_TAB_INDEX = 2;
const List<String> whitelisted_routes = ['/no-internet'];

GoRouter router = GoRouter(
    initialLocation: '/login',
    navigatorKey: rootNavigatorKey,
    routes: [
      buildLoginRoute(),
      GoRoute(
          parentNavigatorKey: rootNavigatorKey,
          path: '/',
          builder: (context, state) => Container(),
          routes: [
            ShellRoute(
                pageBuilder: (context, state, child) => NoTransitionPage(
                        child: Stack(
                      children: [
                        child,
                        NoInternetPermanentToast(
                          shouldShow:
                              whitelisted_routes.contains(state.fullPath),
                        ),
                        const GlobalToastListener()
                      ],
                    )),
                navigatorKey: rootShellNavigatorKey,
                routes: [
                  buildForgotPasswordRoute(),
                  buildForgotPasswordEmailSentRoute(),
                  buildRestaurantsPageRoute(),
                  buildRestaurantRoutes(),
                  buildSettingsRoutes(),
                  buildConnectivityRoutes(),
                ])
          ]),
    ]);

GoRoute buildSettingsRoutes() {
  return GoRoute(
      parentNavigatorKey: rootShellNavigatorKey,
      path: 'settings',
      pageBuilder: (context, state) => const MaterialPage(
            child: SettingsPage(),
          ),
      routes: [
        GoRoute(
            parentNavigatorKey: rootShellNavigatorKey,
            path: 'languages',
            pageBuilder: (context, state) => const MaterialPage(
                  child: LanguagesPage(),
                )),
        GoRoute(
            parentNavigatorKey: rootShellNavigatorKey,
            path: 'notifications',
            pageBuilder: (context, state) => const MaterialPage(
                  child: NotificationsPage(),
                )),
        GoRoute(
            parentNavigatorKey: rootShellNavigatorKey,
            path: 'legal_mentions',
            pageBuilder: (context, state) => const MaterialPage(
                  child: LegalMentions(),
                )),
      ]);
}

GoRoute buildRestaurantRoutes() {
  return GoRoute(
      parentNavigatorKey: rootShellNavigatorKey,
      path: 'restaurant/:restaurant_id',
      builder: (context, state) => Container(),
      routes: [
        ShellRoute(
            navigatorKey: shellNavigatorKey,
            pageBuilder: (context, state, child) => CustomTransitionPage(
                  child: Stack(
                    children: [
                      RestaurantPage(
                        child: child,
                      ),
                    ],
                  ),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child2) =>
                          FadeTransition(
                    opacity: animation,
                    child: child2,
                  ),
                ),
            routes: [
              buildHomeRoutes(),
              buildReviewsRoutes(),
              buildConversationsRoutes(),
              buildPostsRoutes(),
            ]),
      ]);
}

GoRoute buildPostsRoutes() {
  return GoRoute(
      parentNavigatorKey: shellNavigatorKey,
      path: 'posts',
      pageBuilder: (context, state) => const NoTransitionPage(
            child: PostsPage(),
          ),
      routes: [
        GoRoute(
            parentNavigatorKey: rootNavigatorKey,
            path: 'stories',
            pageBuilder: (context, state) => MaterialPage(
                fullscreenDialog: true,
                child: StoryPreview(
                  stories: ((state.extra as Map<String, dynamic>)['stories'] ??
                      []) as List<Story>,
                  initialStoryIndex: ((state.extra
                          as Map<String, dynamic>)['initialStoryIndex'] ??
                      0) as int,
                ))),
        GoRoute(
            parentNavigatorKey: rootNavigatorKey,
            path: 'new',
            pageBuilder: (context, state) => MaterialPage(
                fullscreenDialog: true,
                child: PostCreationPage(
                    postType:
                        (state.extra as Map<String, dynamic>?)?['postType'] ??
                            PostType.CAROUSEL,
                    postSource:
                        (state.extra as Map<String, dynamic>?)?['postSource'] ??
                            Source.social))),
        GoRoute(
            parentNavigatorKey: rootNavigatorKey,
            path: 'edit/:post_id',
            pageBuilder: (context, state) => MaterialPage(
                fullscreenDialog: true,
                child: PostCreationPage(
                    postId: state.pathParameters['post_id']!,
                    postType: (state.extra as Map<String, dynamic>)['postType'],
                    postSource:
                        (state.extra as Map<String, dynamic>)['postSource'])))
      ]);
}

GoRoute buildRestaurantsPageRoute() {
  return GoRoute(
      parentNavigatorKey: rootShellNavigatorKey,
      path: 'restaurants',
      pageBuilder: (context, state) => CustomTransitionPage(
            child: const RestaurantsPage(),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) =>
                    FadeTransition(
              opacity: animation,
              child: child,
            ),
          ));
}

GoRoute buildForgotPasswordEmailSentRoute() {
  return GoRoute(
      parentNavigatorKey: rootShellNavigatorKey,
      path: 'forgot_password_email_sent',
      pageBuilder: (context, state) => CustomTransitionPage(
            child: ForgotPasswordEmailSent(
              email: (state.extra as Map<String, String>)['email'] as String,
            ),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) =>
                    FadeTransition(
              opacity: animation,
              child: child,
            ),
          ));
}

GoRoute buildForgotPasswordRoute() {
  return GoRoute(
      parentNavigatorKey: rootShellNavigatorKey,
      path: 'forgot_password',
      pageBuilder: (context, state) => CustomTransitionPage(
            child: const ForgotPasswordPage(),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) =>
                    FadeTransition(
              opacity: animation,
              child: child,
            ),
          ));
}

GoRoute buildLoginRoute() {
  return GoRoute(
      parentNavigatorKey: rootNavigatorKey,
      path: '/login',
      pageBuilder: (context, state) => CustomTransitionPage(
            child: const LoginPage(),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) =>
                    FadeTransition(
              opacity: animation,
              child: child,
            ),
          ));
}

GoRoute buildHomeRoutes() {
  return GoRoute(
    parentNavigatorKey: shellNavigatorKey,
    path: 'home',
    pageBuilder: (context, state) => const NoTransitionPage(child: Home()),
  );
}

GoRoute buildConversationsRoutes() {
  return GoRoute(
      parentNavigatorKey: shellNavigatorKey,
      path: 'conversations',
      name: 'conversations',
      pageBuilder: (context, state) {
        // get conversationId from extra
        final conversationId =
            (state.extra as Map<String, String?>?)?['conversationId'];
        return NoTransitionPage(
          child: MessagesRootPage(
            selectedConversationId: conversationId,
          ),
        );
      },
      routes: [
        GoRoute(
            parentNavigatorKey: rootNavigatorKey,
            name: 'conversation',
            path: 'conversation/:conversation_id',
            pageBuilder: (context, state) => MaterialPage(
                  child: Consumer2<MessagingProvider, RestaurantProvider>(
                      builder: (context, messagingProvider, restaurantProvider,
                          child) {
                    restaurantProvider.selectedBottomAppBarIndex = 2;
                    String selectedConversationId =
                        state.pathParameters['conversation_id'] ?? "";
                    messagingProvider.selectedConversationId =
                        selectedConversationId;
                    return ConversationPage(
                      messagingProvider: messagingProvider,
                      selectedConversationId: selectedConversationId,
                    );
                  }),
                )),
      ]);
}

GoRoute buildReviewsRoutes() {
  return GoRoute(
      parentNavigatorKey: shellNavigatorKey,
      path: 'reviews',
      name: 'reviews',
      pageBuilder: (context, state) {
        return NoTransitionPage(child: Consumer<RestaurantProvider>(
            builder: (context, restaurantProvider, child) {
          restaurantProvider.selectedBottomAppBarIndex = 1;
          return ReviewsPage(
            selectedReviewId:
                (state.extra as NotificationPayloadData?)?.reviewId,
          );
        }));
      },
      routes: [
        GoRoute(
            parentNavigatorKey: rootNavigatorKey,
            path: ':review_id',
            pageBuilder: (context, state) {
              var extra = state.extra as Map<String, ReplyMethod>;

              ReplyMethod replyMethod = extra['replyMethod']!;

              switch (replyMethod) {
                case ReplyMethod.mail:
                  return MaterialPage(
                    child: ReviewReplyByMailPage(
                      reviewId: state.pathParameters['review_id']!,
                    ),
                  );
                case ReplyMethod.comment:
                  return MaterialPage(
                    child: ReviewPage(
                      reviewId: state.pathParameters['review_id']!,
                    ),
                  );
                default:
                  return NoTransitionPage(
                    child: Container(),
                  );
              }
            }),
      ]);
}
