import 'package:flutter/material.dart';
import 'package:malou/pages/restaurant/pages/posts/tabs/seo/seo-posts-tab.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/tabs/social-networks/social-networks-posts-tab.widget.dart';

import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/posts/seo/seo-posts.provider.dart';
import 'package:malou/shared/services/posts/social-posts/social-posts.provider.dart';
import 'package:provider/provider.dart';

class PostsTabs extends StatefulWidget {
  final TabController tabController;
  const PostsTabs({super.key, required this.tabController});

  @override
  State<PostsTabs> createState() => _PostsTabsState();
}

class _PostsTabsState extends State<PostsTabs> {
  late PostsProvider postsProvider;
  late SeoPostsProvider seoPostsProvider;
  late SocialPostsProvider socialPostsProvider;
  @override
  void initState() {
    postsProvider = Provider.of<PostsProvider>(context, listen: false);
    seoPostsProvider = Provider.of<SeoPostsProvider>(context, listen: false);
    socialPostsProvider =
        Provider.of<SocialPostsProvider>(context, listen: false);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return TabBarView(
      controller: widget.tabController,
      children: const [
        SocialNetworksPostsTab(),
        SeoPostsTab(),
        // StoriesPostsTab()
      ],
    );
  }
}
