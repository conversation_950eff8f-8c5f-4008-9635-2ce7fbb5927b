import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/services/platform/platform.provider.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/toasts/toast.widget.dart';
import 'package:malou/shared/widgets/toasts/toasts.service.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';

class PostsPageHeader extends StatefulWidget {
  const PostsPageHeader({super.key});

  @override
  State<PostsPageHeader> createState() => _PostsPageHeaderState();
}

class _PostsPageHeaderState extends State<PostsPageHeader>
    with TickerProviderStateMixin {
  late PostsProvider postsProvider;
  late PlatformProvider platformProvider;
  late RestaurantProvider restaurantProvider;

  late AnimationController _syncAnimationController;

  @override
  void initState() {
    postsProvider = context.read<PostsProvider>();
    platformProvider = context.read<PlatformProvider>();
    restaurantProvider = context.read<RestaurantProvider>();
    _syncAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    )..repeat();
    super.initState();
  }

  @override
  void dispose() {
    _syncAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
        child: Column(children: [
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(Translation.of(context).posts,
              style: MalouTextStyles(context).text20Bold),
          StreamBuilder(
              stream: CombineLatestStream.combine3(
                  postsProvider.syncronizationStatusByRestaurant$,
                  restaurantProvider.restaurantSubject$,
                  platformProvider.connectedPostsPlatforms$,
                  (a, b, c) => (a, c)),
              builder: (context, snapshot) {
                bool isCurrentRestaurantSyncing = snapshot
                        .data?.$1[restaurantProvider.currentRestaurant!.id] ??
                    false;
                bool hasAnyPostPlatformConnected =
                    snapshot.data?.$2.isNotEmpty ?? true;

                return Opacity(
                  opacity:
                      !hasAnyPostPlatformConnected || isCurrentRestaurantSyncing
                          ? 0.5
                          : 1,
                  child: MaterialButton(
                      onPressed: !hasAnyPostPlatformConnected ||
                              isCurrentRestaurantSyncing
                          ? null
                          : onSynchronize,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      elevation: 0,
                      highlightElevation: 0,
                      minWidth: 36,
                      height: 36,
                      padding: EdgeInsets.zero,
                      color: MalouColors.backgroundDark,
                      child: isCurrentRestaurantSyncing
                          ? RotationTransition(
                              turns: Tween(begin: 0.0, end: 1.0)
                                  .animate(_syncAnimationController),
                              child: const Icon(
                                MalouIcons.sync,
                                color: MalouColors.primary,
                                size: 23,
                              ),
                            )
                          : const Icon(
                              MalouIcons.sync,
                              color: MalouColors.primary,
                              size: 23,
                            )),
                );
              })
        ],
      ),
      const SizedBox(height: 10),
    ]));
  }

  onSynchronize() {
    postsProvider
        .startSyncPosts(
            restaurantProvider.currentRestaurant!.id,
            platformProvider.connectedMessagingPlatforms$.value
                .map((e) => e.key)
                .toList())
        .then((value) {
      postsProvider.onPullToRefresh$.add(postsProvider.selectedTabIndex$.value);
      return ToastsService.instance.add(Toast(
          type: ToastType.success,
          message: Translation.of(context).postsSyncSuccess));
    }).catchError((_) => {
              ToastsService.instance.add(Toast(
                  type: ToastType.error,
                  message: Translation.of(context).postsSyncError))
            });
  }
}
