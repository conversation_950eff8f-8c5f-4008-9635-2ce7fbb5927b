import 'package:flutter/material.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/theme/theme_colors.dart';

class PostTypePreviewIcon extends StatelessWidget {
  final Post post;
  const PostTypePreviewIcon({
    super.key,
    required this.post,
  });

  @override
  Widget build(BuildContext context) {
    return post.postType == PostType.IMAGE ||
            post.postType == PostType.VIDEO ||
            post.postType == PostType.REEL ||
            (post.attachments.length == 1 &&
                post.attachments[0].isVideo() == true)
        ? Container()
        : Center(
            child: Icon(
            MalouIcons.images,
            color: MalouColors.backgroundDark.withOpacity(0.7),
            size: 60,
          ));
  }
}
