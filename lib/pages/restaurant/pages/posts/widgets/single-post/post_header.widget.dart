import 'package:flutter/material.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';

class PostHeader extends StatelessWidget {
  const PostHeader({
    super.key,
    required this.post,
  });

  final Post post;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Stack(
            children: [
              if (post.keys.isNotEmpty)
                ...post.keys.map((e) {
                  String logoPath = PlatformDetails.resolvePlatformLogo(e);
                  return Container(
                    margin: EdgeInsets.only(left: post.keys.indexOf(e) * 15.0),
                    child: CircleAvatar(
                      radius: 12,
                      child: Image.asset(
                        logoPath,
                      ),
                    ),
                  );
                })
              else if (post.key != null)
                CircleAvatar(
                  radius: 12,
                  child: Image.asset(
                      PlatformDetails.resolvePlatformLogo(post.key!)),
                )
            ],
          )
        ],
      ),
    );
  }
}
