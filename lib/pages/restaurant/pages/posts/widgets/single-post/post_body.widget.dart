import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:provider/provider.dart';

class PostBody extends StatelessWidget {
  final Post post;
  const PostBody({super.key, required this.post});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Icon(
                        Icons.calendar_today_outlined,
                        size: 16,
                        color: MalouColors.primary,
                      ),
                      Text(
                          post.getPostDate(
                              context.read<LanguageProvider>().language),
                          style: MalouTextStyles(context).text10Italic),
                    ],
                  ),
                  const SizedBox(height: 10),
                  Text(post.text ?? '',
                      style: MalouTextStyles(context)
                          .text12
                          .copyWith(color: MalouColors.textTitle),
                      maxLines: post.shouldDisplayFeedback() ? 2 : 4,
                      overflow: TextOverflow.ellipsis),
                ],
              ),
              if (post.shouldDisplayFeedback())
                Row(
                  children: [
                    const CircleAvatar(
                      backgroundColor: MalouColors.warning,
                      radius: 6,
                    ),
                    const SizedBox(width: 5),
                    Text(
                        Translation.of(context)
                            .remarksNumber(post.feedback!.getFeedbacksCount()),
                        style: MalouTextStyles(context).text12Italic),
                  ],
                )
            ]),
      ),
    );
  }
}
