import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/pages/restaurant/pages/posts/widgets/single-post/post_body.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/widgets/single-post/post_header.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/widgets/single-post/post_preview_icon.dart';
import 'package:malou/pages/restaurant/pages/posts/widgets/single-post/widgets/post_card_actions_panel.widget.dart';
import 'package:malou/pages/router.dart';
import 'package:malou/shared/helpers/modal_bottom_sheet.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/widgets/medias/post_media_cover.widget.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';
import 'package:provider/provider.dart';

class PostCard extends StatefulWidget {
  final Post post;
  final Function(Post post) deletePostCard;
  const PostCard({super.key, required this.post, required this.deletePostCard});

  @override
  State<PostCard> createState() => _PostCardState();
}

class _PostCardState extends State<PostCard> {
  late RestaurantProvider restaurantProvider;

  @override
  void initState() {
    restaurantProvider = context.read<RestaurantProvider>();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        GestureDetector(
          onTap: () {
            context.push(
                '/restaurant/${restaurantProvider.currentRestaurant!.id}/posts/edit/${widget.post.id}',
                extra: {
                  'postType': widget.post.postType,
                  'postSource': widget.post.source
                });
          },
          onLongPress: () {
            HapticFeedback.mediumImpact();
            AppBottomModal.showTinyModalBottomSheet(
                rootNavigatorKey.currentContext!,
                PostCardActionsPanel(
                  deletePostCard: widget.deletePostCard,
                  post: widget.post,
                ));
          },
          child: Container(
            width: 150,
            height: 290,
            margin: const EdgeInsets.only(right: 20),
            decoration: BoxDecoration(
              border: Border.all(
                width: 2,
                color: widget.post.hasError()
                    ? MalouColors.error
                    : MalouColors.backgroundDark,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                SizedBox(
                    height: 160,
                    child: Stack(
                      children: [
                        PostMediaCover(
                          post: widget.post,
                          borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(10),
                              topRight: Radius.circular(10)),
                        ),
                        PostHeader(post: widget.post),
                        PostTypePreviewIcon(post: widget.post)
                      ],
                    )),
                PostBody(post: widget.post),
              ],
            ),
          ),
        ),
        StreamBuilder(
            stream: context.read<PostsProvider>().postBeingCreated$,
            builder: (context, snapshot) {
              if (snapshot.hasData && snapshot.data?.postId == widget.post.id) {
                return Container(
                  width: 150,
                  height: 290,
                  decoration: BoxDecoration(
                    color: MalouColors.backgroundDark.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: const Center(
                    child: CircularSpinner(
                      color: MalouColors.primary,
                    ),
                  ),
                );
              }
              return const SizedBox();
            })
      ],
    );
  }
}
