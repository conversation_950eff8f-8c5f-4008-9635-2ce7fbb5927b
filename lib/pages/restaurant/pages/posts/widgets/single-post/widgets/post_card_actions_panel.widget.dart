import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/router.dart';
import 'package:malou/shared/helpers/confirm_modal.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:provider/provider.dart';

class PostCardActionsPanel extends StatelessWidget {
  const PostCardActionsPanel({
    super.key,
    required this.post,
    required this.deletePostCard,
  });

  final Post post;
  final Function(Post) deletePostCard;

  @override
  Widget build(BuildContext context) {
    return Consumer<RestaurantProvider>(
        builder: (consumerContext, restaurantProvider, child) {
      return Column(
        children: [
          Material(
            borderRadius: BorderRadius.circular(10),
            clipBehavior: Clip.hardEdge,
            color: Colors.transparent,
            child: ListTile(
              title: Text(Translation.of(context).edit,
                  style: MalouTextStyles(context).text15Semibold),
              onTap: () {
                context.pop();
                context.push(
                    '/restaurant/${restaurantProvider.currentRestaurant!.id}/posts/edit/${post.id}',
                    extra: {
                      'postType': post.postType,
                      'postSource': post.source
                    });
              },
            ),
          ),
          if (post.canBeDeleted()) ...[
            Material(
              borderRadius: BorderRadius.circular(10),
              clipBehavior: Clip.hardEdge,
              color: Colors.transparent,
              child: ListTile(
                title: Text(Translation.of(context).delete,
                    style: MalouTextStyles(context)
                        .text15Semibold
                        .copyWith(color: MalouColors.error)),
                onTap: () {
                  context.pop();
                  // Need new context to avoid ancestor error on context.pop() !!
                  // because we are opening a dialog into another dialog so we lose the context of the first one
                  showConfirmModal(
                    context: rootNavigatorKey.currentContext!,
                    title: Translation.of(context).pursue,
                    subtitle: post.published == PublicationStatus.PUBLISHED
                        ? Translation.of(context)
                            .areYouSureYouWantToDeleteThisPostPublished
                        : Translation.of(context)
                            .areYouSureYouWantToDeleteThisPost,
                    onConfirm: () {
                      rootNavigatorKey.currentContext!.pop();
                      deletePostCard(post);
                    },
                    onCancel: () {
                      rootNavigatorKey.currentContext!.pop();
                    },
                    primaryButtonTitle: Translation.of(context).delete,
                    secondaryButtonTitle: Translation.of(context).cancel,
                  );
                },
              ),
            ),
          ]
        ],
      );
    });
  }
}
