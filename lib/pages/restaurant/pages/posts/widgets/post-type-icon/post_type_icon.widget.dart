import 'package:flutter/material.dart';
import 'package:malou/pages/restaurant/pages/posts/widgets/post-type-icon/widgets/post_type_carousel_icon.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/widgets/post-type-icon/widgets/post_type_video_icon.widge.dart';
import 'package:malou/shared/models/posts/post.model.dart';

class PostTypeIcon extends StatelessWidget {
  final Post post;

  const PostTypeIcon({
    super.key,
    required this.post,
  });

  @override
  Widget build(BuildContext context) {
    PostType postType = post.postType ?? _getPostTypeFromSocialAttachments();
    switch (postType) {
      case PostType.CAROUSEL:
        return const PostTypeCarouselIcon();
      case PostType.IMAGE:
        return Container();
      case PostType.REEL:
        return const PostTypeVideoIcon();
      case PostType.VIDEO:
        return const PostTypeVideoIcon();
      default:
        return Container();
    }
  }

  // fallback function to get post type from social attachments if post type is null
  PostType _getPostTypeFromSocialAttachments() {
    if (post.socialAttachments.length > 1) {
      return PostType.CAROUSEL;
    }

    if (post.isVideo()) {
      return PostType.VIDEO;
    }
    return PostType.IMAGE;
  }
}
