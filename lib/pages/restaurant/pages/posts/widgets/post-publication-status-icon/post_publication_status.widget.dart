import 'package:flutter/material.dart';
import 'package:malou/pages/restaurant/pages/posts/widgets/post-publication-status-icon/widgets/post_publication_draft_icon.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/widgets/post-publication-status-icon/widgets/post_publication_error_icon.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/widgets/post-publication-status-icon/widgets/post_publication_published_icon.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/widgets/post-publication-status-icon/widgets/post_publication_scheduled_icon.widget.dart';
import 'package:malou/shared/models/posts/post.model.dart';

class PostPublicationStatusIcon extends StatelessWidget {
  final Post post;

  const PostPublicationStatusIcon({
    super.key,
    required this.post,
  });

  @override
  Widget build(BuildContext context) {
    switch (post.published) {
      case PublicationStatus.PUBLISHED:
        return const PostPublicationPublishedIcon();
      case PublicationStatus.DRAFT:
        return const PostPublicationDraftIcon();
      case PublicationStatus.PENDING:
        return const PostPublicationScheduledIcon();
      case PublicationStatus.ERROR:
        return const PostPublicationErrorIcon();
      default:
        return Container();
    }
  }
}
