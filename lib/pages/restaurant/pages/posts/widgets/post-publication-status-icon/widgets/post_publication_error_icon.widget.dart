import 'package:flutter/material.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/theme/theme_colors.dart';

class PostPublicationErrorIcon extends StatelessWidget {
  const PostPublicationErrorIcon({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: MalouColors.error,
        borderRadius: BorderRadius.circular(6),
      ),
      child: const Icon(
        MalouIcons.cross,
        size: 16,
        color: Colors.white,
      ),
    );
  }
}