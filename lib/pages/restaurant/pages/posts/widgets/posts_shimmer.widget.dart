import 'package:flutter/material.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/widgets/shimmer/horizontal_shimmer.dart';
import 'package:skeleton_loader/skeleton_loader.dart';

Widget buildPostsShimmerLodaer(context) => SizedBox(
      width: 1000,
      child: ListView(
        shrinkWrap: true,
        scrollDirection: Axis.horizontal,
        children: const [
          PostsSkeletonLoader(),
        ],
      ),
    );

class PostsSkeletonLoader extends StatelessWidget {
  const PostsSkeletonLoader({super.key});
  @override
  Widget build(BuildContext context) {
    return HorizontalSkeletonLoader(
        direction: SkeletonDirection.ltr,
        baseColor: MalouColors.backgroundDark,
        highlightColor: MalouColors.backgroundLight,
        items: 5,
        builder: Container(
          width: 135,
          margin: const EdgeInsets.only(right: 20.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: MalouColors.backgroundDark, width: 1),
          ),
          child: Column(
            children: [
              Container(
                height: 160,
                width: 135,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: MalouColors.backgroundDark,
                ),
              ),
              Container(
                padding: const EdgeInsets.all(9.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 23,
                      width: 119,
                      margin: const EdgeInsets.only(bottom: 10),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: MalouColors.backgroundDark,
                      ),
                    ),
                    Container(
                      height: 15,
                      width: 119,
                      margin: const EdgeInsets.only(bottom: 10),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: MalouColors.backgroundDark,
                      ),
                    ),
                    Container(
                      height: 15,
                      width: 119,
                      margin: const EdgeInsets.only(bottom: 10),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: MalouColors.backgroundDark,
                      ),
                    ),
                    Container(
                      height: 15,
                      width: 100,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: MalouColors.backgroundDark,
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        ));
  }
}
