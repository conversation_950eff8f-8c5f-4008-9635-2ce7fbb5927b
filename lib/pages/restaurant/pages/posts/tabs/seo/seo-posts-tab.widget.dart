import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/messages/widgets/platform_disconnected_warning.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/tabs/seo/widgets/seo-posts-empty.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/widgets/single-post/post_card.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/widgets/posts_shimmer.widget.dart';
import 'package:malou/shared/helpers/use_query.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/models/restaurant.model.dart';
import 'package:malou/shared/services/platform/platform.provider.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/posts/seo/seo-posts.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/listview_refresh/listview_refresh.widget.dart';
import 'package:malou/shared/widgets/post_status_loader/post_status_loader.widget.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';
import 'package:malou/shared/widgets/toasts/toast.widget.dart';
import 'package:malou/shared/widgets/toasts/toasts.service.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';

class SeoPostsTab extends StatefulWidget {
  const SeoPostsTab({super.key});

  @override
  State<SeoPostsTab> createState() => _SeoPostsTabState();
}

class _SeoPostsTabState extends State<SeoPostsTab>
    with AutomaticKeepAliveClientMixin {
  late PostsProvider postsProvider;
  late SeoPostsProvider seoPostsProvider;
  late RestaurantProvider restaurantProvider;
  late PlatformProvider platformProvider;
  late QueryResult<void, DeletePostParams> deletePostQuery;

  int pageSize = 24;
  @override
  void initState() {
    super.initState();
    postsProvider = Provider.of<PostsProvider>(context, listen: false);
    seoPostsProvider = Provider.of<SeoPostsProvider>(context, listen: false);
    restaurantProvider =
        Provider.of<RestaurantProvider>(context, listen: false);
    platformProvider = Provider.of<PlatformProvider>(context, listen: false);

    deletePostQuery = useQuery(postsProvider.deletePost, onProcess: (param) {
      bool postHadBeenDeleted =
          seoPostsProvider.removePostFromPagingController(param.post);
      if (postHadBeenDeleted) {
        ToastsService.instance.add(Toast(
            type: ToastType.success,
            message: Translation.of(context).postDeleted));
      }
    });

    seoPostsProvider.refreshPostsPagingControllers(
        restaurantProvider.currentRestaurant!.id, postsProvider,
        source: Source.seo);
  }

  void deletePostCard(Post post) {
    deletePostQuery.execute(DeletePostParams(
        post: post, restaurantId: restaurantProvider.currentRestaurant!.id));
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: ListviewRefresh(
          onRefresh: () async {
            postsProvider.onPullToRefresh$
                .add(postsProvider.selectedTabIndex$.value);
            Future.delayed(const Duration(seconds: 1), () {
              seoPostsProvider.refreshController.refreshCompleted();
            });
          },
          refreshController: seoPostsProvider.refreshController,
          child: SingleChildScrollView(
            child: !restaurantProvider.currentRestaurant!.isBrandBusiness()
                ? Column(
                    children: [
                      FutureBuilder(
                          future: platformProvider.getPlatformsForRestaurant(
                              restaurantProvider.currentRestaurant!.id),
                          builder: (context, platformsSnapshot) {
                            if (platformsSnapshot.connectionState ==
                                ConnectionState.waiting) {
                              return const SizedBox();
                            }
                            return StreamBuilder(
                              stream: CombineLatestStream(
                                  [
                                    seoPostsProvider.showPublishedPosts$,
                                    seoPostsProvider.showScheduledPosts$,
                                    seoPostsProvider.showDraftPosts$,
                                    seoPostsProvider.showErrorPosts$,
                                    postsProvider
                                        .isGoogleDisconnectedsWarningVisible$,
                                    restaurantProvider.restaurantSubject$
                                  ],
                                  (values) => ({
                                        'isGmbConnected': platformsSnapshot.data
                                            ?.where((element) =>
                                                element.key ==
                                                PlatformKeys.gmb.name)
                                            .isNotEmpty,
                                        'hasPosts': (values[0] as bool) ||
                                            (values[2] as bool) ||
                                            (values[3] as bool) ||
                                            (values[4] as bool),
                                        'isWarningVisible': values[4]
                                      })),
                              builder: (context, snapshot) {
                                bool isWarningViisble =
                                    snapshot.data?['isWarningVisible'] ?? true;
                                if (!isWarningViisble) return Container();
                                bool hasPosts =
                                    (snapshot.data?['hasPosts'] as bool?) ??
                                        false;
                                bool isGmbConnected = (snapshot
                                        .data?['isGmbConnected'] as bool?) ??
                                    false;
                                if (!hasPosts || isGmbConnected) {
                                  return Container();
                                }
                                return PlatformDisconnectedWarning(
                                  missingPlatforms: const ['Google'],
                                  restaurantId:
                                      restaurantProvider.currentRestaurant!.id,
                                  onDismiss: () {
                                    postsProvider
                                        .isGoogleDisconnectedsWarningVisible$
                                        .add(false);
                                  },
                                );
                              },
                            );
                          }),
                      StreamBuilder(
                          stream: CombineLatestStream.combine2<
                                  Map<String, bool>,
                                  Restaurant?,
                                  ({
                                    Map<String, bool> publisingPost,
                                    Restaurant? currentRestaurant
                                  })>(
                              seoPostsProvider.isPublishingSeoPost$,
                              restaurantProvider.restaurantSubject$,
                              (a, b) =>
                                  (currentRestaurant: b, publisingPost: a)),
                          builder: (context, snapshot) {
                            Size size = MediaQuery.of(context).size;
                            if (snapshot.hasData &&
                                snapshot.data?.publisingPost[
                                        snapshot.data?.currentRestaurant?.id] ==
                                    true) {
                              return PostStatusLoader(size: size);
                            }

                            return const SizedBox();
                          }),
                      StreamBuilder<bool>(
                          stream: seoPostsProvider.showErrorPosts$.stream,
                          builder: ((context, snapshot) {
                            return snapshot.data == false
                                ? Container()
                                : Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        Translation.of(context)
                                            .publicationError,
                                        style: MalouTextStyles(context)
                                            .text12Semibold
                                            .copyWith(
                                              color: MalouColors.error,
                                            ),
                                      ),
                                      const SizedBox(
                                        height: 20,
                                      ),
                                      Container(
                                        height: snapshot.data == true ? 290 : 0,
                                        margin:
                                            const EdgeInsets.only(bottom: 30),
                                        child: PagedListView(
                                          scrollDirection: Axis.horizontal,
                                          pagingController: seoPostsProvider
                                              .errorSeoPostsPagingController,
                                          builderDelegate:
                                              PagedChildBuilderDelegate<Post>(
                                            animateTransitions: true,
                                            noItemsFoundIndicatorBuilder:
                                                (context) {
                                              seoPostsProvider.showErrorPosts$
                                                  .add(false);
                                              return Container();
                                            },
                                            firstPageErrorIndicatorBuilder:
                                                (context) {
                                              seoPostsProvider.showErrorPosts$
                                                  .add(false);
                                              return Container();
                                            },
                                            firstPageProgressIndicatorBuilder:
                                                buildPostsShimmerLodaer,
                                            newPageProgressIndicatorBuilder:
                                                (context) =>
                                                    const CircularSpinner(
                                              color: MalouColors.primary,
                                              size: 20,
                                            ),
                                            itemBuilder:
                                                (context, post, index) =>
                                                    PostCard(
                                              post: post,
                                              deletePostCard: deletePostCard,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  );
                          })),
                      StreamBuilder<bool>(
                          stream: seoPostsProvider.showScheduledPosts$.stream,
                          builder: (context, snapshot) {
                            if (snapshot.data == false) return Container();
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  Translation.of(context).scheduled,
                                  style: MalouTextStyles(context)
                                      .text12Semibold
                                      .copyWith(
                                        color: MalouColors.textTitle,
                                      ),
                                ),
                                const SizedBox(
                                  height: 20,
                                ),
                                Container(
                                  height: 290,
                                  margin: const EdgeInsets.only(bottom: 30),
                                  child: PagedListView(
                                    scrollDirection: Axis.horizontal,
                                    pagingController: seoPostsProvider
                                        .scheduledSeoPostsPagingController,
                                    builderDelegate:
                                        PagedChildBuilderDelegate<Post>(
                                      noItemsFoundIndicatorBuilder: (context) {
                                        seoPostsProvider.showScheduledPosts$
                                            .add(false);
                                        return Container();
                                      },
                                      animateTransitions: true,
                                      firstPageProgressIndicatorBuilder:
                                          buildPostsShimmerLodaer,
                                      newPageProgressIndicatorBuilder:
                                          (context) => const CircularSpinner(
                                        color: MalouColors.primary,
                                        size: 20,
                                      ),
                                      itemBuilder: (context, post, index) =>
                                          PostCard(
                                              post: post,
                                              deletePostCard: deletePostCard),
                                    ),
                                  ),
                                ),
                              ],
                            );
                          }),
                      StreamBuilder<bool>(
                          stream: seoPostsProvider.showDraftPosts$.stream,
                          builder: (context, snapshot) {
                            if (snapshot.data == false) return Container();
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  Translation.of(context).drafts,
                                  style: MalouTextStyles(context)
                                      .text12Semibold
                                      .copyWith(
                                        color: MalouColors.textTitle,
                                      ),
                                ),
                                const SizedBox(
                                  height: 20,
                                ),
                                Container(
                                  height: 290,
                                  margin: const EdgeInsets.only(bottom: 30),
                                  child: PagedListView(
                                      scrollDirection: Axis.horizontal,
                                      pagingController: seoPostsProvider
                                          .draftSeoPostsPagingController,
                                      builderDelegate:
                                          PagedChildBuilderDelegate<Post>(
                                        noItemsFoundIndicatorBuilder:
                                            (context) {
                                          seoPostsProvider.showDraftPosts$
                                              .add(false);
                                          return Container();
                                        },
                                        firstPageProgressIndicatorBuilder:
                                            buildPostsShimmerLodaer,
                                        newPageProgressIndicatorBuilder:
                                            (context) => const CircularSpinner(
                                          color: MalouColors.primary,
                                          size: 20,
                                        ),
                                        itemBuilder: (context, post, index) =>
                                            PostCard(
                                                post: post,
                                                deletePostCard: deletePostCard),
                                      )),
                                ),
                              ],
                            );
                          }),
                      StreamBuilder<bool>(
                          stream: seoPostsProvider.showPublishedPosts$.stream,
                          builder: (context, snapshot) {
                            if (snapshot.data == false) return Container();
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  Translation.of(context).published,
                                  style: MalouTextStyles(context)
                                      .text12Semibold
                                      .copyWith(
                                        color: MalouColors.textTitle,
                                      ),
                                ),
                                const SizedBox(
                                  height: 20,
                                ),
                                Container(
                                  height: 290,
                                  margin: const EdgeInsets.only(bottom: 30),
                                  child: PagedListView(
                                      scrollDirection: Axis.horizontal,
                                      pagingController: seoPostsProvider
                                          .publishedSeoPostsPagingController,
                                      builderDelegate:
                                          PagedChildBuilderDelegate<Post>(
                                        noItemsFoundIndicatorBuilder:
                                            (context) {
                                          seoPostsProvider.showPublishedPosts$
                                              .add(false);
                                          return Container();
                                        },
                                        firstPageProgressIndicatorBuilder:
                                            buildPostsShimmerLodaer,
                                        newPageProgressIndicatorBuilder:
                                            (context) => const CircularSpinner(
                                          color: MalouColors.primary,
                                          size: 20,
                                        ),
                                        itemBuilder: (context, post, index) =>
                                            PostCard(
                                                post: post,
                                                deletePostCard: deletePostCard),
                                      )),
                                ),
                              ],
                            );
                          }),
                      SeoEmptyPostsPage(
                          platformProvider: platformProvider,
                          seoPostsProvider: seoPostsProvider,
                          postsProvider: postsProvider)
                    ],
                  )
                : Column(
                    children: [
                      SeoEmptyPostsPage(
                          platformProvider: platformProvider,
                          seoPostsProvider: seoPostsProvider,
                          postsProvider: postsProvider)
                    ],
                  ),
          ),
        ));
  }

  @override
  bool get wantKeepAlive => true;
}
