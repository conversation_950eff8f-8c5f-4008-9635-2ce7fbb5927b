import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/platform/platform.provider.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/posts/seo/seo-posts.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/buttons/styles.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';

class SeoEmptyPostsPage extends StatelessWidget {
  const SeoEmptyPostsPage({
    super.key,
    required this.platformProvider,
    required this.postsProvider,
    required this.seoPostsProvider,
  });

  final PlatformProvider platformProvider;
  final PostsProvider postsProvider;
  final SeoPostsProvider seoPostsProvider;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
        stream: CombineLatestStream(
            [
              platformProvider.connectedPostsPlatforms$.map((event) =>
                  event.map((e) => e.key).contains(PlatformKeys.gmb.name)),
              seoPostsProvider.showPublishedPosts$,
              seoPostsProvider.showScheduledPosts$,
              seoPostsProvider.showDraftPosts$,
              seoPostsProvider.showErrorPosts$
            ],
            (values) => ({
                  'platformsConnected': values[0],
                  'hasPosts': values[1] || values[2] || values[3] || values[4]
                })),
        builder: (context, snapshot) {
          bool isBrand = context
              .read<RestaurantProvider>()
              .currentRestaurant!
              .isBrandBusiness();
          bool hasPlatformsConnected =
              snapshot.data?['platformsConnected'] ?? false;
          bool hasPosts = snapshot.data?['hasPosts'] ?? false;
          if (hasPosts && !isBrand) return Container();
          if ((hasPlatformsConnected && !hasPosts) || isBrand) {
            return SizedBox(
              height: MediaQuery.of(context).size.height * 0.55,
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      IllustrationsPathResolver.resolve(Illustrations.cook),
                      width: 70,
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    SizedBox(
                      width: 300,
                      child: Text(
                        Translation.of(context).emptyPosts,
                        style: MalouTextStyles(context).text14Italic,
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    if (!isBrand)
                      PrimaryButtonContainer(
                          child: ElevatedButton(
                        onPressed: () {
                          context.push(
                              '/restaurant/${context.read<RestaurantProvider>().currentRestaurant!.id}/posts/new',
                              extra: {
                                'postType': PostType.IMAGE,
                                'postSource': Source.seo
                              });
                        },
                        child: Text(
                          Translation.of(context).addFIrstPost,
                          style: MalouTextStyles(context)
                              .text15Semibold
                              .copyWith(color: Colors.white),
                        ),
                      )),
                  ],
                ),
              ),
            );
          }

          return SizedBox(
            height: MediaQuery.of(context).size.height * 0.55,
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    IllustrationsPathResolver.resolve(Illustrations.cook),
                    width: 70,
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  SizedBox(
                    width: 300,
                    child: Text(
                      Translation.of(context).googleConnectedButNoPosts,
                      style: MalouTextStyles(context).text14Italic,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  Column(
                    children: [
                      SecondaryButtonContainer(
                          fullWidth: true,
                          child: ElevatedButton(
                            onPressed: () {
                              context.push(
                                  '/restaurant/${context.read<RestaurantProvider>().currentRestaurant!.id}/posts/new',
                                  extra: {
                                    'postType': PostType.IMAGE,
                                    'postSource': Source.seo
                                  });
                            },
                            child: Text(
                              Translation.of(context).createDraft,
                              style: MalouTextStyles(context)
                                  .text15Semibold
                                  .copyWith(color: MalouColors.primary),
                            ),
                          )),
                      const SizedBox(
                        height: 10,
                      ),
                      Consumer<RestaurantProvider>(
                          builder: (context, restaurantProvider, child) {
                        return PrimaryButtonContainer(
                            fullWidth: true,
                            child: ElevatedButton(
                              onPressed: () {
                                context.push(
                                    '/restaurant/${restaurantProvider.currentRestaurant!.id}/posts/new',
                                    extra: {
                                      'postType': PostType.IMAGE,
                                      'postSource': Source.seo
                                    });
                              },
                              child: Text(
                                Translation.of(context).connectGoogle,
                                style: MalouTextStyles(context)
                                    .text15Semibold
                                    .copyWith(color: Colors.white),
                              ),
                            ));
                      }),
                    ],
                  )
                ],
              ),
            ),
          );
        });
  }
}
