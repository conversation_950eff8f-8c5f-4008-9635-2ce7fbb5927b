import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/messages/widgets/platform_disconnected_warning.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/tabs/social-networks/widgets/social-networks-posts-empty.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/widgets/single-post/post_card.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/widgets/posts_shimmer.widget.dart';
import 'package:malou/shared/helpers/use_query.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/models/restaurant.model.dart';
import 'package:malou/shared/services/hashtags/hashtags.provider.dart';
import 'package:malou/shared/services/platform/platform.provider.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/posts/social-posts/social-posts.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/listview_refresh/listview_refresh.widget.dart';
import 'package:malou/shared/widgets/post_status_loader/post_status_loader.widget.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';
import 'package:malou/shared/widgets/toasts/toast.widget.dart';
import 'package:malou/shared/widgets/toasts/toasts.service.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';

class SocialNetworksPostsTab extends StatefulWidget {
  const SocialNetworksPostsTab({super.key});

  @override
  State<SocialNetworksPostsTab> createState() => _SocialNetworksPostsTabState();
}

class _SocialNetworksPostsTabState extends State<SocialNetworksPostsTab>
    with AutomaticKeepAliveClientMixin {
  late PostsProvider postsProvider;
  late SocialPostsProvider socialPostsProvider;
  late RestaurantProvider restaurantProvider;
  late PlatformProvider platformProvider;
  late HashtagsProvider hashtagsProvider;
  late QueryResult<void, DeletePostParams> deletePostQuery;

  BehaviorSubject<String?> killsub$ = BehaviorSubject();

  int pageSize = 24;
  @override
  void initState() {
    super.initState();
    postsProvider = Provider.of<PostsProvider>(context, listen: false);
    socialPostsProvider =
        Provider.of<SocialPostsProvider>(context, listen: false);
    restaurantProvider =
        Provider.of<RestaurantProvider>(context, listen: false);
    platformProvider = Provider.of<PlatformProvider>(context, listen: false);
    hashtagsProvider = Provider.of<HashtagsProvider>(context, listen: false);

    deletePostQuery = useQuery(postsProvider.deletePost, onProcess: (param) {
      bool postHadBeenDeleted =
          socialPostsProvider.removePostFromPagingController(param.post);
      if (postHadBeenDeleted) {
        ToastsService.instance.add(Toast(
            type: ToastType.success,
            message: Translation.of(context).postDeleted));
      }
    });

    socialPostsProvider.refreshPostsPagingControllers(
        restaurantProvider.currentRestaurant!.id, postsProvider,
        source: Source.social);
    hashtagsProvider
        .getRestaurantHashtags(restaurantProvider.currentRestaurant!);

    restaurantProvider.restaurantSubject$
        .takeUntil(killsub$)
        .switchMap((value) => socialPostsProvider
            .canDisplayFeed(
                restaurantId: restaurantProvider.currentRestaurant!.id)
            .asStream())
        .listen((event) {
      socialPostsProvider.canDisplayFeed$.add(event);
      hashtagsProvider
          .getRestaurantHashtags(restaurantProvider.currentRestaurant!);
    });
  }

  @override
  void dispose() {
    killsub$.add('kill');
    super.dispose();
  }

  void deletePostCard(Post post) {
    deletePostQuery.execute(DeletePostParams(
        post: post, restaurantId: restaurantProvider.currentRestaurant!.id));
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return FutureBuilder(
        future: platformProvider.getPlatformsForRestaurant(
            restaurantProvider.currentRestaurant!.id),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularSpinner());
          }
          return Padding(
            padding: const EdgeInsets.only(top: 12),
            child: ListviewRefresh(
              onRefresh: () async {
                postsProvider.onPullToRefresh$
                    .add(postsProvider.selectedTabIndex$.value);
                Future.delayed(const Duration(seconds: 1), () {
                  socialPostsProvider.refreshController.refreshCompleted();
                });
              },
              refreshController: socialPostsProvider.refreshController,
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    StreamBuilder(
                        stream: CombineLatestStream.combine2<
                                Map<String, bool>,
                                Restaurant?,
                                ({
                                  Map<String, bool> publisingPost,
                                  Restaurant? currentRestaurant
                                })>(
                            socialPostsProvider.isPublishingSocialPost$,
                            restaurantProvider.restaurantSubject$,
                            (a, b) => (currentRestaurant: b, publisingPost: a)),
                        builder: (context, snapshot) {
                          Size size = MediaQuery.of(context).size;
                          if (snapshot.hasData &&
                              snapshot.data?.publisingPost[
                                      snapshot.data?.currentRestaurant?.id] ==
                                  true) {
                            return PostStatusLoader(size: size);
                          }

                          return const SizedBox();
                        }),
                    StreamBuilder(
                      stream: CombineLatestStream(
                          [
                            platformProvider.connectedSocialNetworksPlatforms$,
                            socialPostsProvider.showPublishedPosts$,
                            socialPostsProvider.showScheduledPosts$,
                            socialPostsProvider.showDraftPosts$,
                            socialPostsProvider.showErrorPosts$,
                            postsProvider.isSocialNetworksWarningVisible$,
                            restaurantProvider.restaurantSubject$
                          ],
                          (values) => ({
                                'missingPlatforms': platformProvider
                                    .socialNetworkPlatformsKeys
                                    .where((element) =>
                                        element != PlatformKeys.mapstr)
                                    .map((e) => e.name)
                                    .toSet()
                                    .difference(((values[0] as List<Platform>)
                                        .where((platform) =>
                                            platform.isValid == true)
                                        .map((e) => e.key)
                                        .toSet()))
                                    .toList(),
                                'hasPosts': (values[1] as bool) ||
                                    (values[2] as bool) ||
                                    (values[3] as bool) ||
                                    (values[4] as bool),
                                'isWarningVisible': values[5]
                              })),
                      builder: (context, snapshot) {
                        bool isWarningViisble =
                            snapshot.data?['isWarningVisible'] ?? true;
                        bool hasPosts =
                            (snapshot.data?['hasPosts'] as bool?) ?? false;
                        postsProvider.isPlatformsDisconnectedErrorShown$
                            .add(isWarningViisble && hasPosts);
                        if (!isWarningViisble) return Container();
                        List<String> missingPlatforms = (snapshot
                                .data?['missingPlatforms'] as List<String>?) ??
                            [];
                        if (!hasPosts) return Container();
                        return PlatformDisconnectedWarning(
                          missingPlatforms: missingPlatforms,
                          restaurantId:
                              restaurantProvider.currentRestaurant!.id,
                          onDismiss: () {
                            postsProvider.isSocialNetworksWarningVisible$
                                .add(false);
                          },
                        );
                      },
                    ),
                    StreamBuilder<bool>(
                        stream: socialPostsProvider.showErrorPosts$.stream,
                        builder: ((context, snapshot) {
                          return snapshot.data == false
                              ? Container()
                              : Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      Translation.of(context).publicationError,
                                      style: MalouTextStyles(context)
                                          .text12Semibold
                                          .copyWith(
                                            color: MalouColors.error,
                                          ),
                                    ),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    Container(
                                      height: snapshot.data == true ? 290 : 0,
                                      margin: const EdgeInsets.only(bottom: 30),
                                      child: PagedListView(
                                        scrollDirection: Axis.horizontal,
                                        pagingController: socialPostsProvider
                                            .errorSocialPostsPagingController,
                                        builderDelegate:
                                            PagedChildBuilderDelegate<Post>(
                                                animateTransitions: true,
                                                noItemsFoundIndicatorBuilder:
                                                    (context) {
                                                  socialPostsProvider
                                                      .showErrorPosts$
                                                      .add(false);
                                                  return Container();
                                                },
                                                firstPageErrorIndicatorBuilder:
                                                    (context) {
                                                  socialPostsProvider
                                                      .showErrorPosts$
                                                      .add(false);
                                                  return Container();
                                                },
                                                firstPageProgressIndicatorBuilder:
                                                    buildPostsShimmerLodaer,
                                                newPageProgressIndicatorBuilder:
                                                    (context) =>
                                                        const CircularSpinner(
                                                          color: MalouColors
                                                              .primary,
                                                          size: 20,
                                                        ),
                                                itemBuilder:
                                                    (context, post, index) {
                                                  return PostCard(
                                                      post: post,
                                                      deletePostCard:
                                                          deletePostCard);
                                                }),
                                      ),
                                    ),
                                  ],
                                );
                        })),
                    StreamBuilder<bool>(
                        stream: socialPostsProvider.showScheduledPosts$.stream,
                        builder: (context, snapshot) {
                          if (snapshot.data == false) return Container();
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                Translation.of(context).scheduled,
                                style: MalouTextStyles(context)
                                    .text12Semibold
                                    .copyWith(
                                      color: MalouColors.textTitle,
                                    ),
                              ),
                              const SizedBox(
                                height: 20,
                              ),
                              Container(
                                height: 290,
                                margin: const EdgeInsets.only(bottom: 30),
                                child: PagedListView(
                                  scrollDirection: Axis.horizontal,
                                  pagingController: socialPostsProvider
                                      .scheduledSocialPostsPagingController,
                                  builderDelegate:
                                      PagedChildBuilderDelegate<Post>(
                                    noItemsFoundIndicatorBuilder: (context) {
                                      socialPostsProvider.showScheduledPosts$
                                          .add(false);
                                      return Container();
                                    },
                                    firstPageErrorIndicatorBuilder: (context) {
                                      socialPostsProvider.showScheduledPosts$
                                          .add(false);
                                      return Container();
                                    },
                                    animateTransitions: true,
                                    firstPageProgressIndicatorBuilder:
                                        buildPostsShimmerLodaer,
                                    newPageProgressIndicatorBuilder:
                                        (context) => const CircularSpinner(
                                      color: MalouColors.primary,
                                      size: 20,
                                    ),
                                    itemBuilder: (context, post, index) =>
                                        PostCard(
                                      post: post,
                                      deletePostCard: deletePostCard,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          );
                        }),
                    StreamBuilder<bool>(
                        stream: socialPostsProvider.showDraftPosts$.stream,
                        builder: (context, snapshot) {
                          if (snapshot.data == false) return Container();
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                Translation.of(context).drafts,
                                style: MalouTextStyles(context)
                                    .text12Semibold
                                    .copyWith(
                                      color: MalouColors.textTitle,
                                    ),
                              ),
                              const SizedBox(
                                height: 20,
                              ),
                              Container(
                                height: 290,
                                margin: const EdgeInsets.only(bottom: 30),
                                child: PagedListView(
                                    scrollDirection: Axis.horizontal,
                                    pagingController: socialPostsProvider
                                        .draftSocialPostsPagingController,
                                    builderDelegate:
                                        PagedChildBuilderDelegate<Post>(
                                      noItemsFoundIndicatorBuilder: (context) {
                                        socialPostsProvider.showDraftPosts$
                                            .add(false);
                                        return Container();
                                      },
                                      firstPageErrorIndicatorBuilder:
                                          (context) {
                                        socialPostsProvider.showDraftPosts$
                                            .add(false);
                                        return Container();
                                      },
                                      firstPageProgressIndicatorBuilder:
                                          buildPostsShimmerLodaer,
                                      newPageProgressIndicatorBuilder:
                                          (context) => const CircularSpinner(
                                        color: MalouColors.primary,
                                        size: 20,
                                      ),
                                      itemBuilder: (context, post, index) =>
                                          post.hasError()
                                              ? const SizedBox()
                                              : PostCard(
                                                  post: post,
                                                  deletePostCard:
                                                      deletePostCard),
                                    )),
                              ),
                            ],
                          );
                        }),
                    StreamBuilder<bool>(
                        stream: socialPostsProvider.showPublishedPosts$.stream,
                        builder: (context, snapshot) {
                          if (snapshot.data == false) return Container();
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                Translation.of(context).published,
                                style: MalouTextStyles(context)
                                    .text12Semibold
                                    .copyWith(
                                      color: MalouColors.textTitle,
                                    ),
                              ),
                              const SizedBox(
                                height: 20,
                              ),
                              Container(
                                height: 290,
                                margin: const EdgeInsets.only(bottom: 30),
                                child: PagedListView(
                                    scrollDirection: Axis.horizontal,
                                    pagingController: socialPostsProvider
                                        .publishedSocialPostsPagingController,
                                    builderDelegate:
                                        PagedChildBuilderDelegate<Post>(
                                      noItemsFoundIndicatorBuilder: (context) {
                                        socialPostsProvider.showPublishedPosts$
                                            .add(false);
                                        return Container();
                                      },
                                      firstPageErrorIndicatorBuilder:
                                          (context) {
                                        socialPostsProvider.showPublishedPosts$
                                            .add(false);
                                        return Container();
                                      },
                                      firstPageProgressIndicatorBuilder:
                                          buildPostsShimmerLodaer,
                                      newPageProgressIndicatorBuilder:
                                          (context) => const CircularSpinner(
                                        color: MalouColors.primary,
                                        size: 20,
                                      ),
                                      itemBuilder: (context, post, index) =>
                                          PostCard(
                                              post: post,
                                              deletePostCard: deletePostCard),
                                    )),
                              ),
                            ],
                          );
                        }),
                    SocialEmptyPostsPage(
                        emptyText: Translation.of(context).emptySocialPosts,
                        socialPostsProvider: socialPostsProvider,
                        platformProvider: platformProvider,
                        postsProvider: postsProvider)
                  ],
                ),
              ),
            ),
          );
        });
  }

  @override
  bool get wantKeepAlive => true;
}
