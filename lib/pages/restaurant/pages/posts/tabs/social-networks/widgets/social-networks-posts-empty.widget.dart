import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/app_config.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/platform/platform.provider.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/posts/social-posts/social-posts.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/buttons/styles.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:url_launcher/url_launcher.dart';

class SocialEmptyPostsPage extends StatefulWidget {
  final String emptyText;
  const SocialEmptyPostsPage({
    super.key,
    required this.platformProvider,
    required this.postsProvider,
    this.isStories = false,
    required this.emptyText,
    required this.socialPostsProvider,
  });

  final PlatformProvider platformProvider;
  final PostsProvider postsProvider;
  final bool isStories;
  final SocialPostsProvider socialPostsProvider;

  @override
  State<SocialEmptyPostsPage> createState() => _SocialEmptyPostsPageState();
}

class _SocialEmptyPostsPageState extends State<SocialEmptyPostsPage> {
  late RestaurantProvider restaurantProvider;
  @override
  void initState() {
    restaurantProvider =
        Provider.of<RestaurantProvider>(context, listen: false);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
        stream: CombineLatestStream(
            [
              widget.platformProvider.connectedSocialNetworksPlatforms$,
              widget.socialPostsProvider.showPublishedPosts$,
              widget.socialPostsProvider.showScheduledPosts$,
              widget.socialPostsProvider.showDraftPosts$,
              widget.socialPostsProvider.showErrorPosts$
            ],
            (values) => ({
                  'platformsConnected': widget
                      .platformProvider.socialNetworkPlatformsKeys
                      .where((element) => element != PlatformKeys.mapstr)
                      .map((e) => e.name)
                      .toSet()
                      .difference(
                          ((values[0] as List).map((e) => e.key).toSet()))
                      .toList()
                      .isEmpty,
                  'hasPosts': (values[1] as bool) ||
                      (values[2] as bool) ||
                      (values[3] as bool) ||
                      (values[4] as bool),
                })),
        builder: (context, snapshot) {
          bool hasPlatformsConnected =
              snapshot.data?['platformsConnected'] ?? false;
          bool hasPosts = snapshot.data?['hasPosts'] ?? false;
          if (hasPosts) return Container();
          if (hasPlatformsConnected && !hasPosts) {
            return SizedBox(
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      IllustrationsPathResolver.resolve(Illustrations.cook),
                      width: 70,
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    SizedBox(
                      width: 300,
                      child: Text(
                        widget.emptyText,
                        style: MalouTextStyles(context).text14Italic,
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    if (!widget.isStories)
                      PrimaryButtonContainer(
                          child: ElevatedButton(
                        onPressed: () {
                          context.push(
                              '/restaurant/${restaurantProvider.currentRestaurant!.id}/posts/new',
                              extra: {
                                'postType': PostType.CAROUSEL,
                                'postSource': Source.social
                              });
                        },
                        child: Text(
                          Translation.of(context).addFIrstPost,
                          style: MalouTextStyles(context)
                              .text15Semibold
                              .copyWith(color: Colors.white),
                        ),
                      )),
                  ],
                ),
              ),
            );
          }

          return Center(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  IllustrationsPathResolver.resolve(Illustrations.cook),
                  width: 70,
                ),
                const SizedBox(
                  height: 20,
                ),
                SizedBox(
                  width: 300,
                  child: Text(
                    Translation.of(context).noSocialNetworksConnected,
                    style: MalouTextStyles(context).text14Italic,
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                Column(
                  children: [
                    SecondaryButtonContainer(
                        fullWidth: true,
                        child: ElevatedButton(
                          onPressed: () {
                            context.push(
                                '/restaurant/${restaurantProvider.currentRestaurant!.id}/posts/new',
                                extra: {
                                  'postType': PostType.CAROUSEL,
                                  'postSource': Source.social
                                });
                          },
                          child: Text(
                            Translation.of(context).addDraft,
                            style: MalouTextStyles(context)
                                .text15Semibold
                                .copyWith(color: MalouColors.primary),
                          ),
                        )),
                    const SizedBox(
                      height: 10,
                    ),
                    PrimaryButtonContainer(
                        fullWidth: true,
                        child: ElevatedButton(
                          onPressed: _onRedirectToPlatforms,
                          child: Text(
                            Translation.of(context).connectSocialNetworks,
                            style: MalouTextStyles(context)
                                .text15Semibold
                                .copyWith(color: Colors.white),
                          ),
                        )),
                  ],
                )
              ],
            ),
          );
        });
  }

  void _onRedirectToPlatforms() async {
    Uri uri = Uri.parse(
        '${AppConfig.shared.webAppUri}/restaurants/${restaurantProvider.currentRestaurant!.id}/settings/platforms/connection');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }
}
