import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:malou/pages/restaurant/pages/posts/widgets/post-publication-status-icon/post_publication_status.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/widgets/post-type-icon/post_type_icon.widget.dart';
import 'package:malou/shared/helpers/use_query.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/widgets/medias/post_media_cover.widget.dart';
import 'package:provider/provider.dart';

class FeedItem extends StatefulWidget {
  final Post post;
  final Function(Post post) editFeed;
  const FeedItem({super.key, required this.post, required this.editFeed});

  @override
  State<FeedItem> createState() => _FeedItemState();
}

class _FeedItemState extends State<FeedItem> {
  QueryResult<bool, String?> isUrlExpiredQuery = useQuery(isUrlExpired);
  late PostsProvider postProvider;

  @override
  void initState() {
    postProvider = Provider.of<PostsProvider>(context, listen: false);
    String? url = getUrl(widget.post);

    isUrlExpiredQuery.execute(url);

    isUrlExpiredQuery.data
        .where((event) => event != null)
        .take(1)
        .listen((isExpired) {
      if (isExpired!) {
        postProvider.refresh(widget.post.id!).then((newPost) {
          if (newPost == null) return;
          if (mounted) {
            setState(() {
              widget.post.setAttachments(newPost.attachments);
              widget.post.setSocialAttachments(newPost.socialAttachments);
            });
          }
        });
      }
    });
    super.initState();
  }

  String? getUrl(Post post) {
    if (post.isReel() || post.isVideo()) {
      if (post.getThumbnail() != null) {
        return post.getThumbnail()!;
      }

      if (post.getAttachments().isNotEmpty) {
        return post.getAttachments()[0]!;
      }

      return null;
    } else {
      return widget.post.getAttachments().isNotEmpty
          ? widget.post.getAttachments()[0]!
          : null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        PostMediaCover(
          showPlayIcon: false,
          post: widget.post,
        ),
        Positioned(
          top: 8,
          right: 8,
          height: 24,
          width: 24,
          child: PostPublicationStatusIcon(
            post: widget.post,
          ),
        ),
        Positioned(
          top: 8,
          left: 8,
          height: 24,
          width: 24,
          child: PostTypeIcon(
            post: widget.post,
          ),
        )
      ],
    );
  }
}

Future<bool> isUrlExpired(String? url) async {
  try {
    if (url == null) return true;
    final response = await http.head(Uri.parse(url));
    return response.statusCode != 200;
  } catch (e) {
    return true;
  }
}
