import 'dart:math';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_reorderable_grid_view/widgets/widgets.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/router.dart';
import 'package:malou/shared/helpers/experimentation.service.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/posts/posts.service.dart';
import 'package:malou/shared/services/posts/social-posts/social-posts.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/widgets/toasts/toast.widget.dart';
import 'package:malou/shared/widgets/toasts/toasts.service.dart';
import 'package:provider/provider.dart';

class FeedPageController {
  final experimentationService = ExperimentationService.instance;
  late SocialPostsProvider socialPostsProvider;
  late RestaurantProvider restaurantProvider;

  final ScrollController scrollController = ScrollController();

  final PagingController<int, Post> pagingController =
      PagingController(firstPageKey: 0);

  List<Post> feed = [];
  List<int> nonDraggableIndices = [];

  int? _startIndex;
  int? _endIndex;

  void Function(void Function()) setState;

  FeedPageController(BuildContext context, this.setState) {
    socialPostsProvider =
        Provider.of<SocialPostsProvider>(context, listen: false);
    restaurantProvider =
        Provider.of<RestaurantProvider>(context, listen: false);
    pagingController.addPageRequestListener(_fetchPage);
  }

  void _fetchPage(int pageKey) async {
    socialPostsProvider
        .getFeed(
            pageNumber: pageKey,
            pageSize: 35,
            restaurantId: restaurantProvider.currentRestaurant!.id)
        .then((value) {
      final isLastPage = value.posts.length < 35;

      var postsThatCanBeDisplayedInFeed = value.posts
          .where((element) => element.isReelDisplayedInFeed)
          .toList();

      if (isLastPage) {
        pagingController.appendLastPage(postsThatCanBeDisplayedInFeed);
      } else {
        final nextPageKey = 1 + pageKey;
        pagingController.appendPage(postsThatCanBeDisplayedInFeed, nextPageKey);
      }

      setState(() {
        feed = pagingController.itemList ?? [];
        nonDraggableIndices = feed
            .asMap()
            .entries
            .where((element) => !element.value.canBeRescheduledInFeed())
            .map((e) => e.key)
            .toList();
      });
    }).catchError((error) {
      pagingController.error = error;
    });
  }

  editFeed(Post post) {
    final index = pagingController.itemList!.indexWhere((element) {
      return element.id == post.id;
    });

    if (index != -1) {
      pagingController.itemList![index] = post;
    }
  }

  onDragStarted(int index) {
    _startIndex = index;
  }

  onDragEnd(int index) {
    _endIndex = index;
  }

  onReorder(ReorderedListFunction reorderedListFunction) {
    if ((_startIndex == _endIndex) ||
        _startIndex == null ||
        _endIndex == null) {
      return;
    }
    PostsService service = PostsService();

    // construct reassign array
    List<ReassignItem> reassignArray = _getReassignToSwapPlannedPublicationDate(
        feed,
        _startIndex! > _endIndex! ? Order.up : Order.down,
        _startIndex!,
        _endIndex!);
    final previousState = feed.toList();

    setState(() {
      final begin = feed.slice(0, min(_startIndex!, _endIndex!));
      final end = feed.slice(max(_startIndex!, _endIndex!) + 1, feed.length);
      final minIndex = min(_startIndex!, _endIndex!);
      final maxIndex = max(_startIndex!, _endIndex!);
      if (_startIndex! < _endIndex!) {
        final reordered = feed.slice(
          minIndex + 1,
          maxIndex + 1,
        );
        feed = [...begin, ...reordered, feed[minIndex], ...end];
      } else {
        final reordered = feed.slice(
          minIndex,
          maxIndex,
        );
        feed = [...begin, feed[maxIndex], ...reordered, ...end];
      }
    });

    _startIndex = null;
    _endIndex = null;
    // call swap scheduled date
    service.swapPlannedPublicationDates(reassignArray).catchError((_) {
      ToastsService.instance.add(Toast(
          type: ToastType.error,
          message:
              Translation.of(rootNavigatorKey.currentContext!).unknownError));
      setState(() {
        feed = previousState;
      });
    });
  }

  dispose() {
    pagingController.dispose();
  }

  List<ReassignItem> _getReassignToSwapPlannedPublicationDate(
      List<Post> feed, Order order, int startIndex, int endIndex) {
    final slicedFeed = feed.slice(
      min(startIndex, endIndex),
      max(startIndex, endIndex) + 1,
    );
    final feedLength = slicedFeed.length;
    final reassign = slicedFeed.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      final sourceIndex =
          (index + (order == Order.up ? 1 : -1) + feedLength) % feedLength;
      final sourcePostId = slicedFeed[sourceIndex].id!;
      return ReassignItem(
          sourcePostId: sourcePostId, destinationPostId: item.id!);
    }).toList();

    return reassign;
  }
}

class ReassignItem {
  final String sourcePostId;
  final String destinationPostId;

  ReassignItem({required this.sourcePostId, required this.destinationPostId});

  toJson() {
    return {
      'sourcePostId': sourcePostId,
      'destinationPostId': destinationPostId,
    };
  }
}

enum Order { up, down }
