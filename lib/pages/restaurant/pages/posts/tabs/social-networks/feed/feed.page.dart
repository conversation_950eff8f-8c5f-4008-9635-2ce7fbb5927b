import 'package:flutter/material.dart';
import 'package:flutter_reorderable_grid_view/widgets/widgets.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/posts/tabs/social-networks/feed/feed.controller.dart';
import 'package:malou/pages/restaurant/pages/posts/tabs/social-networks/feed/widgets/feed_item.widget.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:skeleton_loader/skeleton_loader.dart';

class FeedPage extends StatefulWidget {
  const FeedPage({super.key});

  @override
  State<FeedPage> createState() => _FeedPageState();
}

class _FeedPageState extends State<FeedPage> {
  late FeedPageController feedPageController;
  @override
  void initState() {
    feedPageController = FeedPageController(context, setState);
    super.initState();
  }

  @override
  void dispose() {
    feedPageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        body: SafeArea(
          child: Stack(
            fit: StackFit.expand,
            children: [
              ReorderableBuilder(
                enableDraggable: true,
                onReorder: feedPageController.onReorder,
                onDragEnd: feedPageController.onDragEnd,
                onDragStarted: feedPageController.onDragStarted,
                nonDraggableIndices: feedPageController.nonDraggableIndices,
                lockedIndices: feedPageController.nonDraggableIndices,
                scrollController: feedPageController.scrollController,
                builder: (children) {
                  return PagedGridView(
                    shrinkWrap: true,
                    pagingController: feedPageController.pagingController,
                    scrollController: feedPageController.scrollController,
                    builderDelegate: PagedChildBuilderDelegate<Post>(
                      itemBuilder: (context, item, index) =>
                          children.length > index
                              ? children[index]
                              : const SizedBox(),
                      firstPageProgressIndicatorBuilder: (context) =>
                          const SkeletonFeed(),
                    ),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisSpacing: 2,
                      mainAxisSpacing: 2,
                      childAspectRatio: 4 / 5,
                      crossAxisCount: 3,
                    ),
                  );
                },
                children: (feedPageController.feed)
                    .map((item) => FeedItem(
                        key: ValueKey(item.id),
                        post: item,
                        editFeed: feedPageController.editFeed))
                    .toList(),
              ),
              const BackToListButton()
            ],
          ),
        ));
  }
}

class SkeletonFeed extends StatelessWidget {
  const SkeletonFeed({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SkeletonGridLoader(
      items: 21,
      highlightColor: MalouColors.borderPrimary,
      baseColor: MalouColors.backgroundLight,
      direction: SkeletonDirection.ltr,
      itemsPerRow: 3,
      mainAxisSpacing: 2,
      crossAxisSpacing: 2,
      builder: Container(
        height: 100,
        width: 100,
        decoration: const BoxDecoration(color: MalouColors.backgroundDark),
      ),
    );
  }
}

class BackToListButton extends StatelessWidget {
  const BackToListButton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
        bottom: 80,
        left: 0,
        right: 0,
        child: Center(
            child: TextButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all<Color>(MalouColors.textTitle),
                    shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                        RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10.0)))),
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text(Translation.of(context).seeList,
                    style: MalouTextStyles(context)
                        .text13Semibold
                        .copyWith(color: Colors.white)))));
  }
}
