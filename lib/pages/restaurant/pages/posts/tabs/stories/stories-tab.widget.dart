import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/messages/widgets/platform_disconnected_warning.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/tabs/stories/widgets/story.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/widgets/posts_shimmer.widget.dart';
import 'package:malou/shared/helpers/use_query.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/models/posts/story.model.dart';
import 'package:malou/shared/services/platform/platform.provider.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/posts/stories/stories.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';
import 'package:malou/shared/widgets/toasts/toast.widget.dart';
import 'package:malou/shared/widgets/toasts/toasts.service.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';

class StoriesPostsTab extends StatefulWidget {
  const StoriesPostsTab({super.key});

  @override
  State<StoriesPostsTab> createState() => _StoriesPostsTabState();
}

class _StoriesPostsTabState extends State<StoriesPostsTab> {
  late PostsProvider postsProvider;
  late StoriesProvider storiesProvider;
  late RestaurantProvider restaurantProvider;
  late PlatformProvider platformProvider;
  late QueryResult<void, DeletePostParams> deletePostQuery;

  int pageSize = 24;
  @override
  void initState() {
    super.initState();
    postsProvider = Provider.of<PostsProvider>(context, listen: false);
    storiesProvider = Provider.of<StoriesProvider>(context, listen: false);
    restaurantProvider =
        Provider.of<RestaurantProvider>(context, listen: false);
    platformProvider = Provider.of<PlatformProvider>(context, listen: false);

    deletePostQuery = useQuery(postsProvider.deletePost, onProcess: (param) {
      bool postHadBeenDeleted =
          storiesProvider.removePostFromPagingController(param.post);
      if (postHadBeenDeleted) {
        ToastsService.instance.add(Toast(
            type: ToastType.success,
            message: Translation.of(context).storyDeleted));
      }
    });

    storiesProvider.refreshPostsPagingControllers(
        restaurantProvider.currentRestaurant!.id, postsProvider,
        source: Source.social);
  }

  void deletePostCard(Post post) {
    deletePostQuery.execute(DeletePostParams(
        post: post, restaurantId: restaurantProvider.currentRestaurant!.id));
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            StreamBuilder(
              stream: CombineLatestStream(
                  [
                    platformProvider.connectedSocialNetworksPlatforms$,
                    storiesProvider.showPublishedPosts$,
                    storiesProvider.showScheduledPosts$,
                    storiesProvider.showDraftPosts$,
                    storiesProvider.showErrorPosts$,
                    postsProvider.isSocialNetworksWarningVisible$,
                    restaurantProvider.restaurantSubject$
                  ],
                  (values) => ({
                        'missingPlatforms': platformProvider
                            .socialNetworkPlatformsKeys
                            .where((element) => element.name == 'instagram')
                            .map((e) => e.name)
                            .toSet()
                            .difference(
                                ((values[0] as List).map((e) => e.key).toSet()))
                            .toList(),
                        'hasPosts': (values[1] as bool) ||
                            (values[2] as bool) ||
                            (values[3] as bool) ||
                            (values[4] as bool),
                        'isWarningVisible': values[5]
                      })),
              builder: (context, snapshot) {
                bool isWarningViisble =
                    snapshot.data?['isWarningVisible'] ?? true;
                if (!isWarningViisble) return Container();
                bool hasPosts = (snapshot.data?['hasPosts'] as bool?) ?? false;
                List<String> missingPlatforms =
                    (snapshot.data?['missingPlatforms'] as List<String>?) ?? [];
                if (!hasPosts) return Container();
                return PlatformDisconnectedWarning(
                  missingPlatforms: missingPlatforms,
                  restaurantId: restaurantProvider.currentRestaurant!.id,
                  onDismiss: () {
                    postsProvider.isSocialNetworksWarningVisible$.add(false);
                  },
                );
              },
            ),
            StreamBuilder<bool>(
                stream: storiesProvider.showErrorPosts$.stream,
                builder: ((context, snapshot) {
                  return snapshot.data == false
                      ? Container()
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              Translation.of(context).publicationError,
                              style: MalouTextStyles(context)
                                  .text12Semibold
                                  .copyWith(
                                    color: MalouColors.error,
                                  ),
                            ),
                            const SizedBox(
                              height: 20,
                            ),
                            Container(
                              height: snapshot.data == true ? 290 : 0,
                              margin: const EdgeInsets.only(bottom: 30),
                              child: PagedListView(
                                scrollDirection: Axis.horizontal,
                                pagingController: storiesProvider
                                    .errorStoriesPagingController,
                                builderDelegate: PagedChildBuilderDelegate<
                                        Post>(
                                    noItemsFoundIndicatorBuilder: (context) {
                                      storiesProvider.showErrorPosts$
                                          .add(false);
                                      return Container();
                                    },
                                    firstPageErrorIndicatorBuilder: (context) {
                                      storiesProvider.showErrorPosts$
                                          .add(false);
                                      return Container();
                                    },
                                    animateTransitions: true,
                                    firstPageProgressIndicatorBuilder:
                                        buildPostsShimmerLodaer,
                                    newPageProgressIndicatorBuilder:
                                        (context) => const CircularSpinner(
                                              color: MalouColors.primary,
                                              size: 20,
                                            ),
                                    itemBuilder: (context, post, index) {
                                      Story story = Story.fromPost(post);
                                      return StoryCard(
                                        story: story,
                                        deletePostCard: deletePostCard,
                                      );
                                    }),
                              ),
                            ),
                          ],
                        );
                })),
            StreamBuilder<bool>(
                stream: storiesProvider.showActiveStories$,
                builder: (context, snapshot) {
                  if (snapshot.data == false) return Container();
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        Translation.of(context).actives,
                        style: MalouTextStyles(context).text12Semibold.copyWith(
                              color: MalouColors.textTitle,
                            ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      Container(
                          height: 290,
                          margin: const EdgeInsets.only(bottom: 30),
                          child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: storiesProvider.activeStories.length,
                              itemBuilder: (context, idx) => StoryCard(
                                    story: Story.fromPost(
                                        storiesProvider.activeStories[idx]),
                                    deletePostCard: deletePostCard,
                                  ))),
                    ],
                  );
                }),
            StreamBuilder<bool>(
                stream: storiesProvider.showScheduledPosts$.stream,
                builder: (context, snapshot) {
                  if (snapshot.data == false) return Container();
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        Translation.of(context).scheduled,
                        style: MalouTextStyles(context).text12Semibold.copyWith(
                              color: MalouColors.textTitle,
                            ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      Container(
                        height: 290,
                        margin: const EdgeInsets.only(bottom: 30),
                        child: PagedListView(
                          scrollDirection: Axis.horizontal,
                          pagingController:
                              storiesProvider.scheduledStoriesPagingController,
                          builderDelegate: PagedChildBuilderDelegate<Post>(
                              noItemsFoundIndicatorBuilder: (context) {
                                storiesProvider.showScheduledPosts$.add(false);
                                return Container();
                              },
                              firstPageErrorIndicatorBuilder: (context) {
                                storiesProvider.showScheduledPosts$.add(false);
                                return Container();
                              },
                              animateTransitions: true,
                              firstPageProgressIndicatorBuilder:
                                  buildPostsShimmerLodaer,
                              newPageProgressIndicatorBuilder: (context) =>
                                  const CircularSpinner(
                                    color: MalouColors.primary,
                                    size: 20,
                                  ),
                              itemBuilder: (context, post, index) {
                                Story story = Story.fromPost(post);
                                return StoryCard(
                                  story: story,
                                  deletePostCard: deletePostCard,
                                );
                              }),
                        ),
                      ),
                    ],
                  );
                }),
            StreamBuilder<bool>(
                stream: storiesProvider.showDraftPosts$.stream,
                builder: (context, snapshot) {
                  if (snapshot.data == false) return Container();
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        Translation.of(context).drafts,
                        style: MalouTextStyles(context).text12Semibold.copyWith(
                              color: MalouColors.textTitle,
                            ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      Container(
                        height: 290,
                        margin: const EdgeInsets.only(bottom: 30),
                        child: PagedListView(
                            scrollDirection: Axis.horizontal,
                            pagingController:
                                storiesProvider.draftStoriesPagingController,
                            builderDelegate: PagedChildBuilderDelegate<Post>(
                                noItemsFoundIndicatorBuilder: (context) {
                                  storiesProvider.showDraftPosts$.add(false);
                                  return Container();
                                },
                                firstPageErrorIndicatorBuilder: (context) {
                                  storiesProvider.showDraftPosts$.add(false);
                                  return Container();
                                },
                                firstPageProgressIndicatorBuilder:
                                    buildPostsShimmerLodaer,
                                newPageProgressIndicatorBuilder: (context) =>
                                    const CircularSpinner(
                                      color: MalouColors.primary,
                                      size: 20,
                                    ),
                                itemBuilder: (context, post, index) {
                                  Story story = Story.fromPost(post);
                                  return StoryCard(
                                    story: story,
                                    deletePostCard: deletePostCard,
                                  );
                                })),
                      ),
                    ],
                  );
                }),
            StreamBuilder<bool>(
                stream: storiesProvider.showPublishedPosts$.stream,
                builder: (context, snapshot) {
                  if (snapshot.data == false) return Container();
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        Translation.of(context).ended,
                        style: MalouTextStyles(context).text12Semibold.copyWith(
                              color: MalouColors.textTitle,
                            ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      Container(
                        height: 290,
                        margin: const EdgeInsets.only(bottom: 30),
                        child: PagedListView(
                            scrollDirection: Axis.horizontal,
                            pagingController: storiesProvider
                                .publishedStoriesPagingController,
                            builderDelegate: PagedChildBuilderDelegate<Post>(
                                noItemsFoundIndicatorBuilder: (context) {
                                  storiesProvider.showPublishedPosts$
                                      .add(false);
                                  return Container();
                                },
                                firstPageErrorIndicatorBuilder: (context) {
                                  storiesProvider.showPublishedPosts$
                                      .add(false);
                                  return Container();
                                },
                                firstPageProgressIndicatorBuilder:
                                    buildPostsShimmerLodaer,
                                newPageProgressIndicatorBuilder: (context) =>
                                    const CircularSpinner(
                                      color: MalouColors.primary,
                                      size: 20,
                                    ),
                                itemBuilder: (context, post, index) {
                                  Story story = Story.fromPost(post);
                                  if (story.isActive()) {
                                    return Container();
                                  }
                                  return StoryCard(
                                    story: story,
                                    deletePostCard: deletePostCard,
                                  );
                                })),
                      ),
                    ],
                  );
                }),
            //SocialEmptyPostsPage(
            //  emptyText: Translation.of(context).noStories,
            //  socialPostsProvider: ,
            //  platformProvider: platformProvider,
            //  postsProvider: postsProvider,
            //  isStories: true,
            //)
          ],
        ));
  }
}
