import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/router.dart';
import 'package:malou/shared/helpers/modal_bottom_sheet.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/models/posts/story.model.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/services/posts/stories/stories.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/medias/post_media_cover.widget.dart';
import 'package:provider/provider.dart';

class StoryCard extends StatelessWidget {
  final Story story;
  final Function(Post post) deletePostCard;
  const StoryCard(
      {super.key, required this.story, required this.deletePostCard});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        bool isActive = story.isActive();
        if (!isActive) return;
        context.push('/restaurant/${story.restaurantId}/posts/stories', extra: {
          'stories': context.read<StoriesProvider>().activeStories,
          'initialStoryIndex': context
              .read<StoriesProvider>()
              .activeStories
              .indexWhere((element) => element.id == story.id)
        });
      },
      onLongPress: () {
        HapticFeedback.mediumImpact();
        AppBottomModal.updateDeleteActionsDialog(
            rootNavigatorKey.currentContext!, onConfirmDelete: () {
          deletePostCard(story);
        }, onClickEditButton: () {});
      },
      child: Stack(
        children: [
          Container(
            width: 140,
            height: 290,
            margin: const EdgeInsets.only(right: 20),
            decoration: BoxDecoration(
              border: Border.all(
                width: 2,
                color: story.published == PublicationStatus.ERROR
                    ? MalouColors.error
                    : MalouColors.backgroundDark,
              ),
              borderRadius: BorderRadius.circular(10),
            ),
            child: PostMediaCover(
              post: story,
              borderRadius: const BorderRadius.all(Radius.circular(10)),
              fullHeight: true,
            ),
          ),
          Positioned(
            // because of the border 140 - 2 + (-2 left border)
            width: 136,
            bottom: 1,
            left: 2,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.all(Radius.circular(10)),
                color: MalouColors.textTitle.withOpacity(0.5),
              ),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      story.getPostDate(
                          context.read<LanguageProvider>().language),
                      style: MalouTextStyles(context)
                          .text10Semibold
                          .copyWith(color: Colors.white),
                    ),
                    const SizedBox(
                      height: 3,
                    ),
                    Text(
                      story.getStoryDuration(),
                      style: MalouTextStyles(context)
                          .text10Italic
                          .copyWith(color: Colors.white),
                    ),
                    const SizedBox(
                      height: 3,
                    ),
                    if (story.shouldDisplayFeedback())
                      Row(
                        children: [
                          const CircleAvatar(
                            backgroundColor: MalouColors.warning,
                            radius: 6,
                          ),
                          const SizedBox(width: 5),
                          Text(
                              Translation.of(context).remarksNumber(
                                  story.feedback!.getFeedbacksCount()),
                              style: MalouTextStyles(context)
                                  .text10Italic
                                  .copyWith(color: Colors.white)),
                        ],
                      )
                  ]),
            ),
          )
        ],
      ),
    );
  }
}
