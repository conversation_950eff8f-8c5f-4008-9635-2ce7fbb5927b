import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:malou/shared/helpers/utils.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/models/posts/story.model.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/pages/medias_preview.widget.dart';
import 'package:provider/provider.dart';
import 'package:story/story.dart';
import 'package:timeago/timeago.dart' as timeago;

class StoryPreview extends StatefulWidget {
  final List<Story> stories;
  final int initialStoryIndex;
  const StoryPreview(
      {super.key, required this.stories, required this.initialStoryIndex});

  @override
  State<StoryPreview> createState() => _StoryPreviewState();
}

class _StoryPreviewState extends State<StoryPreview> {
  late RestaurantProvider restaurantProvider;

  @override
  void initState() {
    restaurantProvider =
        Provider.of<RestaurantProvider>(context, listen: false);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        body: GestureDetector(
          onVerticalDragEnd: (details) =>
              details.primaryVelocity! > 0 ? context.pop() : null,
          child: Container(
            padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
            child: StoryPageView(
              showShadow: true,
              // onPageLimitReached: () => context.pop(),
              indicatorPadding:
                  const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
              itemBuilder: (context, pageIndex, storyIndex) {
                Story story = widget.stories[storyIndex];

                return Stack(
                  children: [
                    story.postType == PostType.IMAGE
                        ? Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              image: DecorationImage(
                                image: CachedNetworkImageProvider(
                                    story.getAttachments()[0]!),
                                fit: BoxFit.cover,
                              ),
                            ),
                          )
                        : VideoMedia(
                            url: story.getAttachments()[0]!,
                            displayControls: false,
                            autoplay: true,
                          ),
                    Positioned(
                      top: 40,
                      left: 20,
                      right: 0,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              CircleAvatar(
                                radius: 16,
                                backgroundImage: CachedNetworkImageProvider(
                                    restaurantProvider.currentRestaurant!.logo!
                                        .getUrlForSize(
                                            preferedSize: MediaSize.small)),
                              ),
                              const SizedBox(width: 10),
                              Text(restaurantProvider.currentRestaurant!.name,
                                  style: MalouTextStyles(context)
                                      .text14Semibold
                                      .copyWith(color: Colors.white)),
                              const SizedBox(width: 10),
                              Text(
                                  capitalize(timeago.format(
                                      story.socialCreatedAt!,
                                      locale: Intl.getCurrentLocale())),
                                  style: MalouTextStyles(context)
                                      .text14
                                      .copyWith(color: Colors.white)),
                            ],
                          ),
                          IconButton(
                            padding: EdgeInsets.zero,
                            onPressed: () => context.pop(),
                            icon: const Icon(Icons.more_vert,
                                color: Colors.white, size: 20),
                          )
                        ],
                      ),
                    )
                  ],
                );
              },
              storyLength: (pageIndex) {
                return widget.stories.length;
              },
              pageLength: 1,
              initialStoryIndex: (_) => widget.initialStoryIndex,
            ),
          ),
        ));
  }
}
