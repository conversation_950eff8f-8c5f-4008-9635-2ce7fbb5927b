import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/post_creation.controller.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_description/post_description.tab.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_feedbacks/post_feedbacks.tab.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_medias/create_post_medias_tab.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/reel_media/create_reel_medias_tab.widget.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/platform/platform.provider.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:provider/provider.dart';
import 'widgets/post_creation_app_bar.widget.dart';

class PostCreationPage extends StatefulWidget {
  final String? postId;
  final PostType postType;
  final Source postSource;
  const PostCreationPage(
      {super.key,
      this.postId,
      required this.postType,
      required this.postSource});

  @override
  State<PostCreationPage> createState() => _PostCreationPageState();
}

class _PostCreationPageState extends State<PostCreationPage>
    with SingleTickerProviderStateMixin {
  late PostCreationController postCreationController;

  @override
  void initState() {
    postCreationController = PostCreationController(
        postsProvider: Provider.of<PostsProvider>(context, listen: false),
        restaurantProvider:
            Provider.of<RestaurantProvider>(context, listen: false),
        userProvider: Provider.of<UserProvider>(context, listen: false),
        platformProvider: Provider.of<PlatformProvider>(context, listen: false),
        postId: widget.postId,
        postSource: widget.postSource,
        postType: widget.postType);

    postCreationController.intializePostToEdit();
    postCreationController.initializeManagers();

    Provider.of<PostsProvider>(context, listen: false)
        .postCreationTabsController = TabController(length: 3, vsync: this);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () => postCreationController.onClosePressed(
          postCreationController.postToEdit,
          postCreationController.postsProvider,
          context),
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: PostCreationAppBar(
            postType: widget.postType,
            postSource: widget.postSource,
            onPublishPressed: () =>
                postCreationController.onPublishPressed(context),
            onWillPop: () => postCreationController.onClosePressed(
                postCreationController.postToEdit,
                postCreationController.postsProvider,
                context)),
        body: Column(
          children: [
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              decoration: BoxDecoration(
                color: MalouColors.borderPrimary,
                borderRadius: BorderRadius.circular(30),
              ),
              child: TabBar(
                  controller: Provider.of<PostsProvider>(context)
                      .postCreationTabsController,
                  padding: EdgeInsets.zero,
                  labelPadding: EdgeInsets.zero,
                  indicatorPadding: EdgeInsets.zero,
                  indicatorSize: TabBarIndicatorSize.tab,
                  dividerColor: Colors.transparent,
                  indicatorWeight: 0,
                  overlayColor: WidgetStateProperty.all(Colors.transparent),
                  unselectedLabelColor: MalouColors.textBody,
                  labelStyle: MalouTextStyles(context).text12Semibold,
                  unselectedLabelStyle: MalouTextStyles(context).text12Medium,
                  isScrollable: false,
                  labelColor: Colors.white,
                  indicator: BoxDecoration(
                    gradient: MalouColors.gradientFive,
                    borderRadius: BorderRadius.circular(30),
                  ),
                  tabs: [
                    Tab(
                      text: widget.postType == PostType.REEL
                          ? Translation.of(context).video
                          : Translation.of(context).mediasTab,
                    ),
                    Tab(
                      text: Translation.of(context).descriptionTab,
                    ),
                    Tab(
                      text: Translation.of(context).feedbacks,
                    ),
                  ]),
            ),
            Expanded(
                child: TabBarView(
                    controller: Provider.of<PostsProvider>(context)
                        .postCreationTabsController,
                    physics: const NeverScrollableScrollPhysics(),
                    children: [
                  widget.postType == PostType.REEL
                      ? const CreateReelMediaTab()
                      : const CreatePostMediasTab(),
                  const PostDescriptionTab(),
                  const PostFeedbacksTab(),
                ]))
          ],
        ),
      ),
    );
  }
}
