import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/heap.service.dart';
import 'package:malou/shared/helpers/modal_bottom_sheet.dart';
import 'package:malou/shared/helpers/text_editing_controller_stream.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/models/user.model.dart';
import 'package:malou/shared/services/platform/platform.provider.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/posts/seo/seo-posts.provider.dart';
import 'package:malou/shared/services/posts/social-posts/social-posts.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:uuid/uuid.dart';

const VERYFRENCHBEANS_PAGE_ID = '100270695184218';

class PostCreationController {
  final PostsProvider postsProvider;
  final RestaurantProvider restaurantProvider;
  final UserProvider userProvider;
  final PlatformProvider platformProvider;

  final String? postId;
  final Source postSource;
  final PostType postType;

  PostCreationController(
      {required this.postsProvider,
      required this.restaurantProvider,
      required this.userProvider,
      required this.platformProvider,
      this.postId,
      required this.postSource,
      required this.postType});

  get postToEdit => postsProvider.postToEdit;

  void intializePostToEdit() {
    Future.delayed(Duration.zero, () {
      postsProvider.postToEdit = PostsProvider.defaultPost;
      postsProvider.captionsProposalButtons$.add([]);
      // just use to show the right toast message at the end
      postsProvider.fromEdit$ = BehaviorSubject<bool>.seeded(postId != null);

      if (postId != null) {
        initializePostEdition();
      } else {
        postsProvider.selectedPostMediaIndex = -1;
        switch (postSource) {
          case Source.seo:
            initializeSeoPostToCreate();
            break;
          default:
            initializeSocialPostToCreate();
        }
      }
    });
  }

  void initializeSocialPostToCreate() async {
    postsProvider.loadingPostToEdit$.add(true);
    List<Platform> platforms =
        platformProvider.connectedSocialNetworksPlatforms$.value;

    String? facebookSocialId = platforms
        .where((p) => p.key == PlatformKeys.facebook.name)
        .map((p) => p.socialId)
        .firstWhere((id) => true, orElse: () => null);

    String? facebookCity = platforms
        .firstWhere((p) => p.key == PlatformKeys.facebook.name)
        .address
        ?.locality;

    List<PostLocation> locations = await postsProvider.searchPageLocationByText(
        restaurantProvider.currentRestaurant!.name,
        true,
        [VERYFRENCHBEANS_PAGE_ID],
        facebookSocialId,
        facebookCity);

    Post postAboutToBeCreated = PostsProvider.defaultPost.copyWith(
        bindingId: const Uuid().v4(),
        author: Author(
            id: userProvider.currentUser!.id,
            name: userProvider.currentUser!.name),
        postType: postType,
        source: postSource,
        location: locations.isNotEmpty ? locations.first : null,
        attachments: [],
        keys: initPostPlatformKeys(platforms),
        restaurantId: restaurantProvider.currentRestaurant!.id,
        postTextController: TextEditingControllerStream(text: ''));
    postsProvider.postToEdit = postAboutToBeCreated;
    Post newPost = await postsProvider.createPost(postAboutToBeCreated, true);
    postsProvider.postToEdit = postAboutToBeCreated.copyWith(
      id: newPost.id,
    );
    postsProvider.initialPost = postsProvider.postToEdit;
    postsProvider.loadingPostToEdit$.add(false);
  }

  List<PlatformKeys>? initPostPlatformKeys(List<Platform> availablePlatforms) {
    if (postType == PostType.REEL) {
      return [PlatformKeys.instagram];
    }

    return availablePlatforms
        .map((e) {
          if (e.key == PlatformKeys.mapstr.name) {
            return e.credentials?.isNotEmpty == true
                ? parsePlatformKey(e.key)!
                : null;
          }
          return parsePlatformKey(e.key)!;
        })
        .where((element) => element != null)
        .cast<PlatformKeys>()
        .toList();
  }

  void initializeSeoPostToCreate() async {
    postsProvider.loadingPostToEdit$.add(true);
    Post postAboutToBeCreated = PostsProvider.defaultPost.copyWith(
      bindingId: const Uuid().v4(),
      author: Author(
          id: userProvider.currentUser!.id,
          name: userProvider.currentUser!.name),
      postType: postType,
      postTopic: PostTopic.STANDARD,
      source: postSource,
      keys: [PlatformKeys.gmb],
      attachments: [],
      restaurantId: restaurantProvider.currentRestaurant!.id,
      postTextController: TextEditingControllerStream(),
    );
    Post newPost = await postsProvider.createPost(postAboutToBeCreated, true);
    postsProvider.postToEdit =
        postsProvider.initializePostToEdit(postAboutToBeCreated.copyWith(
      id: newPost.id,
    ));
    postsProvider.initialPost = postsProvider.postToEdit;
    postsProvider.loadingPostToEdit$.add(false);
  }

  void initializePostEdition() {
    postsProvider.loadingPostToEdit$.add(true);
    postsProvider.getPostById(postId!).then((post) async {
      if (post.published == PublicationStatus.PUBLISHED &&
          post.attachments.isEmpty) {
        post.attachments = post.socialAttachments.map((e) {
          Media media =
              Media.fromSocialAttachment(e, post.restaurantId, post.author?.id);
          media.setAspectRatioFromUrl();
          return media;
        }).toList();
        await Future.delayed(const Duration(seconds: 1));
      }
      postsProvider.selectedPostMediaIndex =
          post.attachments.isNotEmpty ? 0 : -1;

      postsProvider.postToEdit = postsProvider.initializePostToEdit(
          post.copyWith(
              title: post.title ?? '',
              keys: post.source == Source.seo ? [PlatformKeys.gmb] : post.keys,
              publicationDatePlanned:
                  post.published == PublicationStatus.PENDING
                      ? PublicationDatePlanned.scheduled
                      : PublicationDatePlanned.draft,
              postTextController: TextEditingControllerStream(
                  text: post.getTextWithoutHashtags()),
              event: Event(
                title: post.event?.title ?? '',
                startDate: post.event?.startDate ?? DateTime.now(),
                endDate: post.event?.endDate ??
                    DateTime.now().add(const Duration(days: 1)),
              ),
              offer: Offer(
                couponCode: post.offer?.couponCode ?? '',
                onlineUrl: post.offer?.onlineUrl ?? '',
                termsConditions: post.offer?.termsConditions ?? '',
              )));

      postsProvider.initialPost = postsProvider.postToEdit;
      postsProvider.loadingPostToEdit$.add(false);
    });
  }

  Future onPublishPressed(BuildContext context) {
    PostsProvider postsProvider =
        Provider.of<PostsProvider>(context, listen: false);
    HeapService.instance.track(
        'save_post',
        {
          'postId': postsProvider.postToEdit.id,
          'postType': postsProvider.postToEdit.postType?.name,
          'postSource': postsProvider.postToEdit.source.name,
          'publicationStatus':
              postsProvider.postToEdit.publicationDatePlanned?.name
        },
        context: context);
    return savePost(postsProvider, context, postsProvider.postToEdit)
        .then((value) {
      Navigator.of(context).pop(true);
    }).catchError((e) async {
      Sentry.captureException(e);
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text(e.toString(),
              style: MalouTextStyles(context)
                  .text14Semibold
                  .copyWith(color: Colors.white)),
          backgroundColor: MalouColors.pinkAccent));
    });
  }

  Future<bool> onClosePressed(Post postToEdit, PostsProvider postsProvider,
      BuildContext context) async {
    PostsProvider postsProvider =
        Provider.of<PostsProvider>(context, listen: false);
    if (!postToEdit.canBeEdited() ||
        postToEdit.isEqualTo(postsProvider.initialPost)) {
      postsProvider.onPullToRefresh$.add(0);
      return true;
    }
    return showExitConfirmationModal(context, postToEdit, postsProvider);
  }

  Future<bool> showExitConfirmationModal(
      BuildContext context, Post postToEdit, PostsProvider postsProvider) {
    return AppBottomModal.showTinyModalBottomSheet<bool>(
        context,
        Container(
          padding: const EdgeInsets.only(bottom: 5),
          width: double.infinity,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(true);
                  },
                  style: TextButton.styleFrom(
                      foregroundColor: MalouColors.textBody),
                  child: Text(Translation.of(context).pursueEdit,
                      style: MalouTextStyles(context).text13Semibold.copyWith(
                            color: MalouColors.textBody,
                          ))),
              const Divider(
                color: MalouColors.borderPrimary,
                height: 1,
              ),
              TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(true);
                    Navigator.of(context).pop(true);
                    bool arePostPlatformsConnected =
                        Provider.of<PlatformProvider>(context, listen: false)
                            .connectedPostsPlatforms$
                            .value
                            .where((element) => postToEdit.keys
                                .contains(parsePlatformKey(element.key)!))
                            .isNotEmpty;
                    if (!postToEdit.canBePosted(
                        arePostPlatformsConnected: arePostPlatformsConnected)) {
                      postToEdit.publicationDatePlanned =
                          PublicationDatePlanned.draft;
                    }
                    savePost(postsProvider, context, postToEdit)
                        .then((value) {})
                        .catchError((e) async {
                      Sentry.captureException(e);
                      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                          content: Text(e.toString(),
                              style: MalouTextStyles(context)
                                  .text14Semibold
                                  .copyWith(color: Colors.white)),
                          backgroundColor: MalouColors.pinkAccent));
                    });
                  },
                  child: Text(Translation.of(context).savePost,
                      style: MalouTextStyles(context).text13Semibold.copyWith(
                            color: MalouColors.primary,
                          ))),
              const Divider(
                color: MalouColors.borderPrimary,
                height: 1,
              ),
              TextButton(
                  onPressed: () {
                    // leave this commented for now, we might need to delete the post
                    /*
                    if (postToEdit.source == Source.seo) {
                      context
                          .read<SeoPostsProvider>()
                          .removePostFromPagingController(postToEdit);
                    } else {
                      context
                          .read<SocialPostsProvider>()
                          .removePostFromPagingController(postToEdit);
                    }
                    postsProvider.postsService
                        .deletePost(postsProvider.postToEdit.id!);
                    */
                    Navigator.of(context).pop(true);
                    Navigator.of(context).pop(true);
                  },
                  style: TextButton.styleFrom(
                    foregroundColor: MalouColors.pinkAccent,
                  ),
                  child: Text(Translation.of(context).leaveAndLoseModifications,
                      style: MalouTextStyles(context).text13Semibold.copyWith(
                            color: MalouColors.pinkAccent,
                          ))),
            ],
          ),
        )).then((value) {
      return value ?? false;
    });
  }

  Future savePost(PostsProvider postsProvider, BuildContext context,
      Post postToEdit) async {
    switch (postToEdit.source) {
      case Source.seo:
        return context.read<SeoPostsProvider>().savePost(postsProvider);
      default:
        return context.read<SocialPostsProvider>().savePost(postsProvider);
    }
  }

  void initializeManagers() async {
    List<Manager> managers = await restaurantProvider
        .getManagers(restaurantProvider.currentRestaurant!.id);
    restaurantProvider.managers$.add(managers);
  }
}
