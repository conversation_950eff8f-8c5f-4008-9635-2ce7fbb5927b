import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/widgets/appbar/appbar_back.widget.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/platform/platform.provider.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/posts/seo/seo-posts.provider.dart';
import 'package:malou/shared/services/posts/social-posts/social-posts.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:provider/provider.dart';

class PostCreationAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  final Future<bool> Function() onWillPop;
  final Future Function() onPublishPressed;
  final Source postSource;
  final PostType postType;
  const PostCreationAppBar(
      {super.key,
      required this.onWillPop,
      required this.onPublishPressed,
      required this.postSource,
      required this.postType});

  @override
  Widget build(BuildContext context) {
    return Consumer3<PostsProvider, SeoPostsProvider, SocialPostsProvider>(
        builder: (context, postsProvider, seoPostsProvider, socialPostsProvider,
            child) {
      Post postToEdit = postsProvider.postToEdit;
      bool canPublish = postsProvider.postToEdit.isPostValid(
          arePostPlatformsConnected: Provider.of<PlatformProvider>(context)
              .connectedPostsPlatforms$
              .value
              .where((element) =>
                  postToEdit.keys.contains(parsePlatformKey(element.key)!))
              .where((element) => element.isValid == true)
              .isNotEmpty);
      return AppBarArrowBack(
        onPop: onWillPop,
        icon: const Icon(MalouIcons.cross, color: MalouColors.primary),
        title: postSource == Source.seo
            ? Translation.of(context).addSeoPost
            : postType == PostType.REEL
                ? Translation.of(context).create_reel
                : Translation.of(context).addSocialPost,
        actions: [
          TextButton(
              onPressed: canPublish ? onPublishPressed : null,
              child:
                  Text(postsProvider.postToEdit.getPublishButtonText(context),
                      style: MalouTextStyles(context).text14Semibold.copyWith(
                            color: canPublish
                                ? MalouColors.primary
                                : MalouColors.primary.withOpacity(0.5),
                          )))
        ],
      );
    });
  }

  @override
  Size get preferredSize => const Size.fromHeight(50);
}
