import 'package:flutter/material.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:video_player/video_player.dart';

class ReelDeviceVideoPreviewController {
  final PostsProvider postsProvider;
  VideoPlayerController? videoPlayerController;

  Media media;

  ReelDeviceVideoPreviewController(
      {required this.media, required this.postsProvider});

  BehaviorSubject<bool> loadingVideo$ = BehaviorSubject<bool>.seeded(true);

  GlobalKey videoBoxKey = GlobalKey();

  void loadVideo() {
    media.assetEntity!.file.then((value) {
      videoPlayerController = VideoPlayerController.file(value!);
      videoPlayerController!.initialize().then((_) {
        videoPlayerController!.play();
        loadingVideo$.add(false);
      });
    });
  }

  void disposePlayer() {
    videoPlayerController?.pause();
    videoPlayerController?.dispose();
  }
}
