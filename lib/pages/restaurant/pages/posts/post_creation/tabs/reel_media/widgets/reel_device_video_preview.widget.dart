import 'package:flutter/material.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/reel_media/widgets/reel_device_video_preview.controller.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';
import 'package:provider/provider.dart';
import 'package:video_player/video_player.dart';

class ReelDeviceVideoPreview extends StatefulWidget {
  final Media media;
  const ReelDeviceVideoPreview({super.key, required this.media});

  @override
  State<ReelDeviceVideoPreview> createState() => _ReelDeviceVideoPreviewState();
}

class _ReelDeviceVideoPreviewState extends State<ReelDeviceVideoPreview> {
  late ReelDeviceVideoPreviewController pageController;

  @override
  void initState() {
    PostsProvider postsProvider =
        Provider.of<PostsProvider>(context, listen: false);
    pageController = ReelDeviceVideoPreviewController(
        media: widget.media, postsProvider: postsProvider);
    pageController.loadVideo();
    postsProvider.postCreationTabsController.addListener(() {
      if (postsProvider.postCreationTabsController.indexIsChanging) {
        pageController.videoPlayerController?.pause();
      }
    });
    super.initState();
  }

  @override
  dispose() {
    pageController.disposePlayer();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return Center(
      child: StreamBuilder<Object>(
          stream: pageController.loadingVideo$,
          builder: (context, snapshot) {
            if (snapshot.hasData && snapshot.data == false) {
              return Stack(
                children: [
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        pageController.videoPlayerController!.value.isPlaying
                            ? pageController.videoPlayerController!.pause()
                            : pageController.videoPlayerController!.play();
                      });
                    },
                    child: Center(
                      child: AspectRatio(
                        key: pageController.videoBoxKey,
                        aspectRatio: getAspectRatio(pageController
                            .videoPlayerController!.value.aspectRatio),
                        child: SizedBox(
                            height: pageController
                                .videoPlayerController!.value.size.height,
                            width: size.width,
                            child: VideoPlayer(
                                pageController.videoPlayerController!)),
                      ),
                    ),
                  ),
                ],
              );
            }
            return const CircularSpinner(color: MalouColors.primary, size: 30);
          }),
    );
  }

  double getAspectRatio(double initialAspectRatio) {
    if (initialAspectRatio == 1.0) return 9 / 16;

    return initialAspectRatio > 1.0 ? 16 / 9 : 9 / 16;
  }
}
