import 'package:flutter/material.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:provider/provider.dart';

import 'reel_device_video_preview.widget.dart';
import 'reel_video_preview.widget.dart';

class ReelPreview extends StatefulWidget {
  const ReelPreview({super.key});

  @override
  State<ReelPreview> createState() => _ReelPreviewState();
}

class _ReelPreviewState extends State<ReelPreview> {
  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return Consumer<PostsProvider>(builder: (context, postsProvider, child) {
      Post currentPost = postsProvider.postToEdit;
      if (currentPost.attachments.isNotEmpty) {
        Media currentReel = currentPost.attachments.first;
        bool isFromDevice = currentReel.assetEntity != null;
        return Container(
          width: size.width,
          decoration: const BoxDecoration(color: MalouColors.backgroundDark),
          child: isFromDevice
              ? ReelDeviceVideoPreview(
                  media: currentReel,
                )
              : ReelVideoPreview(
                  media: currentReel,
                ),
        );
      }
      return Container(
        width: size.width,
        decoration: const BoxDecoration(color: MalouColors.backgroundDark),
        child: Column(
          children: [
            SizedBox(
              height: size.height * 0.15,
            ),
            Image.asset(
              IllustrationsPathResolver.resolve(Illustrations.videoPlayer),
              fit: BoxFit.cover,
            ),
          ],
        ),
      );
    });
  }
}
