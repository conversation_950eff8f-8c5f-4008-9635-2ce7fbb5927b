import 'package:flutter/material.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';
import 'package:provider/provider.dart';
import 'package:video_player/video_player.dart';

class ReelVideoPreview extends StatefulWidget {
  final Media media;
  const ReelVideoPreview({super.key, required this.media});

  @override
  State<ReelVideoPreview> createState() => _ReelVideoPreviewState();
}

class _ReelVideoPreviewState extends State<ReelVideoPreview> {
  late PostsProvider postsProvider;
  late VideoPlayerController _controller;
  bool loadingVideo = true;
  GlobalKey videoBoxKey = GlobalKey();
  @override
  void initState() {
    postsProvider = Provider.of<PostsProvider>(context, listen: false);

    _controller =
        VideoPlayerController.networkUrl(Uri.parse(widget.media.getMediaUrl()))
          ..initialize().then((_) {
            setState(() {
              loadingVideo = false;
            });
            setVideoMetadata();
          });
    postsProvider.postCreationTabsController.addListener(() {
      if (postsProvider.postCreationTabsController.indexIsChanging) {
        _controller.pause();
      }
    });
    super.initState();
  }

  void setVideoMetadata() {
    Future.delayed(const Duration(milliseconds: 500), () {
      Size size = MediaQuery.of(context).size;
      Media currentMedia = widget.media.copyWith(
          dimensions: {
            'original': Dimension(
                height: _controller.value.size.height.toInt(),
                width: _controller.value.size.width.toInt())
          },
          resizeMetadata: widget.media.resizeMetadata?.copyWith(
            aspectRatio: getAspectRatio(_controller.value.aspectRatio),
            displayBoxSize: Size(
                size.width,
                videoBoxKey.currentContext?.size?.height.toDouble() ??
                    size.height - 150),
          ));
      postsProvider.postToEdit =
          postsProvider.postToEdit.copyWith(attachments: [currentMedia]);
    });
  }

  @override
  dispose() {
    _controller.pause();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return loadingVideo
        ? const Center(
            child: CircularSpinner(
            color: MalouColors.primary,
            size: 30,
          ))
        : GestureDetector(
            onTap: () {
              setState(() {
                _controller.value.isPlaying
                    ? _controller.pause()
                    : _controller.play();
              });
            },
            child: Center(
              child: AspectRatio(
                key: videoBoxKey,
                aspectRatio: getAspectRatio(_controller.value.aspectRatio),
                child: Stack(
                  children: [
                    SizedBox(
                        height: _controller.value.size.height,
                        width: size.width,
                        child: VideoPlayer(_controller)),
                    if (!_controller.value.isPlaying)
                      Center(
                        child: Container(
                          width: 80,
                          decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.4),
                              shape: BoxShape.circle),
                          child: Center(
                            child: IconButton(
                              padding: EdgeInsets.zero,
                              icon: const Icon(
                                MalouIcons.play,
                                color: Colors.white,
                                size: 50,
                              ),
                              onPressed: () {
                                setState(() {
                                  _controller.play();
                                });
                              },
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          );
  }

  double getAspectRatio(double initialAspectRatio) {
    if (initialAspectRatio == 1.0) return 9 / 16;

    return initialAspectRatio > 1.0 ? 16 / 9 : 9 / 16;
  }
}
