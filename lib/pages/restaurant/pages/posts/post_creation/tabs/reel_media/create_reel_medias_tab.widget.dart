import 'package:flutter/material.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/reel_media/widgets/reel_preview.widget.dart';
import 'package:malou/shared/models/posts/post.model.dart';

import '../post_medias/widgets/post_media_picker/post_media_picker.widget.dart';

class CreateReelMediaTab extends StatefulWidget {
  const CreateReelMediaTab({super.key});

  @override
  State<CreateReelMediaTab> createState() => _CreateReelMediaTabState();
}

class _CreateReelMediaTabState extends State<CreateReelMediaTab> {
  @override
  Widget build(BuildContext context) {
    return const Stack(
      children: [
        ReelPreview(),
        Align(
          alignment: Alignment.bottomCenter,
          child: PostMediaPicker(
            maxAttachmentsBeforeResize: 0,
            minSize: 0.1,
            mediaPickerType: MediaPickerType.video,
          ),
        )
      ],
    );
  }
}
