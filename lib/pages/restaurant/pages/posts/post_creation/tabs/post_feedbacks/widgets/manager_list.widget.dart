import 'package:flutter/material.dart';
import 'package:malou/shared/widgets/avatar/avatar.widget.dart';
import 'package:malou/shared/models/user.model.dart';
import 'package:malou/shared/services/feedbacks/feedbacks.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:rxdart/rxdart.dart';

class ManagerList extends StatelessWidget {
  final TextEditingController textEditingController;
  final BehaviorSubject<bool> isManagersOpen$;
  final BehaviorSubject<List<Manager>> managers$;
  final FeedbacksProvider feedbacksProvider;
  final Function(Manager) onTapManager;
  const ManagerList(
      {super.key,
      required this.isManagersOpen$,
      required this.textEditingController,
      required this.managers$,
      required this.feedbacksProvider,
      required this.onTapManager});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
        stream: isManagersOpen$,
        builder: (context, snapshot) {
          if (!snapshot.hasData || !snapshot.data!) {
            return const SizedBox();
          }
          return StreamBuilder<List<Manager>?>(
              stream: managers$.map((managers) {
            String currentText = textEditingController.text.toLowerCase();
            int selection = textEditingController.selection.baseOffset;
            String textFromStartToCursor = currentText.substring(0, selection);

            int lastAt =
                textFromStartToCursor.lastIndexOf(feedbacksProvider.AT);

            String search = textFromStartToCursor.substring(lastAt + 1);
            if (search.isEmpty) return managers;
            return managers.where((manager) {
              String fullname =
                  "${manager.name} ${manager.lastname}".toLowerCase();
              return fullname.contains(search);
            }).toList();
          }), builder: (context, snapshot) {
            if (!snapshot.hasData || snapshot.data!.isEmpty) {
              return const SizedBox();
            }
            return Positioned(
              bottom: 150,
              left: 0,
              right: 0,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(
                        maxHeight: 150, minWidth: double.infinity),
                    child: DecoratedBox(
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                              color: MalouColors.backgroundDark, width: 1)),
                      child: ListView.separated(
                          shrinkWrap: true,
                          itemCount: snapshot.data!.length,
                          itemBuilder: (context, index) {
                            String fallbackUrl =
                                'https://api.dicebear.com/7.x/initials/svg?seed=${snapshot.data![index].name}.png';
                            return Material(
                              borderOnForeground: false,
                              color: Colors.transparent,
                              child: Ink(
                                child: InkWell(
                                  onTap: () {
                                    onTapManager(snapshot.data![index]);
                                  },
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 10, vertical: 5),
                                    child: Row(
                                      children: [
                                        Avatar(
                                          url: snapshot.data![index]
                                                  .profilePicture ??
                                              fallbackUrl,
                                          factorSize: 0.7,
                                        ),
                                        const SizedBox(
                                          width: 10,
                                        ),
                                        Column(
                                          children: [
                                            Text(
                                                "${snapshot.data![index].name} ${snapshot.data![index].lastname}",
                                                style: MalouTextStyles(context)
                                                    .text12Bold
                                                    .copyWith(
                                                        color: MalouColors
                                                            .textTitle))
                                          ],
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                          separatorBuilder: (context, index) => const Divider(
                                color: MalouColors.borderPrimary,
                                height: 1,
                              )),
                    ),
                  ),
                ),
              ),
            );
          });
        });
  }
}
