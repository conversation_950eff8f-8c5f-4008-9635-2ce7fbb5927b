import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_feedbacks/models/feedback_message_item.model.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_feedbacks/widgets/feedback_message/feedback_message.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_feedbacks/widgets/feedback_message_form.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_feedbacks/widgets/manager_list.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_feedbacks/widgets/no_feedback_messages.widget.dart';
import 'package:malou/shared/helpers/clarify_error.dart';
import 'package:malou/shared/helpers/use_query.dart';
import 'package:malou/shared/models/feedback.model.dart';
import 'package:malou/shared/models/user.model.dart';
import 'package:malou/shared/services/feedbacks/feedbacks.provider.dart';
import 'package:malou/shared/services/feedbacks/feedbacks.service.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/widgets/inputs/styellable_text_field.widget.dart';
import 'package:malou/shared/widgets/toasts/toast.widget.dart';
import 'package:malou/shared/widgets/toasts/toasts.service.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:uuid/uuid.dart';

class PostFeedbacksTab extends StatefulWidget {
  const PostFeedbacksTab({super.key});
  @override
  State<PostFeedbacksTab> createState() => _PostFeedbacksTabState();
}

class _PostFeedbacksTabState extends State<PostFeedbacksTab> {
  late FeedbacksProvider feedbacksProvider;
  late PostsProvider postsProvider;
  late RestaurantProvider restaurantProvider;
  late UserProvider userProvider;

  late TextEditingController _textEditingController;

  final keyboardVisibilityController = KeyboardVisibilityController();
  late FocusNode focusOnReplyTextController;
  ScrollController textFieldScrollController = ScrollController();
  GlobalKey texfieldKey = GlobalKey();

  final PagingController<int, FeedbackMessageItem> _feedbacksPagingController =
      PagingController(firstPageKey: 0);

  late QueryResult<FeedbackMessage?, CreateFeedbackMessageParams>
      createMessageQuery;
  late QueryResult<FeedbackMessage?, UpdateFeedbackMessageParams>
      updateMessageQuery;
  late QueryResult<String?, DeleteFeedbackMessageParams> deleteMessageQuery;
  late QueryResult<List<Manager>, String> getManagersQuery;
  late QueryResult<FeedbackMessage?, MarkFeedbackConversationParams>
      markFeedbackConversationQuery;

  String managersPatternIn = '';
  String managersPatternOut = '';

  bool visibleForAdmins = false;

  ScrollController scrollController = ScrollController();

  BehaviorSubject<FeedbackMessage?> feedbackMessageToEdit$ =
      BehaviorSubject.seeded(null);

  BehaviorSubject<FeedbackMessageType?> currentfeedbackStatus$ =
      BehaviorSubject.seeded(null);

  BehaviorSubject<bool> isManagersOpen$ = BehaviorSubject.seeded(false);

  @override
  void initState() {
    feedbacksProvider = Provider.of<FeedbacksProvider>(context, listen: false);
    restaurantProvider =
        Provider.of<RestaurantProvider>(context, listen: false);
    postsProvider = Provider.of<PostsProvider>(context, listen: false);
    userProvider = Provider.of<UserProvider>(context, listen: false);

    focusOnReplyTextController = FocusNode();

    if (restaurantProvider.managers$.value.isNotEmpty) {
      managersPatternIn =
          '(\\B${feedbacksProvider.USER_TAGGED}${restaurantProvider.managers$.value.map((manager) => manager.name).join('|\\B${feedbacksProvider.USER_TAGGED}')})';
      managersPatternOut = '(\\B${feedbacksProvider.AT}${[
        ...restaurantProvider.managers$.value
            .map((manager) => "${manager.name} ${manager.lastname}"),
        // for the desktop case
        ...restaurantProvider.managers$.value.map((manager) => manager.name)
      ].join('|\\B${feedbacksProvider.AT}')})';
    }

    _textEditingController = StyleableTextFieldController(
      styles: TextPartStyleDefinitions(
        definitionList: <TextPartStyleDefinition>[
          TextPartStyleDefinition(
            style: const TextStyle(
              color: MalouColors.primary,
              fontWeight: FontWeight.bold,
              backgroundColor: MalouColors.backgroundDark,
            ),
            pattern: managersPatternOut,
          ),
        ],
      ),
    );

    _feedbacksPagingController.addPageRequestListener((pageKey) {
      _fetchPage(pageKey);
    });

    createMessageQuery =
        useQuery(feedbacksProvider.createFeedbackMessage, onProcess: (params) {
      return pushFeedbackMessage(
          author: params.author,
          createdAt: params.createdAt,
          text: params.text,
          visibility: params.visibility,
          type: params.type);
    }, onSuccess: (newData, params, onProcessResult) {
      updateFeedbackMessageOnsuccess(newData, onProcessResult!);
    }, onError: (error, params, onProcessResult) {
      deleteFeedbackMessageFromListById(onProcessResult!);
    });

    markFeedbackConversationQuery = useQuery(
        feedbacksProvider.markFeedbackConversation, onProcess: (params) {
      if (params.type == FeedbackMessageType.close) {
        return pushFeedbackMessage(
            author: params.author,
            createdAt: params.createdAt,
            text: null,
            visibility: null,
            type: params.type);
      } else {
        deleteFeedbackMessageFromListById(params.lastFeedbackMessageId);
      }
    }, onSuccess: (newData, params, onProcessResult) {
      if (newData != null) {
        updateFeedbackMessageOnsuccess(newData, onProcessResult!);
      }
    });

    deleteMessageQuery =
        useQuery(feedbacksProvider.deleteFeedbackMessage, onProcess: (params) {
      deleteFeedbackMessageFromListById(params.messageId);
    }, onError: (err, params, onProcessResult) {
      ToastsService.instance.add(Toast(
          type: ToastType.error,
          message: Translation.of(context).deleteFeedbackMessageError));
    });

    updateMessageQuery =
        useQuery(feedbacksProvider.updateFeedbackMessage, onProcess: (params) {
      onProcessUpdateFeedbackMessage(params);
    }, onError: (err, params, onProcessResult) {
      ToastsService.instance.add(Toast(
          type: ToastType.error,
          message: Translation.of(context).updateFeedbackMessageError));
    });

    // skip null value init
    feedbackMessageToEdit$.skip(1).distinct().listen((value) {
      onEmitFeedbackMessageToEdit(value);
    });

    // skip initial value and first emitted value (just above inside _fetchPage)
    // that represent the current status of the feedback
    currentfeedbackStatus$.skip(2).listen((toggledStatus) {
      User me = userProvider.currentUser!;
      markFeedbackConversationQuery.execute(MarkFeedbackConversationParams(
          feedbackId: postsProvider.postToEdit.feedback!.id,
          restaurantId: restaurantProvider.currentRestaurant!.id,
          lastFeedbackMessageId: _feedbacksPagingController
              .itemList!.reversed.last.feedbackMessage.id,
          author: Author(
              email: me.email,
              id: me.id,
              name: me.name,
              profilePictureUrl: me.profilePicture?.getUrls() ?? "",
              fullname: me.fullName,
              lastname: me.lastname,
              role: me.role),
          createdAt: DateTime.now(),
          type: toggledStatus!,
          managersPattern: managersPatternOut,
          managers: restaurantProvider.managers$.value));
    });

    _textEditingController.addListener(() {
      onTextFieldChange();
    });

    _feedbacksPagingController.addListener(() {
      if (_feedbacksPagingController.itemList != null &&
          _feedbacksPagingController.itemList!.isNotEmpty) {
        postsProvider.postToEdit = postsProvider.postToEdit.copyWith(
            feedback: postsProvider.postToEdit.feedback!.copyWith(
                feedbackMessages: _feedbacksPagingController.itemList!
                    .map((e) => e.feedbackMessage)
                    .toList(),
                isOpen:
                    currentfeedbackStatus$.value != FeedbackMessageType.close));
      }
    });

    super.initState();
  }

  void onEmitFeedbackMessageToEdit(FeedbackMessage? value) {
    if (_feedbacksPagingController.itemList == null) return;

    if (value != null) {
      _setAboutToBeEditedMessage(value);
      setState(() {
        visibleForAdmins = value.visibility == FeedbackVisibility.admin;
      });
      _textEditingController.text = value.text!;
      focusOnReplyTextController.requestFocus();
    } else {
      _setAboutToBeEditedMessage(null);
      _textEditingController.clear();
      focusOnReplyTextController.unfocus();
    }
  }

  void onProcessUpdateFeedbackMessage(UpdateFeedbackMessageParams params) {
    _feedbacksPagingController.itemList =
        _feedbacksPagingController.itemList!.map((element) {
      if (element.feedbackMessage.id == params.messageId) {
        return FeedbackMessageItem(
            isAboutToBeEdited: false,
            isSending: true,
            feedbackMessage: element.feedbackMessage
                .copyWith(text: params.text, visibility: params.visibility));
      }
      return element;
    }).toList();
  }

  void onTextFieldChange() {
    // to trigger the filter autocomplete
    restaurantProvider.managers$.add(restaurantProvider.managers$.value);

    String currentText = _textEditingController.text;

    if (currentText.isEmpty) {
      isManagersOpen$.add(false);
      return;
    }

    int selection = _textEditingController.selection.baseOffset;
    if (selection == -1) return;
    String textFromStartToCursor = currentText.substring(0, selection);

    if (textFromStartToCursor.characters.last == feedbacksProvider.AT) {
      if (textFromStartToCursor.length == 1) {
        isManagersOpen$.add(true);
        return;
      }

      String beforeLast =
          textFromStartToCursor.substring(0, textFromStartToCursor.length - 1);
      // check if whitespace before "@"
      if (beforeLast.characters.last == ' ') {
        isManagersOpen$.add(true);
        return;
      }
      if (beforeLast.characters.last == '\n') {
        isManagersOpen$.add(true);
        return;
      }
      isManagersOpen$.add(false);
    }
  }

  String pushFeedbackMessage(
      {required Author author,
      required DateTime createdAt,
      required String? text,
      required FeedbackVisibility? visibility,
      required FeedbackMessageType type}) {
    String randomId = const Uuid().toString();

    FeedbackMessageItem newItem = FeedbackMessageItem(
        feedbackMessage: FeedbackMessage(
            author: author,
            createdAt: createdAt,
            id: randomId,
            text: text,
            visibility: visibility,
            type: type),
        isAboutToBeEdited: false,
        isSending: true);

    _feedbacksPagingController.itemList = [
      newItem,
      ..._feedbacksPagingController.itemList!,
    ];
    scrollController.animateTo(
      0.0,
      curve: Curves.easeOut,
      duration: const Duration(milliseconds: 300),
    );

    return randomId;
  }

  void updateFeedbackMessageOnsuccess(
      FeedbackMessage? newData, String randomId) {
    var idx = _feedbacksPagingController.itemList!
        .indexWhere((element) => element.feedbackMessage.id == randomId);

    if (idx != -1) {
      _feedbacksPagingController.itemList![idx] = FeedbackMessageItem(
          feedbackMessage: newData!.copyWith(
              text: feedbacksProvider.mapTextBeforeDisplay(
                  newData.text, managersPatternIn)),
          isAboutToBeEdited: false);
      _feedbacksPagingController.itemList =
          _feedbacksPagingController.itemList!.map((e) => e).toList();
    }
  }

  void deleteFeedbackMessageFromListById(String messageId) {
    _feedbacksPagingController.itemList = _feedbacksPagingController.itemList!
        .where((element) => element.feedbackMessage.id != messageId)
        .toList();
  }

  void sendMessage(String text, bool visibleForAdmins) {
    if (text.isEmpty) return;

    User me = userProvider.currentUser!;

    createMessageQuery.execute(CreateFeedbackMessageParams(
        feedbackId: postsProvider.postToEdit.feedback!.id,
        restaurantId: restaurantProvider.currentRestaurant!.id,
        author: Author(
            email: me.email,
            id: me.id,
            name: me.name,
            profilePictureUrl: me.profilePicture?.getUrls() ?? "",
            fullname: me.fullName,
            lastname: me.lastname,
            role: me.role),
        visibility: visibleForAdmins
            ? FeedbackVisibility.admin
            : FeedbackVisibility.basic,
        text: text,
        createdAt: DateTime.now(),
        type: FeedbackMessageType.text,
        managersPattern: managersPatternOut,
        managers: restaurantProvider.managers$.value));
  }

  void deleteMessage(String messageId) {
    deleteMessageQuery.execute(DeleteFeedbackMessageParams(
        feedbackId: postsProvider.postToEdit.feedback!.id,
        restaurantId: restaurantProvider.currentRestaurant!.id,
        messageId: messageId));
  }

  void onClickEditButton(FeedbackMessage message) {
    feedbackMessageToEdit$.add(message);
  }

  void _setAboutToBeEditedMessage(FeedbackMessage? message) {
    if (message != null) {
      _feedbacksPagingController.itemList =
          _feedbacksPagingController.itemList!.map((e) {
        if (e.feedbackMessage.id == message.id) {
          return FeedbackMessageItem(
              feedbackMessage: e.feedbackMessage, isAboutToBeEdited: true);
        }
        return FeedbackMessageItem(
            feedbackMessage: e.feedbackMessage, isAboutToBeEdited: false);
      }).toList();
    } else {
      _feedbacksPagingController.itemList =
          _feedbacksPagingController.itemList!.map((e) {
        return FeedbackMessageItem(
            feedbackMessage: e.feedbackMessage, isAboutToBeEdited: false);
      }).toList();
    }
  }

  void updateMessage(String newText, bool visibleForAdmins) {
    if (_textEditingController.text.isEmpty) return;

    if (feedbackMessageToEdit$.value == null) return;

    updateMessageQuery.execute(UpdateFeedbackMessageParams(
      text: newText,
      visibility: visibleForAdmins
          ? FeedbackVisibility.admin
          : FeedbackVisibility.basic,
      feedbackId: postsProvider.postToEdit.feedback!.id,
      restaurantId: restaurantProvider.currentRestaurant!.id,
      messageId: feedbackMessageToEdit$.value!.id,
      managersPattern: managersPatternOut,
    ));
  }

  Future<void> _fetchPage(int pageKey) async {
    // no pagination yet in backend
    if (_feedbacksPagingController.itemList != null &&
        _feedbacksPagingController.itemList!.isNotEmpty) {
      return;
    }

    try {
      if (postsProvider.postToEdit.feedbackId == null) {
        var feedback = await feedbacksProvider
            .createFeedback(postsProvider.postToEdit.id!);
        postsProvider.postToEdit = postsProvider.postToEdit
            .copyWith(feedbackId: feedback.id, feedback: feedback);
      }

      final feedback = await feedbacksProvider.getFeedbackById(
          postsProvider.postToEdit.feedback!.id,
          restaurantProvider.currentRestaurant!.id);

      if (feedback == null || feedback.feedbackMessages.isEmpty) {
        // just to say that is not closed
        currentfeedbackStatus$.add(FeedbackMessageType.text);

        _feedbacksPagingController.appendLastPage([]);
        return;
      }

      FeedbackMessageType status = feedback.isOpen
          ? FeedbackMessageType.text
          : FeedbackMessageType.close;
      currentfeedbackStatus$.add(status);

      _feedbacksPagingController.appendLastPage(feedback
          .feedbackMessages.reversed
          .map((feedbackMessage) => FeedbackMessageItem(
              feedbackMessage: feedbackMessage.copyWith(
                  text: feedbacksProvider.mapTextBeforeDisplay(
                      feedbackMessage.text, managersPatternIn)),
              isAboutToBeEdited: false))
          .toList());
    } catch (error) {
      if (error is DioException) {
        _feedbacksPagingController.error =
            error.response?.data['message'] ?? "";
      } else {
        _feedbacksPagingController.error = error.toString();
      }
    }
  }

  void onTapManager(Manager manager) {
    String currentText = _textEditingController.text;

    int selection = _textEditingController.selection.baseOffset;
    String textFromStartToCursor = currentText.substring(0, selection);

    int lastAt = textFromStartToCursor.lastIndexOf(feedbacksProvider.AT);

    String beforeAt = currentText.substring(0, lastAt);
    String afterSelection = currentText.substring(selection);

    String textToInsert =
        "${feedbacksProvider.AT}${manager.name} ${manager.lastname}";

    String newText = "$beforeAt$textToInsert $afterSelection";

    _textEditingController.text = newText;

    // set cursor at the end of the inserted text
    _textEditingController.selection = TextSelection.fromPosition(
        TextPosition(offset: lastAt + textToInsert.length + 1));
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Stack(
        children: [
          Column(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    feedbackMessageToEdit$.add(null);
                  },
                  child: Padding(
                    key: texfieldKey,
                    padding: const EdgeInsets.symmetric(horizontal: 18),
                    child: PagedListView.separated(
                        reverse: true,
                        pagingController: _feedbacksPagingController,
                        scrollController: scrollController,
                        builderDelegate:
                            PagedChildBuilderDelegate<FeedbackMessageItem>(
                          itemBuilder: (context, item, index) =>
                              FeedbackMessageWidget(
                            feedbackId: postsProvider.postToEdit.feedback!.id,
                            feedbackMessage: item.feedbackMessage,
                            deleteMessage: deleteMessage,
                            onClickEditButton: onClickEditButton,
                            isAboutToBeEdited: item.isAboutToBeEdited,
                            me: userProvider.currentUser!,
                            isSending: item.isSending,
                            textPartStyleDefinition: TextPartStyleDefinition(
                                pattern: managersPatternOut,
                                style: const TextStyle(
                                  color: MalouColors.primary,
                                  fontWeight: FontWeight.bold,
                                  backgroundColor: MalouColors.backgroundDark,
                                )),
                          ),
                          noItemsFoundIndicatorBuilder: (context) =>
                              const NoFeedbackMessages(),
                          transitionDuration: const Duration(milliseconds: 500),
                          firstPageErrorIndicatorBuilder: (context) =>
                              ClarifyErrorWidget(
                                  error: _feedbacksPagingController.error),
                        ),
                        separatorBuilder: (context, index) =>
                            const SizedBox(height: 10)),
                  ),
                ),
              ),
              FeedbackMessageForm(
                  textFieldScrollController: textFieldScrollController,
                  focusOnReplyTextController: focusOnReplyTextController,
                  textEditingController: _textEditingController,
                  onSubmit: (text) {
                    sendMessage(text, visibleForAdmins);
                  },
                  currentfeedbackStatus$: currentfeedbackStatus$,
                  setVisibleForAdmins: (bool value) {
                    setState(() {
                      visibleForAdmins = value;
                    });
                  },
                  userProvider: userProvider,
                  visibleForAdmins: visibleForAdmins,
                  feedbackMessageToEdit$: feedbackMessageToEdit$,
                  onSubmitUpdate: (newText) {
                    updateMessage(newText, visibleForAdmins);
                  })
            ],
          ),
          ManagerList(
              isManagersOpen$: isManagersOpen$,
              textEditingController: _textEditingController,
              managers$: restaurantProvider.managers$,
              feedbacksProvider: feedbacksProvider,
              onTapManager: onTapManager)
        ],
      ),
    );
  }
}
