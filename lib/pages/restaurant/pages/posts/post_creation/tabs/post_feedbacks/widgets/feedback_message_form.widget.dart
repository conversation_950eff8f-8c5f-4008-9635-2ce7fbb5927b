import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/models/feedback.model.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/buttons/styles.dart';
import 'package:rxdart/rxdart.dart';

class FeedbackMessageForm extends StatefulWidget {
  const FeedbackMessageForm(
      {super.key,
      required this.textFieldScrollController,
      required this.focusOnReplyTextController,
      required TextEditingController textEditingController,
      required this.onSubmit,
      required this.feedbackMessageToEdit$,
      required this.onSubmitUpdate,
      required this.currentfeedbackStatus$,
      required this.userProvider,
      required this.visibleForAdmins,
      required this.setVisibleForAdmins})
      : _textEditingController = textEditingController;

  final ScrollController textFieldScrollController;
  final FocusNode focusOnReplyTextController;
  final TextEditingController _textEditingController;
  final Function(String) onSubmit;
  final BehaviorSubject<FeedbackMessage?> feedbackMessageToEdit$;
  final Function(String) onSubmitUpdate;
  final Function(bool) setVisibleForAdmins;
  final BehaviorSubject<FeedbackMessageType?> currentfeedbackStatus$;
  final UserProvider userProvider;
  final bool visibleForAdmins;

  @override
  State<FeedbackMessageForm> createState() => _FeedbackMessageFormState();
}

class _FeedbackMessageFormState extends State<FeedbackMessageForm> {
  String messageContent = '';

  @override
  initState() {
    super.initState();
    widget._textEditingController.addListener(() {
      setState(() {
        messageContent = widget._textEditingController.text;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<FeedbackMessageType?>(
        stream: widget.currentfeedbackStatus$,
        builder: (context, snapshot) {
          if (!snapshot.hasData) {
            return const SizedBox();
          }
          return Column(
            children: [
              if (snapshot.data != FeedbackMessageType.close) ...[
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Stack(
                    children: [
                      Container(
                          margin: const EdgeInsets.only(bottom: 20),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              const SizedBox(
                                width: 15,
                              ),
                              Expanded(
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12),
                                  decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(10),
                                      border: Border.all(
                                          color: MalouColors.backgroundDark,
                                          width: 1)),
                                  child: RawKeyboardListener(
                                    focusNode: FocusNode(),
                                    child: TextFormField(
                                      scrollController:
                                          widget.textFieldScrollController,
                                      focusNode:
                                          widget.focusOnReplyTextController,
                                      controller: widget._textEditingController,
                                      textCapitalization:
                                          TextCapitalization.sentences,
                                      keyboardType: TextInputType.multiline,
                                      minLines: 1,
                                      maxLines: 7,
                                      style: MalouTextStyles(context)
                                          .text13
                                          .copyWith(
                                              color: MalouColors.textTitle),
                                      decoration: InputDecoration(
                                          border: InputBorder.none,
                                          hintStyle: MalouTextStyles(context)
                                              .text13Italic,
                                          hintText: Translation.of(context)
                                              .writeYourFeedback),
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(
                                width: 10,
                              ),
                              StreamBuilder<FeedbackMessage?>(
                                  stream: widget.feedbackMessageToEdit$,
                                  builder: (context, snapshot) {
                                    if (!snapshot.hasData) {
                                      return PrimaryIconButtonContainer(
                                          disabled: messageContent.isEmpty,
                                          height: 50,
                                          width: 50,
                                          child: IconButton(
                                            iconSize: 20,
                                            disabledColor: Colors.white,
                                            onPressed: messageContent.isEmpty
                                                ? null
                                                : () {
                                                    widget.onSubmit(
                                                        messageContent);
                                                    widget
                                                        ._textEditingController
                                                        .clear();
                                                    widget
                                                        .focusOnReplyTextController
                                                        .unfocus();
                                                  },
                                            icon: const Icon(
                                              MalouIcons.send,
                                            ),
                                          ));
                                    } else {
                                      return Row(
                                        children: [
                                          SecondaryButtonContainer(
                                              isCircular: true,
                                              color: MalouColors.textTitle,
                                              borderColor: Colors.transparent,
                                              child: IconButton(
                                                iconSize: 10,
                                                disabledColor: Colors.white,
                                                onPressed: () {
                                                  widget.feedbackMessageToEdit$
                                                      .add(null);
                                                },
                                                icon: const Icon(
                                                  MalouIcons.cross,
                                                  size: 20,
                                                ),
                                              )),
                                          const SizedBox(
                                            width: 4,
                                          ),
                                          SecondaryButtonContainer(
                                              isCircular: true,
                                              disabled: messageContent.isEmpty,
                                              color: MalouColors.success,
                                              borderColor: Colors.transparent,
                                              child: IconButton(
                                                iconSize: 10,
                                                disabledColor: Colors.white,
                                                onPressed:
                                                    messageContent.isEmpty
                                                        ? null
                                                        : () {
                                                            widget.onSubmitUpdate(
                                                                messageContent);
                                                            widget
                                                                .feedbackMessageToEdit$
                                                                .add(null);
                                                          },
                                                icon: const Icon(
                                                  MalouIcons.check,
                                                  size: 20,
                                                ),
                                              ))
                                        ],
                                      );
                                    }
                                  }),
                              const SizedBox(
                                width: 15,
                              ),
                            ],
                          )),
                    ],
                  ),
                ),
                if (widget.userProvider.currentUser!.role == 'admin') ...[
                  Padding(
                    padding: const EdgeInsets.only(left: 20, right: 10),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(Translation.of(context).onlyVisibleByAdmins,
                            style: MalouTextStyles(context)
                                .text12
                                .copyWith(color: MalouColors.textBody)),
                        SizedBox(
                          width: 50,
                          child: FittedBox(
                            fit: BoxFit.fill,
                            child: Switch(
                                value: widget.visibleForAdmins,
                                onChanged: (bool value) {
                                  widget.setVisibleForAdmins(value);
                                }),
                          ),
                        )
                      ],
                    ),
                  ),
                ]
              ],
              Padding(
                padding: const EdgeInsets.only(left: 20, right: 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(Translation.of(context).markFeebackMessagesAsClosed,
                        style: MalouTextStyles(context)
                            .text12
                            .copyWith(color: MalouColors.textBody)),
                    SizedBox(
                      width: 50,
                      child: FittedBox(
                        fit: BoxFit.fill,
                        child: Switch(
                            value: snapshot.data == FeedbackMessageType.close,
                            onChanged: (bool value) {
                              widget.currentfeedbackStatus$.add(value
                                  ? FeedbackMessageType.close
                                  : FeedbackMessageType.reopen);
                            }),
                      ),
                    )
                  ],
                ),
              )
            ],
          );
        });
  }
}
