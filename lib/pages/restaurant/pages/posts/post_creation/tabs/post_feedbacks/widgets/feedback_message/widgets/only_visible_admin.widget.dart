import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

class OnlyVisibleByAdminBadge extends StatelessWidget {
  const OnlyVisibleByAdminBadge({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return IntrinsicWidth(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: MalouColors.greenLight,
        ),
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Icon(
              color: MalouColors.success,
              MalouIcons.eye,
              size: 20,
            ),
            const SizedBox(width: 5),
            Text(
              Translation.of(context).onlyVisibleByAdmin,
              style: MalouTextStyles(context)
                  .text10
                  .copyWith(color: MalouColors.success),
            ),
          ],
        ),
      ),
    );
  }
}
