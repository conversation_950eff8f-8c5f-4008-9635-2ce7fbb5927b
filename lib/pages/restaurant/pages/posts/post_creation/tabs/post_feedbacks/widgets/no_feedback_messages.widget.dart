import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

class NoFeedbackMessages extends StatelessWidget {
  const NoFeedbackMessages({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
        Image.asset(
          IllustrationsPathResolver.resolve(Illustrations.icecream),
          height: 120,
        ),
        const SizedBox(height: 20),
        Text(
            textAlign: TextAlign.center,
            Translation.of(context).noFeedbacksYet,
            style: MalouTextStyles(context)
                .text12Italic
                .copyWith(color: MalouColors.textTitle))
      ]),
    );
  }
}
