import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_feedbacks/widgets/feedback_message/widgets/only_visible_admin.widget.dart';
import 'package:malou/shared/widgets/avatar/avatar.widget.dart';
import 'package:malou/shared/helpers/modal_bottom_sheet.dart';
import 'package:malou/shared/models/feedback.model.dart';
import 'package:malou/shared/models/user.model.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/date/date_formatter.widget.dart';
import 'package:malou/shared/widgets/inputs/styellable_text_field.widget.dart';

class FeedbackMessageWidget extends StatefulWidget {
  final String feedbackId;
  final FeedbackMessage feedbackMessage;
  final Function(String) deleteMessage;
  final Function(FeedbackMessage) onClickEditButton;
  final bool isAboutToBeEdited;
  final bool isSending;
  final User me;
  final TextPartStyleDefinition textPartStyleDefinition;

  const FeedbackMessageWidget(
      {super.key,
      required this.feedbackId,
      required this.feedbackMessage,
      required this.deleteMessage,
      required this.onClickEditButton,
      required this.isAboutToBeEdited,
      required this.me,
      required this.isSending,
      required this.textPartStyleDefinition});

  @override
  State<FeedbackMessageWidget> createState() => _FeedbackMessageWidgetState();
}

class _FeedbackMessageWidgetState extends State<FeedbackMessageWidget> {
  @override
  Widget build(BuildContext context) {
    return buildFeedbackMessageFactory(context, widget.feedbackMessage.type);
  }

  Widget buildFeedbackMessageFactory(
      BuildContext context, FeedbackMessageType type) {
    switch (type) {
      case FeedbackMessageType.text:
        return _buildFeedbackMessageTypeText(context);
      case FeedbackMessageType.reopen:
        return _buildFeedbackMessageTypeReopen(context);
      case FeedbackMessageType.close:
        return _buildFeedbackMessageTypeClosed(context);
      default:
        return const SizedBox();
    }
  }

  Widget _buildFeedbackMessageTypeClosed(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Column(children: [
        const SizedBox(height: 10),
        Text(
            textAlign: TextAlign.center,
            Translation.of(context)
                .feedbackMarkAsClosed(widget.feedbackMessage.author.fullname),
            style: MalouTextStyles(context)
                .text12Bold
                .copyWith(color: MalouColors.textBody)),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: DateFormatter(
              prefix: Translation.of(context).the,
              date: widget.feedbackMessage.createdAt,
              format: 'dd MMMM yyyy - HH:mm',
              style: MalouTextStyles(context)
                  .text10Italic
                  .copyWith(color: MalouColors.textBody)),
        ),
        const Divider(
          color: MalouColors.borderPrimary,
          thickness: 1,
        ),
      ]),
    );
  }

  Widget _buildFeedbackMessageTypeReopen(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Column(children: [
        const SizedBox(height: 10),
        Text(
            textAlign: TextAlign.center,
            Translation.of(context)
                .feedbackMarkAsReopen(widget.feedbackMessage.author.fullname),
            style: MalouTextStyles(context)
                .text12Bold
                .copyWith(color: MalouColors.textBody)),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: DateFormatter(
              prefix: Translation.of(context).the,
              date: widget.feedbackMessage.createdAt,
              format: 'dd MMMM yyyy - HH:mm',
              style: MalouTextStyles(context)
                  .text10Italic
                  .copyWith(color: MalouColors.textBody)),
        ),
        const Divider(
          color: MalouColors.borderPrimary,
          thickness: 1,
        ),
      ]),
    );
  }

  Widget _buildFeedbackMessageTypeText(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    List<TextSpan> textSpanChildren = <TextSpan>[];

    widget.feedbackMessage.text!.splitMapJoin(
      RegExp(widget.textPartStyleDefinition.pattern,
          caseSensitive: false, multiLine: true),
      onMatch: (Match match) {
        String? textPart = match.group(0);

        if (textPart == null) return '';

        TextSpan textSpan = TextSpan(
          text: textPart,
          style: widget.textPartStyleDefinition.style,
        );

        textSpanChildren.add(textSpan);

        return '';
      },
      onNonMatch: (String text) {
        TextSpan textSpan = TextSpan(
          text: text,
        );

        textSpanChildren.add(textSpan);

        return '';
      },
    );

    String fallbackUrl =
        'https://api.dicebear.com/7.x/initials/svg?seed=${widget.feedbackMessage.author.fullname}.png';

    return Column(
      children: [
        Row(
          children: [
            AnimatedContainer(
              margin: EdgeInsets.only(right: widget.isSending ? 10 : 0),
              duration: const Duration(milliseconds: 200),
              width: widget.isSending ? 10 : 0,
              height: 10,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: MalouColors.backgroundDark,
              ),
            ),
            Flexible(
              child: Container(
                alignment: Alignment.topLeft,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: widget.isAboutToBeEdited
                      ? MalouColors.backgroundDark
                      : MalouColors.backgroundLight,
                  border: Border.all(
                      color: widget.isAboutToBeEdited
                          ? MalouColors.primary
                          : MalouColors.borderPrimary,
                      width: 1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 20, horizontal: 24),
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Avatar(
                                  url: widget.feedbackMessage.author
                                          .profilePictureUrl ??
                                      fallbackUrl,
                                  factorSize: 0.7,
                                ),
                                const SizedBox(width: 10),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Row(
                                          children: [
                                            SizedBox(
                                              width: size.width * 0.5,
                                              child: Text(
                                                  widget.feedbackMessage.author
                                                      .fullname,
                                                  style:
                                                      MalouTextStyles(context)
                                                          .text12Bold
                                                          .copyWith(
                                                              color: MalouColors
                                                                  .textTitle),
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                  maxLines: 1,
                                                  softWrap: false),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                    DateFormatter(
                                        date: widget.feedbackMessage.createdAt,
                                        format: 'dd/MM/yyyy - HH:mm',
                                        style: MalouTextStyles(context)
                                            .text10Italic
                                            .copyWith(
                                                color: MalouColors.textBody)),
                                  ],
                                )
                              ],
                            ),
                            if (!widget.isSending &&
                                widget.feedbackMessage.author.id ==
                                    widget.me.id) ...[
                              MaterialButton(
                                onPressed: () {
                                  AppBottomModal.updateDeleteActionsDialog(
                                      context,
                                      deleteSubTitle: Translation.of(context)
                                          .aboutToDeleteMessage,
                                      onConfirmDelete: () {
                                    widget.deleteMessage(
                                        widget.feedbackMessage.id);
                                  }, onClickEditButton: () {
                                    widget.onClickEditButton(
                                        widget.feedbackMessage);
                                  });
                                },
                                // border radius 10px
                                shape: const RoundedRectangleBorder(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(10)),
                                ),
                                elevation: 0,
                                highlightElevation: 0,
                                minWidth: 36,
                                height: 36,
                                padding: EdgeInsets.zero,
                                color: Colors.white,
                                child: const Icon(
                                  Icons.more_horiz,
                                  color: MalouColors.primary,
                                  size: 23,
                                ),
                              ),
                            ]
                          ],
                        ),
                        const SizedBox(height: 10),
                        if (widget.feedbackMessage.visibility ==
                            FeedbackVisibility.admin) ...[
                          const OnlyVisibleByAdminBadge(),
                          const SizedBox(height: 10),
                        ],
                        RichText(
                          text: TextSpan(
                            style: MalouTextStyles(context)
                                .text12
                                .copyWith(color: MalouColors.textBody),
                            children: textSpanChildren,
                          ),
                        )
                      ]),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
