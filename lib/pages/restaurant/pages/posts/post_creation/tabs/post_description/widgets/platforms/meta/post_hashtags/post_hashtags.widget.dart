import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_description/widgets/platforms/meta/post_hashtags/post_hashtags.controller.dart';
import 'package:malou/shared/helpers/heap.service.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/hashtags/hashtags.provider.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/buttons/ai_button.widget.dart';
import 'package:malou/shared/widgets/chips/malou_chip.widget.dart';
import 'package:provider/provider.dart';
import 'package:skeleton_loader/skeleton_loader.dart';

class PostHashtags extends StatefulWidget {
  const PostHashtags({
    super.key,
  });

  @override
  State<PostHashtags> createState() => _PostHashtagsState();
}

class _PostHashtagsState extends State<PostHashtags> {
  late PostHashtagsController pageController;
  late HashtagsProvider hashtagsProvider;
  late PostsProvider postsProvider;
  late Post post;

  @override
  void initState() {
    postsProvider = Provider.of<PostsProvider>(context, listen: false);
    hashtagsProvider = Provider.of<HashtagsProvider>(context, listen: false);
    pageController = PostHashtagsController(
        context: context,
        postsProvider: postsProvider,
        hashtagsProvider: hashtagsProvider,
        setState: setState);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<PostsProvider>(builder: (context, provider, child) {
      post = provider.postToEdit;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              RichText(
                text: TextSpan(
                    text: Translation.of(context).hashtags,
                    style: MalouTextStyles(context)
                        .text12Semibold
                        .copyWith(color: MalouColors.textTitle),
                    children: [
                      TextSpan(
                        text: ' (${post.hashtags.selected.length})',
                        style: MalouTextStyles(context).text12Italic,
                      )
                    ]),
              ),
              const Spacer(),
              Opacity(
                opacity: (post.text ?? '').isNotEmpty ? 1 : 0.5,
                child: TextButton(
                    onPressed: pageController.canGenerateHashtags(context)
                        ? () {
                            HeapService.instance.track(
                                'tracking_hashtags_generate', {},
                                context: context);
                            pageController.hashtagsGenerationQuery
                                .execute(post.hashtags);
                          }
                        : null,
                    child: Row(
                      children: [
                        const Icon(
                          MalouIcons.ia,
                          color: MalouColors.purpleAccent,
                          size: 24,
                        ),
                        const SizedBox(width: 10),
                        if (post.hashtags.selected.isEmpty)
                          Text(Translation.of(context).addHashtags,
                              style: MalouTextStyles(context)
                                  .text12Semibold
                                  .copyWith(color: MalouColors.purpleAccent))
                        else
                          AiButton(
                              onPressed:
                                  pageController.canGenerateHashtags(context)
                                      ? () {
                                          HeapService.instance.track(
                                              'tracking_hashtags_retry', {},
                                              context: context);
                                          pageController.hashtagsGenerationQuery
                                              .execute(post.hashtags);
                                        }
                                      : null,
                              child: Text(Translation.of(context).retry,
                                  style: MalouTextStyles(context)
                                      .text12Semibold
                                      .copyWith(
                                          color: MalouColors.purpleAccent)))
                      ],
                    )),
              ),
              if (post.hashtags.selected.isNotEmpty)
                RotatedBox(
                  quarterTurns: 2,
                  child: IconButton(
                    padding: EdgeInsets.zero,
                    color: MalouColors.purpleAccent,
                    constraints: const BoxConstraints(),
                    onPressed: () => pageController
                        .navigateToHashtagsEditingPage(context, post.hashtags),
                    icon: const Icon(
                      MalouIcons.chevronLeft,
                      color: MalouColors.purpleAccent,
                    ),
                  ),
                ),
            ],
          ),
          StreamBuilder<bool>(
              stream: pageController.hashtagsGenerationQuery.isFetching,
              builder: (context, snapshot) {
                if (snapshot.data == true) {
                  return Wrap(
                      runSpacing: -25,
                      spacing: 0,
                      children: List.generate(
                        6,
                        (index) => Container(
                          height: 65,
                          width: 60,
                          margin: const EdgeInsets.only(right: 10),
                          child: SkeletonLoader(
                              direction: SkeletonDirection.ltr,
                              builder: Container(
                                width: 60,
                                height: 30,
                                decoration: BoxDecoration(
                                  color: MalouColors.backgroundDark,
                                  borderRadius: BorderRadius.circular(5),
                                ),
                              )),
                        ),
                      ));
                }

                return Wrap(
                  spacing: 5,
                  runSpacing: 5,
                  children: post.hashtags.selected
                      .map((e) => MalouChip(
                            text: e.text,
                            withDefaultMargin: false,
                          ))
                      .toList(),
                );
              }),
        ],
      );
    });
  }
}
