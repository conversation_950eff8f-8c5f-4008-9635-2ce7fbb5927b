import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_description/widgets/platforms/meta/post_hashtags/hashtag_list_tile.widget.dart';
import 'package:malou/shared/helpers/heap.service.dart';
import 'package:malou/shared/models/hashtags/hashtag.model.dart';
import 'package:malou/shared/models/posts/post-hashtags.model.dart';
import 'package:malou/shared/services/hashtags/hashtags.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/inputs/text_search.widget.dart';
import 'package:malou/shared/widgets/appbar/appbar_back.widget.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:objectid/objectid.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';

class PostHashtagsEditingPage extends StatefulWidget {
  final PostHashtags postHashtags;
  const PostHashtagsEditingPage({
    super.key,
    required this.postHashtags,
  });

  @override
  State<PostHashtagsEditingPage> createState() =>
      _PostHashtagsEditingPageState();
}

class _PostHashtagsEditingPageState extends State<PostHashtagsEditingPage>
    with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  final hashtagsSubject = BehaviorSubject<String>();
  final hashtagsController = TextEditingController();
  final killSubscriptions$ = BehaviorSubject<bool>();

  late TabController tabController;

  final BehaviorSubject<String> recommandationsSearch$ =
      BehaviorSubject<String>.seeded('');
  final BehaviorSubject<String> favSearch$ = BehaviorSubject<String>.seeded('');

  final TextEditingController recommandationsTextController =
      TextEditingController();
  final TextEditingController favTextController = TextEditingController();

  late final BehaviorSubject<List<Hashtag>> recommendedHashtags$;

  late BehaviorSubject<List<Hashtag>> selectedHashtags$;

  late HashtagsProvider hashtagsProvider;

  @override
  void initState() {
    tabController = TabController(length: 2, vsync: this);

    hashtagsProvider = Provider.of<HashtagsProvider>(context, listen: false);

    recommendedHashtags$ =
        BehaviorSubject<List<Hashtag>>.seeded(widget.postHashtags.suggested);
    selectedHashtags$ =
        BehaviorSubject<List<Hashtag>>.seeded(widget.postHashtags.selected);

    selectedHashtags$.add(widget.postHashtags.selected);
    recommendedHashtags$.add(widget.postHashtags.suggested);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final double height = MediaQuery.of(context).size.height;
    return Scaffold(
        appBar: AppBarArrowBack(
          height: 50,
          icon: const Icon(
            MalouIcons.cross,
            size: 30,
            color: MalouColors.primary,
          ),
          actions: [
            IconButton(
              padding: const EdgeInsets.only(right: 12),
              onPressed: () {
                Navigator.pop(
                    context,
                    PostHashtags(
                      selected: selectedHashtags$.value,
                      suggested: recommendedHashtags$.value,
                    ));
              },
              icon: const Icon(
                MalouIcons.check,
                size: 30,
                color: MalouColors.primary,
              ),
            )
          ],
        ),
        body: Padding(
          padding: const EdgeInsets.all(20),
          child: ListView(
            children: [
              UsageAdvice(
                selectedHashtags$: selectedHashtags$,
              ),
              const SizedBox(
                height: 30,
              ),
              TabBar(
                controller: tabController,
                unselectedLabelStyle: MalouTextStyles(context).text13,
                labelStyle: MalouTextStyles(context).text13Bold,
                labelPadding: const EdgeInsets.all(0),
                tabs: [
                  Tab(
                    child: Row(
                      children: [
                        Text(
                          Translation.of(context).recommandations,
                        ),
                        Text(
                          Translation.of(context).ia,
                          style: MalouTextStyles(context).text13Italic,
                        ),
                      ],
                    ),
                  ),
                  Tab(
                    child: Text(
                      Translation.of(context).favorite,
                    ),
                  )
                ],
                labelColor: MalouColors.textBody,
                unselectedLabelColor: MalouColors.textBody,
                indicatorColor: MalouColors.primary,
                indicatorSize: TabBarIndicatorSize.tab,
              ),
              SizedBox(
                height: height * 0.6,
                child: TabBarView(controller: tabController, children: [
                  _buildHashtagsList(context,
                      searchSubject$: recommandationsSearch$,
                      controller: recommandationsTextController,
                      hashtags$: recommendedHashtags$,
                      isSuggestionsTab: true),
                  _buildHashtagsList(context,
                      searchSubject$: favSearch$,
                      controller: favTextController,
                      hashtags$: hashtagsProvider.restaurantHashtags$,
                      isSuggestionsTab: false),
                ]),
              )
            ],
          ),
        ));
  }

  _buildHashtagsList(BuildContext context,
      {required BehaviorSubject<String> searchSubject$,
      required BehaviorSubject<List<Hashtag>> hashtags$,
      required TextEditingController controller,
      required bool isSuggestionsTab}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12.0),
      child: Column(
        children: [
          TextSearch(
              controller: controller,
              onClearText: () {
                setState(() {
                  controller.clear();
                  searchSubject$.add('');
                });
              },
              onChanged: (value) => setState(() {
                    searchSubject$.add(value);
                  })),
          HashtagsListView(
            searchTextController: controller,
            selectedHashtags$: selectedHashtags$,
            hashtags$: hashtags$,
            hashtagsProvider: hashtagsProvider,
            searchSubject$: searchSubject$,
            isSuggestionTab: isSuggestionsTab,
          )
        ],
      ),
    );
  }

  @override
  void dispose() {
    killSubscriptions$.add(true);
    hashtagsSubject.close();
    hashtagsController.dispose();
    hashtagsSubject.close();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;
}

class HashtagsListView extends StatefulWidget {
  final BehaviorSubject<List<Hashtag>> hashtags$;
  final BehaviorSubject<List<Hashtag>> selectedHashtags$;
  final TextEditingController searchTextController;
  final bool isSuggestionTab;

  final HashtagsProvider hashtagsProvider;
  final BehaviorSubject<String> searchSubject$;

  const HashtagsListView({
    super.key,
    required this.selectedHashtags$,
    required this.hashtags$,
    required this.hashtagsProvider,
    required this.searchSubject$,
    required this.searchTextController,
    required this.isSuggestionTab,
  });

  @override
  State<HashtagsListView> createState() => _HashtagsListViewState();
}

class _HashtagsListViewState extends State<HashtagsListView> {
  bool isEditingMainHashtag = false;
  TextEditingController mainHashtagController = TextEditingController();

  @override
  void initState() {
    mainHashtagController.text = widget
        .hashtagsProvider.restaurantHashtags$.value
        .firstWhere((element) => element.isMain)
        .text;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final height = MediaQuery.of(context).size.height;
    return StreamBuilder<List<Hashtag>>(
      stream: CombineLatestStream.combine2(
              widget.hashtags$, widget.searchSubject$.stream, (a, b) => [a, b])
          .map((event) {
        final hashtags = event[0] as List<Hashtag>;
        final search = event[1] as String;
        if (search.isEmpty) return hashtags;
        return hashtags
            .where((element) =>
                element.text.toLowerCase().contains(search.toLowerCase()))
            .toList();
      }),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return StreamBuilder<List<Hashtag>>(
              stream: CombineLatestStream.combine2(
                  widget.hashtags$,
                  context.read<HashtagsProvider>().restaurantHashtags$,
                  (a, b) => a),
              initialData: const [],
              builder: (context, selectedHashtagsSnapshot) {
                return SizedBox(
                  height: height * 0.5,
                  child: snapshot.data != null &&
                          snapshot.data!.isEmpty &&
                          widget.searchSubject$.value.isNotEmpty
                      ? ListView(
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          children: [
                            HashtagListTile(
                                hashtag: Hashtag(
                                    id: ObjectId().toString(),
                                    isNew: true,
                                    text: widget.searchSubject$.value,
                                    restaurantId: context
                                        .read<RestaurantProvider>()
                                        .currentRestaurant!
                                        .id,
                                    type: HashtagType.UNKNOWN,
                                    createdAt: DateTime.now(),
                                    updatedAt: DateTime.now()),
                                isEditingMainHashtag: isEditingMainHashtag,
                                isHashtagInFavs: isHashtagInFavs,
                                toggleFavHashtag: toggleFavHashtag,
                                isHashtagSelected: isHashtagSelected,
                                toggleAddHashtag: toggleAddHashtag,
                                toggleEditMainHashtag: toggleEditMainHashtag,
                                mainHashtagController: mainHashtagController,
                                editMainhashtagText: editMainhashtagText),
                          ],
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          shrinkWrap: true,
                          itemCount: snapshot.data!.length,
                          itemBuilder: (context, index) {
                            final hashtag = snapshot.data![index];
                            return HashtagListTile(
                              hashtag: hashtag,
                              isEditingMainHashtag: isEditingMainHashtag,
                              isHashtagInFavs: isHashtagInFavs,
                              toggleFavHashtag: toggleFavHashtag,
                              isHashtagSelected: isHashtagSelected,
                              toggleAddHashtag: toggleAddHashtag,
                              toggleEditMainHashtag: toggleEditMainHashtag,
                              mainHashtagController: mainHashtagController,
                              editMainhashtagText: editMainhashtagText,
                            );
                          },
                        ),
                );
              });
        }
        return const SizedBox();
      },
    );
  }

  bool isHashtagSelected(Hashtag hashtag) {
    // ignore: unnecessary_null_comparison
    return widget.selectedHashtags$.value
        .where((element) => element.text == hashtag.text)
        .isNotEmpty;
  }

  bool isHashtagInFavs(Hashtag hashtag) {
    // ignore: unnecessary_null_comparison
    return widget.hashtagsProvider.restaurantHashtags$.value
        .where((element) => element.text == hashtag.text)
        .isNotEmpty;
  }

  toggleAddHashtag(Hashtag hashtag) {
    setState(() {
      if (isHashtagSelected(hashtag)) {
        widget.selectedHashtags$.add(widget.selectedHashtags$.value
            .where((element) => element.text != hashtag.text)
            .toList());
      } else {
        HeapService.instance
            .track('tracking_hashtags_add', {}, context: context);
        final newHashtag = hashtag.copyWith(isNew: false);
        widget.selectedHashtags$
            .add([...widget.selectedHashtags$.value, newHashtag]);
        if (hashtag.isNew == true) {
          widget.searchTextController.clear();
          widget.searchSubject$.add('');
          if (!widget.isSuggestionTab) {
            toggleFavHashtag(newHashtag);
          } else {
            widget.hashtags$.add([...widget.hashtags$.value, newHashtag]);
          }
        }
      }
    });
  }

  toggleFavHashtag(Hashtag hashtag) {
    setState(() {
      if (isHashtagInFavs(hashtag)) {
        widget.hashtagsProvider.removeHashtagFromFavorite(hashtag);
        widget.searchTextController.clear();
        widget.searchSubject$.add('');
        widget.selectedHashtags$.add(widget.selectedHashtags$.value
            .where((element) => element.text != hashtag.text)
            .toList());
      } else {
        HeapService.instance
            .track('tracking_hashtags_add_favorites', {}, context: context);
        widget.hashtagsProvider.addHashtagToFavorite(hashtag);
      }
    });
  }

  toggleEditMainHashtag() {
    setState(() {
      isEditingMainHashtag = !isEditingMainHashtag;
      mainHashtagController.text = widget
          .hashtagsProvider.restaurantHashtags$.value
          .firstWhere((element) => element.isMain)
          .text;
    });
  }

  editMainhashtagText() {
    Hashtag brandHashtag = widget.hashtagsProvider.restaurantHashtags$.value
        .firstWhere((element) => element.isMain);

    String hashtagText =
        '#${mainHashtagController.text.replaceAll('#', '').replaceAll(' ', '').trim()}';
    setState(() {
      if (widget.selectedHashtags$.value
          .where((element) => element.isMain)
          .isNotEmpty) {
        widget.selectedHashtags$.add(widget.selectedHashtags$.value
            .map((element) => element.id == brandHashtag.id
                ? brandHashtag.copyWith(text: hashtagText)
                : element)
            .toList());
      }
      widget.hashtags$.add(widget.hashtags$.value
          .map((element) => element.id == brandHashtag.id
              ? brandHashtag.copyWith(text: hashtagText)
              : element)
          .toList());
      widget.hashtagsProvider.editMainHashtag(hashtagText);
      isEditingMainHashtag = false;
    });
  }
}

class UsageAdvice extends StatelessWidget {
  final Subject selectedHashtags$;
  const UsageAdvice({
    super.key,
    required this.selectedHashtags$,
  });
  final Icon crossIcon =
      const Icon(MalouIcons.cross, color: MalouColors.purple, size: 18);
  final Icon checkIcon =
      const Icon(MalouIcons.check, color: MalouColors.success, size: 18);
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
      decoration: BoxDecoration(
        color: MalouColors.backgroundLight,
        border: Border.all(
          color: MalouColors.borderPrimary,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(10),
      ),
      child: StreamBuilder(
          stream: selectedHashtags$,
          builder: (context, snapshot) {
            final List<Hashtag> selectedHashtags = snapshot.data ?? [];
            bool hasBetween3and6Hashtags =
                selectedHashtags.length >= 3 && selectedHashtags.length <= 6;
            bool hasBrandHashtag =
                selectedHashtags.where((element) => element.isMain).isNotEmpty;
            bool hasSelectedRestaurantHashtag = selectedHashtags
                .where((element) =>
                    element.type == HashtagType.RESTAURANT && !element.isMain)
                .isNotEmpty;
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  Translation.of(context).keywordsAdvices,
                  style: MalouTextStyles(context)
                      .text12Bold
                      .copyWith(color: MalouColors.textTitle),
                ),
                const SizedBox(
                  height: 15,
                ),
                Row(
                  children: [
                    hasBetween3and6Hashtags ? checkIcon : crossIcon,
                    const SizedBox(width: 5),
                    Text(Translation.of(context).hashtagAdvice1,
                        style: MalouTextStyles(context).text10Medium)
                  ],
                ),
                const SizedBox(
                  height: 15,
                ),
                Row(
                  children: [
                    hasBrandHashtag ? checkIcon : crossIcon,
                    const SizedBox(width: 5),
                    Text(Translation.of(context).mainHashtag,
                        style: MalouTextStyles(context).text10Medium)
                  ],
                ),
                const SizedBox(
                  height: 15,
                ),
                Row(
                  children: [
                    hasSelectedRestaurantHashtag ? checkIcon : crossIcon,
                    const SizedBox(width: 5),
                    Text(Translation.of(context).typeAndLocalisation,
                        style: MalouTextStyles(context).text10Medium),
                    Text(Translation.of(context).typeAndLocalisationExample,
                        style: MalouTextStyles(context)
                            .text10Medium
                            .copyWith(fontStyle: FontStyle.italic))
                  ],
                ),
              ],
            );
          }),
    );
  }
}
