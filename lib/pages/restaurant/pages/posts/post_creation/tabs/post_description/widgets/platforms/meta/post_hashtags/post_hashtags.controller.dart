import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_description/widgets/platforms/meta/post_hashtags/post_hashtags.page.widget.dart';
import 'package:malou/shared/helpers/heap.service.dart';
import 'package:malou/shared/helpers/use_query.dart';
import 'package:malou/shared/models/posts/post-hashtags.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/models/restaurant.model.dart';
import 'package:malou/shared/services/hashtags/hashtags.provider.dart';
import 'package:malou/shared/services/keyword/keyword.provider.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:provider/provider.dart';

class PostHashtagsController {
  final PostsProvider postsProvider;
  final HashtagsProvider hashtagsProvider;
  final BuildContext context;
  final void Function(VoidCallback fn) setState;
  PostHashtagsController(
      {required this.context,
      required this.postsProvider,
      required this.hashtagsProvider,
      required this.setState});

  late final hashtagsGenerationQuery = useQuery((_) {
    final Restaurant restaurant =
        context.read<RestaurantProvider>().currentRestaurant!;
    final Post post = postsProvider.postToEdit;
    final language = context.read<LanguageProvider>().language;
    final selectedKeywords =
        context.read<KeywordProvider>().selectedKeywords.value;
    setState(() {});
    return hashtagsProvider.getHashtagsSuggestions(
        restaurantId: restaurant.id,
        restaurantName: restaurant.name,
        postDescription: post.text!,
        favoriteHashtags: hashtagsProvider.restaurantHashtags$.value
            .map((e) => e.text)
            .toList(),
        restaurantCategory: [
          restaurant.category!.getCategoryNameForLang(language.name)
        ],
        restaurantLocality: restaurant.address?.locality ?? '',
        restaurantPostalCode: restaurant.address?.postalCode ?? '',
        keywords: selectedKeywords
            .map((e) => e.keyword.getBricksTexts())
            .flattened
            .toList());
  }, onSuccess: (value, params, _) {
    postsProvider.postToEdit = postsProvider.postToEdit.copyWith(
        hashtags: PostHashtags(
            selected: value.preSelectedHashtags,
            suggested: value.generatedHashtags));
  });

  navigateToHashtagsEditingPage(
      BuildContext context, PostHashtags postHashtags) {
    Navigator.of(context)
        .push<PostHashtags>(
      MaterialPageRoute(
        builder: (context) =>
            PostHashtagsEditingPage(postHashtags: postHashtags),
      ),
    )
        .then((value) {
      if (value != null) {
        postsProvider.postToEdit = postsProvider.postToEdit.copyWith(
          hashtags: value,
        );
      }
    });
  }

  void removeHashtag(String hashtagId) {
    HeapService.instance
        .track('tracking_hashtags_remove', {}, context: context);
    postsProvider.postToEdit = postsProvider.postToEdit.copyWith(
      hashtags: postsProvider.postToEdit.hashtags.copyWith(
        selected: postsProvider.postToEdit.hashtags.selected
            .where((element) => element.id != hashtagId)
            .toList(),
      ),
    );
  }

  bool canGenerateHashtags(BuildContext context) {
    final post = context.read<PostsProvider>().postToEdit;
    return (post.text ?? '').isNotEmpty &&
        hashtagsGenerationQuery.isFetching.value == false;
  }
}
