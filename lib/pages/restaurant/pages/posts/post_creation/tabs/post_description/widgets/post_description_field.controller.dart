import 'package:flutter/material.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';

class PostDescriptionFieldsController {
  final PostsProvider postsProvider;

  PostDescriptionFieldsController({
    required this.postsProvider,
  });

  setPostDescription(String text) {
    String textWithoutHashTags =
        text.replaceAllMapped(RegExp(r'#[a-zA-Z0-9]+'), (match) => '');
    postsProvider.postToEdit =
        postsProvider.postToEdit.copyWith(text: textWithoutHashTags);
    postsProvider.postToEdit.postTextController!.value =
        TextEditingValue(text: textWithoutHashTags);
  }

  Future<String> generateTextFromAI(String description, String lang) {
    Post post = postsProvider.postToEdit;
    return postsProvider.generateTextFromAI(GenerateTextFromAIParams(
        postId: post.id!,
        description: description,
        restaurantId: post.restaurantId,
        lang: lang,
        keys: post.keys));
  }

  Future<List<String>> generateTextFromAIAdvancedSettings(
      String description, String lang, bool shouldUseImageAnalysis) {
    Post post = postsProvider.postToEdit;
    return postsProvider.generateTextFromAIAdvancedSettings(
        GenerateTextFromAIParams(
            postId: post.id!,
            description: description,
            restaurantId: post.restaurantId,
            lang: lang,
            keys: post.keys,
            shouldUseImageAnalysis: shouldUseImageAnalysis));
  }

  Future<String> translateTextFromAI(String text, String lang) {
    Post post = postsProvider.postToEdit;
    return postsProvider.translateTextFromAI(TranslateTextFromAIParams(
      postId: post.id!,
      text: text,
      restaurantId: post.restaurantId,
      lang: lang,
    ));
  }

  Future<String> optimizeTextFromAI(String text) {
    Post post = postsProvider.postToEdit;
    return postsProvider.optimizeTextFromAI(OptimizeTextFromAIParams(
        postId: post.id!,
        restaurantId: post.restaurantId,
        textToOptimize: text,
        keys: post.keys));
  }
}
