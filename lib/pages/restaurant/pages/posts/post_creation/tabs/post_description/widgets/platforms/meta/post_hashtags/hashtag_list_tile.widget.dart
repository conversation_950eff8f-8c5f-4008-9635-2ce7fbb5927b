import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/models/hashtags/hashtag.model.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/image/cached_image.widget.dart';
import 'package:provider/provider.dart';

class HashtagListTile extends StatelessWidget {
  final Hashtag hashtag;
  final bool isEditingMainHashtag;
  final bool Function(Hashtag hashtag) isHashtagInFavs;
  final void Function(Hashtag hashtag) toggleFavHashtag;
  final bool Function(Hashtag hashtag) isHashtagSelected;
  final void Function(Hashtag hashtag) toggleAddHashtag;
  final void Function() toggleEditMainHashtag;
  final TextEditingController mainHashtagController;
  final void Function() editMainhashtagText;

  const HashtagListTile(
      {super.key,
      required this.hashtag,
      required this.isEditingMainHashtag,
      required this.isHashtagInFavs,
      required this.toggleFavHashtag,
      required this.isHashtagSelected,
      required this.toggleAddHashtag,
      required this.toggleEditMainHashtag,
      required this.mainHashtagController,
      required this.editMainhashtagText});

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        border: Border.all(
          color: MalouColors.borderPrimary,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(5),
      ),
      child: ListTile(
        splashColor: MalouColors.borderPrimary,
        horizontalTitleGap: 0,
        minLeadingWidth: 0,
        leading: hashtag.isMain &&
                context.read<RestaurantProvider>().currentRestaurant?.logo !=
                    null
            ? Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15.0),
                child: CachedImage(
                  height: 20,
                  width: 20,
                  fit: BoxFit.contain,
                  url: context
                      .read<RestaurantProvider>()
                      .currentRestaurant!
                      .logo!
                      .getUrlForSize(preferedSize: MediaSize.small),
                  radius: BorderRadius.circular(9999),
                ),
              )
            : hashtag.isNew == true
                ? const SizedBox(
                    width: 20,
                  )
                : IconButton(
                    padding: const EdgeInsets.all(0),
                    onPressed: () => toggleFavHashtag(hashtag),
                    icon: isHashtagInFavs(hashtag)
                        ? const Icon(
                            MalouIcons.fav,
                            color: MalouColors.warning,
                            size: 24,
                          )
                        : const Icon(
                            MalouIcons.favStroke,
                            color: MalouColors.backgroundDark,
                            size: 24,
                          )),
        contentPadding: const EdgeInsets.all(0),
        visualDensity: VisualDensity.compact,
        trailing: !isEditingMainHashtag || !hashtag.isMain
            ? Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (hashtag.isMain)
                    IconButton(
                      padding: const EdgeInsets.all(0),
                      onPressed: toggleEditMainHashtag,
                      icon: SvgPicture.asset(
                        'assets/icons/edit.svg',
                        color: MalouColors.primary,
                        width: 20,
                      ),
                    ),
                  IconButton(
                      padding: const EdgeInsets.all(0),
                      onPressed: () => toggleAddHashtag(hashtag),
                      icon: isHashtagSelected(hashtag)
                          ? Container(
                              padding: const EdgeInsets.all(4),
                              decoration: const BoxDecoration(
                                  color: MalouColors.primary,
                                  shape: BoxShape.circle),
                              child: const Icon(
                                MalouIcons.check,
                                color: Colors.white,
                                size: 16,
                              ),
                            )
                          : SvgPicture.asset(
                              'assets/icons/add.svg',
                              color: MalouColors.purpleLight,
                              width: 24,
                            )),
                ],
              )
            : SizedBox(
                width: width * 0.8,
                child: Row(
                  children: [
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 5),
                        height: 31,
                        decoration: const BoxDecoration(
                            border: Border(
                                bottom: BorderSide(
                                    color: MalouColors.purpleLight, width: 1))),
                        margin: const EdgeInsets.symmetric(horizontal: 10),
                        child: TextField(
                          controller: mainHashtagController,
                          style: MalouTextStyles(context)
                              .text10
                              .copyWith(color: MalouColors.textTitle),
                          decoration: const InputDecoration(
                            border: InputBorder.none,
                          ),
                        ),
                      ),
                    ),
                    IconButton(
                        padding: const EdgeInsets.all(6),
                        constraints:
                            const BoxConstraints(minWidth: 0, minHeight: 0),
                        style: IconButton.styleFrom(
                          backgroundColor: MalouColors.textTitle,
                        ),
                        onPressed: toggleEditMainHashtag,
                        icon: const Icon(MalouIcons.cross,
                            color: Colors.white, size: 18)),
                    IconButton(
                        padding: const EdgeInsets.all(6),
                        constraints:
                            const BoxConstraints(minWidth: 0, minHeight: 0),
                        style: IconButton.styleFrom(
                          backgroundColor: MalouColors.success,
                        ),
                        onPressed: editMainhashtagText,
                        icon: const Icon(MalouIcons.check,
                            color: Colors.white, size: 18))
                  ],
                ),
              ),
        title: isEditingMainHashtag && hashtag.isMain
            ? const SizedBox()
            : Text(hashtag.text,
                style: MalouTextStyles(context)
                    .text10
                    .copyWith(color: MalouColors.textTitle)),
        onTap: () => toggleAddHashtag(hashtag),
      ),
    );
  }
}
