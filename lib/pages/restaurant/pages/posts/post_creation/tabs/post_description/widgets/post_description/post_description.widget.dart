import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_description/widgets/platforms/meta/reel_cover/reel_cover.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_description/widgets/post_description_field.controller.dart';
import 'package:malou/shared/helpers/heap.service.dart';
import 'package:malou/shared/helpers/modal_bottom_sheet.dart';
import 'package:malou/shared/helpers/utils.dart';
import 'package:malou/shared/models/ai_interaction.model.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/pages/ai_generation/ai_generation.dart';
import 'package:malou/shared/pages/ai_generation/pages/models/generate_from_ai_params.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/buttons/ai_button.widget.dart';
import 'package:malou/shared/widgets/errors/error_dialog.widget.dart';
import 'package:malou/shared/widgets/inputs/text_field.widget.dart';
import 'package:malou/shared/widgets/modals/translation_laguange_modal.widget.dart';
import 'package:uuid/uuid.dart';

class PostDescription extends StatefulWidget {
  final PostsProvider postsProvider;
  final List<PlatformKeys> platformKeys;
  final PostDescriptionFieldsController postDescriptionFieldsController;
  const PostDescription(
      {super.key,
      required this.postsProvider,
      required this.platformKeys,
      required this.postDescriptionFieldsController});

  @override
  State<PostDescription> createState() => _PostDescriptionState();
}

class _PostDescriptionState extends State<PostDescription> {
  bool isAiProcessing = true;
  bool isTextEmpty = true;
  @override
  void initState() {
    widget.postsProvider
        .initPostInteractions()
        .then((_) => setState(() {
              isAiProcessing = false;
            }))
        .catchError((error) {
      setState(() {
        isAiProcessing = false;
      });
    });
    widget.postsProvider.postToEdit.postTextController?.textObs$
        .listen((value) {
      setState(() {
        isTextEmpty = value.isEmpty;
      });
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                  flex: 5,
                  child: MalouTextFormField(
                    controller:
                        widget.postsProvider.postToEdit.postTextController,
                    maxLength: widget.platformKeys.contains(PlatformKeys.mapstr)
                        ? 300
                        : null,
                    maxLines: 10,
                    showAiProcessingSpinner: isAiProcessing,
                    keyboardType: TextInputType.multiline,
                    minLines: 1,
                    onChanged: debounceTime(
                        (value) => widget.postsProvider.postToEdit = widget
                            .postsProvider.postToEdit
                            .copyWith(text: value),
                        duration: const Duration(milliseconds: 100)),
                    hint: Translation.of(context).writeLegendHere,
                    label: Translation.of(context).legendLabel,
                    validator: (value) => value!.isEmpty &&
                            widget.postsProvider.postToEdit
                                    .publicationDatePlanned !=
                                PublicationDatePlanned.draft
                        ? Translation.of(context).mandatoryField
                        : null,
                    showAiButtonNextToLabel: !isTextEmpty,
                    showAiButtonInsideField: isTextEmpty,
                    showAiButtonOnlyIcon:
                        widget.postsProvider.postToEdit.isReel(),
                    onAiPressed: () => onWriteWithAiPressed(context),
                  )),
              if (widget.postsProvider.postToEdit.postType == PostType.REEL &&
                  widget.postsProvider.postToEdit.attachments.isNotEmpty)
                ReelCover(context: context)
            ]),
        const SizedBox(
          height: 10,
        ),
        if (widget.postsProvider.postToEdit.text != null &&
            widget.postsProvider.postToEdit.text!.isNotEmpty)
          Row(
            children: [
              AiButton(
                onPressed: isAiProcessing
                    ? null
                    : () => optimizeTextFromAI(
                        widget
                            .postsProvider.postToEdit.postTextController!.text,
                        context),
                child: Text(Translation.of(context).optimize,
                    style: MalouTextStyles(context).text12Semibold.copyWith(
                          color: MalouColors.purpleAccent,
                        )),
              ),
              const SizedBox(
                width: 10,
              ),
              AiButton(
                onPressed: isAiProcessing
                    ? null
                    : () => openTranslateTextFromAI(context),
                child: Row(
                  children: [
                    Text(Translation.of(context).translate,
                        style: MalouTextStyles(context).text12Semibold.copyWith(
                              color: MalouColors.purpleAccent,
                            )),
                    const SizedBox(
                      width: 5,
                    ),
                    const Icon(
                      Icons.keyboard_arrow_down_outlined,
                      color: MalouColors.purpleAccent,
                      size: 20,
                    )
                  ],
                ),
              ),
              AiButton(
                padding: const EdgeInsets.all(7),
                onPressed: isAiProcessing ||
                        !widget.postsProvider.interactionsManager.hasPrevious
                    ? null
                    : () => setState(() {
                          final newText = widget
                              .postsProvider.interactionsManager
                              .previous();
                          widget.postDescriptionFieldsController
                              .setPostDescription(newText);
                        }),
                child: SvgPicture.asset(
                  'assets/icons/undo.svg',
                  color: MalouColors.purpleAccent,
                  width: 20,
                ),
              ),
              SizedBox(
                width: 35,
                child: AiButton(
                  padding: const EdgeInsets.all(7),
                  onPressed: isAiProcessing ||
                          !widget.postsProvider.interactionsManager.hasNext
                      ? null
                      : () => setState(() {
                            final newText =
                                widget.postsProvider.interactionsManager.next();
                            widget.postDescriptionFieldsController
                                .setPostDescription(newText);
                          }),
                  child: SvgPicture.asset(
                    'assets/icons/redo.svg',
                    color: MalouColors.purpleAccent,
                    width: 20,
                  ),
                ),
              )
            ],
          )
      ],
    );
  }

  void openTranslateTextFromAI(BuildContext context) {
    AppBottomModal.showTinyModalBottomSheet(context,
        TranslationLanguageModal(onValidate: (selectedLang) async {
      await translateTextFromAI(
              widget.postsProvider.postToEdit.postTextController!.text,
              selectedLang,
              context)
          .then((value) {
        if (value == null) {
          context.pop();
          showDialog(
              builder: (context) => ErrorDialog(
                  errorMessage: Translation.of(context).anErrorOccured),
              context: context);
          return;
        }
        setState(() {
          widget.postsProvider.interactionsManager
              .add(MinimalAiInteraction(id: const Uuid().v4(), text: value));
        });
        widget.postDescriptionFieldsController.setPostDescription(value);
        context.pop();
      });
    }));
  }

  void onWriteWithAiPressed(
    BuildContext context,
  ) {
    HeapService.instance.track(
        'post_ia_description_generation',
        {
          'postId': widget.postsProvider.postToEdit.id,
          'postType': widget.postsProvider.postToEdit.postType?.name,
          'postSource': widget.postsProvider.postToEdit.source.name,
        },
        context: context);
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => AIGeneration(
                  label: Translation.of(context).aiPostCaptionFormfieldLabel,
                  placeholder:
                      Translation.of(context).aiPostCaptionFormfieldHint,
                  onAiValidateSuggestion: () => HeapService.instance.track(
                      'post_ia_description_generation_confirmation',
                      {
                        'postId': widget.postsProvider.postToEdit.id,
                        'postType':
                            widget.postsProvider.postToEdit.postType?.name,
                        'postSource':
                            widget.postsProvider.postToEdit.source.name,
                      },
                      context: context),
                  text:
                      widget.postsProvider.postToEdit.postTextController!.text,
                  setValueFromAI: (String generatedText) {
                    String textWithoutHashTags = generatedText.replaceAllMapped(
                        RegExp(r'#[a-zA-Z0-9]+'), (match) => '');
                    widget.postsProvider.postToEdit = widget
                        .postsProvider.postToEdit
                        .copyWith(text: textWithoutHashTags);
                    widget.postsProvider.postToEdit.postTextController!.value =
                        TextEditingValue(text: textWithoutHashTags);
                    setState(() {
                      widget.postsProvider.interactionsManager.add(
                          MinimalAiInteraction(
                              id: const Uuid().v4(),
                              text: textWithoutHashTags));
                    });
                  },
                  generateFromAI: (GenerateFromAIParams params) {
                    HeapService.instance.track(
                        'post_ia_description_generation',
                        {
                          'postId': widget.postsProvider.postToEdit.id,
                          'postType':
                              widget.postsProvider.postToEdit.postType?.name,
                          'postSource':
                              widget.postsProvider.postToEdit.source.name,
                        },
                        context: context);
                    return widget.postDescriptionFieldsController
                        .generateTextFromAI(params.description, params.lang);
                  },
                  generateFromAIAdvancedSettings:
                      (GenerateFromAIParams params) {
                    HeapService.instance.track(
                        'post_ia_description_generation_advanced_settings',
                        {
                          'postId': widget.postsProvider.postToEdit.id,
                          'postType':
                              widget.postsProvider.postToEdit.postType?.name,
                          'postSource':
                              widget.postsProvider.postToEdit.source.name,
                        },
                        context: context);
                    return widget.postDescriptionFieldsController
                        .generateTextFromAIAdvancedSettings(
                            params.description,
                            params.lang,
                            params.shouldUseImageAnalysis ?? false);
                  },
                  postProvider: widget.postsProvider,
                )));
  }

  translateTextFromAI(String text, String lang, BuildContext context) {
    HeapService.instance.track(
        'post_ia_description_generation_translation',
        {
          'postId': widget.postsProvider.postToEdit.id,
          'postType': widget.postsProvider.postToEdit.postType?.name,
          'postSource': widget.postsProvider.postToEdit.source.name,
        },
        context: context);
    return widget.postDescriptionFieldsController
        .translateTextFromAI(text, lang);
  }

  optimizeTextFromAI(String text, BuildContext context) {
    setState(() {
      isAiProcessing = true;
    });
    HeapService.instance.track(
        'post_ia_description_generation_optimization',
        {
          'postId': widget.postsProvider.postToEdit.id,
          'postType': widget.postsProvider.postToEdit.postType?.name,
          'postSource': widget.postsProvider.postToEdit.source.name,
        },
        context: context);
    return widget.postDescriptionFieldsController
        .optimizeTextFromAI(text)
        .then((value) {
      widget.postDescriptionFieldsController.setPostDescription(value);
      setState(() {
        widget.postsProvider.interactionsManager
            .add(MinimalAiInteraction(id: const Uuid().v4(), text: value));
        isAiProcessing = false;
      });
    });
  }
}
