import 'package:flutter/material.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_description/widgets/platforms/meta/reel_cover/reel_cover_picker.controller.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_medias/widgets/post_media_picker/post_media_picker.widget.dart';
import 'package:malou/shared/widgets/appbar/appbar_back.widget.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';
import 'package:malou/shared/widgets/spinners/page_spinner.widget.dart';
import 'package:photo_manager_image_provider/photo_manager_image_provider.dart';
import 'package:provider/provider.dart';

import 'widgets/fitted_video_player.widget.dart';
import 'widgets/video_thumbnail_slider.widget.dart';

class ReelCoverPicker extends StatefulWidget {
  final Media media;
  const ReelCoverPicker({super.key, required this.media});

  @override
  State<ReelCoverPicker> createState() => _ReelCoverPickerState();
}

class _ReelCoverPickerState extends State<ReelCoverPicker> {
  ReelCoverPickerController pageController = ReelCoverPickerController();
  late Media? originalThumbnail;
  @override
  void initState() {
    originalThumbnail =
        Provider.of<PostsProvider>(context, listen: false).postToEdit.thumbnail;
    super.initState();
  }

  @override
  void dispose() {
    pageController.disposeController();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return Scaffold(
      appBar: AppBarArrowBack(
        height: 70,
        onPop: () async {
          Provider.of<PostsProvider>(context, listen: false)
              .updatePostThumbnail(originalThumbnail);
          return true;
        },
        icon: const Icon(
          MalouIcons.cross,
          size: 30,
          color: MalouColors.primary,
        ),
        actions: [
          IconButton(
            padding: const EdgeInsets.only(right: 12),
            onPressed: () async {
              PageSpinner.show(context);
              pageController.createPostThumbnailQueryResult.execute(context);
            },
            icon: const Icon(
              MalouIcons.check,
              size: 30,
              color: MalouColors.primary,
            ),
          )
        ],
      ),
      body: Stack(
        children: [
          FutureBuilder(
              future: pageController.initVideoController(widget.media),
              builder: (context, snapshot) {
                if (snapshot.hasData && snapshot.data == true) {
                  return SizedBox(
                    width: size.width,
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            height: 400,
                            child: Stack(
                              children: [
                                AspectRatio(
                                  aspectRatio: 9 / 16,
                                  child: FittedVideoPlayer(
                                    controller: pageController.videoController,
                                    height: 400,
                                  ),
                                ),
                                Consumer<PostsProvider>(
                                    builder: (context, postsProvider, child) {
                                  if (postsProvider.postToEdit.thumbnail !=
                                      null) {
                                    return AspectRatio(
                                        aspectRatio: 9 / 16,
                                        child: postsProvider.postToEdit
                                                    .thumbnail!.assetEntity !=
                                                null
                                            ? ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                child: AssetEntityImage(
                                                  postsProvider.postToEdit
                                                      .thumbnail!.assetEntity!,
                                                  isOriginal: true,
                                                  height: 400,
                                                  fit: BoxFit.cover,
                                                ),
                                              )
                                            : ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                child: Image.network(
                                                  postsProvider
                                                      .postToEdit.thumbnail!
                                                      .getMediaUrl(
                                                          size: 'original'),
                                                  fit: BoxFit.cover,
                                                  loadingBuilder: (context,
                                                          child,
                                                          loadingProgress) =>
                                                      loadingProgress == null
                                                          ? child
                                                          : const Center(
                                                              child:
                                                                  CircularSpinner(
                                                                color:
                                                                    MalouColors
                                                                        .primary,
                                                              ),
                                                            ),
                                                ),
                                              ));
                                  }
                                  return const SizedBox();
                                }),
                              ],
                            ),
                          ),
                          const SizedBox(
                            height: 40,
                          ),
                          VideoThumbnailSlider(
                            controller: pageController.videoController,
                            height: 80,
                            width: size.width - 50,
                            splitImage: 4,
                            backgroundColor: MalouColors.backgroundDark,
                          )
                        ]),
                  );
                }

                return const Center(
                  child: CircularSpinner(
                    color: MalouColors.primary,
                  ),
                );
              }),
          const Align(
            alignment: Alignment.bottomCenter,
            child: PostMediaPicker(
              isThumbnailPicker: true,
              mediaPickerType: MediaPickerType.image,
              maxAttachmentsBeforeResize: 1,
              minSize: 0.08,
              initialSize: 0.2,
              shouldAddPostAttachments: false,
            ),
          )
        ],
      ),
    );
  }
}
