import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/widgets/appbar/appbar_back.widget.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/platform/platform.provider.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/widgets/inputs/text_search.widget.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';

const VERYFRENCHBEANS_PAGE_ID = '100270695184218';

class PostLocationPage extends StatefulWidget {
  const PostLocationPage({super.key});

  @override
  State<PostLocationPage> createState() => _PostLocationPageState();
}

class _PostLocationPageState extends State<PostLocationPage> {
  late PostsProvider postsProvider;
  final TextEditingController controller = TextEditingController();
  final BehaviorSubject<String> search$ = BehaviorSubject.seeded('');
  final BehaviorSubject<bool> showLoader$ = BehaviorSubject.seeded(false);

  @override
  void initState() {
    postsProvider = Provider.of<PostsProvider>(context, listen: false);
    controller.text = postsProvider.postToEdit.location?.name ?? '';
    search$.add(controller.text);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppBarArrowBack(
          icon: Icon(
        MalouIcons.cross,
        color: MalouColors.primary,
      )),
      body: Builder(builder: (context) {
        return Consumer2<PostsProvider, PlatformProvider>(
            builder: (context, postsProvider, platformsProvider, child) {
          return GestureDetector(
            onTap: () => FocusScope.of(context).unfocus(),
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 16),
              shrinkWrap: true,
              children: [
                TextSearch(
                  showLoader: showLoader$,
                  controller: controller,
                  onChanged: (value) {
                    search$.add(value);
                  },
                  onClearText: onClearText,
                  hint: Translation.of(context).searchPlace,
                ),
                SizedBox(
                  height: MediaQuery.of(context).size.height * 0.8,
                  child: StreamBuilder(
                      stream: search$
                          .doOnData((event) {
                            showLoader$.add(event.isNotEmpty);
                          })
                          .debounceTime(const Duration(milliseconds: 500))
                          .doOnData((event) {
                            showLoader$.add(false);
                          }),
                      builder: ((context, snap) {
                        if (!snap.hasData) return const SizedBox();
                        String? searchText = snap.data;
                        if (searchText == null || searchText.isEmpty) {
                          return const SizedBox();
                        }

                        return FutureBuilder(
                            future: postsProvider.searchPageLocationByText(
                                searchText,
                                true,
                                [VERYFRENCHBEANS_PAGE_ID]).then((value) {
                              showLoader$.add(false);
                              return value;
                            }),
                            builder: (context, snapshot) {
                              return ListView.builder(
                                itemCount: snapshot.data?.length ?? 0,
                                itemBuilder: (context, index) {
                                  if (snapshot.data == null) {
                                    return const SizedBox();
                                  }
                                  PostLocation postLocation =
                                      snapshot.data![index];
                                  return ListTile(
                                    title: Text(postLocation.name),
                                    subtitle: Text(postLocation.location
                                            ?.getFormattedAddress() ??
                                        ''),
                                    onTap: () {
                                      postsProvider.postToEdit = postsProvider
                                          .postToEdit
                                          .copyWith(location: postLocation);
                                      Navigator.of(context).pop();
                                    },
                                  );
                                },
                              );
                            });
                      })),
                )
              ],
            ),
          );
        });
      }),
    );
  }

  void onClearText() {
    setState(() {
      controller.clear();
    });
  }
}
