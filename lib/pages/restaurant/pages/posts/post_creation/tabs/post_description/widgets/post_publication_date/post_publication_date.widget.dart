import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/date_picker/date_picker.theme.dart';
import 'package:malou/shared/widgets/date_picker/time_picker.theme.dart';
import 'package:malou/shared/widgets/disabled_widget/disabled_widget.widget.dart';
import 'package:provider/provider.dart';

class PostPublicationDate extends StatefulWidget {
  const PostPublicationDate({super.key});

  @override
  State<PostPublicationDate> createState() => _PostPublicationDateState();
}

class _PostPublicationDateState extends State<PostPublicationDate> {
  final List<PublicationDatePlanned> publicationDateOptions = [
    PublicationDatePlanned.now,
    PublicationDatePlanned.scheduled,
    PublicationDatePlanned.draft,
  ];

  @override
  Widget build(BuildContext context) {
    return Consumer<PostsProvider>(builder: (context, postsProvider, child) {
      Post post = postsProvider.postToEdit;
      DateTime postPlannedPublicationDate =
          (post.plannedPublicationDate ?? DateTime.now()).toLocal();
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(Translation.of(context).publicationDate,
              style: MalouTextStyles(context)
                  .text12Semibold
                  .copyWith(color: MalouColors.textTitle)),
          for (PublicationDatePlanned publicationDatePlanned
              in publicationDateOptions)
            DisabledWidget(
              disabled:
                  post.publicationDatePlanned != PublicationDatePlanned.draft &&
                      post.keys.isEmpty,
              child: Container(
                constraints: publicationDatePlanned !=
                            PublicationDatePlanned.now &&
                        post.publicationDatePlanned == publicationDatePlanned
                    ? null
                    : const BoxConstraints(maxHeight: 40),
                padding: EdgeInsets.zero,
                child: ListTile(
                  onTap: () {
                    postsProvider.postToEdit = postsProvider.postToEdit
                        .copyWith(
                            publicationDatePlanned: publicationDatePlanned);
                  },
                  titleAlignment: ListTileTitleAlignment.top,
                  contentPadding: EdgeInsets.zero,
                  title: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        publicationDatePlanned == PublicationDatePlanned.now
                            ? Translation.of(context).now
                            : publicationDatePlanned ==
                                    PublicationDatePlanned.draft
                                ? Translation.of(context).draft
                                : Translation.of(context).schedule,
                        style: MalouTextStyles(context)
                            .text12Medium
                            .copyWith(color: MalouColors.textTitle),
                      ),
                      Container(
                        constraints:
                            const BoxConstraints(maxHeight: 30, maxWidth: 30),
                        child: Radio(
                          materialTapTargetSize:
                              MaterialTapTargetSize.shrinkWrap,
                          visualDensity:
                              const VisualDensity(horizontal: 0, vertical: 0),
                          value: publicationDatePlanned,
                          groupValue: post.publicationDatePlanned,
                          onChanged: (value) {
                            postsProvider.postToEdit = postsProvider.postToEdit
                                .copyWith(publicationDatePlanned: value);
                          },
                        ),
                      ),
                    ],
                  ),
                  subtitle: publicationDatePlanned !=
                              PublicationDatePlanned.now &&
                          post.publicationDatePlanned == publicationDatePlanned
                      ? Container(
                          padding: const EdgeInsets.only(left: 20.0),
                          constraints: const BoxConstraints(maxHeight: 120),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              ListTile(
                                minVerticalPadding: 0,
                                contentPadding: EdgeInsets.zero,
                                minLeadingWidth: 0,
                                dense: true,
                                titleAlignment: ListTileTitleAlignment.center,
                                visualDensity: const VisualDensity(
                                    horizontal: 0, vertical: -3),
                                leading: Container(
                                    height: 7,
                                    width: 7,
                                    decoration: const BoxDecoration(
                                      color: MalouColors.primary,
                                      shape: BoxShape.circle,
                                    )),
                                title: Text(Translation.of(context).date,
                                    style: MalouTextStyles(context)
                                        .text12Semibold
                                        .copyWith(
                                            color: MalouColors.textTitle)),
                                subtitle: Text(
                                    DateFormat(
                                            'dd MMM yyyy',
                                            context
                                                .read<LanguageProvider>()
                                                .language
                                                .name)
                                        .format(postPlannedPublicationDate),
                                    style:
                                        MalouTextStyles(context).text12Italic),
                                trailing: const RotatedBox(
                                  quarterTurns: 2,
                                  child: Icon(
                                    MalouIcons.chevronLeft,
                                    color: MalouColors.primary,
                                  ),
                                ),
                                onTap: () {
                                  FocusScope.of(context).unfocus();
                                  showDatePicker(
                                    locale: Locale(context
                                        .read<LanguageProvider>()
                                        .language
                                        .name),
                                    context: context,
                                    initialDate: (postPlannedPublicationDate
                                                .isBefore(DateTime.now())
                                            ? DateTime.now()
                                                .add(const Duration(hours: 1))
                                                .toLocal()
                                            : postPlannedPublicationDate)
                                        .toLocal(),
                                    firstDate: DateTime.now().toLocal(),
                                    lastDate: DateTime.now()
                                        .toLocal()
                                        .add(const Duration(days: 365)),
                                    builder: datePickerThemeBuilder,
                                    helpText: null,
                                    confirmText:
                                        Translation.of(context).confirm,
                                    cancelText: Translation.of(context).cancel,
                                    initialEntryMode:
                                        DatePickerEntryMode.calendarOnly,
                                    initialDatePickerMode: DatePickerMode.day,
                                  ).then((value) {
                                    if (value != null) {
                                      setState(() {
                                        postsProvider.postToEdit =
                                            postsProvider.postToEdit.copyWith(
                                                plannedPublicationDate: DateTime(
                                                    value.year,
                                                    value.month,
                                                    value.day,
                                                    postPlannedPublicationDate
                                                        .hour,
                                                    postPlannedPublicationDate
                                                        .minute));
                                      });
                                    }
                                  });
                                },
                              ),
                              ListTile(
                                onTap: () {
                                  FocusScope.of(context).unfocus();
                                  showTimePicker(
                                          context: context,
                                          builder: timePickerThemeBuilder,
                                          confirmText:
                                              Translation.of(context).validate,
                                          cancelText:
                                              Translation.of(context).cancel,
                                          initialEntryMode:
                                              TimePickerEntryMode.dialOnly,
                                          initialTime: TimeOfDay.fromDateTime(
                                              postPlannedPublicationDate))
                                      .then((value) {
                                    if (value != null) {
                                      setState(() {
                                        postsProvider.postToEdit =
                                            postsProvider.postToEdit.copyWith(
                                                plannedPublicationDate: DateTime(
                                                    postPlannedPublicationDate
                                                        .year,
                                                    postPlannedPublicationDate
                                                        .month,
                                                    postPlannedPublicationDate
                                                        .day,
                                                    value.hour,
                                                    value.minute));
                                      });
                                    }
                                  });
                                },
                                minVerticalPadding: 0,
                                contentPadding: EdgeInsets.zero,
                                minLeadingWidth: 0,
                                dense: true,
                                titleAlignment: ListTileTitleAlignment.center,
                                visualDensity: const VisualDensity(
                                    horizontal: 0, vertical: -3),
                                leading: Container(
                                    height: 7,
                                    width: 7,
                                    decoration: const BoxDecoration(
                                      color: MalouColors.primary,
                                      shape: BoxShape.circle,
                                    )),
                                title: Text(Translation.of(context).hour,
                                    style: MalouTextStyles(context)
                                        .text12Semibold
                                        .copyWith(
                                            color: MalouColors.textTitle)),
                                subtitle: Text(
                                    DateFormat(
                                            DateFormat.HOUR_MINUTE,
                                            context
                                                .read<LanguageProvider>()
                                                .language
                                                .name)
                                        .format(postPlannedPublicationDate),
                                    style:
                                        MalouTextStyles(context).text12Italic),
                                trailing: const RotatedBox(
                                  quarterTurns: 2,
                                  child: Icon(
                                    MalouIcons.chevronLeft,
                                    color: MalouColors.primary,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                      : null,
                ),
              ),
            )
        ],
      );
    });
  }
}
