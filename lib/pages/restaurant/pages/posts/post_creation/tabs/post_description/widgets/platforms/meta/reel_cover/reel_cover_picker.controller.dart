import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_description/widgets/platforms/meta/reel_cover/utils/video_utils.dart';
import 'package:malou/shared/helpers/use_query.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/services/medias/medias.provider.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/widgets/spinners/page_spinner.widget.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:provider/provider.dart';
import 'package:video_player/video_player.dart';

class ReelCoverPickerController {
  late VideoPlayerController videoController;
  late QueryResult<void, BuildContext> createPostThumbnailQueryResult;

  Future<bool> initVideoController(Media media) async {
    createPostThumbnailQueryResult =
        useQuery<void, BuildContext, void>((BuildContext context) async {
      await onCoverPicked(context);
    }, onSuccess: (data, context, _) {
      PageSpinner.hide(context);
      Navigator.of(context).pop();
    }, onError: (error, context, _) {
      PageSpinner.hide(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(error.cause!['message']!),
          backgroundColor: MalouColors.error,
        ),
      );
    });

    if (media.assetEntity != null) {
      File file = (await media.assetEntity!.file)!;
      videoController = VideoPlayerController.file(file);
    } else {
      videoController =
          VideoPlayerController.networkUrl(Uri.parse(media.getMediaUrl()));
    }

    await videoController.initialize();

    return true;
  }

  Future<void> onCoverPicked(BuildContext context) async {
    MediasProvider mediasProvider =
        Provider.of<MediasProvider>(context, listen: false);
    RestaurantProvider restaurantProvider =
        Provider.of<RestaurantProvider>(context, listen: false);
    PostsProvider postsProvider =
        Provider.of<PostsProvider>(context, listen: false);

    if (postsProvider.postToEdit.thumbnail != null) {
      if (postsProvider.postToEdit.thumbnail!.assetEntity != null) {
        // upload new thumbnail
        Media media = await mediasProvider.uploadDeviceMedia(
            restaurantProvider.currentRestaurant!.id,
            postsProvider.postToEdit.thumbnail!.assetEntity!);
        postsProvider.updatePostThumbnail(media);
      }

      return;
    }

    Uint8List? imageData = await VideoUtils().getThumbnail(
        videoController.dataSource,
        position: videoController.value.position);

    if (imageData != null) {
      await PhotoManager.requestPermissionExtend().then((value) {
        if (value == PermissionState.denied) {
          throw Exception('Permission denied');
        }
      });
      AssetEntity? asset = await PhotoManager.editor
          .saveImage(
        imageData,
        filename: 'thumbnail-${DateTime.now()}.jpg',
        desc: 'thumbnail-${DateTime.now()}',
      )
          .catchError((error) {
        throw Exception(error);
      });

      Media media = await mediasProvider.uploadDeviceMedia(
          restaurantProvider.currentRestaurant!.id, asset);

      postsProvider.updatePostThumbnail(media);

      await PhotoManager.editor.deleteWithIds([asset.id]);
    } else {
      throw Exception('Error while getting thumbnail');
    }
  }

  void disposeController() {
    videoController.dispose();
  }
}
