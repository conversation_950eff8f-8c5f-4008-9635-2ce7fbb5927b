import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/widgets/appbar/appbar_back.widget.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/inputs/text_field.widget.dart';
import 'package:provider/provider.dart';

class ChooseActionUrlButtonPage extends StatefulWidget {
  final String ctaTitle;
  final String? initialValue;
  const ChooseActionUrlButtonPage(
      {super.key, required this.ctaTitle, this.initialValue});

  @override
  State<ChooseActionUrlButtonPage> createState() =>
      _ChooseActionUrlButtonPageState();
}

class _ChooseActionUrlButtonPageState extends State<ChooseActionUrlButtonPage> {
  late final TextEditingController controller;
  late RestaurantProvider rp;

  String? selectedSavedLink;
  bool invalidUrl = false;

  @override
  void initState() {
    controller = TextEditingController(text: widget.initialValue ?? '');
    rp = Provider.of<RestaurantProvider>(context, listen: false);
    if (widget.initialValue != null &&
        rp.currentRestaurant!.relatedUrls.contains(widget.initialValue)) {
      selectedSavedLink = widget.initialValue;
    }
    invalidUrl = !isUrlValid(controller.text);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<PostsProvider>(builder: (context, postsProvider, child) {
      return Scaffold(
          appBar: AppBarArrowBack(
            title: widget.ctaTitle,
            height: 70,
            actions: [
              IconButton(
                onPressed: invalidUrl
                    ? null
                    : () {
                        Navigator.of(context).pop(controller.text);
                      },
                icon: Icon(MalouIcons.check,
                    color:
                        MalouColors.primary.withOpacity(invalidUrl ? 0.5 : 1)),
              )
            ],
          ),
          body: Consumer<RestaurantProvider>(
              builder: (context, restaurantProvider, child) {
            return GestureDetector(
              onTap: () {
                FocusScope.of(context).unfocus();
              },
              child: ListView(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                children: [
                  MalouTextFormField(
                    textCapitalization: TextCapitalization.none,
                    autocorrect: false,
                    validator: (value) {
                      if (value == null ||
                          value.isEmpty ||
                          !isUrlValid(value)) {
                        invalidUrl = true;
                        return Translation.of(context).invalidLink;
                      }

                      invalidUrl = false;

                      return null;
                    },
                    showBorders: true,
                    contentPadding: const EdgeInsets.all(15),
                    maxLines: 1,
                    controller: controller,
                    onChanged: (value) {
                      List<String> values = restaurantProvider
                          .currentRestaurant!.relatedUrls
                          .where((element) => element != null)
                          .cast<String>()
                          .toList();
                      setState(() {
                        selectedSavedLink = null;
                        if (values.contains(value)) {
                          selectedSavedLink = value;
                        }
                      });
                    },
                    hint: 'https://website.com',
                  ),
                  SizedBox(height: MediaQuery.of(context).size.height * 0.1),
                  if (restaurantProvider
                      .currentRestaurant!.relatedUrls.isNotEmpty)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          Translation.of(context).savedLinks,
                          style:
                              MalouTextStyles(context).text13Semibold.copyWith(
                                    color: MalouColors.textTitle,
                                  ),
                        ),
                        const SizedBox(height: 10),
                        for (String link in restaurantProvider
                            .currentRestaurant!.relatedUrls
                            .where((element) => element != null)
                            .cast()
                            .toList())
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                selectedSavedLink = link;
                                controller.text = link;
                                invalidUrl = !isUrlValid(link);
                              });
                            },
                            child: Container(
                              margin: const EdgeInsets.only(bottom: 10),
                              padding: const EdgeInsets.all(15),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  color: selectedSavedLink == link
                                      ? MalouColors.backgroundDark
                                      : Colors.white,
                                  border: Border.all(
                                    color: selectedSavedLink == link
                                        ? MalouColors.borderSecondary
                                        : MalouColors.borderPrimary,
                                    width: 1,
                                  )),
                              child: Center(
                                child: Text(link,
                                    style: MalouTextStyles(context)
                                        .text13Semibold),
                              ),
                            ),
                          )
                      ],
                    ),
                ],
              ),
            );
          }));
    });
  }

  bool isUrlValid(String url) {
    return Uri.parse(url).isAbsolute;
  }
}
