import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/chips/malou_chip.widget.dart';
import 'package:provider/provider.dart';
import 'choose_action_button.page.widget.dart';

class ChooseActionButton extends StatelessWidget {
  const ChooseActionButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<PostsProvider>(builder: (context, postProvider, child) {
      Post post = postProvider.postToEdit;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                Translation.of(context).chooseActionButton,
                style: MalouTextStyles(context)
                    .text12Semibold
                    .copyWith(color: MalouColors.textTitle),
              ),
              const Spacer(),
              if (post.source == Source.social)
                Tooltip(
                  message: Translation.of(context).onlyAvailableOnMapstr,
                  child: const Icon(Icons.info_outline_rounded,
                      color: MalouColors.primary, size: 25),
                ),
              RotatedBox(
                quarterTurns: 2,
                child: IconButton(
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  onPressed: () => navigateToConfigureActionButtonPage(context),
                  icon: const Icon(
                    MalouIcons.chevronLeft,
                    color: MalouColors.primary,
                  ),
                ),
              )
            ],
          ),
          if (postProvider.postToEdit.callToAction?.actionType != null)
            MalouChip(
              text: post.displayCallToActionName(context),
              onClosed: () {
                Post post = postProvider.postToEdit;
                post.callToAction = null;
                postProvider.postToEdit = post;
              },
            )
        ],
      );
    });
  }

  void navigateToConfigureActionButtonPage(BuildContext context) {
    Navigator.of(context).push(MaterialPageRoute(
      builder: (context) => const ChooseActionButtonPage(),
    ));
  }
}
