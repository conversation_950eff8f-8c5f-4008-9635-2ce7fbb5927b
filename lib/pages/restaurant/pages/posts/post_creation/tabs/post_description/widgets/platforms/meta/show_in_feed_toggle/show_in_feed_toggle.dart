import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:provider/provider.dart';

class ShowInFeedToggle extends StatefulWidget {
  const ShowInFeedToggle({super.key});

  @override
  State<ShowInFeedToggle> createState() => _ShowInFeedToggleState();
}

class _ShowInFeedToggleState extends State<ShowInFeedToggle> {
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(Translation.of(context).showInFeed,
            style: MalouTextStyles(context)
                .text12Semibold
                .copyWith(color: MalouColors.textTitle)),
        Consumer<PostsProvider>(builder: (context, postsProvider, child) {
          return Switch(
            value: postsProvider.postToEdit.isReelDisplayedInFeed,
            onChanged: (value) {
              postsProvider.postToEdit = postsProvider.postToEdit.copyWith(
                isReelDisplayedInFeed: value,
              );
            },
          );
        })
      ],
    );
  }
}
