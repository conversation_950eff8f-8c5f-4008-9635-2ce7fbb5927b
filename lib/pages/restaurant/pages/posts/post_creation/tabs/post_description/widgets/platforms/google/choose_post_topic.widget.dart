import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/helpers/modal_bottom_sheet.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/chips/malou_chip.widget.dart';
import 'package:provider/provider.dart';

class PostChooseTopic extends StatelessWidget {
  final Post post;
  const PostChooseTopic({
    super.key,
    required this.post,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              Translation.of(context).postTopic,
              style: MalouTextStyles(context)
                  .text12Semibold
                  .copyWith(color: MalouColors.textTitle),
            ),
            const Spacer(),
            RotatedBox(
              quarterTurns: 2,
              child: IconButton(
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                onPressed: () => navigateToChooseLocationPage(context),
                icon: const Icon(
                  MalouIcons.chevronLeft,
                  color: MalouColors.primary,
                ),
              ),
            )
          ],
        ),
        if (post.postTopic != null)
          MalouChip(text: Post.getPostTopicText(context, post.postTopic!))
      ],
    );
  }

  void navigateToChooseLocationPage(BuildContext context) {
    List<PostTopic> topics = [
      PostTopic.STANDARD,
      PostTopic.EVENT,
      PostTopic.OFFER
    ];
    AppBottomModal.showAppBottomModal(context,
        Consumer<PostsProvider>(builder: (context, postsProvider, child) {
      return Column(children: [
        for (var topic in topics)
          ListTile(
            title: Text(
              Post.getPostTopicText(context, topic),
              style: MalouTextStyles(context)
                  .text13Semibold
                  .copyWith(color: MalouColors.textBody),
            ),
            trailing: topic == postsProvider.postToEdit.postTopic
                ? const Icon(Icons.check, color: MalouColors.primary)
                : null,
            onTap: () {
              context.pop();
              postsProvider.postToEdit = postsProvider.postToEdit.copyWith(
                  postTopic: topic,
                  event: topic != PostTopic.STANDARD
                      ? Event(
                          title: postsProvider.postToEdit.event?.title ?? '',
                          startDate:
                              postsProvider.postToEdit.event?.startDate ??
                                  DateTime.now(),
                          endDate: postsProvider.postToEdit.event?.endDate ??
                              DateTime.now().add(const Duration(days: 1)),
                        )
                      : null,
                  offer: topic == PostTopic.OFFER
                      ? Offer(
                          couponCode:
                              postsProvider.postToEdit.offer?.couponCode ?? '',
                          onlineUrl:
                              postsProvider.postToEdit.offer?.onlineUrl ?? '',
                          termsConditions:
                              postsProvider.postToEdit.offer?.termsConditions ??
                                  '',
                        )
                      : null);
            },
          )
      ]);
    }), maxSize: 0.25, minSize: 0.25);
  }
}
