import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_description/widgets/platforms/google/choose_event_duration.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_description/widgets/platforms/google/choose_offer_additionnal_informations.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_description/widgets/platforms/meta/show_in_feed_toggle/show_in_feed_toggle.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_description/widgets/post_description/post_description.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_description/widgets/post_description_field.controller.dart';
import 'package:malou/shared/helpers/utils.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/widgets/inputs/text_field.widget.dart';
import 'package:provider/provider.dart';

import 'platforms/google/choose_post_keywords.widget.dart';
import 'platforms/google/choose_post_topic.widget.dart';
import 'platforms/meta/post_hashtags/post_hashtags.widget.dart';
import 'post_call_to_action/choose_action_button.widget.dart';
import 'post_location/post_location.widget.dart';
import 'post_publication_date/post_publication_date.widget.dart';

class PostDescriptionFields extends StatefulWidget {
  const PostDescriptionFields({super.key});

  @override
  State<PostDescriptionFields> createState() => _PostDescriptionFieldsState();
}

class _PostDescriptionFieldsState extends State<PostDescriptionFields> {
  late PostDescriptionFieldsController postDescriptionFieldsController;

  @override
  void initState() {
    postDescriptionFieldsController = PostDescriptionFieldsController(
      postsProvider: Provider.of<PostsProvider>(context, listen: false),
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<PostsProvider>(builder: (context, postsProvider, child) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...buildWidgetsBySelectedPlatforms(
              postsProvider.postToEdit.keys, postsProvider),
          const SizedBox(
            height: 20,
          )
        ],
      );
    });
  }

  List<Widget> buildWidgetsBySelectedPlatforms(
      List<PlatformKeys> platformKeys, PostsProvider postsProvider) {
    List<PlatformKeys> metaPlatformsKeys = [
      PlatformKeys.facebook,
      PlatformKeys.instagram
    ];

    Map<String, Widget?> widgets = {
      'topic': _arePlatformsSelected(platformKeys, [PlatformKeys.gmb])
          ? PostChooseTopic(post: postsProvider.postToEdit)
          : null,
      'title': _arePlatformsSelected(platformKeys, [PlatformKeys.mapstr])
          ? MalouTextFormField(
              initialValue: postsProvider.postToEdit.title,
              maxLength: 60,
              onChanged: debounceTime((value) => postsProvider.postToEdit =
                  postsProvider.postToEdit.copyWith(title: value)),
              validator: (value) => value!.isEmpty &&
                      postsProvider.postToEdit.publicationDatePlanned !=
                          PublicationDatePlanned.draft
                  ? Translation.of(context).mandatoryField
                  : null,
              hint: Translation.of(context).writeTitleHere,
              label: Translation.of(context).titleLabel,
              showInfoIcon: true,
              tooltipMessage: Translation.of(context).onlyAvailableOnMapstr)
          : null,
      'description': PostDescription(
          postsProvider: postsProvider,
          platformKeys: platformKeys,
          postDescriptionFieldsController: postDescriptionFieldsController),
      'keywords': _arePlatformsSelected(platformKeys, [PlatformKeys.gmb])
          ? const ChoosePostKeywords()
          : null,
      'titleGoogle': _arePlatformsSelected(platformKeys, [PlatformKeys.gmb]) &&
              postsProvider.postToEdit.postTopic != PostTopic.STANDARD
          ? MalouTextFormField(
              validator: postsProvider.postToEdit.postTopic == PostTopic.STANDARD
                  ? null
                  : (value) => value!.isEmpty && postsProvider.postToEdit.publicationDatePlanned != PublicationDatePlanned.draft
                      ? Translation.of(context).mandatoryField
                      : null,
              initialValue: postsProvider.postToEdit.event?.title ?? '',
              maxLength: 58,
              onChanged: debounceTime((value) => postsProvider.postToEdit =
                  postsProvider.postToEdit.copyWith(
                      event: postsProvider.postToEdit.event?.copyWith(title: value) ??
                          Event(title: value))),
              hint: Translation.of(context).writeTitleHere,
              label: postsProvider.postToEdit.postTopic == PostTopic.STANDARD
                  ? Translation.of(context).addTitle
                  : '${Translation.of(context).addTitle} *')
          : null,
      'hashtags': _arePlatformsSelected(platformKeys, metaPlatformsKeys)
          ? const PostHashtags()
          : null,
      'location': _arePlatformsSelected(
              platformKeys, [...metaPlatformsKeys, PlatformKeys.mapstr])
          ? const PostLocationDisplay()
          : null,
      'actionButton': _arePlatformsSelected(
              platformKeys, [PlatformKeys.mapstr, PlatformKeys.gmb])
          ? _arePlatformsSelected(platformKeys, [PlatformKeys.gmb])
              ? postsProvider.postToEdit.postTopic == PostTopic.STANDARD ||
                      postsProvider.postToEdit.postTopic == PostTopic.EVENT
                  ? const ChooseActionButton()
                  : null
              : const ChooseActionButton()
          : null,
      'eventDuration':
          _arePlatformsSelected(platformKeys, [PlatformKeys.gmb]) &&
                  postsProvider.postToEdit.postTopic != PostTopic.STANDARD
              ? ChooseEventDuration(post: postsProvider.postToEdit)
              : null,
      'offerAddtionalInformations': _arePlatformsSelected(
                  platformKeys, [PlatformKeys.gmb]) &&
              postsProvider.postToEdit.postTopic == PostTopic.OFFER
          ? ChooseOfferAdditionnalInformations(post: postsProvider.postToEdit)
          : null,
      'taggedAccounts': _arePlatformsSelected(platformKeys, metaPlatformsKeys)
          ? // PostTaggedAccounts(post: postsProvider.postToEdit)
          null
          : null,
      'publicationDate': const PostPublicationDate(),
      'showInFeed':
          _arePlatformsSelected(platformKeys, [PlatformKeys.instagram]) &&
                  postsProvider.postToEdit.postType == PostType.REEL
              ? const ShowInFeedToggle()
              : null,
    };
    return widgets.values
        .map((e) => e == null
            ? const SizedBox()
            : Container(
                margin: const EdgeInsets.only(top: 20),
                child: postsProvider.postToEdit.canBeEdited()
                    ? e
                    : Opacity(
                        opacity: 0.7,
                        child: IgnorePointer(
                          child: e,
                        ),
                      )))
        .toList();
  }
}

bool _arePlatformsSelected(List<PlatformKeys> platformKeys,
    List<PlatformKeys> platformsKeysToCheckFor) {
  return platformKeys
      .where((element) => platformsKeysToCheckFor.contains(element))
      .isNotEmpty;
}
