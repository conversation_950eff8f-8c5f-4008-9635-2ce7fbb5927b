import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/constant.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/platform/platform.provider.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:provider/provider.dart';

class PostDescriptionPlatformsSelection extends StatefulWidget {
  const PostDescriptionPlatformsSelection({super.key});

  @override
  State<PostDescriptionPlatformsSelection> createState() =>
      _PostDescriptionPlatformsSelectionState();
}

class _PostDescriptionPlatformsSelectionState
    extends State<PostDescriptionPlatformsSelection> {
  @override
  Widget build(BuildContext context) {
    return Consumer<PostsProvider>(builder: (context, postsProvider, child) {
      Post postToEdit = postsProvider.postToEdit;
      PlatformProvider platformProvider = context.read<PlatformProvider>();
      List<PlatformKeys> platforms = postToEdit.source == Source.seo
          ? [PlatformKeys.gmb]
          : postToEdit.postType == PostType.REEL
              ? platformProvider.socialNetworkPlatformsKeys
                  .where((element) => element != PlatformKeys.mapstr)
                  .toList()
              : platformProvider.socialNetworkPlatformsKeys;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            Translation.of(context).postOnPlatforms,
            style: MalouTextStyles(context)
                .text12Semibold
                .copyWith(color: MalouColors.textTitle),
          ),
          const SizedBox(height: 10),
          StreamBuilder(
              stream: postToEdit.source == Source.seo
                  ? platformProvider.connectedPostsPlatforms$.map((event) =>
                      event.where(
                          (element) => element.key == PlatformKeys.gmb.name))
                  : platformProvider.connectedSocialNetworksPlatforms$,
              builder: (context, snapshot) {
                if (!snapshot.hasData) {
                  return Container();
                } else {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      for (var platform in platforms)
                        platform == PlatformKeys.mapstr &&
                                mapstrHasCredentials(
                                    snapshot, PlatformKeys.mapstr) &&
                                isPlatformConnected(snapshot, platform) &&
                                postToEdit.canBeEdited()
                            ? const SizedBox()
                            : Opacity(
                                opacity:
                                    isPlatformConnected(snapshot, platform) &&
                                            postToEdit.canBeEdited()
                                        ? 1
                                        : 0.7,
                                child: Row(
                                  children: [
                                    Image.asset(
                                      PlatformDetails.resolvePlatformLogo(
                                          platform),
                                      width: 29,
                                      height: 29,
                                    ),
                                    const SizedBox(width: 10),
                                    Text(
                                      AppConstant.getDetails(platform).name,
                                      style: MalouTextStyles(context)
                                          .text12
                                          .copyWith(
                                            color: MalouColors.textTitle,
                                          ),
                                    ),
                                    const Spacer(),
                                    SizedBox(
                                      width: 40,
                                      child: Switch(
                                        value: platform == PlatformKeys.mapstr
                                            ? !mapstrHasCredentials(snapshot,
                                                    PlatformKeys.mapstr) &&
                                                postToEdit.keys
                                                    .map((e) => e.name)
                                                    .contains(platform.name) &&
                                                isPlatformConnected(
                                                    snapshot, platform)
                                            : postToEdit.keys
                                                    .map((e) => e.name)
                                                    .contains(platform.name) &&
                                                isPlatformConnected(
                                                    snapshot, platform),
                                        onChanged: platform ==
                                                PlatformKeys.mapstr
                                            ? mapstrHasCredentials(snapshot,
                                                        PlatformKeys.mapstr) ||
                                                    !postToEdit.canBeEdited()
                                                ? null
                                                : onPlatformSwitchChanged(
                                                    snapshot,
                                                    platform,
                                                    postToEdit,
                                                    postsProvider)
                                            : onPlatformSwitchChanged(
                                                snapshot,
                                                platform,
                                                postToEdit,
                                                postsProvider),
                                      ),
                                    )
                                  ],
                                ),
                              )
                    ],
                  );
                }
              })
        ],
      );
    });
  }

  bool mapstrHasCredentials(
      AsyncSnapshot<Iterable<Platform>> snapshot, PlatformKeys platform) {
    return snapshot.data!
            .cast<Platform?>()
            .firstWhere((element) => element?.key == platform.name,
                orElse: () => null)
            ?.credentials
            ?.isEmpty ??
        true;
  }

  bool isPlatformConnected(
      AsyncSnapshot<Iterable<Platform>> snapshot, PlatformKeys platform) {
    return snapshot.data!.map((e) => e.key).contains(platform.name);
  }

  Function(bool)? onPlatformSwitchChanged(
      AsyncSnapshot<Iterable<Platform>> snapshot,
      PlatformKeys platform,
      Post postToEdit,
      PostsProvider postsProvider) {
    return snapshot.data!.map((e) => e.key).contains(platform.name) &&
            postToEdit.canBeEdited()
        ? (_) {
            setState(() {
              if (postToEdit.keys.map((e) => e.name).contains(platform.name)) {
                postsProvider.postToEdit = postsProvider.postToEdit.copyWith(
                    keys: postToEdit.keys
                      ..removeWhere((e) => e.name == platform.name));
              } else {
                postsProvider.postToEdit = postsProvider.postToEdit
                    .copyWith(keys: postToEdit.keys..add(platform));
              }
            });
          }
        : null;
  }
}
