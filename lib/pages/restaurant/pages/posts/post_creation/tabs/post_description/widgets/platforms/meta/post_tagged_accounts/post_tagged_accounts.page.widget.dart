import 'package:flutter/material.dart';
import 'package:malou/shared/widgets/appbar/appbar_back.widget.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/services/platform/platform.provider.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/widgets/inputs/text_field.widget.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';

class PostTaggedAccountsPage extends StatefulWidget {
  const PostTaggedAccountsPage({super.key});

  @override
  State<PostTaggedAccountsPage> createState() => _PostTaggedAccountsPageState();
}

class _PostTaggedAccountsPageState extends State<PostTaggedAccountsPage> {
  late PostsProvider postsProvider;
  final TextEditingController controller = TextEditingController();
  final BehaviorSubject<String> search$ = BehaviorSubject.seeded('');
  final BehaviorSubject<bool> showLoader$ = BehaviorSubject.seeded(false);

  @override
  void initState() {
    postsProvider = Provider.of<PostsProvider>(context, listen: false);
    controller.text = postsProvider.postToEdit.location?.name ?? '';
    search$.add(controller.text);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarArrowBack(
        height: 70,
        icon: const Icon(MalouIcons.cross, color: MalouColors.primary),
        actions: [
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.check, color: MalouColors.primary),
            iconSize: 30,
          ),
        ],
      ),
      body: Builder(builder: (context) {
        return Consumer2<PostsProvider, PlatformProvider>(
            builder: (context, postsProvider, platformsProvider, child) {
          return GestureDetector(
            onTap: () => FocusScope.of(context).unfocus(),
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 14),
              shrinkWrap: true,
              children: [
                MalouTextFormField(
                  showBorders: true,
                  contentPadding: const EdgeInsets.all(15),
                  maxLines: 1,
                  controller: controller,
                  onChanged: (value) {
                    search$.add(value);
                  },
                  hint: 'http://website.com',
                ),
                SizedBox(
                  height: MediaQuery.of(context).size.height * 0.8,
                )
              ],
            ),
          );
        });
      }),
    );
  }
}
