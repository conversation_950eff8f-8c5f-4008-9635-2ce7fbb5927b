import 'dart:io';

import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

class FittedVideoPlayer extends StatefulWidget {
  const FittedVideoPlayer(
      {required this.controller, this.height = 300, Key? key})
      : super(key: key);
  final VideoPlayerController controller;
  final double height;

  @override
  State<FittedVideoPlayer> createState() => _FittedVideoPlayerState();
}

class _FittedVideoPlayerState extends State<FittedVideoPlayer> {
  late double width = MediaQuery.of(context).size.width;
  late double height = widget.height;
  @override
  void initState() {
    super.initState();
  }

  void getVideoRatio() {
    if (widget.controller.value.isInitialized) {
      updateWidthHeight();
    }
  }

  void updateWidthHeight() {
    final ratio = widget.controller.value.aspectRatio;
    if (height * ratio <= width) {
      width = height * ratio;
    } else {
      height = width / ratio;
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    updateWidthHeight();
    return Center(
      child: SizedBox(
        width: width,
        height: height,
        child: VideoPlayerView(
          videoController: widget.controller,
        ),
      ),
    );
  }
}

class VideoPlayerView extends StatefulWidget {
  const VideoPlayerView({this.videoController, this.media, Key? key})
      : super(key: key);
  final VideoPlayerController? videoController;
  final File? media;
  @override
  State<VideoPlayerView> createState() => _VideoPlayerViewState();
}

class _VideoPlayerViewState extends State<VideoPlayerView> {
  late final _videoController = widget.videoController;
  @override
  void initState() {
    initVideoController();
    super.initState();
  }

  void initVideoController() async {
    if (!_videoController!.value.isInitialized) {
      await _videoController?.initialize();
    }
    setState(() {});
  }

  @override
  void dispose() {
    _videoController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: VideoPlayer(_videoController!));
  }
}
