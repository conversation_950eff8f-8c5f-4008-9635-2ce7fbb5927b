import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/use_query.dart';
import 'package:malou/shared/models/brick.model.dart';
import 'package:malou/shared/models/keywords/keyword.model.dart';
import 'package:malou/shared/models/keywords/keywords_analysis.model.dart';
import 'package:malou/shared/models/keywords/keywords_score.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/keyword/keyword.provider.dart';
import 'package:malou/shared/services/keyword/keywords.types.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/chips/malou_chip.widget.dart';
import 'package:malou/shared/widgets/score-gauge/score_gauge.widget.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';

class ChoosePostKeywords extends StatefulWidget {
  const ChoosePostKeywords({super.key});

  @override
  State<ChoosePostKeywords> createState() => _ChoosePostKeywordsState();
}

class _ChoosePostKeywordsState extends State<ChoosePostKeywords> {
  late KeywordProvider keywordProvider;
  late PostsProvider postsProvider;
  late QueryResult<KeywordsScoreProcessResult,
      ComputeKeywordsScoreAndTipsParams> computeKeywordsScoreAndTipsQuery;

  GlobalKey iconKey = GlobalKey();
  double bottomOffset = 0;

  @override
  void initState() {
    super.initState();
    keywordProvider = Provider.of<KeywordProvider>(context, listen: false);
    postsProvider = Provider.of<PostsProvider>(context, listen: false);
    computeKeywordsScoreAndTipsQuery =
        useQuery(keywordProvider.computeKeywordsScoreAndTips);

    postsProvider.postToEdit.postTextController!.textObs$
        .debounceTime(const Duration(milliseconds: 250))
        .listen((event) {
      computeKeywordsScoreAndTipsQuery.execute((
        restaurant: context.read<RestaurantProvider>().currentRestaurant!,
        textToCompute: event ?? "",
        textType: KeywordScoreTextType.post,
        lang: null,
        reviewerName: null,
        responseTime: null,
        isLowRatedReview: null,
        reviewText: null,
        reviewerNameValidation: null
      ));
    });

    computeKeywordsScoreAndTipsQuery.data.listen((keywordScore) {
      if (keywordScore == null) return;

      String targetLanguage = KeywordHelper.getLangWithMostOccurences(
          keywordProvider.selectedKeywords.value);
      List<SimpleBrick> bricksAssociatedWithKeywords =
          KeywordHelper.buildSimpleBricks(
              keywordProvider.selectedKeywords.value,
              context.read<RestaurantProvider>().currentRestaurant ??
                  context.read<RestaurantProvider>().userRestaurants.firstWhere(
                      (r) => r.id == postsProvider.postToEdit.restaurantId),
              targetLanguage);
      var bricks = bricksAssociatedWithKeywords
          .map((brick) => (
                brick: brick,
                selected: keywordProvider.isBrickUsed(
                    brick, keywordScore, targetLanguage)
              ))
          .toList();

      postsProvider.postToEdit = postsProvider.postToEdit.copyWith(
          keywordAnalysis: KeywordAnalysis(
        keywords: keywordScore.bricksFound.map((e) => e.text).toList(),
        count: keywordScore.bricksFound.length,
        score: keywordScore.score,
      ));
      postsProvider.brickSuggestions$.add(bricks);
      postsProvider.currentScore$.add(keywordScore.score);
    });
  }

  void onTapKeyword(
      int indexPost, post, ({SimpleBrick brick, bool selected}) element) {
    // Optimistic update, to get rid of lambda delay
    var bricksTemp = [...postsProvider.brickSuggestions$.value];
    bricksTemp[indexPost] = (brick: element.brick, selected: true);
    postsProvider.brickSuggestions$.add(bricksTemp);
    // end optimistic update

    var offset =
        postsProvider.postToEdit.postTextController?.selection.base.offset ?? 0;
    if (offset == -1) offset = 0;
    String? text = postsProvider.postToEdit.postTextController?.text;
    String keyword = element.brick.text;
    if (text!.isNotEmpty) offset = offset.clamp(0, text.length);
    text = text.substring(0, offset) + keyword + text.substring(offset);
    if (offset > 0 && !(text[offset - 1] == ' ')) {
      text = '${text.substring(0, offset)} ${text.substring(offset)}';
      offset++;
    }
    if (offset + keyword.length < text.length &&
        !(text[offset + keyword.length] == ' ')) {
      text =
          '${text.substring(0, offset + keyword.length)} ${text.substring(offset + keyword.length)}';
    }
    int newOffset = offset + keyword.length + 1;

    setState(() {
      postsProvider.postToEdit.postTextController?.value =
          TextEditingValue(text: text ?? post.text);
      // cursor
      postsProvider.postToEdit.postTextController?.value = TextEditingValue(
        text: text!,
        selection: TextSelection.collapsed(offset: newOffset),
      );
    });

    postsProvider.postToEdit = postsProvider.postToEdit.copyWith(
        text:
            '${post.text != null ? "${post.text} " : ''}${element.brick.text} ');
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<PostsProvider, KeywordProvider, RestaurantProvider>(
        builder: (context, postsProvider, keywordProvider, restaurantProvider,
            child) {
      Post post = postsProvider.postToEdit;

      return StreamBuilder<List<RestaurantKeyword>>(
          stream: keywordProvider.selectedKeywords,
          builder: (context, snapshot) {
            List<RestaurantKeyword> availableKeywords = snapshot.data ?? [];
            return Stack(
              clipBehavior: Clip.none,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        RichText(
                          text: TextSpan(
                              text: Translation.of(context).keywords,
                              style: MalouTextStyles(context)
                                  .text12Semibold
                                  .copyWith(color: MalouColors.textTitle),
                              children: [
                                TextSpan(
                                  text:
                                      ' (${countKeywordsInPostText(post.text ?? '', availableKeywords)})',
                                  style: MalouTextStyles(context).text12Italic,
                                )
                              ]),
                        ),
                        const Spacer(),
                        StreamBuilder<KeywordsScoreProcessResult?>(
                            stream: computeKeywordsScoreAndTipsQuery.data,
                            builder: (context, snapshot) {
                              return Row(
                                children: [
                                  StreamBuilder(
                                    stream: postsProvider.currentScore$,
                                    builder: (context, snapshot) => ScoreGauge(
                                      score: snapshot.data ?? 0,
                                      onWhiteBackground: true,
                                      displayLabel: false,
                                    ),
                                  ),
                                  const SizedBox(
                                    width: 10,
                                  ),
                                  GestureDetector(
                                    onTap: () => setState(() {
                                      bottomOffset = getBottomOffset();
                                      showTooltipOverlay(
                                          context, postsProvider);
                                    }),
                                    child: Icon(
                                      Icons.info_outline,
                                      key: iconKey,
                                      size: 25,
                                      color: MalouColors.primary,
                                    ),
                                  )
                                ],
                              );
                            })
                      ],
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    StreamBuilder<List<({SimpleBrick brick, bool selected})>>(
                        stream: postsProvider.brickSuggestions$,
                        builder: (context, snapshot) {
                          if (snapshot.data == null) return const SizedBox();
                          return Wrap(
                            spacing: 6,
                            runSpacing: 6.0,
                            children: snapshot.data!
                                .mapIndexed(
                                    (idx, element) => MalouChipSelectable(
                                          text: element.brick.text,
                                          selected: element.selected,
                                          onTap: () {
                                            onTapKeyword(idx, post, element);
                                          },
                                        ))
                                .toList(),
                          );
                        })
                  ],
                ),
              ],
            );
          });
    });
  }

  void showTooltipOverlay(BuildContext context, PostsProvider postsProvider) {
    BehaviorSubject<bool> showTooltip$ = BehaviorSubject.seeded(true);
    final OverlayEntry entry = OverlayEntry(builder: (context) {
      return SizedBox(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        child: Stack(
          children: [
            ModalBarrier(
              color: Colors.transparent,
              dismissible: true,
              onDismiss: () {
                showTooltip$.add(false);
              },
            ),
            StreamBuilder<KeywordsScoreProcessResult?>(
                stream: computeKeywordsScoreAndTipsQuery.data,
                builder: (context, snapshot) {
                  if (snapshot.data == null) return const SizedBox();
                  return Animate(
                    effects: const [
                      FadeEffect(duration: Duration(milliseconds: 200)),
                    ],
                    child: Positioned(
                      key: GlobalKey(),
                      top: bottomOffset +
                          30 +
                          postsProvider.scrollController.offset -
                          getHeightFromTextLength(postsProvider
                                  .postToEdit.postTextController?.text ??
                              ''),
                      right: 30,
                      child: Container(
                        width: MediaQuery.of(context).size.width * 0.8,
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: MalouColors.textTitle,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: snapshot.data!.details
                              .map((tip) => Container(
                                    margin: const EdgeInsets.only(bottom: 10),
                                    child: Row(
                                      children: [
                                        tip.fulfilled
                                            ? const Icon(
                                                Icons.check,
                                                color: MalouColors.success,
                                              )
                                            : const Icon(
                                                Icons.close,
                                                color: MalouColors.primary,
                                              ),
                                        const SizedBox(
                                          width: 10,
                                        ),
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            if (tip.criteria !=
                                                KeywordCriteria
                                                    .restaurantName) ...[
                                              Text(
                                                "${tip.getTitle(context)} ${tip.value}/${tip.fulfilledValue?.optimalValue ?? tip.fulfilledValue?.minimumValue}",
                                                style: MalouTextStyles(context)
                                                    .text10Semibold
                                                    .copyWith(
                                                        color: Colors.white),
                                              ),
                                            ] else ...[
                                              Text(
                                                tip.getTitle(context),
                                                style: MalouTextStyles(context)
                                                    .text10Semibold
                                                    .copyWith(
                                                        color: Colors.white),
                                              )
                                            ],
                                            if (tip.getTip(context) != null &&
                                                tip.getTip(context)!.isNotEmpty)
                                              SizedBox(
                                                width: 200,
                                                child: Text(
                                                    tip.getTip(context)!,
                                                    softWrap: true,
                                                    style:
                                                        MalouTextStyles(context)
                                                            .text10Italic
                                                            .copyWith(
                                                                color: Colors
                                                                    .white)),
                                              ),
                                          ],
                                        )
                                      ],
                                    ),
                                  ))
                              .toList(),
                        ),
                      ),
                    ),
                  )
                      .then(
                        delay: const Duration(seconds: 3),
                      )
                      .fadeOut(
                        duration: const Duration(milliseconds: 200),
                      );
                }),
          ],
        ),
      );
    });
    Overlay.of(context).insert(entry);
    showTooltip$.listen((event) {
      if (!event) {
        entry.remove();
      }
    });
    Future.delayed(const Duration(seconds: 3, milliseconds: 300), () {
      entry.remove();
    });
  }

  int countKeywordsInPostText(String text, List<RestaurantKeyword> keywords) {
    int count = 0;
    String targetLanguage = KeywordHelper.getLangWithMostOccurences(
        keywordProvider.selectedKeywords.value);
    for (var keyword in KeywordHelper.buildSimpleBricks(
        keywords,
        context.read<RestaurantProvider>().currentRestaurant!,
        targetLanguage)) {
      if (text.contains(keyword.text)) {
        count++;
      }
    }
    return count;
  }

  double getBottomOffset() {
    final RenderBox renderBox =
        iconKey.currentContext!.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);
    return position.dy;
  }

  double getHeightFromTextLength(String text) {
    double height = (text.length / 6);
    return height;
  }
}
