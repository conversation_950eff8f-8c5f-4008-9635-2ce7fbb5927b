import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/chips/malou_chip.widget.dart';

import 'post_tagged_accounts.page.widget.dart';

class PostTaggedAccounts extends StatelessWidget {
  final Post post;
  const PostTaggedAccounts({super.key, required this.post});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              Translation.of(context).tagAccounts,
              style: MalouTextStyles(context)
                  .text12Semibold
                  .copyWith(color: MalouColors.textTitle),
            ),
            const Spacer(),
            Tooltip(
              message: Translation.of(context).onlyAvailableInstagram,
              textStyle: MalouTextStyles(context).text10.copyWith(
                    color: Colors.white,
                  ),
              child: const Icon(Icons.info_outline_rounded,
                  color: MalouColors.primary, size: 25),
            ),
            RotatedBox(
              quarterTurns: 2,
              child: IconButton(
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                onPressed: () => navigateToConfigureTagAccountsPage(context),
                icon: const Icon(
                  MalouIcons.chevronLeft,
                  color: MalouColors.primary,
                ),
              ),
            )
          ],
        ),
        if (post.userTags != null)
          Row(
            children: [
              for (var userTag in post.userTags!)
                MalouChip(
                  text: userTag.username,
                )
            ],
          )
      ],
    );
  }

  void navigateToConfigureTagAccountsPage(BuildContext context) {
    Navigator.of(context).push(MaterialPageRoute(
      builder: (context) => const PostTaggedAccountsPage(),
    ));
  }
}
