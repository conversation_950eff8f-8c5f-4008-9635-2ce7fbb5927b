import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/date_picker/date_picker.theme.dart';
import 'package:provider/provider.dart';

class ChooseEventDuration extends StatelessWidget {
  final Post post;
  const ChooseEventDuration({super.key, required this.post});

  @override
  Widget build(BuildContext context) {
    bool isOffer = post.postTopic == PostTopic.OFFER;
    return Consumer<PostsProvider>(builder: (context, postsProvider, child) {
      DateTime startDate =
          postsProvider.postToEdit.event?.startDate ?? DateTime.now();
      DateTime endDate = postsProvider.postToEdit.event?.endDate ??
          DateTime.now().add(const Duration(days: 1));
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
              '${Translation.of(context).dateOf} ${isOffer ? Translation.of(context).theOffer : Translation.of(context).theEvent}',
              style: MalouTextStyles(context)
                  .text12Semibold
                  .copyWith(color: MalouColors.textTitle)),
          Container(
            padding: const EdgeInsets.only(left: 20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ListTile(
                  minVerticalPadding: 0,
                  contentPadding: EdgeInsets.zero,
                  minLeadingWidth: 0,
                  dense: true,
                  titleAlignment: ListTileTitleAlignment.center,
                  visualDensity:
                      const VisualDensity(horizontal: 0, vertical: -3),
                  leading: Container(
                      height: 7,
                      width: 7,
                      decoration: const BoxDecoration(
                        color: MalouColors.primary,
                        shape: BoxShape.circle,
                      )),
                  title: Text(Translation.of(context).from,
                      style: MalouTextStyles(context)
                          .text12Semibold
                          .copyWith(color: MalouColors.textTitle)),
                  subtitle: Text(DateFormat('dd MMM yyyy').format(startDate),
                      style: MalouTextStyles(context).text12Italic),
                  trailing: const RotatedBox(
                    quarterTurns: 2,
                    child: Icon(
                      MalouIcons.chevronLeft,
                      color: MalouColors.primary,
                    ),
                  ),
                  onTap: () {
                    FocusScope.of(context).unfocus();
                    showDatePicker(
                      context: context,
                      initialDate: startDate,
                      firstDate: DateTime.now(),
                      lastDate: DateTime.now().add(const Duration(days: 365)),
                      builder: datePickerThemeBuilder,
                      helpText: null,
                      confirmText: Translation.of(context).confirm,
                      cancelText: Translation.of(context).cancel,
                      initialEntryMode: DatePickerEntryMode.calendarOnly,
                      initialDatePickerMode: DatePickerMode.day,
                    ).then((value) {
                      if (value != null) {
                        postsProvider.postToEdit = postsProvider.postToEdit
                            .copyWith(
                                event: postsProvider.postToEdit.event
                                        ?.copyWith(startDate: value) ??
                                    Event(
                                        title: postsProvider
                                                .postToEdit.event?.title ??
                                            '',
                                        startDate: value,
                                        endDate: postsProvider
                                                .postToEdit.event?.endDate ??
                                            value
                                                .add(const Duration(days: 1))));
                      }
                    });
                  },
                ),
                ListTile(
                  minVerticalPadding: 0,
                  contentPadding: EdgeInsets.zero,
                  minLeadingWidth: 0,
                  dense: true,
                  titleAlignment: ListTileTitleAlignment.center,
                  visualDensity:
                      const VisualDensity(horizontal: 0, vertical: -3),
                  leading: Container(
                      height: 7,
                      width: 7,
                      decoration: const BoxDecoration(
                        color: MalouColors.primary,
                        shape: BoxShape.circle,
                      )),
                  title: Text(Translation.of(context).to,
                      style: MalouTextStyles(context)
                          .text12Semibold
                          .copyWith(color: MalouColors.textTitle)),
                  subtitle: Text(DateFormat('dd MMM yyyy').format(endDate),
                      style: MalouTextStyles(context).text12Italic),
                  trailing: const RotatedBox(
                    quarterTurns: 2,
                    child: Icon(
                      MalouIcons.chevronLeft,
                      color: MalouColors.primary,
                    ),
                  ),
                  onTap: () {
                    FocusScope.of(context).unfocus();
                    showDatePicker(
                      context: context,
                      initialDate:
                          endDate.isBefore(startDate) ? startDate : endDate,
                      firstDate: startDate,
                      lastDate: DateTime.now().add(const Duration(days: 365)),
                      builder: datePickerThemeBuilder,
                      helpText: null,
                      confirmText: Translation.of(context).validate,
                      cancelText: Translation.of(context).cancel,
                      initialEntryMode: DatePickerEntryMode.calendarOnly,
                      initialDatePickerMode: DatePickerMode.day,
                    ).then((value) {
                      if (value != null) {
                        postsProvider.postToEdit = postsProvider.postToEdit
                            .copyWith(
                                event: postsProvider.postToEdit.event
                                        ?.copyWith(endDate: value) ??
                                    Event(
                                        title: postsProvider
                                                .postToEdit.event?.title ??
                                            '',
                                        startDate: postsProvider
                                                .postToEdit.event?.startDate ??
                                            DateTime.now(),
                                        endDate: value));
                      }
                    });
                  },
                ),
              ],
            ),
          )
        ],
      );
    });
  }
}
