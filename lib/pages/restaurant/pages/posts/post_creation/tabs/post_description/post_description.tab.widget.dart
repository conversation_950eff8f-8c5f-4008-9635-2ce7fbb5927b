import 'package:flutter/material.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_description/widgets/post_description_platforms_selection.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_description/widgets/post_description_fields.widget.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:provider/provider.dart';

class PostDescriptionTab extends StatefulWidget {
  const PostDescriptionTab({super.key});

  @override
  State<PostDescriptionTab> createState() => _PostDescriptionTabState();
}

class _PostDescriptionTabState extends State<PostDescriptionTab> {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Consumer<PostsProvider>(builder: (context, postsProvider, child) {
        Post postToEdit = context.watch<PostsProvider>().postToEdit;
        return Form(
          key: formKey,
          child: ListView(
            physics: const ClampingScrollPhysics(),
            controller: postsProvider.scrollController,
            padding: const EdgeInsets.only(left: 16, right: 16, bottom: 20),
            children: [
              if (postToEdit.source == Source.social && !postToEdit.isReel())
                const PostDescriptionPlatformsSelection(),
              const PostDescriptionFields(),
              const SizedBox(
                height: 20,
              )
            ],
          ),
        );
      }),
    );
  }
}
