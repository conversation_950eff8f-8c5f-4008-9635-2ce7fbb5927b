import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:provider/provider.dart';

import '../../post_call_to_action/choose_action_button_url.page.widget.dart';

class ChooseOfferAdditionnalInformations extends StatelessWidget {
  final Post post;
  const ChooseOfferAdditionnalInformations({super.key, required this.post});

  @override
  Widget build(BuildContext context) {
    return Consumer<PostsProvider>(builder: (context, postsProvider, child) {
      Offer offer = post.offer!;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(Translation.of(context).addInfosAboutOffer,
              style: MalouTextStyles(context)
                  .text12Semibold
                  .copyWith(color: MalouColors.textTitle)),
          Container(
            padding: const EdgeInsets.only(left: 20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                OfferInformationTile(
                  title: Translation.of(context).addCoupon,
                  nextPageTitle: Translation.of(context).promoOrCoupon,
                  subtitle: offer.couponCode ?? '',
                  afterInformationAdded: (value) {
                    postsProvider.postToEdit = postsProvider.postToEdit
                        .copyWith(
                            offer: postsProvider.postToEdit.offer
                                ?.copyWith(couponCode: value));
                  },
                ),
                OfferInformationTile(
                  title: Translation.of(context).addLinkToOffer,
                  nextPageTitle: Translation.of(context).linkOfOffer,
                  subtitle: offer.onlineUrl ?? '',
                  afterInformationAdded: (value) {
                    postsProvider.postToEdit = postsProvider.postToEdit
                        .copyWith(
                            offer: postsProvider.postToEdit.offer
                                ?.copyWith(onlineUrl: value));
                  },
                ),
                OfferInformationTile(
                  title: Translation.of(context).conditionsOfOffer,
                  nextPageTitle: Translation.of(context).conditionOffer,
                  subtitle: offer.termsConditions ?? '',
                  afterInformationAdded: (value) {
                    postsProvider.postToEdit = postsProvider.postToEdit
                        .copyWith(
                            offer: postsProvider.postToEdit.offer
                                ?.copyWith(termsConditions: value));
                  },
                ),
              ],
            ),
          )
        ],
      );
    });
  }
}

class OfferInformationTile extends StatelessWidget {
  final String title;
  final String nextPageTitle;
  final String subtitle;
  final Function(String) afterInformationAdded;
  const OfferInformationTile({
    super.key,
    required this.title,
    required this.subtitle,
    required this.afterInformationAdded,
    required this.nextPageTitle,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
        minVerticalPadding: 0,
        contentPadding: EdgeInsets.zero,
        minLeadingWidth: 0,
        dense: true,
        titleAlignment: ListTileTitleAlignment.center,
        visualDensity: const VisualDensity(horizontal: 0, vertical: -3),
        leading: Container(
            height: 7,
            width: 7,
            decoration: const BoxDecoration(
              color: MalouColors.primary,
              shape: BoxShape.circle,
            )),
        title: Text(title,
            style: MalouTextStyles(context)
                .text12Semibold
                .copyWith(color: MalouColors.textTitle)),
        subtitle: Text(subtitle.isEmpty ? '-' : subtitle,
            style: MalouTextStyles(context).text12Italic),
        trailing: const RotatedBox(
          quarterTurns: 2,
          child: Icon(
            MalouIcons.chevronLeft,
            color: MalouColors.primary,
          ),
        ),
        onTap: () {
          FocusScope.of(context).unfocus();
          Navigator.of(context)
              .push(
                MaterialPageRoute(
                  builder: (context) => ChooseActionUrlButtonPage(
                    ctaTitle: nextPageTitle,
                    initialValue: subtitle,
                  ),
                ),
              )
              .then((value) => afterInformationAdded(value));
        });
  }
}
