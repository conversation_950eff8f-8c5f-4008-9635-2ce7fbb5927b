import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_description/widgets/platforms/meta/reel_cover/reel_cover_picker.widget.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/image/cached_image.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_medias/widgets/post_media_picker/widgets/widgets/widgets/video_thumbnail.widget.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:photo_manager_image_provider/photo_manager_image_provider.dart';
import 'package:provider/provider.dart';

class ReelCover extends StatelessWidget {
  const ReelCover({
    super.key,
    required this.context,
  });

  final BuildContext context;

  @override
  Widget build(BuildContext context) {
    return Consumer<PostsProvider>(builder: (context, postsProvider, child) {
      return Container(
          margin: const EdgeInsets.only(left: 10),
          child: Column(
            children: [
              postsProvider.postToEdit.thumbnail != null
                  ? CachedImage(
                      url: postsProvider.postToEdit.thumbnail!.getMediaUrl(),
                      width: 80,
                      height: 120,
                      fit: BoxFit.cover,
                      radius: BorderRadius.circular(10))
                  : postsProvider.postToEdit.attachments.first.assetEntity !=
                          null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(10),
                          clipBehavior: Clip.hardEdge,
                          child: AssetEntityImage(
                            width: 80,
                            height: 120,
                            postsProvider
                                .postToEdit.attachments.first.assetEntity!,
                            fit: BoxFit.cover,
                            thumbnailSize: const ThumbnailSize.square(120),
                            thumbnailFormat: ThumbnailFormat.jpeg,
                          ),
                        )
                      : VideoMediaThumbnail(
                          showPlayIcon: false,
                          width: 80,
                          height: 120,
                          /**
                           * To get a good visual quality
                           * we will generate a thumbnail that is 4 times the size of the container that will display it
                           */
                          maxThumbWidth: 80 * 4,
                          maxThumbHeight: 120 * 4,
                          thumbnailurl: postsProvider
                              .postToEdit.attachments.first.thumbnail,
                          momentInMs: postsProvider
                                  .postToEdit.thumbnailOffsetTimeInMs ??
                              0,
                          url: postsProvider.postToEdit.attachments.first
                              .getMediaUrl(),
                          borderRadius: BorderRadius.circular(10)),
              if (postsProvider.postToEdit.keys.length == 1 &&
                  postsProvider.postToEdit.keys.first == PlatformKeys.instagram)
                TextButton(
                    onPressed: () {
                      Navigator.of(context).push(MaterialPageRoute(
                          builder: (context) => ReelCoverPicker(
                                media:
                                    postsProvider.postToEdit.attachments.first,
                              )));
                    },
                    child: Text(
                      Translation.of(context).cover,
                      style: MalouTextStyles(context)
                          .text13Semibold
                          .copyWith(color: MalouColors.primary),
                    ))
            ],
          ));
    });
  }
}
