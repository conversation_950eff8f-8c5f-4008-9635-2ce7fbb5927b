import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/widgets/appbar/appbar_back.widget.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:provider/provider.dart';

import 'choose_action_button_url.page.widget.dart';

class ChooseActionButtonPage extends StatefulWidget {
  const ChooseActionButtonPage({super.key});

  @override
  State<ChooseActionButtonPage> createState() => _ChooseActionButtonPageState();
}

class _ChooseActionButtonPageState extends State<ChooseActionButtonPage> {
  late PostsProvider postsProvider;
  Map<CallToActionType, String> callToActions = {};

  @override
  void initState() {
    postsProvider = Provider.of<PostsProvider>(context, listen: false);

    Future.delayed(const Duration(milliseconds: 100), () {
      Map<CallToActionType, String> mapstrCallToActionTexts = {
        CallToActionType.SEE: Translation.of(context).see,
        CallToActionType.BOOK: Translation.of(context).book,
        CallToActionType.ORDER: Translation.of(context).order,
        CallToActionType.MENU: Translation.of(context).seeMenu,
        CallToActionType.JOIN: Translation.of(context).participate,
        CallToActionType.MORE: Translation.of(context).learnMore
      };

      Map<CallToActionType, String> gmbCallToActionTexts = {
        CallToActionType.BOOK: Translation.of(context).book,
        CallToActionType.ORDER: Translation.of(context).order,
        CallToActionType.BUY: Translation.of(context).buy,
        CallToActionType.LEARN_MORE: Translation.of(context).learnMore,
        CallToActionType.SIGN_UP: Translation.of(context).signup,
        CallToActionType.CALL: Translation.of(context).call
      };
      setState(() {
        callToActions = postsProvider.postToEdit.source == Source.seo
            ? gmbCallToActionTexts
            : mapstrCallToActionTexts;
      });
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppBarArrowBack(
        height: 70,
        icon: Icon(MalouIcons.cross, color: MalouColors.primary),
      ),
      body: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 14),
        shrinkWrap: true,
        itemCount: callToActions.length,
        itemBuilder: (context, index) {
          String translatedCTAName = callToActions.values.elementAt(index);
          CallToActionType ctaType = callToActions.keys.elementAt(index);
          return ListTile(
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 14,
            ),
            title: Text(translatedCTAName,
                style: MalouTextStyles(context)
                    .text15Semibold
                    .copyWith(color: MalouColors.textBody)),
            trailing: ctaType == CallToActionType.CALL ||
                    ctaType == CallToActionType.SEE
                ? null
                : const Icon(
                    Icons.chevron_right,
                    color: MalouColors.primary,
                    size: 26,
                  ),
            onTap: () {
              if (ctaType == CallToActionType.CALL ||
                  ctaType == CallToActionType.SEE) {
                postsProvider.postToEdit = postsProvider.postToEdit.copyWith(
                    callToAction: CallToAction(url: '', actionType: ctaType));

                Navigator.of(context).pop();
                return;
              }
              Navigator.of(context)
                  .push(MaterialPageRoute(
                builder: (context) => ChooseActionUrlButtonPage(
                  ctaTitle: translatedCTAName,
                  initialValue: postsProvider.postToEdit.callToAction?.url,
                ),
              ))
                  .then((value) {
                if (value == null || value == '') {
                  postsProvider.postToEdit =
                      postsProvider.postToEdit.copyWith(callToAction: null);
                  return;
                }
                if (postsProvider.postToEdit.callToAction == null) {
                  postsProvider.postToEdit = postsProvider.postToEdit.copyWith(
                      callToAction:
                          CallToAction(url: value, actionType: ctaType));
                } else {
                  postsProvider.postToEdit = postsProvider.postToEdit.copyWith(
                      callToAction: postsProvider.postToEdit.callToAction!
                          .copyWith(url: value, actionType: ctaType));
                }
              });
            },
            titleAlignment: ListTileTitleAlignment.center,
          );
        },
      ),
    );
  }
}
