import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/chips/malou_chip.widget.dart';
import 'package:provider/provider.dart';

import 'post_location.page.widget.dart';

class PostLocationDisplay extends StatelessWidget {
  const PostLocationDisplay({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<PostsProvider>(builder: (context, postsProvider, child) {
      Post post = postsProvider.postToEdit;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                Translation.of(context).addLocation,
                style: MalouTextStyles(context)
                    .text12Semibold
                    .copyWith(color: MalouColors.textTitle),
              ),
              const Spacer(),
              Tooltip(
                triggerMode: TooltipTriggerMode.tap,
                enableFeedback: true,
                message: Translation.of(context).onlyAvailableOnMeta,
                textStyle: MalouTextStyles(context).text10.copyWith(
                      color: Colors.white,
                    ),
                child: const Icon(
                  Icons.info_outline,
                  color: MalouColors.primary,
                ),
              ),
              RotatedBox(
                quarterTurns: 2,
                child: IconButton(
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  onPressed: () => navigateToChooseLocationPage(context),
                  icon: const Icon(
                    MalouIcons.chevronLeft,
                    color: MalouColors.primary,
                  ),
                ),
              )
            ],
          ),
          if (post.location != null)
            MalouChip(
              text: post.location!.name,
              onClosed: () {
                Post post = postsProvider.postToEdit;
                post.location = null;
                postsProvider.postToEdit = post;
              },
            )
        ],
      );
    });
  }

  void navigateToChooseLocationPage(BuildContext context) {
    Navigator.of(context).push(MaterialPageRoute(
      builder: (context) => const PostLocationPage(),
    ));
  }
}
