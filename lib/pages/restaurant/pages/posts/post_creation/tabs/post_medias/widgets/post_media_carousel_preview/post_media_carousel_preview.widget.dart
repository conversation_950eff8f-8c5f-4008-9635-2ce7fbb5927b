import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/widgets/image/cached_image.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_medias/widgets/post_media_picker/widgets/widgets/widgets/video_thumbnail.widget.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:photo_manager_image_provider/photo_manager_image_provider.dart';
import 'package:provider/provider.dart';

class PostMediaCarouselPreview extends StatefulWidget {
  const PostMediaCarouselPreview({super.key});

  @override
  State<PostMediaCarouselPreview> createState() =>
      _PostMediaCarouselPreviewState();
}

class _PostMediaCarouselPreviewState extends State<PostMediaCarouselPreview> {
  @override
  Widget build(BuildContext context) {
    return Consumer<PostsProvider>(builder: (context, postsProvider, child) {
      return SizedBox(
        height: 70,
        width: double.infinity,
        child: ReorderableListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            itemCount: postsProvider.postToEdit.attachments.length,
            proxyDecorator: (child, _, animation) {
              return Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 5,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Animate(
                  child: child,
                ).scale(
                  begin: const Offset(1, 1),
                  end: const Offset(1.5, 1.5),
                ),
              );
            },
            itemBuilder: (context, index) {
              Media media = postsProvider.postToEdit.attachments[index];

              return GestureDetector(
                key: ValueKey(media.id),
                onTap: () {
                  postsProvider.selectedPostMediaIndex = index;
                },
                child: Container(
                  height: 50,
                  width: 50,
                  margin: const EdgeInsets.only(right: 5),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(5),
                  ),
                  child: media.isVideo() && media.assetEntity == null
                      ? VideoMediaThumbnail(
                          thumbnailurl: media.thumbnail,
                          url: media.getMediaUrl(),
                          width: 50,
                          height: 50,
                          fit: BoxFit.cover,
                          borderRadius: BorderRadius.circular(5),
                        )
                      : media.assetEntity != null
                          ? ClipRRect(
                              borderRadius: BorderRadius.circular(5),
                              child: Stack(
                                children: [
                                  AssetEntityImage(
                                    media.assetEntity!,
                                    width: 50,
                                    height: 50,
                                    fit: BoxFit.cover,
                                    isOriginal: false,
                                    thumbnailSize:
                                        const ThumbnailSize.square(50),
                                    thumbnailFormat: ThumbnailFormat.jpeg,
                                  ),
                                  if (media.assetEntity?.type ==
                                      AssetType.video)
                                    Center(
                                      child: Icon(
                                        MalouIcons.play,
                                        color: Colors.white.withOpacity(0.8),
                                        size: 20,
                                      ),
                                    ),
                                ],
                              ),
                            )
                          : CachedImage(
                              url: media.getMediaUrl(size: 'small'),
                              fit: BoxFit.cover,
                              height: 50,
                              radius: BorderRadius.circular(5),
                            ),
                ),
              );
            },
            onReorder: (oldIndex, newIndex) {
              postsProvider.reorderPostToEditAttachments(oldIndex, newIndex);
            }),
      );
    });
  }
}
