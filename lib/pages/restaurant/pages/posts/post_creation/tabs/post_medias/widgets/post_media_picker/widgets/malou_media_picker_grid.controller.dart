import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:malou/shared/helpers/pagination.model.dart';
import 'package:malou/shared/helpers/utils.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/models/medias/folder.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/folder/folder.service.dart';
import 'package:malou/shared/services/medias/medias.service.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';

class MalouMediaPickerGridController {
  late RestaurantProvider restaurantProvider;
  late PagingController<int, MediaOrFolder> pagingController;
  TextEditingController textController = TextEditingController();
  FolderService folderService = FolderService();
  MalouMediaPickerGridController(BuildContext context) {
    restaurantProvider =
        Provider.of<RestaurantProvider>(context, listen: false);
  }

  List<Folder> folders = [];

  BehaviorSubject<bool> neverPostedOnly$ = BehaviorSubject<bool>.seeded(false);

  void fetchPage(
    int pageKey,
    MediaPickerType mediaPickerType,
    /*
        This field is for including current post medias into the gallery display
    */
    bool shouldAddPostAttachments,
    BuildContext context,
    String? folderId,
  ) async {
    MediaType mediaType;
    if (mediaPickerType == MediaPickerType.video) {
      mediaType = MediaType.video;
    } else if (mediaPickerType == MediaPickerType.image) {
      mediaType = MediaType.photo;
    } else {
      mediaType = MediaType.all;
    }

    try {
      final result = await MediasService.getMedias(
          restaurantId: restaurantProvider.currentRestaurant!.id,
          pagination:
              Pagination(pageNumber: pageKey, pageSize: 30, total: null),
          mediaType: mediaType,
          neverPostedOnly: neverPostedOnly$.value,
          folderId: folderId,
          searchText: textController.text);
      var newItems = (result.data ?? [])
          .map((media) => MediaOrFolder(media: media))
          .toList();
      final isLastPage = newItems.length < 30;
      if (isLastPage) {
        if (pageKey == 0) {
          newItems.insertAll(
              0,
              folders
                  .where((folder) => normalizeString(folder.name)
                      .contains(normalizeString(textController.text)))
                  .map((folder) => MediaOrFolder(folder: folder))
                  .toList());
        }

        pagingController.appendLastPage(newItems);
      } else {
        Post post =
            // ignore: use_build_context_synchronously
            Provider.of<PostsProvider>(context, listen: false).postToEdit;
        if (pageKey == 0 && shouldAddPostAttachments) {
          newItems = newItems.where((media) {
            return post.attachments
                .where((attachment) => attachment.id == media.media!.id)
                .isEmpty;
          }).toList();
          newItems.insertAll(
              0,
              post.attachments
                  .map((media) => MediaOrFolder(media: media))
                  .toList());
        }
        if (pageKey == 0) {
          newItems.insertAll(
              0,
              folders
                  .where((folder) => normalizeString(folder.name)
                      .contains(normalizeString(textController.text)))
                  .map((folder) => MediaOrFolder(folder: folder))
                  .toList());
        }

        newItems = newItems.toSet().toList();
        final nextPageKey = pageKey + 1;
        pagingController.appendPage(newItems, nextPageKey);
      }
    } catch (error) {
      pagingController.error = error;
    }
  }

  Future<List<Folder>> fetchFolders() async {
    final result = await folderService
        .getFolders(restaurantProvider.currentRestaurant!.id);
    return result.data ?? [];
  }

  initializePagingController(
      {required MediaPickerType mediaPickerType,
      required bool shouldAddPostAttachments,
      required BuildContext context,
      String? folderId}) {
    pagingController = PagingController(firstPageKey: 0);
    pagingController.addPageRequestListener((pageKey) {
      fetchPage(pageKey, mediaPickerType, shouldAddPostAttachments, context,
          folderId);
    });
  }

  toggleNeverPostedOnly({required bool switchValue}) {
    neverPostedOnly$.add(switchValue);
    pagingController.refresh();
  }
}

class MediaOrFolder {
  final Media? media;
  final Folder? folder;
  MediaOrFolder({this.media, this.folder});

  bool get isMedia => media != null;
  bool get isFolder => folder != null;
}
