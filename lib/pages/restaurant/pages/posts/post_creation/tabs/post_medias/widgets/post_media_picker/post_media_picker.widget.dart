import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_medias/widgets/post_media_picker/post_media_picker.controller.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_medias/widgets/post_media_picker/post_media_picker.provider.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_medias/widgets/post_media_picker/widgets/device_media_picker_grid.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_medias/widgets/post_media_picker/widgets/malou_media_picker_grid.widget.dart';
import 'package:provider/provider.dart';

class PostMediaPicker extends StatefulWidget {
  final MediaPickerType mediaPickerType;
  final bool isThumbnailPicker;
  final double initialSize;
  final double minSize;
  final bool shouldAddPostAttachments;
  final int maxAttachmentsBeforeResize;
  const PostMediaPicker({
    super.key,
    this.minSize = 0.4,
    this.maxAttachmentsBeforeResize = 1,
    this.initialSize = 0.5,
    required this.mediaPickerType,
    this.isThumbnailPicker = false,
    this.shouldAddPostAttachments = true,
  });
  @override
  State<PostMediaPicker> createState() => PostMediaPickerState();
}

class PostMediaPickerState extends State<PostMediaPicker>
    with SingleTickerProviderStateMixin {
  PostMediaPickerController controller = PostMediaPickerController();
  @override
  void initState() {
    controller.initialize(context, this, widget.maxAttachmentsBeforeResize,
        widget.initialSize, widget.minSize);
    super.initState();
  }

  @override
  void dispose() {
    controller.destroy$.add(null);
    controller.sheetScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => PostMediaPickerProvider(),
      child: Container(
          decoration: BoxDecoration(
              color: Colors.red,
              borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20), topRight: Radius.circular(20)),
              boxShadow: [
                BoxShadow(
                  color: MalouColors.primary.withOpacity(0.3),
                  blurRadius: 8,
                )
              ]),
          child: DraggableScrollableSheet(
              controller: controller.sheetScrollController,
              minChildSize: widget.minSize,
              initialChildSize: widget.initialSize,
              maxChildSize: controller.maxSize,
              expand: false,
              snap: true,
              snapSizes: [
                widget.minSize,
                controller.snapSize,
                controller.maxSize
              ],
              builder: (context, scrollController) {
                return Container(
                  decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20))),
                  child: Stack(
                    children: [
                      StreamBuilder(
                          stream: controller.tabIndex$,
                          builder: (context, snapshot) {
                            if (!snapshot.hasData) {
                              return const SizedBox();
                            }
                            return (snapshot.data) == 0
                                ? Consumer<PostMediaPickerProvider>(
                                    builder: (context, provider, child) {
                                    return MalouMediaPickerGrid(
                                      onShouldScrollUp: () => controller
                                          .sheetScrollController
                                          .jumpTo(controller.maxSize),
                                      folderId: provider.selectedFolder?.id,
                                      scrollController: scrollController,
                                      mediaPickerType: widget.mediaPickerType,
                                      isThumbnailPicker:
                                          widget.isThumbnailPicker,
                                      shouldAddPostAttachments:
                                          widget.shouldAddPostAttachments,
                                    );
                                  })
                                : DeviceMediaPickerGrid(
                                    scrollController: scrollController,
                                    mediaPickerType: widget.mediaPickerType,
                                    isThumbnailPicker: widget.isThumbnailPicker,
                                    padding: const EdgeInsets.only(top: 65),
                                  );
                          }),
                      GestureDetector(
                        onPanUpdate: (details) {
                          final screenSize = MediaQuery.of(context).size.height;
                          final positionRatio = 1.0 -
                              (details.globalPosition.dy / screenSize) +
                              0.2; // 1.0 to 0.0

                          final position = positionRatio > controller.maxSize
                              ? controller.maxSize
                              : positionRatio < widget.minSize
                                  ? widget.minSize
                                  : positionRatio;
                          controller.sheetScrollController.jumpTo(position);
                        },
                        child: Container(
                            height: 65,
                            decoration: const BoxDecoration(
                              color: Colors.white,
                            ),
                            padding: const EdgeInsets.only(top: 10),
                            child: Column(
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    Container(
                                      width: 30,
                                      height: 5,
                                      decoration: const BoxDecoration(
                                          color: MalouColors.purpleLight,
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(12.0))),
                                    ),
                                  ],
                                ),
                                Consumer<PostMediaPickerProvider>(
                                    builder: (context, provider, child) {
                                  if (provider.selectedFolder != null) {
                                    return Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 12.0),
                                      child: Row(
                                        children: [
                                          IconButton(
                                              onPressed: () =>
                                                  provider.selectFolder(null),
                                              icon: const Icon(
                                                Icons.arrow_back,
                                                color: MalouColors.primary,
                                              )),
                                          Text(
                                            provider.selectedFolder!.name,
                                            style: MalouTextStyles(context)
                                                .text15Semibold,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ],
                                      ),
                                    );
                                  }
                                  return TabBar(
                                    indicatorColor: MalouColors.primary,
                                    indicatorSize: TabBarIndicatorSize.tab,
                                    tabs: [
                                      Tab(
                                        child: Text(
                                          Translation.of(context).malouGallery,
                                          style: MalouTextStyles(context)
                                              .text15Semibold,
                                        ),
                                      ),
                                      Tab(
                                        child: Text(
                                          Translation.of(context).myMedias,
                                          style: MalouTextStyles(context)
                                              .text15Semibold,
                                        ),
                                      ),
                                    ],
                                    controller: controller.tabController,
                                  );
                                })
                              ],
                            )),
                      ),
                    ],
                  ),
                );
              })),
    );
  }
}
