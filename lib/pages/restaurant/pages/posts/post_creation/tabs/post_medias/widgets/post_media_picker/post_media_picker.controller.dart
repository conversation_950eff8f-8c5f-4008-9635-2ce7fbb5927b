import 'package:flutter/material.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_medias/widgets/post_media_picker/post_media_picker.widget.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';

class PostMediaPickerController {
  final DraggableScrollableController sheetScrollController =
      DraggableScrollableController();

  late PostsProvider postsProvider;
  late TabController tabController;
  final BehaviorSubject destroy$ = BehaviorSubject();

  final double maxSize = 0.9;
  double snapSize = 0.5;
  BehaviorSubject<int> tabIndex$ = BehaviorSubject.seeded(0);

  PostMediaPickerController();

  initialize(
    BuildContext context,
    PostMediaPickerState state,
    int maxAttachmentsBeforeResize,
    double initialSize,
    double minSize,
  ) {
    tabController = TabController(length: 2, vsync: state, initialIndex: 0);
    postsProvider = context.read<PostsProvider>();

    postsProvider.addedNewMedia$.takeUntil(destroy$).listen((_) {
      if (postsProvider.postToEdit.attachments.length >
              maxAttachmentsBeforeResize &&
          sheetScrollController.size <= maxSize) {
        sheetScrollController.animateTo(minSize,
            duration: const Duration(milliseconds: 300), curve: Curves.easeIn);
      }
      if (postsProvider.postToEdit.attachments.length ==
              maxAttachmentsBeforeResize &&
          sheetScrollController.size < initialSize) {
        sheetScrollController.animateTo(initialSize,
            duration: const Duration(milliseconds: 300), curve: Curves.easeIn);
      }
    });

    tabController.addListener(() {
      if (tabController.index != tabIndex$.value) {
        tabIndex$.add(tabController.index);
      }
    });
  }
}
