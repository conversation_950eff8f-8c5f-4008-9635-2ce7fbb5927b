import 'package:flutter/material.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/helpers/utils.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';
import 'package:video_player/video_player.dart';

class VideoMediaSquare extends StatefulWidget {
  final String url;
  final BorderRadius radius;
  final bool showDuration;
  final void Function(Duration)? onDurationChange;
  const VideoMediaSquare({
    super.key,
    required this.url,
    this.radius = BorderRadius.zero,
    this.showDuration = true,
    this.onDurationChange,
  });

  @override
  State<VideoMediaSquare> createState() => _VideoMediaSquareState();
}

class _VideoMediaSquareState extends State<VideoMediaSquare> {
  late VideoPlayerController _controller;
  bool isErrorLoadingVideo = false;
  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.networkUrl(Uri.parse(widget.url))
      ..initialize().then((_) {
        setState(() {
          isErrorLoadingVideo = false;
          widget.onDurationChange?.call(_controller.value.duration);
        });
        _controller.setLooping(true);
      }).catchError((error) {
        setState(() {
          isErrorLoadingVideo = true;
        });
      });
  }

  @override
  Widget build(BuildContext context) {
    return isErrorLoadingVideo
        ? Container(
            decoration: BoxDecoration(
                color: MalouColors.backgroundDark,
                borderRadius: BorderRadius.circular(10)),
            child: Center(
              child: Text('Error loading video',
                  style: MalouTextStyles(context)
                      .text10Semibold
                      .copyWith(color: MalouColors.textBody)),
            ),
          )
        : _controller.value.isInitialized
            ? Stack(
                children: [
                  SizedBox.expand(
                    child: FittedBox(
                      fit: BoxFit.fill,
                      child: SizedBox(
                        width: _controller.value.aspectRatio,
                        height: 1,
                        child: ClipRRect(
                            borderRadius: widget.radius,
                            child: VideoPlayer(_controller)),
                      ),
                    ),
                  ),
                  if (widget.showDuration)
                    Align(
                      alignment: Alignment.bottomRight,
                      child: Container(
                        margin: const EdgeInsets.all(5),
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.5),
                        ),
                        child: Text(formatDuration(_controller.value.duration),
                            style: MalouTextStyles(context)
                                .text10Semibold
                                .copyWith(color: Colors.white)),
                      ),
                    ),
                  if (!widget.showDuration)
                    Align(
                        alignment: Alignment.center,
                        child: Icon(
                          MalouIcons.play,
                          color: Colors.white.withOpacity(0.8),
                          size: 25,
                        ))
                ],
              )
            : const Center(
                child: CircularSpinner(size: 20, color: MalouColors.primary),
              );
  }
}
