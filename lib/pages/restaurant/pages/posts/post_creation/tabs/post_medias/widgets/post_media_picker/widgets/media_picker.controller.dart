import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/helpers/clarify_error.dart';
import 'package:malou/shared/helpers/use_query.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/pages/ai_generation/pages/models/fetch_image_description_params.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/posts/posts.service.dart';
import 'package:malou/shared/widgets/snackbars/snackbar.dart';

class MediaPickerController {
  final BuildContext context;
  PostsService postsService = PostsService();
  late QueryResult<void, FetchImageDescriptionParams>
      handleFetchMediaDescriptionQuery;

  Function() onThumbnailTap(
      PostsProvider postsProvider, Media item, bool isSelected) {
    return () => postsProvider.updatePostThumbnail(isSelected ? null : item);
  }

  bool isThumbnailUnselectable(
    bool canSelectMedia,
    bool isSelected,
  ) {
    return !canSelectMedia && !isSelected;
  }

  Function() onMediaTap(
    bool isVideoOnlyAndMediaIsVideo,
    bool isPostAlreadyPublished,
    bool isVideoAndTooShort,
    BuildContext context,
    bool isSeoAndMediaVideo,
    bool isSeoAndAlreadyHasAttachment,
    bool isSelected,
    bool isCarouselRestrainedByFb,
    bool canSelectMedia,
    bool isMapstrAndMediaVideo,
    PostsProvider postsProvider,
    Media item,
  ) {
    if (!isVideoOnlyAndMediaIsVideo) {
      return () => MalouSnackbars.showInfoSnackbar(
            context: context,
            duration: const Duration(seconds: 2),
            label: Translation.of(context).youCanSelectVideosOnly,
          );
    }

    if (postsProvider.postToEdit.postType == PostType.REEL &&
        postsProvider.postToEdit.attachments.isNotEmpty &&
        !isSelected) {
      return () => MalouSnackbars.showInfoSnackbar(
            context: context,
            duration: const Duration(seconds: 2),
            label: Translation.of(context).youCanSelectOnlyOnevideo,
          );
    }

    if (isPostAlreadyPublished) {
      return () => MalouSnackbars.showInfoSnackbar(
            context: context,
            duration: const Duration(seconds: 2),
            label: Translation.of(context).cantEditMediaPublished,
          );
    }

    if (isVideoAndTooShort) {
      return () => MalouSnackbars.showInfoSnackbar(
            context: context,
            duration: const Duration(seconds: 2),
            label: Translation.of(context).videoTooShortMessage,
          );
    }

    if (isSeoAndMediaVideo) {
      return () => MalouSnackbars.showInfoSnackbar(
            context: context,
            duration: const Duration(seconds: 2),
            label: Translation.of(context).videosOnGoogleForbiddenMessage,
          );
    }

    if (isSeoAndAlreadyHasAttachment && !isSelected) {
      return () => MalouSnackbars.showInfoSnackbar(
            context: context,
            duration: const Duration(seconds: 2),
            label: Translation.of(context).carrouselForbiddenOnGoogleMessage,
          );
    }

    if (isCarouselRestrainedByFb && !isSelected) {
      return () => MalouSnackbars.showInfoSnackbar(
            context: context,
            duration: const Duration(seconds: 2),
            label: Translation.of(context).videosInCarrouselForbiddenOnFacebook,
          );
    }

    if (isMapstrAndMediaVideo) {
      return () => MalouSnackbars.showInfoSnackbar(
            context: context,
            duration: const Duration(seconds: 2),
            label: Translation.of(context).videosForbiddenOnMapstr,
          );
    }

    if (canSelectMedia) {
      return () => postsProvider.updatePostMediasSelection(item);
    }

    return () => MalouSnackbars.showInfoSnackbar(
          context: context,
          duration: const Duration(seconds: 2),
          label: Translation.of(context).carrouselMediasLimitReachedMessage,
        );
  }

  List<Media> _mergeMediaList(List<Media> mainList, List<Media> listToMerge) {
    final Map<String, Media> listToMergeMap = {
      for (var media in listToMerge)
        if (media.assetEntity?.id != null) media.assetEntity!.id: media,
    };

    return mainList
        .map((mediaA) => listToMergeMap[mediaA.id] ?? mediaA)
        .toList();
  }

  Future<void> handleFetchMediaDescription(
      FetchImageDescriptionParams params) async {
    bool isDeviceMedia = params.isDeviceMedia;
    PostsProvider postsProvider = params.postsProvider;
    Media media = params.media;

    if (!isDeviceMedia) {
      await postsProvider.fetchMediaDescription(mediaId: media.id!);

      List<Media> mediasWithCorrectIds = _mergeMediaList(
          postsProvider.postToEdit.attachments,
          postsProvider.lastUpdatedMedias$.value);
      postsProvider.updateUsablePhotoForImageAnalysis(
          attachments: mediasWithCorrectIds);
      return;
    }

    // for media coming from device, we need to save the media in db first, before fetching description
    ApiResult<List<Media>> res = await postsService.uploadAndResizePostMedias(
        postsProvider.postToEdit,
        shouldResize: false);
    List<Media> updatedMedias = res.data!;

    await postsProvider.updatePost(
        postId: postsProvider.postToEdit.id!,
        postUpdate: {
          'attachments': updatedMedias.map((media) => media.id).toList()
        });
    postsProvider.lastUpdatedMedias$.add(updatedMedias);
    postsProvider.updateUsablePhotoForImageAnalysis(attachments: updatedMedias);

    bool hasAssociatedMedia =
        updatedMedias.any((element) => element.assetEntity?.id == media.id);
    if (!hasAssociatedMedia) {
      return;
    }

    Media? associatedMedia = updatedMedias
        .firstWhere((element) => element.assetEntity?.id == media.id);
    if (associatedMedia.id != null) {
      await postsProvider.fetchMediaDescription(mediaId: associatedMedia.id!);
    }
  }

  bool isUnselectable(
      bool isVideoOnlyAndMediaIsVideo,
      bool isPostAlreadyPublished,
      bool canSelectMedia,
      bool isCarouselRestrainedByFb,
      bool isSeoAndAlreadyHasAttachment,
      bool isSeoAndMediaVideo,
      bool isVideoAndTooShort,
      bool isSelected,
      bool isMapstrAndMediaVideo,
      PostsProvider postsProvider) {
    bool isMaxMediaReachedForReel =
        postsProvider.postToEdit.postType == PostType.REEL &&
            postsProvider.postToEdit.attachments.isNotEmpty &&
            !isSelected;
    postsProvider.postToEdit.attachments.isNotEmpty;
    return (!isVideoOnlyAndMediaIsVideo ||
            isMaxMediaReachedForReel ||
            isPostAlreadyPublished ||
            !canSelectMedia ||
            isCarouselRestrainedByFb ||
            isSeoAndAlreadyHasAttachment ||
            isSeoAndMediaVideo ||
            isVideoAndTooShort ||
            isMapstrAndMediaVideo) &&
        !isSelected;
  }

  MediaPickerController({required this.context}) {
    handleFetchMediaDescriptionQuery = useQuery(handleFetchMediaDescription,
        onError: (result, params, onProcessResult) {
      displaySnackbarWithClarifiedError(context, result);
    });
  }
}
