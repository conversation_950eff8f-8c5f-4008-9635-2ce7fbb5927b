import 'package:app_settings/app_settings.dart';
import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:skeleton_loader/skeleton_loader.dart';

import 'media_picker.controller.dart';
import 'widgets/media_grid_item.widget.dart';

class DeviceMediaPickerGrid extends StatefulWidget {
  final MediaPickerType mediaPickerType;
  final bool isThumbnailPicker;
  final ScrollController scrollController;
  final EdgeInsets padding;
  const DeviceMediaPickerGrid({
    super.key,
    required this.mediaPickerType,
    this.isThumbnailPicker = false,
    required this.scrollController,
    this.padding = const EdgeInsets.all(0),
  });

  @override
  State<DeviceMediaPickerGrid> createState() => DeviceMediaPickerGridState();
}

class DeviceMediaPickerGridState extends State<DeviceMediaPickerGrid>
    with AutomaticKeepAliveClientMixin {
  PermissionState _ps = PermissionState.denied;
  @override
  void initState() {
    PhotoManager.requestPermissionExtend().then((permissionState) {
      setState(() {
        _ps = permissionState;
      });
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return _ps == PermissionState.authorized || _ps == PermissionState.limited
        ? DeviceMediaGrid(
            padding: widget.padding,
            scrollController: widget.scrollController,
            mediaPickerType: widget.mediaPickerType,
            isThumbnailPicker: widget.isThumbnailPicker,
          )
        : Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(
                  height: 30,
                ),
                Image.asset(
                  IllustrationsPathResolver.resolve(Illustrations.icecream),
                  height: 80,
                ),
                const SizedBox(height: 20),
                SizedBox(
                  width: 250,
                  child: Text(
                    Translation.of(context).noAccessToMedias,
                    style: MalouTextStyles(context)
                        .text13Semibold
                        .copyWith(color: MalouColors.textTitle),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                SizedBox(
                  width: 300,
                  child: Text(
                    Translation.of(context).mediasAccessDescription,
                    style: MalouTextStyles(context).text12,
                    textAlign: TextAlign.center,
                  ),
                ),
                TextButton(
                    onPressed: () => AppSettings.openAppSettings().then((_) {
                          PhotoManager.requestPermissionExtend()
                              .then((permissionState) {
                            setState(() {
                              _ps = permissionState;
                            });
                          });
                        }),
                    child: Text(Translation.of(context).authorizeMedias,
                        style: MalouTextStyles(context)
                            .text12Semibold
                            .copyWith(color: MalouColors.primary)))
              ],
            ),
          );
  }

  @override
  bool get wantKeepAlive => true;
}

class DeviceMediaGrid extends StatefulWidget {
  final MediaPickerType mediaPickerType;
  final bool isThumbnailPicker;
  final ScrollController scrollController;
  final EdgeInsets padding;
  const DeviceMediaGrid({
    super.key,
    required this.mediaPickerType,
    this.isThumbnailPicker = false,
    required this.scrollController,
    this.padding = const EdgeInsets.all(0),
  });

  @override
  State<DeviceMediaGrid> createState() => _DeviceMediaGridState();
}

class _DeviceMediaGridState extends State<DeviceMediaGrid> {
  late PagingController<int, Media> _pagingController;
  final pageSize = 40;
  late MediaPickerController mediaPickerController =
      MediaPickerController(context: context);

  @override
  void initState() {
    _pagingController = PagingController(firstPageKey: 0);
    _pagingController.addPageRequestListener((pageKey) {
      _fetchPage(pageKey);
    });
    super.initState();
  }

  _fetchPage(int pageKey) async {
    try {
      final newItems = await _getDeviceMedias(pageKey);
      final isLastPage = newItems.length < pageSize;
      if (isLastPage) {
        _pagingController.appendLastPage(newItems);
      } else {
        final nextPageKey = pageKey + 1;
        _pagingController.appendPage(newItems, nextPageKey);
      }
    } catch (error) {
      _pagingController.error = error;
    }
  }

  Future<List<Media>> _getDeviceMedias(int pageKey) async {
    RequestType type = RequestType.common;

    if (widget.mediaPickerType == MediaPickerType.video) {
      type = RequestType.video;
    } else if (widget.mediaPickerType == MediaPickerType.image) {
      type = RequestType.image;
    } else {
      type = RequestType.common;
    }
    final List<AssetPathEntity> albums = await PhotoManager.getAssetPathList(
        type: type,
        filterOption: FilterOptionGroup()
          ..setOption(
              AssetType.video,
              const FilterOption(
                  durationConstraint: DurationConstraint(
                      min: Duration(seconds: 3),
                      max: Duration(minutes: 1, seconds: 30)))));
    final List<AssetEntity> assets = await albums[0]
        .getAssetListPaged(
          page: pageKey,
          size: pageSize,
        )
        .then((value) => value);
    List<Media> medias = [];
    for (AssetEntity asset in assets) {
      medias.add(Media(
          id: asset.id,
          isDeviceMedia: true,
          category: '',
          userId: '',
          restaurantId: '',
          type: asset.type == AssetType.image
              ? MediaType.photo.name
              : MediaType.video.name,
          urls: {'original': null},
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          dimensions: {
            'original': Dimension(width: asset.width, height: asset.height)
          },
          format: '',
          postIds: [],
          sizes: {},
          socialId: '',
          assetEntity: asset,
          resizeMetadata: ResizeMetadata(
            aspectRatio: asset.width / asset.height,
            cropPosition: CropPosition(left: 0, top: 0),
          ),
          tagIds: []));
    }
    return medias;
  }

  @override
  Widget build(BuildContext context) {
    return PagedGridView<int, Media>(
        scrollController: widget.scrollController,
        shrinkWrap: true,
        pagingController: _pagingController,
        padding: widget.padding,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          mainAxisSpacing: 2,
          crossAxisSpacing: 2,
        ),
        builderDelegate: PagedChildBuilderDelegate<Media>(
            noItemsFoundIndicatorBuilder: (context) => Column(
                  children: [
                    const SizedBox(
                      height: 40,
                    ),
                    Image.asset(
                      IllustrationsPathResolver.resolve(Illustrations.cook),
                      height: 80,
                    ),
                    const SizedBox(height: 20),
                    SizedBox(
                      width: 200,
                      child: Text(
                        Translation.of(context).emptyDeviceMedias,
                        style: MalouTextStyles(context).text14Italic,
                        textAlign: TextAlign.center,
                      ),
                    )
                  ],
                ),
            firstPageProgressIndicatorBuilder: (context) => SkeletonGridLoader(
                  items: 12,
                  highlightColor: MalouColors.borderPrimary,
                  baseColor: MalouColors.backgroundLight,
                  direction: SkeletonDirection.ltr,
                  itemsPerRow: 4,
                  mainAxisSpacing: 2,
                  crossAxisSpacing: 2,
                  builder: Container(),
                ),
            itemBuilder: (context, item, index) => MediaGridItem(
                media: item,
                index: index,
                isThumbnailPicker: widget.isThumbnailPicker,
                isDeviceMedia: true)));
  }
}
