import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_medias/widgets/post_media_picker/widgets/widgets/media_picker_header.widget.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/models/medias/folder.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_medias/widgets/post_media_picker/widgets/malou_media_picker_grid.controller.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_medias/widgets/post_media_picker/widgets/widgets/folder_grid_item.widget.dart';
import 'package:skeleton_loader/skeleton_loader.dart';

import 'widgets/media_grid_item.widget.dart';

class MalouMediaPickerGrid extends StatefulWidget {
  final MediaPickerType mediaPickerType;
  final bool isThumbnailPicker;
  final bool shouldAddPostAttachments;
  final ScrollController scrollController;
  final EdgeInsets padding;
  final String? folderId;
  final VoidCallback? onShouldScrollUp;
  const MalouMediaPickerGrid({
    super.key,
    required this.mediaPickerType,
    this.isThumbnailPicker = false,
    this.shouldAddPostAttachments = true,
    required this.scrollController,
    this.padding = const EdgeInsets.all(0),
    this.folderId,
    this.onShouldScrollUp,
  });

  @override
  State<MalouMediaPickerGrid> createState() => _MalouMediaPickerGridState();
}

class _MalouMediaPickerGridState extends State<MalouMediaPickerGrid> {
  late MalouMediaPickerGridController controller;

  @override
  Widget build(BuildContext context) {
    controller = MalouMediaPickerGridController(context);
    controller.initializePagingController(
        mediaPickerType: widget.mediaPickerType,
        shouldAddPostAttachments: widget.shouldAddPostAttachments,
        context: context,
        folderId: widget.folderId);
    return StreamBuilder<bool>(
        stream: controller.neverPostedOnly$,
        builder: (context, snapshot) {
          if (!snapshot.hasData) {
            return const SizedBox();
          }
          final neverPostedOnly = snapshot.data!;
          return FutureBuilder(
              future: controller.fetchFolders(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return SkeletonLoader(
                    items: 12,
                    highlightColor: MalouColors.borderPrimary,
                    baseColor: MalouColors.backgroundLight,
                    direction: SkeletonDirection.ltr,
                    builder: Container(
                      decoration: const BoxDecoration(
                          color: MalouColors.backgroundDark),
                    ),
                  );
                }
                if (!snapshot.hasData) {
                  return const Text('empty');
                }
                if (widget.folderId == null) {
                  controller.folders = snapshot.data!;
                }

                return Column(
                  children: [
                    MediaPickerHeader(
                        neverPostedOnly: neverPostedOnly,
                        controller: controller,
                        widget: widget),
                    Expanded(
                      child: MediasGrid(
                          widget: widget,
                          controller: controller,
                          folders: snapshot.data!),
                    ),
                  ],
                );
              });
        });
  }
}

class MediasGrid extends StatelessWidget {
  const MediasGrid({
    super.key,
    required this.widget,
    required this.controller,
    required this.folders,
  });

  final MalouMediaPickerGrid widget;
  final MalouMediaPickerGridController controller;
  final List<Folder> folders;

  @override
  Widget build(BuildContext context) {
    return PagedGridView(
      scrollController: widget.scrollController,
      shrinkWrap: true,
      padding: widget.padding,
      pagingController: controller.pagingController,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          mainAxisSpacing: 2,
          crossAxisSpacing: 2,
          childAspectRatio: 1 / 1),
      builderDelegate: PagedChildBuilderDelegate<MediaOrFolder>(
          noItemsFoundIndicatorBuilder: (context) => Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(height: 40),
                  Image.asset(
                    IllustrationsPathResolver.resolve(Illustrations.cook),
                    height: 80,
                  ),
                  const SizedBox(height: 20),
                  SizedBox(
                    width: 200,
                    child: Text(
                      Translation.of(context).emptyGalleryMedias,
                      style: MalouTextStyles(context).text14Italic,
                    ),
                  )
                ],
              ),
          firstPageProgressIndicatorBuilder: (context) => SkeletonGridLoader(
                items: 12,
                highlightColor: MalouColors.borderPrimary,
                baseColor: MalouColors.backgroundLight,
                direction: SkeletonDirection.ltr,
                itemsPerRow: 4,
                mainAxisSpacing: 2,
                crossAxisSpacing: 2,
                builder: Container(
                  decoration:
                      const BoxDecoration(color: MalouColors.backgroundDark),
                ),
              ),
          itemBuilder: (context, item, index) => item.isFolder
              ? FolderGridItem(folder: item.folder!)
              : MediaGridItem(
                  media: item.media!,
                  index: index,
                  isThumbnailPicker: widget.isThumbnailPicker,
                  isDeviceMedia: false)),
    );
  }
}
