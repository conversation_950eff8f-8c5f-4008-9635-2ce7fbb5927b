import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_medias/widgets/post_media_picker/widgets/malou_media_picker_grid.controller.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_medias/widgets/post_media_picker/widgets/malou_media_picker_grid.widget.dart';
import 'package:malou/shared/helpers/utils.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/inputs/text_search.widget.dart';

class MediaPickerHeader extends StatelessWidget {
  const MediaPickerHeader({
    super.key,
    required this.neverPostedOnly,
    required this.controller,
    required this.widget,
  });

  final bool neverPostedOnly;
  final MalouMediaPickerGridController controller;
  final MalouMediaPickerGrid widget;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          margin: const EdgeInsets.only(top: 65),
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Text(
                  Translation.of(context).displayOnlyNeverPosted,
                  style: MalouTextStyles(context).text12,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Switch(
                value: neverPostedOnly,
                onChanged: (value) => controller.toggleNeverPostedOnly(
                  switchValue: value,
                ),
              ),
            ],
          ),
        ),
        Container(
            padding: const EdgeInsets.only(right: 20, left: 20, bottom: 10),
            child: TextSearch(
              onFocus: () => widget.onShouldScrollUp!(),
              controller: controller.textController,
              onClearText: () => {},
              onChanged: debounceTime((_) {
                controller.pagingController.refresh();
              }),
            )),
      ],
    );
  }
}
