import 'package:flutter/material.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/helpers/utils.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:photo_manager_image_provider/photo_manager_image_provider.dart';

class DeviceMediaGridItem extends StatelessWidget {
  final Media media;
  final int index;
  const DeviceMediaGridItem(
      {super.key, required this.media, required this.index});

  @override
  Widget build(BuildContext context) {
    return Stack(
      fit: StackFit.expand,
      children: [
        AssetEntityImage(
          media.assetEntity!,
          fit: BoxFit.cover,
          isOriginal: false,
          thumbnailSize: const ThumbnailSize.square(150),
          thumbnailFormat: ThumbnailFormat.jpeg,
        ),
        if (media.assetEntity?.type == AssetType.video)
          Align(
            alignment: Alignment.bottomRight,
            child: Container(
              margin: const EdgeInsets.all(5),
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
              ),
              child: Text(
                  formatDuration(media.assetEntity?.videoDuration ??
                      const Duration(seconds: 0)),
                  style: MalouTextStyles(context)
                      .text10Semibold
                      .copyWith(color: Colors.white)),
            ),
          ),
        if (media.assetEntity?.type == AssetType.video)
          Align(
              alignment: Alignment.center,
              child: Icon(
                MalouIcons.play,
                color: Colors.white.withOpacity(0.8),
                size: 25,
              )),
      ],
    );
  }
}
