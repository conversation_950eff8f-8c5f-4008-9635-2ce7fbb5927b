import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_speed_dial/flutter_speed_dial.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/models/posts/post.model.dart'
    show PublicationStatus;
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:photo_manager_image_provider/photo_manager_image_provider.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';

const double sizeFactor = 0.4;

class ZoomableImage extends StatefulWidget {
  final Media media;
  final double firstMediaAspectRatio;
  final PostsProvider postsProvider;
  final void Function(double) onScaleUpdate;
  final double scale;
  const ZoomableImage(
      {super.key,
      required this.media,
      required this.firstMediaAspectRatio,
      required this.postsProvider,
      required this.onScaleUpdate,
      required this.scale});

  @override
  State<ZoomableImage> createState() => _ZoomableImageState();
}

class _ZoomableImageState extends State<ZoomableImage> {
  List<IconData> cropIcons = [
    Icons.crop_square_outlined,
    Icons.crop_landscape_outlined,
    Icons.crop_portrait_outlined
  ];

  IconData selectedCropIcon = Icons.crop_square_outlined;
  double boxImageWidth = 0;
  String firstMediaBiggerSide = 'width';
  late PostsProvider postsProvider;
  late StreamSubscription sub;
  Key _imageKey = UniqueKey();

  @override
  void initState() {
    postsProvider = Provider.of<PostsProvider>(context, listen: false);
    Future.delayed(const Duration(milliseconds: 200), () {
      // ignore: avoid_function_literals_in_foreach_calls
      postsProvider.postToEdit.attachments.forEach((element) {
        setState(() {
          setMediaScale(element);
        });
      });
    });

    postsProvider.addedNewMedia$ = BehaviorSubject.seeded(null);

    listenToNewMediaAdded();

    double square = 1.0;
    if (widget.firstMediaAspectRatio > square) {
      selectedCropIcon = Icons.crop_landscape_outlined;
    } else if (widget.firstMediaAspectRatio == square) {
      selectedCropIcon = Icons.crop_square_outlined;
    } else {
      selectedCropIcon = Icons.crop_portrait_outlined;
    }

    super.initState();
  }

  void listenToNewMediaAdded() {
    sub = postsProvider.addedNewMedia$
        .delay(const Duration(milliseconds: 100))
        .skip(1)
        .listen((value) {
      setState(() {
        setMediaScale(widget.media);
      });
    });
  }

  void setMediaScale(Media media) {
    if (media.resizeMetadata == null) return;
    double initialScale = media.getInitialScale(
        getScreenSizeFromAspectRatio(media.resizeMetadata!.aspectRatio!),
        media.resizeMetadata!.aspectRatio!);

    if (media.transformationController?.value.getMaxScaleOnAxis() == 1.0) {
      media.transformationController?.value.scale(initialScale);
    }
  }

  @override
  dispose() {
    sub.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return Stack(
      children: [
        Center(
            child: AspectRatio(
          aspectRatio: getAspectRatio(widget.firstMediaAspectRatio),
          child: InteractiveViewer(
              onInteractionUpdate: (details) {
                widget.onScaleUpdate.call(details.scale);
              },
              constrained: false,
              transformationController: widget.media.transformationController,
              minScale: 0.1,
              maxScale: 4.0,
              scaleEnabled: true,
              panEnabled: true,
              child: buildImage(size)),
        )),
        Consumer<PostsProvider>(builder: (context, postsProvider, child) {
          bool isFirstMedia = postsProvider.selectedPostMediaIndex == 0;
          bool isAlreadyPublished =
              postsProvider.postToEdit.published == PublicationStatus.PUBLISHED;
          if (!isFirstMedia || isAlreadyPublished) return const SizedBox();
          return Positioned(
              bottom: 20,
              right: 10,
              child: SpeedDial(
                  icon: selectedCropIcon,
                  activeIcon: selectedCropIcon,
                  foregroundColor: Colors.white,
                  visible: true,
                  closeManually: false,
                  renderOverlay: false,
                  curve: Curves.bounceIn,
                  heroTag: 'speed-dial-hero-tag',
                  elevation: 8.0,
                  gradientBoxShape: BoxShape.circle,
                  gradient: MalouColors.gradientFive,
                  spaceBetweenChildren: 10,
                  shape: const CircleBorder(),
                  backgroundColor: MalouColors.primary,
                  activeBackgroundColor: MalouColors.primary,
                  onPress: null,
                  children: cropIcons
                      .map((e) => SpeedDialChild(
                            onTap: () {
                              onCropSizeChange(e, postsProvider);
                            },
                            child: Icon(e),
                            label: getCropLabel(e),
                            labelBackgroundColor: MalouColors.textTitle,
                            labelStyle: MalouTextStyles(context)
                                .text12Semibold
                                .copyWith(color: Colors.white),
                            backgroundColor: MalouColors.primary,
                            foregroundColor: Colors.white,
                            shape: const CircleBorder(),
                          ))
                      .toList()));
        })
      ],
    );
  }

  String getCropLabel(IconData e) {
    return cropIcons.indexOf(e) == 0
        ? Translation.of(context).square
        : cropIcons.indexOf(e) == 1
            ? Translation.of(context).landsquape
            : Translation.of(context).portrait;
  }

  void onCropSizeChange(IconData e, PostsProvider postsProvider) {
    setState(() {
      selectedCropIcon = e;
      double aspectRatio = getAspectRatioFromSelectedIcon(e);
      Size screenSize = getScreenSizeFromAspectRatio(aspectRatio);
      postsProvider.postToEdit = postsProvider.postToEdit.copyWith(
          attachments: postsProvider.postToEdit.attachments
              .map((element) => element.copyWith(
                  aspectRatio: aspectRatio,
                  isDirty: true,
                  resizeMetadata: element.resizeMetadata?.copyWith(
                    aspectRatio: aspectRatio,
                    displayBoxSize: screenSize,
                  ),
                  transformationController: TransformationController(
                      Matrix4.identity()
                        ..scale(
                            element.getInitialScale(screenSize, aspectRatio)))))
              .toList());
    });
  }

  double getAspectRatioFromSelectedIcon(IconData e) {
    var aspectRatio = cropIcons.indexOf(e) == 0
        ? 1.0
        : cropIcons.indexOf(e) == 1
            ? 16 / 9
            : 4 / 5;
    return aspectRatio;
  }

  Widget buildImage(Size size) {
    return widget.media.assetEntity != null
        ? AssetEntityImage(
            widget.media.assetEntity!,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) => Image.asset(
                fit: BoxFit.cover,
                IllustrationsPathResolver.resolve(Illustrations.defaultMalou)),
          )
        : CachedNetworkImage(
            key: _imageKey,
            imageUrl: widget.media.getMediaUrl(size: MediaSize.original.name),
            fit: BoxFit.cover,
            alignment: Alignment.center,
            errorListener: (error) => setState(() {
              _imageKey = UniqueKey();
            }),
          );
  }

  double getAspectRatio(double initialAspectRatio) {
    if (initialAspectRatio == 1.0) return 1.0;

    return initialAspectRatio > 1.0 ? 16 / 9 : 4 / 5;
  }

  Size getScreenSizeFromAspectRatio(double aspectRatio) {
    Size size;
    if (aspectRatio > 1) {
      size = Size(
        MediaQuery.of(context).size.width,
        MediaQuery.of(context).size.width / aspectRatio,
      );
    } else if (aspectRatio == 1) {
      size = Size(
        MediaQuery.of(context).size.height * sizeFactor,
        MediaQuery.of(context).size.height * sizeFactor,
      );
    } else {
      size = Size(
        MediaQuery.of(context).size.height *
            sizeFactor *
            getAspectRatio(aspectRatio),
        MediaQuery.of(context).size.height * sizeFactor,
      );
    }
    return size;
  }
}
