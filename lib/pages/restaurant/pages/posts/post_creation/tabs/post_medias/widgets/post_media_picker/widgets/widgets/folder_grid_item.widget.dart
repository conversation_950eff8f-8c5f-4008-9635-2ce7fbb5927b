import 'package:flutter/material.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_medias/widgets/post_media_picker/post_media_picker.provider.dart';
import 'package:malou/shared/models/medias/folder.model.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:provider/provider.dart';

class FolderGridItem extends StatelessWidget {
  final Folder folder;

  const FolderGridItem({super.key, required this.folder});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => context.read<PostMediaPickerProvider>().selectFolder(folder),
      child: Container(
        decoration: const BoxDecoration(color: MalouColors.backgroundDark),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Text(
                folder.name,
                style: MalouTextStyles(context).text12Medium,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Icon(
              Icons.folder,
              color: MalouColors.backgroundWhite,
              size: MediaQuery.of(context).size.width * 0.15,
            ),
          ],
        ),
      ),
    );
  }
}
