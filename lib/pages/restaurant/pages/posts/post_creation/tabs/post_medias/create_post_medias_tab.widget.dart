import 'package:flutter/material.dart';
import 'package:malou/shared/models/posts/post.model.dart';

import 'widgets/post_media_carousel_preview/post_media_carousel_preview.widget.dart';
import 'widgets/post_media_picker/post_media_picker.widget.dart';
import 'widgets/post_media_preview/post_media_preview.widget.dart';

class CreatePostMediasTab extends StatefulWidget {
  const CreatePostMediasTab({super.key});

  @override
  State<CreatePostMediasTab> createState() => _CreatePostMediasTabState();
}

class _CreatePostMediasTabState extends State<CreatePostMediasTab> {
  @override
  Widget build(BuildContext context) {
    return const Stack(
      children: [
        Column(
          children: [PostMediaPreview(), PostMediaCarouselPreview()],
        ),
        Align(
          alignment: Alignment.bottomCenter,
          child: PostMediaPicker(
            mediaPickerType: MediaPickerType.all,
          ),
        )
      ],
    );
  }
}
