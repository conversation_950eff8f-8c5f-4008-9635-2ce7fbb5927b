import 'package:flutter/material.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/pages/ai_generation/pages/models/fetch_image_description_params.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/posts/posts.service.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_medias/widgets/post_media_picker/widgets/media_picker.controller.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_medias/widgets/post_media_picker/widgets/widgets/widgets/device_meida_grid_item.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_medias/widgets/post_media_picker/widgets/widgets/widgets/malou_media_grid_item.widget.dart';
import 'package:provider/provider.dart';

class MediaGridItem extends StatefulWidget {
  final Media media;
  final int index;
  final bool isThumbnailPicker;
  final bool isDeviceMedia;
  const MediaGridItem(
      {super.key,
      required this.media,
      required this.index,
      required this.isDeviceMedia,
      this.isThumbnailPicker = false});

  @override
  State<MediaGridItem> createState() => _MediaGridItemState();
}

class _MediaGridItemState extends State<MediaGridItem> {
  bool isVideoOnlyAndMediaIsVideo = false;
  bool isSelected = false;
  bool isVideoAndTooShort = false;
  bool canSelectMedia = true;
  bool isCarouselRestrainedByFb = false;
  bool isSeoAndMediaVideo = false;
  bool isMapstrAndMediaVideo = false;
  bool isSeoAndAlreadyHasAttachment = false;
  String selectedMediaIndex = '';
  late MediaPickerController mediaPickerController =
      MediaPickerController(context: context);
  PostsService postsService = PostsService();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () async {
          PostsProvider pp = Provider.of<PostsProvider>(context, listen: false);
          if (widget.isThumbnailPicker) {
            mediaPickerController.onThumbnailTap(
                pp, widget.media, isSelected)();
            return;
          }
          mediaPickerController.onMediaTap(
              isVideoOnlyAndMediaIsVideo,
              pp.postToEdit.published == PublicationStatus.PUBLISHED &&
                  !pp.postToEdit.isSeo(),
              isVideoAndTooShort,
              context,
              isSeoAndMediaVideo,
              isSeoAndAlreadyHasAttachment,
              isSelected,
              isCarouselRestrainedByFb,
              canSelectMedia,
              isMapstrAndMediaVideo,
              pp,
              widget.media)();

          if (widget.media.type == MediaType.photo.name) {
            await mediaPickerController.handleFetchMediaDescriptionQuery
                .execute(FetchImageDescriptionParams(
                    isDeviceMedia: widget.isDeviceMedia,
                    postsProvider: pp,
                    media: widget.media));
          }
        },
        child: Stack(
          children: [
            widget.media.assetEntity != null
                ? DeviceMediaGridItem(media: widget.media, index: widget.index)
                : MalouMediaGridItem(media: widget.media, index: widget.index),
            Consumer<PostsProvider>(builder: (context, postsProvider, child) {
              resetProperties(postsProvider);
              return Stack(
                children: [
                  if (isSelected)
                    Container(
                      color: MalouColors.textTitle.withOpacity(0.5),
                      child: Center(
                          child:
                              postsProvider.postToEdit.postType == PostType.REEL
                                  ? const Icon(
                                      MalouIcons.check,
                                      color: Colors.white,
                                      size: 25,
                                    )
                                  : Text(
                                      selectedMediaIndex,
                                      style: MalouTextStyles(context)
                                          .text20Bold
                                          .copyWith(color: Colors.white),
                                    )),
                    ),
                  if (!widget.isThumbnailPicker &&
                      mediaPickerController.isUnselectable(
                          isVideoOnlyAndMediaIsVideo,
                          postsProvider.postToEdit.published ==
                                  PublicationStatus.PUBLISHED &&
                              !postsProvider.postToEdit.isSeo(),
                          canSelectMedia,
                          isCarouselRestrainedByFb,
                          isSeoAndAlreadyHasAttachment,
                          isSeoAndMediaVideo,
                          isVideoAndTooShort,
                          isSelected,
                          isMapstrAndMediaVideo,
                          postsProvider))
                    Container(
                      color: Colors.grey.withOpacity(0.6),
                    ),
                  if (widget.isThumbnailPicker &&
                      mediaPickerController.isThumbnailUnselectable(
                          canSelectMedia, isSelected))
                    Container(
                      color: Colors.grey.withOpacity(0.6),
                    ),
                ],
              );
            }),
          ],
        ));
  }

  void resetProperties(PostsProvider postsProvider) {
    isSelected = widget.isThumbnailPicker
        ? postsProvider.postToEdit.thumbnail?.id == widget.media.id
        : postsProvider.postToEdit.attachments.cast<Media?>().firstWhere(
                    (element) => element?.id == widget.media.id,
                    orElse: () => null) !=
                null
            ? true
            : false;
    selectedMediaIndex = (postsProvider.postToEdit.attachments
                .indexWhere((element) => element.id == widget.media.id) +
            1)
        .toString();
    isVideoAndTooShort = widget.media.isVideo() &&
        (widget.media.getDurationInSeconds() ?? 10) <= 3;
    canSelectMedia = postsProvider.postToEdit.attachments.length <
            PostsProvider.MAX_POST_ATTACHMENTS
        ? true
        : postsProvider.postToEdit.attachments.length ==
                    (widget.isThumbnailPicker
                        ? 1
                        : PostsProvider.MAX_POST_ATTACHMENTS) &&
                isSelected
            ? true
            : false;
    isCarouselRestrainedByFb =
        (postsProvider.postToEdit.hasFbCheckedAndImageSelected() &&
                widget.media.isVideo()) ||
            postsProvider.postToEdit.hasFbCheckedAndVideoSelected();
    isSeoAndMediaVideo =
        postsProvider.postToEdit.isSeo() && widget.media.isVideo();
    isSeoAndAlreadyHasAttachment = postsProvider.postToEdit.isSeo() &&
        postsProvider.postToEdit.attachments.isNotEmpty;
    isVideoOnlyAndMediaIsVideo =
        postsProvider.postToEdit.postType == PostType.REEL
            ? widget.media.isVideo()
            : true;
    isMapstrAndMediaVideo =
        postsProvider.postToEdit.keys.contains(PlatformKeys.mapstr) &&
            widget.media.isVideo() &&
            postsProvider.postToEdit.attachments.isEmpty;
  }
}
