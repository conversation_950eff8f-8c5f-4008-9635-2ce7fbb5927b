import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:malou/shared/models/media.model.dart';

import 'video_thumbnail.widget.dart';

class MalouMediaGridItem extends StatelessWidget {
  final Media media;
  final int index;
  const MalouMediaGridItem(
      {super.key, required this.media, required this.index});

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return !media.isVideo()
        ? Image.network(
            media.getMediaUrl(size: MediaSize.small.name),
            fit: BoxFit.cover,
            height: size.height,
            width: size.width,
            errorBuilder: (context, error, stackTrace) => CachedNetworkImage(
              imageUrl: media.getMediaUrl(size: MediaSize.small.name),
              fit: BoxFit.cover,
            ),
          )
        : VideoMediaThumbnail(
            thumbnailurl: media.thumbnail,
            url: media.getMediaUrl(),
            width: size.width,
            height: size.height,
            fit: BoxFit.cover,
            borderRadius: BorderRadius.circular(0),
          );
  }
}
