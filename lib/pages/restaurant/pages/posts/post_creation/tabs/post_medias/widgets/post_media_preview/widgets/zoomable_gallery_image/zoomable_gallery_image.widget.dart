import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:malou/shared/helpers/utils.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'zoomable_image.widget.dart';
import 'zoomable_image_grid.widget.dart';

class ZoomableGalleryImage extends StatefulWidget {
  final Media media;
  const ZoomableGalleryImage({super.key, required this.media});

  @override
  State<ZoomableGalleryImage> createState() => _ZoomableGalleryImageState();
}

class _ZoomableGalleryImageState extends State<ZoomableGalleryImage>
    with TickerProviderStateMixin {
  bool showGrid = false;
  late BehaviorSubject<void> refreshMedia$;
  late String? previousSelectedMediaId;
  late PostsProvider postsProvider;
  double? firstMediaAspectRatio;
  double scale = 1.0;

  @override
  void initState() {
    previousSelectedMediaId = widget.media.id;
    postsProvider = Provider.of<PostsProvider>(context, listen: false);
    refreshMedia$ = BehaviorSubject<void>.seeded(null);
    refreshMedia$.listen((_) => refreshMediaAspectRatio());
    firstMediaAspectRatio =
        postsProvider.postToEdit.attachments[0].resizeMetadata?.aspectRatio ??
            1.0;

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<PostsProvider>(builder: (context, postsProvider, child) {
      if (previousSelectedMediaId !=
          postsProvider.postToEdit
              .attachments[postsProvider.selectedPostMediaIndex].id) {
        previousSelectedMediaId = postsProvider
            .postToEdit.attachments[postsProvider.selectedPostMediaIndex].id;
        refreshMedia$.add(null);
      }
      firstMediaAspectRatio =
          postsProvider.postToEdit.attachments[0].resizeMetadata?.aspectRatio ??
              1.0;
      return Stack(
        children: [
          ZoomableImage(
            scale: scale,
            media: widget.media,
            firstMediaAspectRatio: firstMediaAspectRatio ?? 1.0,
            postsProvider: postsProvider,
            onScaleUpdate: (scale) {
              debounceTime((_) {
                postsProvider.postToEdit = postsProvider.postToEdit.copyWith(
                    attachments: postsProvider.postToEdit.attachments
                        .map((e) {
                          if (e.id == widget.media.id) {
                            return e.copyWith(isDirty: true);
                          }
                          return e;
                        })
                        .cast<Media>()
                        .toList());
              })(scale);
              setState(() {
                showGrid = true;
              });
            },
          ),
          IgnorePointer(
            ignoring: true,
            child: Align(
                alignment: Alignment.center,
                child: showGrid
                    ? Animate(
                        onComplete: (_) {
                          setState(() {
                            showGrid = false;
                          });
                        },
                        child: const ZoomableImageGrid(),
                      ).fadeOut(
                        duration: const Duration(milliseconds: 500),
                        delay: const Duration(milliseconds: 4000),
                        curve: Curves.easeIn,
                      )
                    : Container()),
          )
        ],
      );
    });
  }

  Future<void> refreshMediaAspectRatio() async {
    PostsProvider postsProvider =
        Provider.of<PostsProvider>(context, listen: false);
    int selectedPostMediaIndex = postsProvider.selectedPostMediaIndex.isNegative
        ? 0
        : postsProvider.selectedPostMediaIndex;
    bool isFirstMedia = selectedPostMediaIndex == 0;
    Media currentMedia;
    if (postsProvider.postToEdit.attachments.isEmpty) {
      currentMedia = widget.media;
      isFirstMedia = true;
      currentMedia = await currentMedia.setAspectRatioFromUrl();
    } else {
      currentMedia =
          postsProvider.postToEdit.attachments[selectedPostMediaIndex];
    }

    if (isFirstMedia) {
      setState(() {
        firstMediaAspectRatio = currentMedia.resizeMetadata?.aspectRatio;
      });
      setMediaAspectRatio(postsProvider, currentMedia,
          currentMedia.resizeMetadata?.aspectRatio ?? 1.0);
    } else {
      setMediaAspectRatio(
          postsProvider, currentMedia, firstMediaAspectRatio ?? 1.0);
    }
  }

  void setMediaAspectRatio(
      PostsProvider postsProvider, Media currentMedia, double aspectRatio) {
    postsProvider.postToEdit = postsProvider.postToEdit.copyWith(
        attachments: postsProvider.postToEdit.attachments
            .map((e) {
              return e.copyWith(
                  resizeMetadata: e.resizeMetadata?.copyWith(
                      aspectRatio: aspectRatio,
                      displayBoxSize:
                          getScreenSizeFromAspectRatio(aspectRatio)));
            })
            .cast<Media>()
            .toList());
  }

  Size getScreenSizeFromAspectRatio(double aspectRatio) {
    Size size;
    if (aspectRatio > 1) {
      size = Size(
        MediaQuery.of(context).size.width,
        MediaQuery.of(context).size.width / aspectRatio,
      );
    } else if (aspectRatio == 1) {
      size = Size(
        MediaQuery.of(context).size.height * sizeFactor,
        MediaQuery.of(context).size.height * sizeFactor,
      );
    } else {
      size = Size(
        MediaQuery.of(context).size.height *
            sizeFactor *
            getAspectRatio(aspectRatio),
        MediaQuery.of(context).size.height * sizeFactor,
      );
    }
    return size;
  }

  double getAspectRatio(double initialAspectRatio) {
    if (initialAspectRatio == 1.0) return 1.0;

    return initialAspectRatio > 1.0 ? 16 / 9 : 4 / 5;
  }
}
