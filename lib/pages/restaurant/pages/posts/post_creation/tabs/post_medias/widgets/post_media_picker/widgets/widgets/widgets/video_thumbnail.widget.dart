import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/widgets/image/cached_image.widget.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';
import 'package:video_thumbnail/video_thumbnail.dart';

class VideoMediaThumbnail extends StatelessWidget {
  final String url;
  final String? thumbnailurl;
  final double width;
  final double height;
  final BoxFit fit;
  final BorderRadius borderRadius;
  final bool showPlayIcon;
  final int momentInMs;
  final int maxThumbWidth;
  final int maxThumbHeight;
  VideoMediaThumbnail({
    Key? key,
    required this.url,
    this.width = 100,
    this.height = 100,
    this.fit = BoxFit.cover,
    required this.borderRadius,
    this.showPlayIcon = true,
    this.momentInMs = 1000,
    this.thumbnailurl,
    int? maxThumbWidth,
    int? maxThumbHeight,
  })  : maxThumbWidth = maxThumbWidth ?? width.toInt(),
        maxThumbHeight = maxThumbHeight ?? height.toInt(),
        super(key: key);

  @override
  Widget build(BuildContext context) {
    return thumbnailurl != null
        ? Stack(
            children: [
              CachedImage(
                  url: thumbnailurl,
                  height: height,
                  width: width,
                  radius: borderRadius),
              if (showPlayIcon)
                Center(
                    child: Icon(
                  MalouIcons.play,
                  color: Colors.white.withOpacity(0.8),
                  size: 25,
                ))
            ],
          )
        : ClipRRect(
            borderRadius: borderRadius,
            child: FutureBuilder(
              future: VideoThumbnail.thumbnailData(
                  video: url,
                  imageFormat: ImageFormat.JPEG,
                  maxWidth: maxThumbWidth,
                  maxHeight: maxThumbHeight,
                  timeMs: momentInMs,
                  quality: 10),
              builder: (context, snapshot) {
                if (snapshot.hasError) {
                  return SizedBox(
                    width: width,
                    height: height,
                    child: const Center(
                        child: Icon(
                      Icons.error,
                      color: MalouColors.error,
                      size: 20,
                    )),
                  );
                }

                if (snapshot.connectionState == ConnectionState.waiting) {
                  return SizedBox(
                    width: width,
                    height: height,
                    child: const Center(
                      child:
                          CircularSpinner(size: 20, color: MalouColors.primary),
                    ),
                  );
                }
                if (snapshot.connectionState == ConnectionState.done &&
                    (snapshot.hasError || snapshot.data == null)) {
                  return SizedBox(
                    width: width,
                    height: height,
                    child: const Center(
                        child: Icon(
                      Icons.error,
                      color: MalouColors.error,
                      size: 20,
                    )),
                  );
                }
                return Stack(
                  children: [
                    Image.memory(snapshot.data as Uint8List,
                        width: width,
                        height: height,
                        fit: fit,
                        errorBuilder: (context, error, stackTrace) => SizedBox(
                              width: width,
                              height: height,
                              child: const Center(
                                  child: Icon(
                                Icons.error,
                                color: MalouColors.error,
                                size: 20,
                              )),
                            )),
                    if (showPlayIcon)
                      Align(
                          alignment: Alignment.center,
                          child: Icon(
                            MalouIcons.play,
                            color: Colors.white.withOpacity(0.8),
                            size: 25,
                          )),
                  ],
                );
              },
            ),
          );
  }
}
