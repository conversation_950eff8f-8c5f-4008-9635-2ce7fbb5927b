import 'dart:async';

import 'package:flutter/material.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:video_player/video_player.dart';

class PostVideoPreview extends StatefulWidget {
  final Media media;

  const PostVideoPreview({super.key, required this.media});

  @override
  State<PostVideoPreview> createState() => _PostVideoPreviewState();
}

class _PostVideoPreviewState extends State<PostVideoPreview> {
  VideoPlayerController? _controller;
  late PostsProvider postsProvider;
  late BehaviorSubject<PostsProvider> refreshMedia$;
  bool isErrorLoadingVideo = false;
  bool isLoading = true;
  late String? previousSelectedMediaId;
  GlobalKey videoBoxKey = GlobalKey();

  @override
  void initState() {
    previousSelectedMediaId = widget.media.id;
    postsProvider = Provider.of<PostsProvider>(context, listen: false);
    refreshMedia$ = BehaviorSubject<PostsProvider>.seeded(postsProvider);
    refresh(postsProvider);
    refreshMedia$.listen((postsProvider) {
      if (postsProvider.postToEdit.attachments.isEmpty) {
        return;
      }
      if (postsProvider.postToEdit
              .attachments[postsProvider.selectedPostMediaIndex].id !=
          previousSelectedMediaId) {
        refresh(postsProvider);
        return;
      }
      return;
    });

    postsProvider.postCreationTabsController.addListener(() {
      if (postsProvider.postCreationTabsController.indexIsChanging) {
        _controller?.pause();
      }
    });

    super.initState();
  }

  @override
  dispose() {
    refreshMedia$.close();
    _controller?.pause();
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<PostsProvider>(builder: (context, postsProvider, child) {
      refreshMedia$.add(postsProvider);
      return isLoading
          ? Container(
              decoration: BoxDecoration(
                  color: MalouColors.backgroundDark,
                  borderRadius: BorderRadius.circular(10)),
              child: const Center(
                  child: CircularSpinner(
                size: 50,
                color: MalouColors.primary,
              )))
          : isErrorLoadingVideo
              ? Container(
                  decoration: BoxDecoration(
                      color: MalouColors.backgroundDark,
                      borderRadius: BorderRadius.circular(10)),
                  child: Center(
                    child: Text('Error loading video',
                        style: MalouTextStyles(context)
                            .text10Semibold
                            .copyWith(color: MalouColors.textBody)),
                  ),
                )
              : GestureDetector(
                  onTap: () {
                    setState(() {
                      _controller!.value.isPlaying
                          ? _controller?.pause()
                          : _controller?.play();
                    });
                  },
                  child: Stack(
                    children: [
                      Center(
                        child: Consumer<PostsProvider>(
                            builder: (context, postsProvider, child) {
                          double firstMediaAspectRatio = postsProvider
                                  .postToEdit
                                  .attachments[0]
                                  .resizeMetadata
                                  ?.aspectRatio ??
                              _controller!.value.aspectRatio;
                          return AspectRatio(
                              key: videoBoxKey,
                              aspectRatio:
                                  getAspectRatio(firstMediaAspectRatio),
                              child: FittedBox(
                                  fit: BoxFit.cover,
                                  clipBehavior: Clip.hardEdge,
                                  child: SizedBox(
                                      height: _controller?.value.size.height,
                                      width: _controller?.value.size.width,
                                      child: VideoPlayer(_controller!))));
                        }),
                      ),
                      if (!_controller!.value.isPlaying)
                        Center(
                          child: Container(
                            width: 80,
                            decoration: BoxDecoration(
                                color: Colors.black.withOpacity(0.4),
                                shape: BoxShape.circle),
                            child: Center(
                              child: IconButton(
                                padding: EdgeInsets.zero,
                                icon: const Icon(
                                  MalouIcons.play,
                                  color: Colors.white,
                                  size: 50,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _controller?.play();
                                  });
                                },
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                );
    });
  }

  void refresh(PostsProvider postsProvider) async {
    setState(() {
      isLoading = true;
    });
    previousSelectedMediaId = postsProvider
        .postToEdit.attachments[postsProvider.selectedPostMediaIndex].id;

    if (mounted) {
      if (_controller != null) {
        _controller?.pause();
        _controller?.dispose();
      }
      if (widget.media.assetEntity != null) {
        _controller = VideoPlayerController.file(
            (await widget.media.assetEntity!.file)!)
          ..initialize().then((value) => onInitialize()).catchError((error) {
            if (mounted) {
              setState(() {
                isLoading = false;
                isErrorLoadingVideo = true;
              });
            }
          });
      } else {
        _controller = VideoPlayerController.networkUrl(
            Uri.parse(widget.media.getMediaUrl()))
          ..initialize().then((_) => onInitialize()).catchError((error) {
            if (mounted) {
              setState(() {
                isLoading = false;
                isErrorLoadingVideo = true;
              });
            }
          });
      }
    }
  }

  FutureOr onInitialize() {
    Future.delayed(const Duration(seconds: 1), () {
      Media currentMedia = postsProvider
          .postToEdit.attachments[postsProvider.selectedPostMediaIndex];

      currentMedia.resizeMetadata?.displayBoxSize = Size(
        videoBoxKey.currentContext?.size?.width ?? 0,
        videoBoxKey.currentContext?.size?.height ?? 0,
      );
      postsProvider.postToEdit = postsProvider.postToEdit.copyWith(
          attachments: postsProvider.postToEdit.attachments
              .map((e) {
                if (e.id == currentMedia.id) {
                  return currentMedia;
                }
                return e;
              })
              .cast<Media>()
              .toList());
    });
    return Future.delayed(const Duration(milliseconds: 400), () {
      setState(() {
        _controller?.setLooping(true);
        isLoading = false;
      });

      PostsProvider postsProvider =
          Provider.of<PostsProvider>(context, listen: false);
      Media currentMedia = postsProvider
          .postToEdit.attachments[postsProvider.selectedPostMediaIndex];
      currentMedia = currentMedia.copyWith(
          dimensions: {
            'original': Dimension(
                height: _controller!.value.size.height.toInt(),
                width: _controller!.value.size.width.toInt())
          },
          resizeMetadata: currentMedia.resizeMetadata?.copyWith(
            aspectRatio: postsProvider.selectedPostMediaIndex == 0
                ? getAspectRatio(_controller!.value.aspectRatio)
                : getAspectRatio(postsProvider
                    .postToEdit.attachments[0].resizeMetadata!.aspectRatio!),
          ));

      postsProvider.postToEdit = postsProvider.postToEdit.copyWith(
          attachments: postsProvider.postToEdit.attachments
              .map((e) {
                if (e.id == currentMedia.id) {
                  return currentMedia;
                }
                return e;
              })
              .cast<Media>()
              .toList());
    });
  }

  double getAspectRatio(double initialAspectRatio) {
    if (initialAspectRatio == 1.0) return 1.0;

    return initialAspectRatio > 1.0 ? 16 / 9 : 4 / 5;
  }
}
