import 'package:flutter/material.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:provider/provider.dart';

import 'widgets/post_video_preview.widget.dart';
import 'widgets/zoomable_gallery_image/zoomable_gallery_image.widget.dart';

const sizeFactor = 0.4;

class PostMediaPreview extends StatefulWidget {
  const PostMediaPreview({super.key});

  @override
  State<PostMediaPreview> createState() => _PostMediaPreviewState();
}

class _PostMediaPreviewState extends State<PostMediaPreview> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return Consumer<PostsProvider>(builder: (context, postsProvider, child) {
      Media? selectedMedia = postsProvider.selectedPostMediaIndex.isNegative ||
              postsProvider.selectedPostMediaIndex >=
                  postsProvider.postToEdit.attachments.length
          ? null
          : postsProvider
              .postToEdit.attachments[postsProvider.selectedPostMediaIndex];
      return StreamBuilder<bool>(
          stream: postsProvider.loadingPostToEdit$,
          builder: (context, snapshot) {
            if (!snapshot.hasData) return Container();
            if (snapshot.data == true) {
              return SizedBox(
                height: size.height * sizeFactor,
                width: double.infinity,
                child: const Center(
                  child: CircularSpinner(
                    color: MalouColors.primary,
                    size: 30,
                  ),
                ),
              );
            }
            return Container(
              height: size.height * sizeFactor,
              clipBehavior: Clip.antiAlias,
              decoration: const BoxDecoration(
                color: MalouColors.backgroundDark,
              ),
              child: Center(
                  child: selectedMedia != null
                      ? MediaPreview(
                          media: selectedMedia,
                          post: postsProvider.postToEdit,
                        )
                      : Image.asset(
                          IllustrationsPathResolver.resolve(
                              Illustrations.placeholder),
                          height: 150,
                        )),
            );
          });
    });
  }
}

class MediaPreview extends StatelessWidget {
  final Post post;
  final Media media;
  const MediaPreview({super.key, required this.media, required this.post});

  @override
  Widget build(BuildContext context) {
    return media.isVideo() || media.assetEntity?.type == AssetType.video
        ? PostVideoPreview(media: media)
        : post.isPublished()
            ? Image.network(
                media.getMediaUrl(),
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return const Center(
                    child: CircularSpinner(
                      color: MalouColors.primary,
                      size: 30,
                    ),
                  );
                },
                fit: BoxFit.contain,
              )
            : ZoomableGalleryImage(media: media);
  }
}
