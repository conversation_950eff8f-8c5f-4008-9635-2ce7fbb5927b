import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_speed_dial/flutter_speed_dial.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/posts/tabs/social-networks/feed/feed.page.dart';
import 'package:malou/pages/restaurant/pages/posts/widgets/posts_page_header.widget.dart';
import 'package:malou/pages/restaurant/pages/posts/widgets/posts_tabs.widget.dart';
import 'package:malou/shared/helpers/heap.service.dart';
import 'package:malou/shared/models/posts/post-status.model.dart';
import 'package:malou/shared/models/posts/post.model.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/posts/seo/seo-posts.provider.dart';
import 'package:malou/shared/services/posts/social-posts/social-posts.provider.dart';
import 'package:malou/shared/services/posts/stories/stories.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/toasts/toast.widget.dart';
import 'package:malou/shared/widgets/toasts/toasts.service.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';

class PostsPage extends StatefulWidget {
  const PostsPage({super.key});

  @override
  State<PostsPage> createState() => _PostsPageState();
}

class _PostsPageState extends State<PostsPage>
    with SingleTickerProviderStateMixin {
  late PostsProvider postsProvider;
  late SocialPostsProvider socialPostsProvider;
  late SeoPostsProvider seoPostsProvider;
  late StoriesProvider storiesProvider;
  late RestaurantProvider restaurantProvider;
  String currentRestaurantId = '';
  ValueNotifier<bool> tabChanged = ValueNotifier<bool>(false);

  Subject killSubscription$ = PublishSubject();

  bool isSocialTabSelected = true;

  @override
  void initState() {
    postsProvider = Provider.of<PostsProvider>(context, listen: false);
    socialPostsProvider =
        Provider.of<SocialPostsProvider>(context, listen: false);
    seoPostsProvider = Provider.of<SeoPostsProvider>(context, listen: false);
    storiesProvider = Provider.of<StoriesProvider>(context, listen: false);
    restaurantProvider =
        Provider.of<RestaurantProvider>(context, listen: false);
    currentRestaurantId = restaurantProvider.currentRestaurant!.id;
    _initTabControllerAndListenToTabChange();

    CombineLatestStream.combine2(
        restaurantProvider.restaurantSubject$,
        postsProvider.onPullToRefresh$,
        (a, b) => b).skip(1).takeUntil(killSubscription$).listen((tab) {
      switch (postsProvider.selectedTabIndex$.value) {
        case 0:
          socialPostsProvider.refreshPostsPagingControllers(
              restaurantProvider.currentRestaurant!.id, postsProvider,
              source: Source.social);
          break;
        case 1:
          seoPostsProvider.refreshPostsPagingControllers(
              restaurantProvider.currentRestaurant!.id, postsProvider,
              source: Source.seo);
        case 2:
          storiesProvider.refreshPostsPagingControllers(
              restaurantProvider.currentRestaurant!.id, postsProvider,
              source: Source.social);
          break;
      }
    });

    postsProvider.postBeingCreated$
        .skip(1)
        .takeUntil(killSubscription$)
        .listen((postBeingCreated) {
      if (postBeingCreated.postId == null &&
          postBeingCreated.stepStatus != null) {
        if (postBeingCreated.stepStatus == StepStatus.success) {
          bool fromEdit = postsProvider.fromEdit$.value;
          ToastsService.instance.add(Toast(
            message: fromEdit
                ? Translation.of(context).postSuccessfullyModified
                : Translation.of(context).postCreated,
            type: ToastType.success,
          ));
        } else {
          ToastsService.instance.add(Toast(
            message: Translation.of(context).errorCreatingPost,
            type: ToastType.error,
          ));
        }
      }
    });

    super.initState();
  }

  @override
  void dispose() {
    postsProvider.tabController.dispose();
    killSubscription$.add(null);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 16.0, right: 16.0),
      child: Stack(
        children: [
          NestedScrollView(
            headerSliverBuilder: (context, value) {
              return [
                const PostsPageHeader(),
                SliverToBoxAdapter(
                  child: Container(
                    padding: EdgeInsets.zero,
                    decoration: BoxDecoration(
                      color: MalouColors.borderPrimary,
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: TabBar(
                        padding: EdgeInsets.zero,
                        labelPadding: EdgeInsets.zero,
                        indicatorPadding: EdgeInsets.zero,
                        indicatorColor: Colors.transparent,
                        dividerColor: Colors.transparent,
                        indicatorSize: TabBarIndicatorSize.tab,
                        indicatorWeight: 0,
                        overlayColor:
                            WidgetStateProperty.all(Colors.transparent),
                        unselectedLabelColor: MalouColors.textBody,
                        labelStyle: MalouTextStyles(context).text12Semibold,
                        labelColor: Colors.white,
                        controller: postsProvider.tabController,
                        indicator: BoxDecoration(
                          gradient: MalouColors.gradientFive,
                          borderRadius: BorderRadius.circular(30),
                        ),
                        tabs: [
                          Tab(
                            text: Translation.of(context).socialNetworks,
                          ),
                          Tab(
                            text: Translation.of(context).google,
                          ),
                          // Tab(
                          //   text: Translation.of(context).stories,
                          // ),
                        ]),
                  ),
                )
              ];
            },
            body: PostsTabs(
              tabController: postsProvider.tabController,
            ),
          ),
          Consumer<PostsProvider>(builder: (context, pp, child) {
            if (restaurantProvider.currentRestaurant?.isBrandBusiness() ==
                    true &&
                pp.selectedTabIndex$.value == 1) {
              return const SizedBox();
            }
            return Positioned(
                bottom: 20,
                right: 10,
                child: SpeedDial(
                  closeDialOnPop: true,
                  openCloseDial: tabChanged,
                  icon: Icons.add,
                  activeIcon: Icons.close,
                  foregroundColor: Colors.white,
                  visible: true,
                  closeManually: false,
                  renderOverlay: false,
                  curve: Curves.bounceIn,
                  heroTag: 'speed-dial-hero-tag',
                  elevation: 8.0,
                  gradientBoxShape: BoxShape.circle,
                  gradient: MalouColors.gradientFive,
                  spaceBetweenChildren: 10,
                  shape: const CircleBorder(),
                  backgroundColor: MalouColors.primary,
                  activeBackgroundColor: MalouColors.primary,
                  onPress: pp.selectedTabIndex$.value != 1
                      ? null
                      : () {
                          HeapService.instance
                              .track('create_post_seo', {}, context: context);
                          context.push(
                              '/restaurant/$currentRestaurantId/posts/new',
                              extra: {
                                'postType': PostType.IMAGE,
                                'postSource': Source.seo,
                              });
                        },
                  children: pp.selectedTabIndex$.value == 1
                      ? []
                      : [
                          SpeedDialChild(
                              onTap: () {
                                HeapService.instance.track(
                                    'create_post_carousel', {},
                                    context: context);
                                context.push(
                                    '/restaurant/$currentRestaurantId/posts/new',
                                    extra: {
                                      'postType': PostType.CAROUSEL,
                                      'postSource': postsProvider
                                                  .selectedTabIndex$.value ==
                                              1
                                          ? Source.seo
                                          : Source.social,
                                    });
                              },
                              child: const Icon(Icons.image_outlined),
                              backgroundColor: MalouColors.primary,
                              foregroundColor: Colors.white,
                              shape: const CircleBorder(),
                              label: 'Post',
                              labelBackgroundColor: MalouColors.textTitle,
                              labelStyle: MalouTextStyles(context)
                                  .text12Semibold
                                  .copyWith(color: Colors.white)),
                          SpeedDialChild(
                            onTap: () {
                              HeapService.instance.track('create_post_reel', {},
                                  context: context);
                              context.push(
                                  '/restaurant/$currentRestaurantId/posts/new',
                                  extra: {
                                    'postType': PostType.REEL,
                                    'postSource':
                                        postsProvider.selectedTabIndex$.value ==
                                                1
                                            ? Source.seo
                                            : Source.social,
                                  });
                            },
                            child: const Icon(Icons.video_collection_outlined),
                            backgroundColor: MalouColors.primary,
                            foregroundColor: Colors.white,
                            shape: const CircleBorder(),
                            label: 'Reel',
                            labelBackgroundColor: MalouColors.textTitle,
                            labelStyle: MalouTextStyles(context)
                                .text12Semibold
                                .copyWith(color: Colors.white),
                          ),
                        ],
                ));
          }),
          if (isSocialTabSelected) ...[
            StreamBuilder<bool>(
                stream: socialPostsProvider.canDisplayFeed$.stream,
                builder: (context, snapshot) {
                  if (!snapshot.hasData || !snapshot.data!) {
                    return const SizedBox();
                  }
                  return Positioned(
                      bottom: 20,
                      left: 0,
                      right: 0,
                      child: Center(
                          child: TextButton(
                                  style: ButtonStyle(
                                      backgroundColor:
                                          WidgetStateProperty.all<Color>(
                                              MalouColors.textTitle),
                                      shape: WidgetStateProperty.all<
                                              RoundedRectangleBorder>(
                                          RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      10.0)))),
                                  onPressed: () {
                                    HeapService.instance.track('see_feed', {},
                                        context: context);
                                    Navigator.of(context, rootNavigator: true)
                                        .push(
                                      MaterialPageRoute(
                                          fullscreenDialog: true,
                                          builder: (context) =>
                                              const FeedPage()),
                                    )
                                        .then((value) {
                                      socialPostsProvider
                                          .refreshPostsPagingControllers(
                                              restaurantProvider
                                                  .currentRestaurant!.id,
                                              postsProvider,
                                              source: Source.social);
                                    });
                                  },
                                  child: Text(Translation.of(context).seeFeed,
                                      style: MalouTextStyles(context)
                                          .text13Semibold
                                          .copyWith(color: Colors.white)))
                              .animate()
                              .fade(
                                duration: const Duration(milliseconds: 100),
                              )
                              .slideY(
                                duration: const Duration(milliseconds: 100),
                              )));
                })
          ]
        ],
      ),
    );
  }

  void _initTabControllerAndListenToTabChange() {
    postsProvider.tabController = TabController(length: 2, vsync: this);
    postsProvider.tabController.addListener(() => setState(() {
          if (postsProvider.tabController.indexIsChanging) {
            tabChanged.value = false;
            postsProvider.selectedTabIndex$
                .add(postsProvider.tabController.index);
            HeapService.instance.track(
                'posts_tab_changed',
                {
                  'tab': postsProvider.tabController.index,
                },
                context: context);
            // Display feed button only on social tab
            if (postsProvider.tabController.index == 0) {
              isSocialTabSelected = true;
            } else {
              isSocialTabSelected = false;
            }
          }
        }));
  }
}
