import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/heap.service.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/models/reviews_filters.model.dart';
import 'package:malou/shared/services/messaging/messaging.provider.dart';
import 'package:malou/shared/services/platform/platform.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/review/review.provider.dart';
import 'package:malou/shared/services/review/review.service.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';
import 'package:provider/provider.dart';

class Home extends StatefulWidget {
  const Home({super.key});

  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home> {
  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;

    return Consumer2<UserProvider, RestaurantProvider>(
        builder: (context, userProvider, restaurantProvider, child) {
      var currentUser = userProvider.currentUser;
      var currentRestaurant = restaurantProvider.currentRestaurant;
      return ListView(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RichText(
                  text: TextSpan(
                    text: 'Hello ${currentUser?.name} 👋 \n',
                    style: MalouTextStyles(context)
                        .text20Medium
                        .copyWith(fontSize: 31),
                    children: [
                      TextSpan(
                        text: Translation.of(context).whatsNew,
                        style: MalouTextStyles(context)
                            .text20Bold
                            .copyWith(fontSize: 31),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                if (!currentRestaurant!.isBrandBusiness()) ...[
                  const ReviewsStatCard(),
                  const SizedBox(
                    height: 20,
                  ),
                  const ReviewsRatingStatCard(),
                  const SizedBox(
                    height: 20,
                  ),
                ],
                Text(Translation.of(context).todo,
                    style: MalouTextStyles(context).text15Bold),
              ],
            ),
          ),
          SizedBox(
            width: size.width,
            height: size.width * 0.45,
            child: ListView(
              shrinkWrap: true,
              scrollDirection: Axis.horizontal,
              children: [
                const SizedBox(
                  width: 16,
                ),
                UnreadMessagesCard(size: size),
                const SizedBox(
                  width: 20,
                ),
                if (!currentRestaurant.isBrandBusiness()) ...[
                  UnansweredReviewsCard(size: size),
                  const SizedBox(
                    width: 16,
                  ),
                ]
              ],
            ),
          ),
          const SizedBox(
            height: 20,
          )
        ],
      );
    });
  }
}

class UnansweredReviewsCard extends StatefulWidget {
  const UnansweredReviewsCard({
    super.key,
    required this.size,
  });

  final Size size;

  @override
  State<UnansweredReviewsCard> createState() => _UnansweredReviewsCardState();
}

class _UnansweredReviewsCardState extends State<UnansweredReviewsCard> {
  @override
  Widget build(BuildContext context) {
    return Consumer3<ReviewProvider, RestaurantProvider, UserProvider>(builder:
        (context, reviewProvider, restaurantProvider, userProvider, child) {
      return GestureDetector(
        onTap: () {
          var organization = userProvider.getOrganizationById(
              restaurantProvider.currentRestaurant!.organizationId ?? '');
          HeapService.instance.track('click_unanswered_reviews_card', {
            'restaurantId': restaurantProvider.currentRestaurant!.id,
            'restaurant':
                '${restaurantProvider.currentRestaurant?.name} - ${restaurantProvider.currentRestaurant?.getFullFormattedAddress()}',
            'restaurantOrganisation': organization?.name
          });
          context.go(
              '/restaurant/${restaurantProvider.currentRestaurant!.id}/reviews');
          setState(() {
            restaurantProvider.selectedBottomAppBarIndex = 1;
          });
        },
        child: Container(
          height: widget.size.width * 0.45,
          width: widget.size.width * 0.45,
          padding: const EdgeInsets.all(12),
          constraints: BoxConstraints(maxWidth: widget.size.width),
          decoration: BoxDecoration(
              color: MalouColors.backgroundDark,
              borderRadius: BorderRadius.circular(10)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: MalouColors.primary.withOpacity(0.3),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      MalouIcons.reviews,
                      color: MalouColors.primary,
                      size: 30,
                    ),
                  ),
                  const Icon(Icons.arrow_right_alt,
                      color: MalouColors.primary, size: 35)
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  FutureBuilder<int>(
                      future: reviewProvider.countUnansweredReviews(),
                      builder: (context, snapshot) {
                        if (snapshot.hasData) {
                          return Text(
                            snapshot.hasData ? snapshot.data.toString() : '0',
                            style: MalouTextStyles(context)
                                .text20Bold
                                .copyWith(fontSize: 31),
                          );
                        }
                        return Container(
                          margin: const EdgeInsets.only(bottom: 15),
                          child: const CircularSpinner(
                            color: MalouColors.primary,
                            size: 30,
                          ),
                        );
                      }),
                  Text(Translation.of(context).unansweredReviews,
                      style: MalouTextStyles(context).text13Italic),
                  FutureBuilder<int?>(
                      future: reviewProvider.countUrgentReviews(),
                      builder: (context, snapshot) {
                        if (snapshot.hasData && snapshot.data != 0) {
                          return Text(
                              '(${snapshot.data} ${Translation.of(context).urgent})',
                              style: MalouTextStyles(context).text10Italic);
                        }
                        return const SizedBox(
                          height: 10,
                        );
                      }),
                ],
              )
            ],
          ),
        ),
      );
    });
  }
}

class UnreadMessagesCard extends StatefulWidget {
  const UnreadMessagesCard({
    super.key,
    required this.size,
  });

  final Size size;

  @override
  State<UnreadMessagesCard> createState() => _UnreadMessagesCardState();
}

class _UnreadMessagesCardState extends State<UnreadMessagesCard> {
  @override
  Widget build(BuildContext context) {
    return Consumer4<MessagingProvider, RestaurantProvider, PlatformProvider,
            UserProvider>(
        builder: (context, messagingProvider, restaurantProvider,
            platformProvider, userProvider, child) {
      return GestureDetector(
        onTap: () {
          var organization = userProvider.getOrganizationById(
              restaurantProvider.currentRestaurant!.organizationId ?? '');
          HeapService.instance.track('click_unread_messages_card', {
            'restaurantId': restaurantProvider.currentRestaurant!.id,
            'restaurant':
                '${restaurantProvider.currentRestaurant?.name} - ${restaurantProvider.currentRestaurant?.getFullFormattedAddress()}',
            'restaurantOrganisation': organization?.name
          });
          context.go(
              '/restaurant/${restaurantProvider.currentRestaurant!.id}/conversations');
          setState(() {
            restaurantProvider.selectedBottomAppBarIndex =
                restaurantProvider.currentRestaurant!.isBrandBusiness() ? 1 : 2;
          });
        },
        child: Container(
          height: widget.size.width * 0.45,
          width: widget.size.width * 0.45,
          padding: const EdgeInsets.all(12),
          constraints: BoxConstraints(maxWidth: widget.size.width),
          decoration: BoxDecoration(
              color: MalouColors.warningLight,
              borderRadius: BorderRadius.circular(10)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: MalouColors.warning.withOpacity(0.3),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      MalouIcons.convers,
                      color: MalouColors.warning,
                      size: 30,
                    ),
                  ),
                  const Icon(Icons.arrow_right_alt,
                      color: MalouColors.warning, size: 35)
                ],
              ),
              FutureBuilder<int>(
                  future: messagingProvider.initUnreadConversationsCount(
                      restaurantProvider.currentRestaurant!.id,
                      platformProvider.connectedMessagingPlatforms$.value),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return Container(
                        margin: const EdgeInsets.only(bottom: 15),
                        child: const CircularSpinner(
                          color: MalouColors.warning,
                          size: 30,
                        ),
                      );
                    }

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          (snapshot.data ?? 0).toString(),
                          style: MalouTextStyles(context)
                              .text20Bold
                              .copyWith(fontSize: 31),
                        ),
                        Text(Translation.of(context).unreadMessages,
                            style: MalouTextStyles(context).text13Italic),
                        const SizedBox(
                          height: 10,
                        ),
                      ],
                    );
                  }),
            ],
          ),
        ),
      );
    });
  }
}

class ReviewsStatCard extends StatelessWidget {
  const ReviewsStatCard({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;

    Color oceanBlueAccent = const Color(0XFF20B2F0);
    Color oceanBlue = const Color(0XFFD7EEFF);
    return Consumer3<ReviewProvider, RestaurantProvider, PlatformProvider>(
        builder: (context, reviewProvider, restaurantProvider,
            platformsProvider, child) {
      return Container(
        height: 110,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
        width: size.width,
        decoration: BoxDecoration(
            color: oceanBlue, borderRadius: BorderRadius.circular(10)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                FutureBuilder<List<int>>(
                    future: Future.wait([
                      ReviewService.getRestaurantReviewsTotal(
                          restaurantProvider.currentRestaurant!.id,
                          platformsProvider.connectedReviewsPlatforms$.value
                              .map((e) => e.key)
                              .toList()),
                      ReviewService.getRestaurantReviewsTotal(
                          restaurantProvider.currentRestaurant!.id,
                          platformsProvider.connectedReviewsPlatforms$.value
                              .map((e) => e.key)
                              .toList(),
                          MalouComparisonPeriod.previous_period)
                    ]),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return Container(
                          margin: const EdgeInsets.only(bottom: 15),
                          child: CircularSpinner(
                            color: oceanBlueAccent,
                            size: 30,
                          ),
                        );
                      }
                      if (snapshot.hasData) {
                        int diff = (snapshot.data![0] - snapshot.data![1]);
                        return Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(snapshot.data![0].toString(),
                                style: MalouTextStyles(context)
                                    .text20Bold
                                    .copyWith(fontSize: 31)),
                            const SizedBox(width: 10),
                            Container(
                              margin: const EdgeInsets.only(top: 10),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Transform.flip(
                                    flipY: diff < 0,
                                    child: SvgPicture.asset(
                                        'assets/other/blue_arrow.svg',
                                        width: 20,
                                        height: 20),
                                  ),
                                  const SizedBox(
                                    width: 5,
                                  ),
                                  Text(diff.toString(),
                                      style: MalouTextStyles(context)
                                          .text13Bold
                                          .copyWith(color: oceanBlueAccent))
                                ],
                              ),
                            ),
                          ],
                        );
                      }
                      return Text('0',
                          style: MalouTextStyles(context)
                              .text20Bold
                              .copyWith(fontSize: 31));
                    }),
                RichText(
                    text: TextSpan(
                        text: Translation.of(context).receivedReviews,
                        style: MalouTextStyles(context).text13Italic,
                        children: [
                      TextSpan(
                          text: Translation.of(context).lastThirtyDays,
                          style: MalouTextStyles(context).text10Italic)
                    ]))
              ],
            ),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: oceanBlueAccent.withOpacity(0.3),
                shape: BoxShape.circle,
              ),
              child: Center(
                  child: Icon(MalouIcons.reviews,
                      color: oceanBlueAccent, size: 25)),
            )
          ],
        ),
      );
    });
  }
}

class ReviewsRatingStatCard extends StatelessWidget {
  const ReviewsRatingStatCard({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;

    Color purple = const Color(0XFFBD4AC8).withOpacity(0.25);
    Color purpleAccent = const Color(0XFFAC32B7);

    return Consumer3<ReviewProvider, RestaurantProvider, PlatformProvider>(
        builder: (context, reviewProvider, restaurantProvider,
            platformsProvider, child) {
      return Container(
        height: 110,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
        width: size.width,
        decoration: BoxDecoration(
            color: purple, borderRadius: BorderRadius.circular(10)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                FutureBuilder<List<double>>(
                    future: Future.wait([
                      ReviewService.getRestaurantReviewsAverage(
                          restaurantProvider.currentRestaurant!.id,
                          platformsProvider.connectedReviewsPlatforms$.value
                              .map((e) => e.key)
                              .toList()),
                      ReviewService.getRestaurantReviewsAverage(
                          restaurantProvider.currentRestaurant!.id,
                          platformsProvider.connectedReviewsPlatforms$.value
                              .map((e) => e.key)
                              .toList(),
                          MalouComparisonPeriod.previous_period)
                    ]),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return Container(
                          margin: const EdgeInsets.only(bottom: 15),
                          child: CircularSpinner(
                            color: purpleAccent,
                            size: 30,
                          ),
                        );
                      }
                      if (snapshot.hasData) {
                        return Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(snapshot.data![0].toStringAsFixed(1),
                                style: MalouTextStyles(context)
                                    .text20Bold
                                    .copyWith(fontSize: 31)),
                            const SizedBox(width: 10),
                          ],
                        );
                      }
                      return Text('0',
                          style: MalouTextStyles(context)
                              .text20Bold
                              .copyWith(fontSize: 31));
                    }),
                RichText(
                    text: TextSpan(
                  text: Translation.of(context).reviewsRating,
                  style: MalouTextStyles(context).text13Italic,
                ))
              ],
            ),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: purpleAccent.withOpacity(0.3),
                shape: BoxShape.circle,
              ),
              child: Center(
                  child: Icon(MalouIcons.starOutline,
                      color: purpleAccent, size: 25)),
            )
          ],
        ),
      );
    });
  }
}
