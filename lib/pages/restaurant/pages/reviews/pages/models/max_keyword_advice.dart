import 'package:malou/pages/restaurant/pages/reviews/pages/models/keyword_advice.dart';
import 'package:malou/shared/models/keywords/keyword.model.dart';

class MaxKeywordAdvice extends KeywordAdvice {
  final int max = 2;
  MaxKeywordAdvice({required String text}) : super(text: text);

  @override
  bool completed(List<Keyword> keywords, String reply) {
    int nbContains = 0;
    for (var keyword in keywords) {
      if (reply.contains(keyword.text)) {
        nbContains++;
      }
      if (nbContains >= max) {
        return true;
      }
    }
    return false;
  }
}
