import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/helpers/modal_bottom_sheet.dart';
import 'package:malou/shared/helpers/use_query.dart';
import 'package:malou/shared/models/keywords/keywords_score.model.dart';
import 'package:malou/shared/models/quick_text.model.dart';
import 'package:malou/shared/models/reviews.model.dart';
import 'package:malou/shared/models/template.model.dart';
import 'package:malou/shared/services/keyword/keyword.provider.dart';
import 'package:malou/shared/services/organization/organization.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/review/review.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/appbar/appbar_back.widget.dart';
import 'package:malou/shared/widgets/buttons/styles.dart';
import 'package:malou/shared/widgets/chips/quick_text_chips_list.widget.dart';
import 'package:malou/shared/widgets/list_picker/list_picker.widget.dart';
import 'package:malou/shared/widgets/templates_picker/templates_picker.widget.dart';
import 'package:malou/shared/widgets/toasts/toast.widget.dart';
import 'package:malou/shared/widgets/toasts/toasts.service.dart';
import 'package:provider/provider.dart';
import 'package:reactive_forms/reactive_forms.dart';
import 'package:rxdart/rxdart.dart';

class ReviewReplyByMailPage extends StatefulWidget {
  final String reviewId;
  const ReviewReplyByMailPage({super.key, required this.reviewId});

  @override
  State<ReviewReplyByMailPage> createState() => _ReviewReplyByMailPageState();
}

class _ReviewReplyByMailPageState extends State<ReviewReplyByMailPage>
    with TickerProviderStateMixin {
  final TextEditingController _textEditingController = TextEditingController();
  late ReviewProvider reviewProvider;
  late RestaurantProvider restaurantProvider;
  late KeywordProvider keywordProvider;
  late UserProvider userProvider;
  late OrganizationProvider organizationProvider;

  BehaviorSubject<Review?> review = BehaviorSubject.seeded(null);

  String replyContent = "";

  BehaviorSubject<KeywordsScoreProcessResult> keywordScoreAndTips =
      BehaviorSubject.seeded(KeywordsScoreProcessResult(
          score: 0,
          details: [],
          bricksFound: [],
          restaurantNameFound: false,
          reviewerNameFound: false));

  final keyboardVisibilityController = KeyboardVisibilityController();

  bool hasClickedOnEditReply = false;

  List<QuickText> quickTexts = [];

  late QueryResult<Review, ReviewReplyByMailParams> reviewReplyByMailQuery;

  final sendEmailForm = FormGroup({
    'name': FormControl<String>(validators: [Validators.required]),
    'email': FormControl<String>(
        validators: [Validators.required, Validators.email]),
    'object': FormControl<String>(validators: [Validators.required]),
    'text': FormControl<String>(validators: [Validators.required]),
  });

  bool isFormValid = false;

  @override
  void initState() {
    super.initState();
    reviewProvider = Provider.of<ReviewProvider>(context, listen: false);
    keywordProvider = Provider.of<KeywordProvider>(context, listen: false);
    restaurantProvider =
        Provider.of<RestaurantProvider>(context, listen: false);
    userProvider = Provider.of<UserProvider>(context, listen: false);
    organizationProvider =
        Provider.of<OrganizationProvider>(context, listen: false);

    reviewProvider.getReviewById(widget.reviewId).then((value) {
      review.add(value);
    });

    CombineLatestStream.combine2(
            reviewProvider.draftedReviewTexts.take(1), review, (a, b) => [a, b])
        .where((event) => event[1] != null)
        .take(1)
        .listen((value) {
      Map<String, String> draftedReviewTexts = value[0] as Map<String, String>;
      if (draftedReviewTexts[widget.reviewId] != null &&
          draftedReviewTexts[widget.reviewId]!.isNotEmpty) {
        sendEmailForm.control('text').value =
            draftedReviewTexts[widget.reviewId] ?? '';
      }
    });

    sendEmailForm.controls['text']!.valueChanges.listen((value) {
      if (value != null) {
        reviewProvider.draftedReviewTexts.add({
          widget.reviewId: value as String,
        });
      }
    });

    review.listen((value) {
      if (value != null) {
        quickTexts = [
          {
            'key': 'client_name',
            'displayKey': '@${Translation.of(context).clientName}',
            'content': value.displayName(context)
          },
          {
            'key': 'my_firstname',
            'displayKey': '@${Translation.of(context).myName}',
            'content': userProvider.currentUser!.name
          },
          {
            'key': 'business_name',
            'displayKey': '@${Translation.of(context).restaurantName}',
            'content': restaurantProvider.currentRestaurant!.name
          },
        ]
            .map((e) => QuickText(
                key: e['key'] ?? '',
                displayKey: e['displayKey'] ?? '',
                content: e['content'] ?? ''))
            .toList(growable: false);
      }
    });

    reviewReplyByMailQuery = useQuery(reviewProvider.replyByMail);

    reviewReplyByMailQuery.errorFetching.listen((errorFetching) {
      if (errorFetching.isError) {
        ToastsService.instance.add(Toast(
            type: ToastType.error,
            message: Translation.of(context).reviewReplyError));
      }
    });

    sendEmailForm.control('name').value = userProvider.currentUser!.name;

    _textEditingController.addListener(() {
      sendEmailForm.control('text').value = _textEditingController.text;
    });

    sendEmailForm.valueChanges.listen((value) {
      setState(() {
        isFormValid = sendEmailForm.valid;
      });
    });
  }

  String replaceVariables(String text) {
    String newText = text;
    for (var element in quickTexts) {
      newText = newText.replaceAll('[{${element.key}}]', element.content);
    }
    return newText;
  }

  void openTemplatesPicker() {
    AppBottomModal.showAppBottomModal<Template>(
        context,
        TemplatesPicker(
          restaurantId: restaurantProvider.currentRestaurant!.id,
          type: TemplateType.REVIEW,
          templateMatcher: TemplateMatcherReview(
            review.value!,
          ),
        ), afterClosed: (result) {
      sendEmailForm.control('text').value = replaceVariables(result.text);
    });
  }

  void openMailsPicker() {
    AppBottomModal.showAppBottomModal<String>(
        context,
        ListPicker(
          title: Translation.of(context).emailSelected,
          data: organizationProvider
                  .currentOrganization.value.verifiedEmailsForCampaigns ??
              [],
        ), afterClosed: (result) {
      sendEmailForm.control('email').value = result;
    });
  }

  @override
  void dispose() {
    review.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
        length: 2,
        child: Scaffold(
            appBar: AppBarArrowBack(
              title: Translation.of(context).replyByMail,
            ),
            body: _buildBody()));
  }

  Widget _buildBody() {
    return StreamBuilder<Review?>(
        stream: review.stream,
        builder: (context, snapshot) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 15),
            child: ReactiveForm(
              formGroup: sendEmailForm,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(
                      height: 5,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 15),
                      child: ReactiveTextField(
                          formControlName: 'name',
                          validationMessages: {
                            'required': (error) =>
                                Translation.of(context).errorRequired
                          },
                          style: MalouTextStyles(context)
                              .text13
                              .copyWith(color: MalouColors.textTitle),
                          decoration: InputDecoration(
                              focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: const BorderSide(
                                      color: MalouColors.primary)),
                              enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: const BorderSide(
                                      color: MalouColors.backgroundDark)),
                              border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: const BorderSide(
                                      color: MalouColors.backgroundDark)),
                              hintStyle: MalouTextStyles(context).text13Italic,
                              hintText: Translation.of(context).reviewFrom)),
                    ),
                    const SizedBox(
                      height: 5,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 15),
                      child: ReactiveTextField(
                          readOnly: true,
                          onTap: (c) => openMailsPicker(),
                          formControlName: 'email',
                          showErrors: (c) => false,
                          style: MalouTextStyles(context)
                              .text13
                              .copyWith(color: MalouColors.textTitle),
                          decoration: InputDecoration(
                              suffixIcon: const Icon(
                                Icons.keyboard_arrow_down,
                                color: MalouColors.primary,
                              ),
                              focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: const BorderSide(
                                      color: MalouColors.primary)),
                              enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: const BorderSide(
                                      color: MalouColors.backgroundDark)),
                              border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: const BorderSide(
                                      color: MalouColors.backgroundDark)),
                              hintStyle: MalouTextStyles(context).text13Italic,
                              hintText: Translation.of(context).emailSelected)),
                    ),
                    const SizedBox(
                      height: 5,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 15),
                      child: ReactiveTextField(
                          autofocus: true,
                          formControlName: 'object',
                          validationMessages: {
                            'required': (error) =>
                                Translation.of(context).errorRequired,
                          },
                          style: MalouTextStyles(context)
                              .text13
                              .copyWith(color: MalouColors.textTitle),
                          decoration: InputDecoration(
                              focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: const BorderSide(
                                      color: MalouColors.primary)),
                              enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: const BorderSide(
                                      color: MalouColors.backgroundDark)),
                              border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: const BorderSide(
                                      color: MalouColors.backgroundDark)),
                              hintStyle: MalouTextStyles(context).text13Italic,
                              hintText: Translation.of(context).object)),
                    ),
                    const SizedBox(
                      height: 5,
                    ),
                    QuickTextsList(
                        quickTexts: quickTexts,
                        textEditingController: _textEditingController),
                    const SizedBox(
                      height: 10,
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        const SizedBox(
                          width: 15,
                        ),
                        IconButton(
                          constraints: const BoxConstraints(),
                          onPressed: openTemplatesPicker,
                          icon: const Icon(
                            Icons.bookmarks_outlined,
                            size: 20,
                            color: MalouColors.primary,
                          ),
                        ),
                        Expanded(
                          child: ReactiveTextField(
                            controller: _textEditingController,
                            formControlName: 'text',
                            keyboardType: TextInputType.multiline,
                            minLines: 5,
                            maxLines: 10,
                            validationMessages: {
                              'required': (error) =>
                                  Translation.of(context).errorRequired,
                            },
                            style: MalouTextStyles(context)
                                .text13
                                .copyWith(color: MalouColors.textTitle),
                            decoration: InputDecoration(
                                focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10),
                                    borderSide: const BorderSide(
                                        color: MalouColors.primary)),
                                enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10),
                                    borderSide: const BorderSide(
                                        color: MalouColors.backgroundDark)),
                                border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10),
                                    borderSide: const BorderSide(
                                        color: MalouColors.backgroundDark)),
                                hintStyle:
                                    MalouTextStyles(context).text13Italic,
                                hintText: 'Ecrire votre réponse'),
                          ),
                        ),
                        const SizedBox(
                          width: 10,
                        ),
                        StreamBuilder<bool>(
                          stream: reviewReplyByMailQuery.isFetching,
                          builder: (context, snapshot) {
                            return Opacity(
                              opacity: isFormValid && !snapshot.data! ? 1 : 0.5,
                              child: PrimaryIconButtonContainer(
                                  child: IconButton(
                                disabledColor: Colors.white,
                                onPressed: () => onSubmit(snapshot),
                                icon: const Icon(
                                  MalouIcons.send,
                                  size: 20,
                                ),
                              )),
                            );
                          },
                        ),
                        const SizedBox(
                          width: 15,
                        ),
                      ],
                    )
                  ],
                ),
              ),
            ),
          );
        });
  }

  onSubmit(AsyncSnapshot<bool> snapshot) {
    if (isFormValid && !snapshot.data!) {
      reviewReplyByMailQuery
          .execute(ReviewReplyByMailParams(
        review: review.value!,
        text: sendEmailForm.control('text').value,
        restaurantId: restaurantProvider.currentRestaurant!.id,
        user: userProvider.currentUser!,
        fromEmail: sendEmailForm.control('email').value,
        fromName: sendEmailForm.control('name').value,
        object: sendEmailForm.control('object').value,
      ))
          .then((value) {
        if (value != null) {
          _textEditingController.clear();
          reviewProvider.draftedReviewTexts.add({
            widget.reviewId: '',
          });
          review.add(value.copyWith(client: review.value?.client));
          ToastsService.instance.add(Toast(
              type: ToastType.success,
              illustration: Illustrations.cook,
              message: Translation.of(context).reviewReplySentSuccessfully));
          reviewProvider.handleReviewListAfterReply(review.value!);
          sendEmailForm.reset(removeFocus: true);
        }
      });
    }
  }
}
