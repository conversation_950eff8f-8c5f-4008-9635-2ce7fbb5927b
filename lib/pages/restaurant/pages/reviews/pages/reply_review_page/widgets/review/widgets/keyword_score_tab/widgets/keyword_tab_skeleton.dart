import 'package:flutter/material.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:skeleton_loader/skeleton_loader.dart';

class KeywordsTabSkeleton extends StatelessWidget {
  const KeywordsTabSkeleton({
    super.key,
    required this.size,
  });

  final Size size;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SkeletonLoader(
          builder: Container(
            margin: const EdgeInsets.only(bottom: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                    width: size.width * 0.4,
                    height: 50,
                    decoration: BoxDecoration(
                      color: MalouColors.backgroundDark,
                      borderRadius: BorderRadius.circular(10),
                    )),
                Container(
                    width: size.width * 0.2,
                    height: 50,
                    decoration: BoxDecoration(
                      color: MalouColors.backgroundDark,
                      borderRadius: BorderRadius.circular(10),
                    )),
              ],
            ),
          ),
          highlightColor: MalouColors.borderPrimary,
          baseColor: MalouColors.backgroundDark,
          direction: SkeletonDirection.ltr,
        ),
        SkeletonLoader(
          builder: Container(
            margin: const EdgeInsets.only(top: 10),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                      width: double.infinity,
                      height: 30,
                      decoration: BoxDecoration(
                        color: MalouColors.backgroundDark,
                        borderRadius: BorderRadius.circular(10),
                      )),
                ),
                const SizedBox(
                  width: 10,
                ),
                Expanded(
                  child: Container(
                      width: double.infinity,
                      height: 30,
                      decoration: BoxDecoration(
                        color: MalouColors.backgroundDark,
                        borderRadius: BorderRadius.circular(10),
                      )),
                ),
                const SizedBox(
                  width: 10,
                ),
                Expanded(
                  child: Container(
                      width: double.infinity,
                      height: 30,
                      decoration: BoxDecoration(
                        color: MalouColors.backgroundDark,
                        borderRadius: BorderRadius.circular(10),
                      )),
                ),
              ],
            ),
          ),
          items: 5,
          highlightColor: MalouColors.borderPrimary,
          baseColor: MalouColors.backgroundDark,
          direction: SkeletonDirection.ltr,
        ),
        SkeletonLoader(
          builder: Container(
            margin: const EdgeInsets.only(top: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                    width: size.width * 0.35,
                    height: 20,
                    decoration: BoxDecoration(
                      color: MalouColors.backgroundDark,
                      borderRadius: BorderRadius.circular(10),
                    )),
                const SizedBox(
                  height: 10,
                ),
                Container(
                    width: size.width * 0.2,
                    height: 15,
                    decoration: BoxDecoration(
                      color: MalouColors.backgroundDark,
                      borderRadius: BorderRadius.circular(10),
                    ))
              ],
            ),
          ),
          highlightColor: MalouColors.borderPrimary,
          baseColor: MalouColors.backgroundDark,
          direction: SkeletonDirection.ltr,
        ),
        SkeletonLoader(
          builder: Container(
            margin: const EdgeInsets.only(top: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                    width: size.width * 0.5,
                    height: 30,
                    decoration: BoxDecoration(
                      color: MalouColors.backgroundDark,
                      borderRadius: BorderRadius.circular(10),
                    )),
                const SizedBox(
                  width: 10,
                ),
                Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      color: MalouColors.backgroundDark,
                      borderRadius: BorderRadius.circular(10),
                    )),
              ],
            ),
          ),
          items: 3,
          highlightColor: MalouColors.borderPrimary,
          baseColor: MalouColors.backgroundDark,
          direction: SkeletonDirection.ltr,
        ),
      ],
    );
  }
}
