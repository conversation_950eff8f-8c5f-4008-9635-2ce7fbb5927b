import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/reviews/pages/reply_review_page/reply_review.controller.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/models/keywords/keywords_score.model.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

class KeywordsUsageTips extends StatelessWidget {
  const KeywordsUsageTips({
    super.key,
    required this.reviewReplyController,
    required this.size,
  });

  final ReviewReplyController reviewReplyController;
  final Size size;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(Translation.of(context).keywordsAdvices,
            style: MalouTextStyles(context)
                .text13Bold
                .copyWith(color: MalouColors.textTitle)),
        const SizedBox(
          height: 10,
        ),
        StreamBuilder<KeywordsScoreProcessResult?>(
            stream: reviewReplyController.computeKeywordsScoreAndTipsQuery.data,
            builder: (context, snapshot) {
              if (snapshot.data != null) {
                List<KeywordScoreDetail> details = snapshot.data!.details
                    .where((e) =>
                        e.criteria.name != 'restaurantName' ||
                        e.fulfilledValue?.optimalValue != 0)
                    .toList();
                return ListView.separated(
                  shrinkWrap: true,
                  physics: const ClampingScrollPhysics(),
                  itemCount: details.length,
                  itemBuilder: (context, index) {
                    final detail = details[index];
                    return Row(
                      children: [
                        if (!detail.fulfilled)
                          const Icon(
                            MalouIcons.cross,
                            color: MalouColors.primary,
                            size: 20,
                          )
                        else
                          const Icon(
                            MalouIcons.check,
                            color: MalouColors.success,
                            size: 20,
                          ),
                        const SizedBox(
                          width: 15,
                        ),
                        SizedBox(
                          width: size.width * 0.65,
                          child: Wrap(
                            children: [
                              Text(detail.getTitle(context),
                                  style: MalouTextStyles(context)
                                      .text12Semibold
                                      .copyWith(color: MalouColors.textBody)),
                              const SizedBox(
                                width: 5,
                              ),
                              if (!detail.shouldAvoidTips())
                                Text(
                                    "${detail.value}/${detail.fulfilledValue!.optimalValue ?? detail.fulfilledValue!.minimumValue}",
                                    style: MalouTextStyles(context)
                                        .text12Semibold
                                        .copyWith(color: MalouColors.textBody)),
                              const SizedBox(
                                width: 5,
                              ),
                              if (detail.getTip(context) != null)
                                Text(detail.getTip(context)!,
                                    style: MalouTextStyles(context)
                                        .text10Italic
                                        .copyWith(color: MalouColors.textBody)),
                            ],
                          ),
                        ),
                      ],
                    );
                  },
                  separatorBuilder: (context, index) => const SizedBox(
                    height: 15,
                  ),
                );
              }
              return Text(Translation.of(context).noKeywordsYet,
                  style: MalouTextStyles(context)
                      .text10Italic
                      .copyWith(color: MalouColors.textTitle));
            }),
      ],
    );
  }
}
