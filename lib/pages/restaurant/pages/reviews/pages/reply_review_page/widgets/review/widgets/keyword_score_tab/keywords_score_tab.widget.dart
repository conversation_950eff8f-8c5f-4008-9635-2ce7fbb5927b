import 'package:flutter/material.dart';
import 'package:malou/pages/restaurant/pages/reviews/pages/reply_review_page/reply_review.controller.dart';
import 'package:malou/pages/restaurant/pages/reviews/pages/reply_review_page/widgets/review/widgets/keyword_score_tab/widgets/keywords_available.widget.dart';
import 'package:malou/pages/restaurant/pages/reviews/pages/reply_review_page/widgets/review/widgets/keyword_score_tab/widgets/keywords_usage_tips.widget.dart';
import 'package:malou/pages/restaurant/pages/reviews/pages/reply_review_page/widgets/review/widgets/keyword_score_tab/widgets/select_keyword_language.dart';
import 'package:malou/shared/theme/theme_colors.dart';

class KeywordsScoreTab extends StatelessWidget {
  const KeywordsScoreTab(
      {super.key,
      required this.reviewReplyController,
      required this.isInputReplyDisplayed});

  final ReviewReplyController reviewReplyController;
  final bool isInputReplyDisplayed;

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    return ListView(
      children: [
        Container(
          decoration: BoxDecoration(
              color: MalouColors.backgroundLight,
              border: Border.all(color: MalouColors.borderPrimary, width: 1),
              borderRadius: const BorderRadius.all(Radius.circular(10.0))),
          child: Padding(
              padding: EdgeInsets.all(size.width * 0.06),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SelectKeywordLanguage(
                        reviewReplyController: reviewReplyController),
                    const SizedBox(
                      height: 20,
                    ),
                    KeywordsAvailable(
                        reviewReplyController: reviewReplyController,
                        size: size,
                        isInputReplyDisplayed: isInputReplyDisplayed),
                    const SizedBox(
                      height: 30,
                    ),
                    KeywordsUsageTips(
                        reviewReplyController: reviewReplyController,
                        size: size),
                  ])),
        )
      ],
    );
  }
}
