import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/reviews/pages/reply_review_page/reply_review.controller.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:provider/provider.dart';

class SelectKeywordLanguage extends StatelessWidget {
  const SelectKeywordLanguage({
    super.key,
    required this.reviewReplyController,
  });

  final ReviewReplyController reviewReplyController;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<AppLocale>(
        stream: reviewReplyController.selectedLanguage$.stream,
        builder: (context, snapshot) {
          if (!snapshot.hasData) {
            return const SizedBox(
              height: 0,
            );
          }
          final selectedLanguage = snapshot.data!;
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 200),
                child: Text(Translation.of(context).keywords,
                    style: MalouTextStyles(context)
                        .text13Bold
                        .copyWith(color: MalouColors.textTitle)),
              ),
              InkWell(
                onTap: () {
                  reviewReplyController.openKeywordLanguagePicker();
                },
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                        context
                            .read<LanguageProvider>()
                            .getAvailableLanguages(context)[selectedLanguage]!,
                        style: MalouTextStyles(context)
                            .text13Bold
                            .copyWith(color: MalouColors.textTitle)),
                    const SizedBox(
                      width: 5,
                    ),
                    const RotatedBox(
                      quarterTurns: 3,
                      child: Icon(
                        size: 16,
                        MalouIcons.chevronLeft,
                        color: MalouColors.primary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        });
  }
}
