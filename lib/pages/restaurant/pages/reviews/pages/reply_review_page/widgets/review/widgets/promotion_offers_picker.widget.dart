import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/helpers/ordinal.dart';
import 'package:malou/shared/helpers/promotion_offers.dart';
import 'package:malou/shared/models/reviews.model.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/buttons/selected_button.widget.dart';
import 'package:malou/shared/widgets/buttons/styles.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';

class PromotionOffersPicker extends StatefulWidget {
  final BehaviorSubject<List<PromotionOffer>> offers$;
  final Function(UbereatsPromotionValue key) onOfferSelected;
  final Function(UbereatsPromotionValue key) onChooseOffer;
  final String currencyCode;
  final int orderCount;
  final double amount;
  const PromotionOffersPicker({
    super.key,
    required this.offers$,
    required this.onOfferSelected,
    required this.onChooseOffer,
    required this.currencyCode,
    required this.orderCount,
    required this.amount,
  });

  @override
  State<PromotionOffersPicker> createState() => _PromotionOffersPickerState();
}

class _PromotionOffersPickerState extends State<PromotionOffersPicker> {
  late RestaurantProvider restaurantProvider;
  late LanguageProvider languageProvider;

  @override
  void initState() {
    super.initState();
    restaurantProvider =
        Provider.of<RestaurantProvider>(context, listen: false);
    languageProvider = Provider.of<LanguageProvider>(context, listen: false);
  }

  @override
  Widget build(BuildContext context) {
    var amoutFormatted = NumberFormat.decimalPatternDigits(decimalDigits: 2)
        .format(widget.amount);
    var currencySymbol = NumberFormat.simpleCurrency(
      name: widget.currencyCode,
    ).currencySymbol;

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Row(
            children: [
              IconButton(
                icon: const Icon(
                  MalouIcons.arrowLeft,
                  color: MalouColors.primary,
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
              const SizedBox(width: 10),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(Translation.of(context).sendPromotion,
                      style: MalouTextStyles(context)
                          .text20Semibold
                          .copyWith(color: MalouColors.textTitle)),
                  Text(
                    Translation.of(context).orderDetails(
                        "$amoutFormatted $currencySymbol",
                        getOrderCountText(widget.orderCount,
                            lang: languageProvider.language)),
                    style: MalouTextStyles(context)
                        .text12Italic
                        .copyWith(color: MalouColors.textBody),
                  )
                ],
              ),
            ],
          ),
          const SizedBox(height: 10),
          StreamBuilder<List<PromotionOffer>>(
              stream: widget.offers$,
              builder: (context, snapshot) {
                var offers = snapshot.data;
                if (offers == null) {
                  return const SizedBox();
                }
                return Column(
                  children: [
                    for (var offer in offers) ...[
                      SelectedButton(
                        alignment: Alignment.centerLeft,
                        buttonText: offer.displayFormat,
                        selected: offer.selected,
                        onPressed: () {
                          widget.onOfferSelected(offer.key);
                        },
                      ),
                      const SizedBox(height: 10),
                    ],
                    PrimaryButtonContainer(
                        fullWidth: true,
                        child: ElevatedButton(
                          onPressed: () {
                            var chosenOffer = offers.firstWhere(
                              (element) => element.selected,
                            );
                            widget.onChooseOffer(chosenOffer.key);
                            Navigator.pop(context);
                          },
                          child: Text(Translation.of(context).validate,
                              style: MalouTextStyles(context)
                                  .text15Semibold
                                  .copyWith(color: Colors.white)),
                        ))
                  ],
                );
              }),
        ],
      ),
    );
  }
}
