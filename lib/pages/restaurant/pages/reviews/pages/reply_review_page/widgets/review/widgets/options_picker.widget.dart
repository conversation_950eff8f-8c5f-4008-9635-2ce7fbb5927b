import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/reviews/pages/reply_review_page/reply_review.controller.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

class OptionsPicker extends StatelessWidget {
  final ReviewReplyController reviewReplyController;

  const OptionsPicker({
    super.key,
    required this.reviewReplyController,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Column(
          children: [
            InkWell(
              onTap: () {
                Navigator.of(context).pop();
                reviewReplyController.openTemplatesPicker();
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(children: [
                    const SizedBox(width: 30),
                    Text(Translation.of(context).reviewTemplates,
                        style: MalouTextStyles(context)
                            .text15Semibold
                            .copyWith(color: MalouColors.textBody))
                  ]),
                  const Icon(
                    Icons.chevron_right,
                    color: MalouColors.primary,
                    size: 40,
                  )
                ],
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              // top and bottom 10
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: InkWell(
                onTap: () {
                  reviewReplyController.openPromotionOffersPicker();
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(children: [
                      const SizedBox(width: 30),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(Translation.of(context).sendPromotion,
                              style: MalouTextStyles(context)
                                  .text15Semibold
                                  .copyWith(color: MalouColors.textBody)),
                          StreamBuilder(
                            stream: reviewReplyController.promotionOffers$,
                            builder: (context, snapshot) {
                              var offers = snapshot.data;
                              if (offers == null) {
                                return const SizedBox();
                              }
                              var chosenOffer = offers.firstWhere(
                                  (element) => element.chosen,
                                  );
                              return Text(
                                chosenOffer.displayFormat,
                                style: MalouTextStyles(context)
                                    .text12Italic
                                    .copyWith(color: MalouColors.textBody),
                              );
                            }
                          )
                        ],
                      ),
                    ]),
                    const Icon(
                      Icons.chevron_right,
                      color: MalouColors.primary,
                      size: 40,
                    )
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 20,
            ),
          ],
        )
      ],
    );
  }
}
