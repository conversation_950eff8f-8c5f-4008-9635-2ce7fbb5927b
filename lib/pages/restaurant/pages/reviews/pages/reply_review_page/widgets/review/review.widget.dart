import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/reviews/pages/reply_review_page/reply_review.controller.dart';
import 'package:malou/pages/restaurant/pages/reviews/pages/reply_review_page/widgets/review/widgets/keyword_score_tab/keywords_score_tab.widget.dart';
import 'package:malou/pages/restaurant/pages/reviews/widgets/review_card/review_card.widget.dart';
import 'package:malou/pages/restaurant/pages/reviews/widgets/skeleton_reviews_card.widget.dart';
import 'package:malou/shared/helpers/promotion_offers.dart';
import 'package:malou/shared/models/reviews.model.dart';
import 'package:malou/shared/models/user.model.dart';
import 'package:malou/shared/services/keyword/keyword.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/chips/quick_text_chips_list.widget.dart';
import 'package:malou/shared/widgets/forms/fixed_bottom_form_screen.widget.dart';
import 'package:malou/shared/widgets/score-gauge/score_gauge.widget.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';

class ReviewWidget extends StatelessWidget {
  const ReviewWidget({
    super.key,
    required this.reviewReplyController,
  });

  final ReviewReplyController reviewReplyController;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Consumer3<RestaurantProvider, UserProvider, KeywordProvider>(
          builder: (context, restaurantProvider, userProvider, keywordProvider,
              child) {
        var size = MediaQuery.of(context).size;
        bool displaySemanticAnalyses = false;
        UserRestaurant userRestaurant = userProvider.currentUser!
            .getUserRestaurantByRestaurantId(
                restaurantProvider.currentRestaurant!.id);
        if (userRestaurant.displaySemanticAnalyses != null &&
            userRestaurant.displaySemanticAnalyses!) {
          displaySemanticAnalyses = true;
        }
        return StreamBuilder<Review?>(
            stream: reviewReplyController.review$,
            builder: (context, snapshot) {
              if (snapshot.data == null) {
                return const SizedBox();
              }
              Review currentReview = snapshot.data!;
              bool isInputReplyDisplayed = ((!snapshot.data!.hasReply() &&
                      snapshot.data!.canBeAnswered()) ||
                  snapshot.data!.canHaveMultipleReplies() ||
                  reviewReplyController.hasClickedOnEditReply);
              return Column(children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: size.width * 0.05),
                  child: Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: MalouColors.borderPrimary,
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: TabBar(
                        isScrollable: false,
                        overlayColor:
                            WidgetStateProperty.all(Colors.transparent),
                        indicatorSize: TabBarIndicatorSize.tab,
                        dividerColor: Colors.transparent,
                        controller: reviewReplyController.tabController,
                        padding: EdgeInsets.zero,
                        indicatorPadding: EdgeInsets.zero,
                        labelPadding: EdgeInsets.zero,
                        unselectedLabelColor: MalouColors.textBody,
                        labelStyle: MalouTextStyles(context).text12Semibold,
                        labelColor: Colors.white,
                        indicator: BoxDecoration(
                          gradient: MalouColors.gradientFive,
                          borderRadius: BorderRadius.circular(30),
                        ),
                        tabs: [
                          Tab(child: Text(Translation.of(context).review)),
                          Tab(
                              child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                Translation.of(context).keywords_score,
                              ),
                              const SizedBox(
                                width: 10,
                              ),
                              StreamBuilder(
                                  stream: reviewReplyController
                                      .computeKeywordsScoreAndTipsQuery.data,
                                  builder: (context, snapshot) {
                                    return ScoreGauge(
                                        displayLabel: false,
                                        score: snapshot.data?.score ?? 0);
                                  })
                            ],
                          )),
                        ]),
                  ),
                ),
                Expanded(
                    child: GestureDetector(
                  onTap: () {
                    FocusScope.of(context).unfocus();
                  },
                  child: Padding(
                      padding: EdgeInsets.only(
                          top: size.height * 0.03,
                          left: size.width * 0.05,
                          right: size.width * 0.05),
                      child: TabBarView(
                        controller: reviewReplyController.tabController,
                        children: [
                          ListView(children: [
                            if (snapshot.data != null)
                              Column(children: [
                                ReviewCard(
                                    displayRestaurantHeader: true,
                                    review: snapshot.data!,
                                    displaySemanticAnalyses:
                                        displaySemanticAnalyses,
                                    listMode: false,
                                    restaurant:
                                        restaurantProvider.currentRestaurant!,
                                    keywords:
                                        keywordProvider.selectedKeywords.value,
                                    onEditReply:
                                        reviewReplyController.onEditReply),
                                StreamBuilder(stream: reviewReplyController
                                    .promotionOffers$
                                    .where((offers) {
                                  return offers.isNotEmpty;
                                }), builder: (context, snapshot) {
                                  var offers = snapshot.data;
                                  if (offers == null) {
                                    return const SizedBox();
                                  }

                                  var chosenOffer = offers.where((offer) {
                                    return offer.chosen;
                                  }).first;

                                  if (chosenOffer.key ==
                                      UbereatsPromotionValue.NONE) {
                                    return const SizedBox();
                                  }

                                  return Padding(
                                    padding: const EdgeInsets.only(top: 20),
                                    child: Row(
                                      children: [
                                        Text(
                                            Translation.of(context)
                                                .sendPromotionOf(
                                                    chosenOffer.displayFormat),
                                            style: MalouTextStyles(context)
                                                .text12Semibold
                                                .copyWith(
                                                    color:
                                                        MalouColors.textTitle)),
                                        const SizedBox(width: 10),
                                        GestureDetector(
                                          onTap: () {
                                            reviewReplyController.onChooseOffer(
                                                UbereatsPromotionValue.NONE);
                                          },
                                          child: Text(
                                            Translation.of(context).delete,
                                            style: MalouTextStyles(context)
                                                .text12Bold
                                                .copyWith(
                                                    color: MalouColors.primary),
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                })
                              ])
                            else
                              const ReviewsCardSkeleton()
                          ]),
                          KeywordsScoreTab(
                              reviewReplyController: reviewReplyController,
                              isInputReplyDisplayed: isInputReplyDisplayed),
                        ],
                      )),
                )),
                if (isInputReplyDisplayed)
                  StreamBuilder(
                      stream: CombineLatestStream(
                          [
                            reviewReplyController
                                .generateAnswerFromAIQuery.isFetching,
                            reviewReplyController.reviewReplyQuery.isFetching,
                            reviewReplyController
                                .restaurantProvider.aiRemainingCredits$.stream,
                            reviewReplyController
                                .textEditingControllerStream.textObs$.stream
                          ],
                          (values) => (
                                isFetchingAnswerFromAI: values[0],
                                isFetchingReviewReply: values[1],
                                aiRemainingCredits: values[2],
                                textEditingController: values[3]
                              )),
                      builder: (context, snapshot) {
                        return FixedBottomFormScreen(
                            keepKeyboardOpenOnTapOutside: true,
                            quickTextsList: QuickTextsList(
                                quickTexts: reviewReplyController.quickTexts,
                                textEditingController: reviewReplyController
                                    .textEditingControllerStream),
                            leftIcon: Icon(
                              currentReview.hasPromotionOffers(
                                      restaurantProvider.currentRestaurant!,
                                      context)
                                  ? Icons.add_circle_outline
                                  : Icons.bookmarks_outlined,
                              size: 20,
                              color: MalouColors.primary,
                            ),
                            controller: reviewReplyController
                                .textEditingControllerStream,
                            maxLines: 7,
                            showAiButtonInsideField: true,
                            keyboardType: TextInputType.multiline,
                            hint: Translation.of(context).writeAnswerReview,
                            minLines: 1,
                            showBorders: true,
                            onAiPressed: reviewReplyController.onAiPressed,
                            onAiProcessing:
                                snapshot.data?.isFetchingAnswerFromAI != null &&
                                    snapshot.data!.isFetchingAnswerFromAI
                                        as bool,
                            leftIconButtonOnPressed:
                                currentReview.hasPromotionOffers(
                                        restaurantProvider.currentRestaurant!,
                                        context)
                                    ? reviewReplyController.openOptionsPicker
                                    : reviewReplyController.openTemplatesPicker,
                            remainingAICredits:
                                snapshot.data?.aiRemainingCredits != null
                                    ? snapshot.data!.aiRemainingCredits as int
                                    : null,
                            onSubmit: (reviewReplyController
                                        .replyContent.isNotEmpty &&
                                    snapshot.data?.isFetchingReviewReply !=
                                        null &&
                                    !(snapshot.data!.isFetchingReviewReply
                                        as bool))
                                ? reviewReplyController.onSubmit
                                : null);
                      })
              ]);
            });
      }),
    );
  }
}
