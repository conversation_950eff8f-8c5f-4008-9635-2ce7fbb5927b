import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/reviews/pages/reply_review_page/reply_review.controller.dart';
import 'package:malou/shared/models/brick.model.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/chips/malou_chip.widget.dart';

class KeywordsAvailable extends StatefulWidget {
  const KeywordsAvailable({
    super.key,
    required this.reviewReplyController,
    required this.size,
    required this.isInputReplyDisplayed,
  });

  final ReviewReplyController reviewReplyController;
  final Size size;
  final bool isInputReplyDisplayed;

  @override
  _KeywordsAvailableState createState() => _KeywordsAvailableState();
}

class _KeywordsAvailableState extends State<KeywordsAvailable> {
  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
        stream: widget.reviewReplyController.allBricks$,
        builder: (context, snapshot) {
          if (snapshot.data != null && snapshot.data!.isNotEmpty) {
            var allBricks =
                snapshot.data as List<({SimpleBrick brick, bool selected})>;
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Wrap(
                  spacing: 10,
                  runSpacing: 10,
                  children: allBricks
                      .mapIndexed((idx, element) => MalouChipSelectable(
                            text: element.brick.text,
                            onTap: () {
                              // Optimistic update
                              widget.reviewReplyController
                                  .onTapKeyword(idx, allBricks);
                            },
                            selected: element.selected,
                          ))
                      .toList(),
                ),
                const SizedBox(height: 23),
              ],
            );
          }
          return Text(Translation.of(context).noKeywordsYet,
              style: MalouTextStyles(context)
                  .text10Italic
                  .copyWith(color: MalouColors.textTitle));
        });
  }
}
