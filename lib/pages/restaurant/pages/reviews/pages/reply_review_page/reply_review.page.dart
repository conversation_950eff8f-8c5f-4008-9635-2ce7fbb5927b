import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/reviews/pages/reply_review_page/reply_review.controller.dart';
import 'package:malou/pages/restaurant/pages/reviews/pages/reply_review_page/widgets/review/review.widget.dart';
import 'package:malou/shared/services/keyword/keyword.provider.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/services/restaurant-ai-settings/restaurant_ai_setting.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/review/review.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/widgets/appbar/appbar_back.widget.dart';
import 'package:provider/provider.dart';

class ReviewPage extends StatefulWidget {
  final String reviewId;
  const ReviewPage({super.key, required this.reviewId});

  @override
  State<ReviewPage> createState() => _ReviewPageState();
}

class _ReviewPageState extends State<ReviewPage> with TickerProviderStateMixin {
  late ReviewReplyController reviewReplyController;

  @override
  void initState() {
    reviewReplyController = ReviewReplyController(
      context: context,
      languageProvider: Provider.of<LanguageProvider>(context, listen: false),
      tabController: TabController(length: 2, vsync: this),
      reviewProvider: Provider.of<ReviewProvider>(context, listen: false),
      restaurantProvider:
          Provider.of<RestaurantProvider>(context, listen: false),
      keywordProvider: Provider.of<KeywordProvider>(context, listen: false),
      userProvider: Provider.of<UserProvider>(context, listen: false),
      reviewId: widget.reviewId,
      setState: setState,
      restaurantAiSettingsProvider: Provider.of<RestaurantAiSettingsProvider>(
        context,
        listen: false,
      ),
    );
    super.initState();
  }

  @override
  void dispose() {
    reviewReplyController.review$.close();
    reviewReplyController.focusOnReplyTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBarArrowBack(
          title: Translation.of(context).reply,
        ),
        body: ReviewWidget(reviewReplyController: reviewReplyController));
  }
}
