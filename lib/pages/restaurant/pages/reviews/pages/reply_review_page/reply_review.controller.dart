import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/reviews/pages/reply_review_page/widgets/review/widgets/options_picker.widget.dart';
import 'package:malou/pages/restaurant/pages/reviews/pages/reply_review_page/widgets/review/widgets/promotion_offers_picker.widget.dart';
import 'package:malou/pages/router.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/helpers/clarify_error.dart';
import 'package:malou/shared/helpers/experimentation.service.dart';
import 'package:malou/shared/helpers/heap.service.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/helpers/modal_bottom_sheet.dart';
import 'package:malou/shared/helpers/promotion_offers.dart';
import 'package:malou/shared/helpers/text_editing_controller_stream.dart';
import 'package:malou/shared/helpers/use_query.dart';
import 'package:malou/shared/models/brick.model.dart';
import 'package:malou/shared/models/keywords/keyword.model.dart';
import 'package:malou/shared/models/keywords/keywords_analysis.model.dart';
import 'package:malou/shared/models/keywords/keywords_score.model.dart';
import 'package:malou/shared/models/quick_text.model.dart';
import 'package:malou/shared/models/reviewer_name_validation.model.dart';
import 'package:malou/shared/models/reviews.model.dart';
import 'package:malou/shared/models/template.model.dart';
import 'package:malou/shared/pages/ai_generation/ai_generation.dart';
import 'package:malou/shared/pages/ai_generation/pages/ai_generation_edit_pannel/pages/models/translate_from_ai_params.model.dart';
import 'package:malou/shared/pages/ai_generation/pages/models/generate_from_ai_params.model.dart';
import 'package:malou/shared/pages/ai_generation/pages/models/help_idea_button_state.model.dart';
import 'package:malou/shared/services/ai/ai.service.dart';
import 'package:malou/shared/services/keyword/keyword.provider.dart';
import 'package:malou/shared/services/keyword/keywords.types.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/services/restaurant-ai-settings/restaurant_ai_setting.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/review/review.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/widgets/language_picker/language_picker.dart';
import 'package:malou/shared/widgets/templates_picker/templates_picker.widget.dart';
import 'package:malou/shared/widgets/toasts/toast.widget.dart';
import 'package:malou/shared/widgets/toasts/toasts.service.dart';
import 'package:rxdart/rxdart.dart';

class ReviewReplyController {
  final experimentationService = ExperimentationService.instance;

  final BuildContext context;
  final String reviewId;
  final TabController tabController;
  final ReviewProvider reviewProvider;
  final RestaurantProvider restaurantProvider;
  final KeywordProvider keywordProvider;
  final LanguageProvider languageProvider;
  final UserProvider userProvider;
  final Function setState;
  final RestaurantAiSettingsProvider restaurantAiSettingsProvider;

  final TextEditingControllerStream textEditingControllerStream =
      TextEditingControllerStream();
  BehaviorSubject<Review?> review$ = BehaviorSubject.seeded(null);

  String replyContent = "";

  final keyboardVisibilityController = KeyboardVisibilityController();

  bool hasClickedOnEditReply = false;

  late FocusNode focusOnReplyTextController;

  List<QuickText> quickTexts = [];

  late QueryResult<Review, ReviewReplyParams> reviewReplyQuery;

  ScrollController textFieldScrollController = ScrollController();

  Comment? currentCommentThatIsAboutToBeEdited;

  late QueryResult<String, GenerateReviewAnswerFromAIParams>
      generateAnswerFromAIQuery;

  late QueryResult<String, String> getAutoAiPropositionReviewAnswerQuery;
  late QueryResult<KeywordsScoreProcessResult,
      ComputeKeywordsScoreAndTipsParams> computeKeywordsScoreAndTipsQuery;
  BehaviorSubject<KeywordsScoreProcessResult?> keywordScore$ =
      BehaviorSubject.seeded(null);

  // Reviewer name validation - equivalent to Angular's reviewerNameValidation signal
  BehaviorSubject<ReviewerNameValidation> reviewerNameValidation$ =
      BehaviorSubject.seeded(ReviewerNameValidation(
          gender: Civility.other,
          firstName: '',
          isFirstNameValid: false,
          lastName: '',
          isLastNameValid: false));

  late StreamSubscription _reviewerNameValidationSubscription;

  BehaviorSubject<List<({SimpleBrick brick, bool selected})>> allBricks$ =
      BehaviorSubject.seeded([]);

  BehaviorSubject<AppLocale> selectedLanguage$ = BehaviorSubject<AppLocale>();

  BehaviorSubject<List<PromotionOffer>> promotionOffers$ =
      BehaviorSubject.seeded([]);

  ReviewReplyController(
      {required this.context,
      required this.tabController,
      required this.reviewProvider,
      required this.restaurantProvider,
      required this.keywordProvider,
      required this.userProvider,
      required this.reviewId,
      required this.languageProvider,
      required this.setState,
      required this.restaurantAiSettingsProvider}) {
    focusOnReplyTextController = FocusNode();

    _setAutoAiPropositionQuery();

    _setReviewReplyQuery();

    computeKeywordsScoreAndTipsQuery =
        useQuery(keywordProvider.computeKeywordsScoreAndTips);

    _setGenerateAnswerFromAiQuery();

    _listenForReviewFetched();

    getAutoAiPropositionReviewAnswerQuery.execute(reviewId);

    _setQuickTextsOnReview();

    _updateKeywordsScoreOnTextOrReviewChange();

    _autofillAiDraftAnswer();

    _setReplyContentOnTextfieldChange();

    _refreshBrickSuggestionsOnLanguageChangeAndScoreData();

    _setPromotionOffersOnReview();

    _listenForReviewerNameValidation();
  }

  void _listenForReviewFetched() {
    reviewProvider.getReviewById(reviewId).then((value) {
      if (review$.isClosed) {
        review$ = BehaviorSubject.seeded(value);
        return;
      }
      review$.add(value);

      String? lang = value.lang;
      if (lang == null || lang.toLowerCase() == 'unknown') {
        lang = languageProvider.language.name;
      }
      selectedLanguage$.add(AppLocale.values.byName(lang));
    });
  }

  void _listenForReviewerNameValidation() {
    _reviewerNameValidationSubscription = review$
        .switchMap((review) {
          if (review == null) {
            return Stream.value(null);
          }

          if (reviewerNameValidation$.value.isDefaultReviewerNameValidation()) {
            return _getReviewerNameValidationStream(
                review.id, languageProvider.language.name);
          }
          return Stream.value(null);
        })
        .where((result) => result != null)
        .cast<ApiResult<ReviewerNameValidationResponse>>()
        .listen((result) {
          if (result.data != null) {
            reviewerNameValidation$.add(result.data!.reviewerNameValidation);
          }
        });
  }

  Stream<ApiResult<ReviewerNameValidationResponse>?>
      _getReviewerNameValidationStream(String reviewId, String lang) {
    return Stream.fromFuture(
            reviewProvider.getReviewerNameValidation(reviewId, lang))
        .handleError((error) {
      return null;
    });
  }

  void _setGenerateAnswerFromAiQuery() {
    generateAnswerFromAIQuery = useQuery(generateAnswerFromAI,
        onSuccess: (result, params, onProcessResult) {
      restaurantProvider.aiRemainingCredits$
          .add(restaurantProvider.aiRemainingCredits$.value - 1);
      textEditingControllerStream.value = TextEditingValue(text: result);
    }, onError: (result, params, onProcessResult) {});
  }

  void _setReviewReplyQuery() {
    reviewReplyQuery = useQuery(
      reviewProvider.reply,
      onError: (error, params, onProcessResult) {
        _handleAnswerReviewError(error);
      },
      onSuccess: (review, p1, p2) {
        HeapService.instance.track(
            'review_answered',
            {
              'reviewId': review.id,
            },
            context: context);
        textEditingControllerStream.clear();
        reviewProvider.draftedReviewTexts.add({
          reviewId: '',
        });

        review$.add(review);
        reviewProvider.handleReviewListAfterReply(review$.value!);
        Navigator.of(context).pop();
        ToastsService.instance.add(Toast(
            type: ToastType.success,
            illustration: Illustrations.karlOk,
            message: Translation.of(context).reviewReplySentSuccessfully));
      },
    );
  }

  void _setAutoAiPropositionQuery() {
    getAutoAiPropositionReviewAnswerQuery =
        useQuery(reviewProvider.getAutoAiPropositionReviewAnswer,
            onError: (result, params, onProcessResult) {
      displaySnackbarWithClarifiedError(context, result);
    });
  }

  void _setQuickTextsOnReview() {
    review$.listen((currentReview) {
      if (currentReview != null) {
        _setQuickTexts(currentReview);
      }
    });
  }

  void _setPromotionOffersOnReview() {
    review$.listen((currentReview) {
      if (currentReview != null) {
        var offers = currentReview.getPromotionOffers(
            restaurantProvider.currentRestaurant!, context);
        promotionOffers$.add(offers);
      }
    });
  }

  void onOfferSelected(UbereatsPromotionValue key) {
    final offers = promotionOffers$.value.map((offer) {
      if (offer.key == key) {
        return (
          key: offer.key,
          value: offer.value,
          selected: true,
          chosen: offer.chosen,
          displayFormat: offer.displayFormat
        );
      }
      return (
        key: offer.key,
        value: offer.value,
        selected: false,
        chosen: offer.chosen,
        displayFormat: offer.displayFormat
      );
    }).toList();
    promotionOffers$.add(offers);
  }

  void onChooseOffer(UbereatsPromotionValue key) {
    final offers = promotionOffers$.value.map((offer) {
      if (offer.key == key) {
        return (
          key: offer.key,
          value: offer.value,
          selected: true,
          chosen: true,
          displayFormat: offer.displayFormat
        );
      }
      return (
        key: offer.key,
        value: offer.value,
        selected: false,
        chosen: false,
        displayFormat: offer.displayFormat
      );
    }).toList();
    promotionOffers$.add(offers);
  }

  void _updateKeywordsScoreOnTextOrReviewChange() {
    CombineLatestStream<dynamic, List<dynamic>>([
      review$,
      textEditingControllerStream.textObs$.distinct(),
      reviewerNameValidation$,
      selectedLanguage$,
    ], (values) => values)
        .where((combinedData) => combinedData[0] != null)
        .debounceTime(const Duration(milliseconds: 250))
        .listen((value) {
      Review review = value[0] as Review;
      final responseTime = review.getResponseTime();
      String? textInsideInputReply = value[1] as String?;
      ReviewerNameValidation reviewerNameValidation =
          value[2] as ReviewerNameValidation;

      String textToCompute = "";

      bool isInputReplyDisplayed =
          ((!review.hasReply() && review.canBeAnswered()) ||
              review.canHaveMultipleReplies() ||
              hasClickedOnEditReply);

      if (isInputReplyDisplayed) {
        textToCompute = textInsideInputReply ?? '';
      } else if (review.hasReply()) {
        textToCompute = review.comments.last.text;
      }

      computeKeywordsScoreAndTipsQuery.execute((
        textToCompute: textToCompute,
        restaurant: restaurantProvider.currentRestaurant!,
        textType: review.getKeywordScoreTextType(),
        lang: selectedLanguage$.value.name,
        responseTime: responseTime,
        reviewerName: review.reviewer?.displayName,
        isLowRatedReview: review.isLowRatedReview(),
        reviewText: review.text,
        reviewerNameValidation:
            reviewerNameValidation.isDefaultReviewerNameValidation()
                ? review.reviewerNameValidation
                : reviewerNameValidation,
      ));
    });
  }

  void _setReplyContentOnTextfieldChange() {
    textEditingControllerStream.addListener(() {
      if (replyContent == textEditingControllerStream.text) {
        return;
      }
      setState(() {
        replyContent = textEditingControllerStream.text;

        if (hasClickedOnEditReply) {
          reviewProvider.draftedReviewTexts.add({
            "${reviewId}_${currentCommentThatIsAboutToBeEdited?.id}":
                replyContent,
          });
        } else {
          reviewProvider.draftedReviewTexts.add({
            reviewId: replyContent,
          });
        }
        if (textFieldScrollController.hasClients) {
          textFieldScrollController.animateTo(
              textFieldScrollController.position.maxScrollExtent,
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeIn);
        }
      });
    });
  }

  void _autofillAiDraftAnswer() {
    CombineLatestStream<dynamic, List<dynamic>>([
      reviewProvider.draftedReviewTexts.take(1),
      review$,
      getAutoAiPropositionReviewAnswerQuery.data
    ], (values) => values)
        .where((combinedData) =>
            combinedData[1] != null && combinedData[2] != null)
        .take(1)
        .listen((value) {
      Map<String, String> draftedReviewTexts = value[0] as Map<String, String>;
      if (draftedReviewTexts[reviewId] != null &&
          draftedReviewTexts[reviewId]!.isNotEmpty) {
        textEditingControllerStream.value = TextEditingValue(
          text: draftedReviewTexts[reviewId] ?? '',
          selection: TextSelection.fromPosition(
            TextPosition(
              offset: draftedReviewTexts[reviewId]!.length,
            ),
          ),
        );
      } else {
        String aiAutoProposition = value[2];
        if (aiAutoProposition.isNotEmpty) {
          textEditingControllerStream.value = TextEditingValue(
            text: aiAutoProposition,
            selection: TextSelection.fromPosition(
              TextPosition(
                offset: aiAutoProposition.length,
              ),
            ),
          );
        }
      }
    });
  }

  void _refreshBrickSuggestionsOnLanguageChangeAndScoreData() {
    CombineLatestStream(
        [selectedLanguage$, computeKeywordsScoreAndTipsQuery.data],
        (values) => values).listen((values) {
      AppLocale language = values[0] as AppLocale;
      KeywordsScoreProcessResult? keywordScore =
          values[1] as KeywordsScoreProcessResult?;
      String targetLanguage = language.name;

      List<SimpleBrick> bricksAssociatedWithKeywords =
          KeywordHelper.buildSimpleBricks(
              keywordProvider.selectedKeywords.value,
              restaurantProvider.currentRestaurant!,
              targetLanguage,
              reviewerName: review$.value?.reviewer?.displayName,
              isLowRatedReview: review$.value?.isLowRatedReview() ?? false,
              restaurantAiSettings:
                  experimentationService.isOn('release-custom-ai-settings')
                      ? restaurantAiSettingsProvider.restaurantAiSettings$.value
                      : null);
      var bricks = bricksAssociatedWithKeywords
          .map((brick) => (
                brick: brick,
                selected: keywordProvider.isBrickUsed(
                    brick, keywordScore, language.name),
              ))
          .toList();
      if (keywordScore != null) {
        keywordScore$.add(keywordScore);
      }

      var restaurantNameBrick = bricks.where((element) {
        return element.brick.category == 'restaurantName';
      }).first;
      var reviewerNameBrick = bricks.where((element) {
        return element.brick.category == 'reviewerName';
      }).firstOrNull;
      var sortedBricks = [
        (
          brick: restaurantNameBrick.brick,
          selected: restaurantNameBrick.selected,
        ),
      ];
      if (reviewerNameBrick != null) {
        sortedBricks.add((
          brick: reviewerNameBrick.brick,
          selected: reviewerNameBrick.selected,
        ));
      }
      var uniqueBricks = <({SimpleBrick brick, bool selected})>[];
      for (var brick in [...sortedBricks, ...bricks]) {
        if (uniqueBricks
            .any((element) => element.brick.text == brick.brick.text)) {
          continue;
        }
        uniqueBricks.add(brick);
      }
      allBricks$.add(uniqueBricks);
    });
  }

  void _setQuickTexts(Review value) {
    quickTexts = [
      {
        'key': 'client_name',
        'displayKey': '@${Translation.of(context).clientName}',
        'content': value.displayName(context)
      },
      {
        'key': 'my_firstname',
        'displayKey': '@${Translation.of(context).myName}',
        'content': userProvider.currentUser!.name
      },
      {
        'key': 'business_name',
        'displayKey': '@${Translation.of(context).restaurantName}',
        'content': restaurantProvider.currentRestaurant!.name
      },
    ]
        .map((e) => QuickText(
            key: e['key'] ?? '',
            displayKey: e['displayKey'] ?? '',
            content: e['content'] ?? ''))
        .toList(growable: false);
  }

  void onEditReply(Comment commentReply) {
    setState(() {
      hasClickedOnEditReply = true;
      currentCommentThatIsAboutToBeEdited = commentReply;
    });
    String textToSet = reviewProvider
            .draftedReviewTexts.value["${reviewId}_${commentReply.id}"] ??
        commentReply.text;
    textEditingControllerStream.text = textToSet;
    focusOnReplyTextController.requestFocus();
  }

  void _handleAnswerReviewError(ErrorFetching error) {
    if (error.hasMalouError(MalouErrorCode.REVIEW_NOT_FOUND)) {
      ToastsService.instance.add(Toast(
          type: ToastType.error,
          message: Translation.of(context).reviewDoesNotExistAnymore));

      reviewProvider.pagingController.itemList?.removeWhere((element) {
        return element.id == reviewId;
      });
      reviewProvider.deleteReviewById(reviewId);
      context.pop();
    } else {
      ToastsService.instance.add(Toast(
          type: ToastType.error,
          message: Translation.of(context).reviewReplyError));
    }
  }

  String replaceVariables(String text) {
    String newText = text;
    for (var element in quickTexts) {
      newText = newText.replaceAll('[{${element.key}}]', element.content);
    }
    return newText;
  }

  void openTemplatesPicker() {
    AppBottomModal.showAppBottomModalV2<Template>(
        context,
        TemplatesPicker(
          restaurantId: restaurantProvider.currentRestaurant!.id,
          type: TemplateType.REVIEW,
          templateMatcher: TemplateMatcherReview(
            review$.value!,
          ),
        ), afterClosed: (result) {
      textEditingControllerStream.text = replaceVariables(result.text);
    });
  }

  void openPromotionOffersPicker() {
    AppBottomModal.showTinyModalBottomSheet(
        context,
        PromotionOffersPicker(
          offers$: promotionOffers$,
          onOfferSelected: onOfferSelected,
          onChooseOffer: onChooseOffer,
          currencyCode: review$.value?.order?.currencyCode ?? 'USD',
          amount: review$.value?.order?.orderTotal ?? 0,
          orderCount: review$.value?.eaterTotalOrders ?? 0,
        ),
        afterClosed: (result) {});
  }

  void openOptionsPicker() {
    AppBottomModal.showTinyModalBottomSheet<bool>(
        rootNavigatorKey.currentContext!,
        OptionsPicker(
          reviewReplyController: this,
        ),
        afterClosed: (value) {});
  }

  List<CaptionProposalButtonState> buildHelpIdeaButtons() {
    return [
      CaptionProposalButtonState(
          selected: false,
          buttonText: Translation.of(context).describeDish,
          content: Translation.of(context).aDish),
      CaptionProposalButtonState(
          selected: false,
          buttonText: Translation.of(context).describeGiveaway,
          content: Translation.of(context).giveaway),
      CaptionProposalButtonState(
          selected: false,
          buttonText: Translation.of(context).giveAdvice,
          content: Translation.of(context).advice),
    ];
  }

  Future<String> generateAnswerFromAI(GenerateReviewAnswerFromAIParams params) {
    HeapService.instance.track(
        'reviews_answer_ai_generation',
        {
          'reviewId': reviewId,
        },
        context: context);
    return reviewProvider.generateTextFromAI(params);
  }

  void onAiPressed() {
    if (textEditingControllerStream.text.isEmpty) {
      generateAnswerFromAIQuery.execute((
        lang: languageProvider.language.name,
        restaurantId: restaurantProvider.currentRestaurant!.id,
        reviewId: reviewId
      ));
      return;
    }

    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => AIGeneration(
                  label: Translation.of(context).generateReviewReply,
                  placeholder:
                      Translation.of(context).generateReviewReplyPlaceholder,
                  showPromptStep: false,
                  showEditPannelGeneration: true,
                  text: textEditingControllerStream.text,
                  setValueFromAI: (String generatedText) {
                    textEditingControllerStream.value =
                        TextEditingValue(text: generatedText);
                  },
                  generateFromAI: (GenerateFromAIParams params) {
                    HeapService.instance.track(
                        'reviews_answer_ai_generation',
                        {
                          'reviewId': reviewId,
                        },
                        context: context);
                    return generateAnswerFromAI((
                      lang: params.lang,
                      restaurantId: restaurantProvider.currentRestaurant!.id,
                      reviewId: reviewId
                    ));
                  },
                  translateFromAI: (TranslateFromAIParams params) {
                    HeapService.instance.track(
                        'reviews_answer_ai_generation_translation',
                        {
                          'reviewId': reviewId,
                        },
                        context: context);
                    return reviewProvider.translateAnswerFromAI((
                      lang: params.lang,
                      restaurantId: restaurantProvider.currentRestaurant!.id,
                      reviewId: reviewId,
                      text: params.text
                    ));
                  },
                  optimizeFromAI: (String text) {
                    HeapService.instance.track(
                        'reviews_answer_ai_generation_optimization',
                        {
                          'reviewId': reviewId,
                        },
                        context: context);
                    return reviewProvider.optimizeTextFromAI((
                      relatedEntityId: reviewId,
                      restaurantId: restaurantProvider.currentRestaurant!.id,
                      textToOptimize: text,
                      textToOptimizeType:
                          AiTextToOptimizeType.REVIEW_ANSWER.name.toString(),
                      lang: review$.value!.lang
                    ));
                  },
                )));
  }

  void onSubmit() {
    KeywordAnalysis keywordAnalysis = KeywordAnalysis(
      count: keywordScore$.value?.bricksFound.length ?? 0,
      keywords:
          keywordScore$.value?.bricksFound.map((e) => e.text).toList() ?? [],
      score: keywordScore$.value?.score ?? 0,
    );
    var ubereatsPromotionValue = _getSelectedPromotionOffer();
    reviewReplyQuery.execute(ReviewReplyParams(
        review: review$.value!,
        text: replyContent,
        restaurantId: restaurantProvider.currentRestaurant!.id,
        keywordsAnalysis: keywordAnalysis,
        ubereatsPromotionValue: ubereatsPromotionValue));
  }

  String getKeywordsScoreLanguagePayload(String reviewLanguage) {
    return ['en', 'fr'].contains(reviewLanguage) ? reviewLanguage : 'en';
  }

  void onTapKeyword(int index,
      List<({SimpleBrick brick, bool selected})> bricksAssociatedWithKeywords) {
    // Optimistic update, to get rid of lambda delay
    final bricksTemp = [...bricksAssociatedWithKeywords];
    final brickToInsert = bricksTemp[index];
    bricksTemp[index] = (
      brick: brickToInsert.brick,
      selected: true,
    );
    allBricks$.add(bricksTemp);
    // end optimistic update
    textEditingControllerStream.insertFromCursor(brickToInsert.brick.text);
  }

  onKeywordsLanguageChanged(AppLocale newLanguage, BuildContext context) {
    selectedLanguage$.add(newLanguage);
    HeapService.instance.track('review_answer_change_keywords_language',
        {'reviewId': reviewId, 'language': newLanguage.name},
        context: context);
  }

  UbereatsPromotionValue? _getSelectedPromotionOffer() {
    var chosen =
        promotionOffers$.value.where((element) => element.chosen).firstOrNull;

    if (chosen == null) {
      return null;
    }

    return chosen.key;
  }

  openKeywordLanguagePicker() {
    AppBottomModal.showTinyModalBottomSheet<bool>(
        rootNavigatorKey.currentContext!,
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          child: LanguagePicker(
            language: selectedLanguage$.value,
            onClickLanguage: (AppLocale lang) {
              onKeywordsLanguageChanged(lang, context);
              Navigator.of(context).pop();
            },
            languageProvider: languageProvider,
          ),
        ),
        afterClosed: (value) {});
  }

  // Dispose method to clean up subscriptions
  void dispose() {
    _reviewerNameValidationSubscription.cancel();
    review$.close();
    reviewerNameValidation$.close();
    keywordScore$.close();
    allBricks$.close();
    selectedLanguage$.close();
    promotionOffers$.close();
    textEditingControllerStream.dispose();
    focusOnReplyTextController.dispose();
  }
}
