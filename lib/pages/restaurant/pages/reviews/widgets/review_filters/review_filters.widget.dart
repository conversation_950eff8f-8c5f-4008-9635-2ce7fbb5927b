import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/reviews/widgets/review_filters/widgets/review_filters_date.widget.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/helpers/modal_bottom_sheet.dart';
import 'package:malou/shared/models/reviews_filters.model.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/review/review.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/buttons/styles.dart';
import 'package:provider/provider.dart';

class ReviewFiltersWidget extends StatefulWidget {
  final bool displayAnalyzeReviews;

  const ReviewFiltersWidget({
    super.key,
    required this.displayAnalyzeReviews,
  });

  @override
  State<ReviewFiltersWidget> createState() => _ReviewFiltersWidgetState();
}

class _ReviewFiltersWidgetState extends State<ReviewFiltersWidget> {
  late ReviewProvider reviewProvider;
  bool displayAnalyzeReviews = false;
  ReviewsFilters? filtersTmp;

  @override
  void initState() {
    super.initState();
    reviewProvider = Provider.of<ReviewProvider>(context, listen: false);
    displayAnalyzeReviews = widget.displayAnalyzeReviews;
    setState(() {
      filtersTmp = reviewProvider.filters.value.copyWith();
    });
  }

  @override
  Widget build(BuildContext contexte) {
    return Consumer2<RestaurantProvider, UserProvider>(
        builder: (context, restaurantProvider, userProvider, child) {
      return StreamBuilder<ReviewsFilters>(
          stream: reviewProvider.filters,
          builder: (context, snapshot) {
            return Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(
                        MalouIcons.filter,
                        color: MalouColors.primary,
                        size: 23,
                      ),
                      const SizedBox(width: 10),
                      Text(Translation.of(context).filter,
                          style: MalouTextStyles(context)
                              .text20Semibold
                              .copyWith(color: MalouColors.textTitle)),
                    ],
                  ),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      ConstrainedBox(
                        constraints: const BoxConstraints(maxWidth: 250),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(Translation.of(context).displayAnalyzeReviews,
                                style: MalouTextStyles(context)
                                    .text13Semibold
                                    .copyWith(color: MalouColors.textTitle)),
                          ],
                        ),
                      ),
                      Switch(
                          activeColor: MalouColors.primary,
                          activeTrackColor: MalouColors.backgroundDark,
                          inactiveTrackColor: MalouColors.backgroundDark,
                          inactiveThumbColor: MalouColors.textBody,
                          value: displayAnalyzeReviews,
                          onChanged: (bool value) {
                            setState(() {
                              displayAnalyzeReviews = value;
                            });
                          })
                    ],
                  ),
                  const SizedBox(height: 20),
                  InkWell(
                    onTap: () {
                      AppBottomModal.showTinyModalBottomSheet(
                          context,
                          ReviewFiltersDate(
                            defaultPeriod: filtersTmp!.period!,
                            onApplyDates: (MalouPeriod period) {
                              setState(() {
                                filtersTmp!.period = period;
                              });
                            },
                          ),
                          barrierColor: Colors.transparent);
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(Translation.of(context).period,
                                  style: MalouTextStyles(context)
                                      .text13Semibold
                                      .copyWith(color: MalouColors.textTitle)),
                              SizedBox(
                                child: Text(
                                  ReviewsFilters.translatePeriod(
                                      filtersTmp!.period!, context),
                                  style: MalouTextStyles(context)
                                      .text12Italic
                                      .copyWith(color: MalouColors.textBody),
                                ),
                              )
                            ],
                          )
                        ]),
                        const Icon(
                          Icons.chevron_right,
                          color: MalouColors.primary,
                          size: 40,
                        )
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),
                  PrimaryButtonContainer(
                      fullWidth: true,
                      child: ElevatedButton(
                        onPressed: () {
                          // need copyWith to trigger constructor logic in ReviewsFilters
                          reviewProvider.filters.add(filtersTmp!.copyWith());
                          Navigator.pop(context, displayAnalyzeReviews);
                        },
                        child: Text(Translation.of(context).validate,
                            style: MalouTextStyles(context)
                                .text15Semibold
                                .copyWith(color: Colors.white)),
                      ))
                ],
              ),
            );
          });
    });
  }
}
