import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/models/reviews_filters.model.dart';
import 'package:malou/shared/pages/ai_generation/pages/ai_generation_edit_pannel/pages/models/selected_button.model.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/buttons/selected_button.widget.dart';

class ReviewFiltersDate extends StatelessWidget {
  final void Function(MalouPeriod) onApplyDates;
  final MalouPeriod defaultPeriod;
  const ReviewFiltersDate(
      {super.key, required this.onApplyDates, required this.defaultPeriod});

  @override
  Widget build(BuildContext context) {
    List<SelectedButtonState<MalouPeriod>> datesPreset = MalouPeriod.values
        .map((period) => SelectedButtonState<MalouPeriod>(
            buttonText: ReviewsFilters.translatePeriod(
                period, context),
            value: period,
            selected: period == defaultPeriod))
        .toList();
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Row(
            children: [
              IconButton(
                icon: const Icon(
                  MalouIcons.arrowLeft,
                  color: MalouColors.primary,
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
              const SizedBox(width: 10),
              Text(Translation.of(context).period,
                  style: MalouTextStyles(context)
                      .text20Semibold
                      .copyWith(color: MalouColors.textTitle)),
            ],
          ),
          const SizedBox(height: 10),
          Column(
            children: [
              for (var date in datesPreset) ...[
                SelectedButton(
                  buttonText: date.buttonText,
                  selected: date.selected,
                  onPressed: () {
                    onApplyDates(date.value);
                    context.pop();
                  },
                ),
                const SizedBox(height: 10),
              ]
            ],
          ),
          const SizedBox(height: 10),
        ],
      ),
    );
  }
}
