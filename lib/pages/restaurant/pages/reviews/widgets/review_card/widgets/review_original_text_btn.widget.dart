import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/constant.dart';
import 'package:malou/shared/helpers/lang_tag.service.dart';
import 'package:malou/shared/models/reviews.model.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

class ReviewOriginalTextBtn extends StatelessWidget {
  final Function setOriginalReviewText;
  final Review review;
  final LangTagService langTagService;

  const ReviewOriginalTextBtn({
    Key? key,
    required this.setOriginalReviewText,
    required this.review,
    required this.langTagService,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(
          Translation.of(context).translated,
          style: MalouTextStyles(context)
              .text12Italic
              .copyWith(color: MalouColors.textBody),
        ),
        const SizedBox(width: 5),
        GestureDetector(
          onTap: () {
            setOriginalReviewText();
          },
          child: Text(
            Translation.of(context).seeOriginalReviewText(langTagService
                .getLangTag(review.lang ?? AppConstant.APP_DEFAULT_LANGUAGE)),
            style: MalouTextStyles(context)
                .text12Bold
                .copyWith(color: MalouColors.primary),
          ),
        ),
      ],
    );
  }
}
