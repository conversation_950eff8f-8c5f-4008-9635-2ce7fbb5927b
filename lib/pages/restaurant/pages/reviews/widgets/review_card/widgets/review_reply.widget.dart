import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/helpers/use_query.dart';
import 'package:malou/shared/models/keywords/keywords_score.model.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/models/restaurant.model.dart';
import 'package:malou/shared/models/reviews.model.dart';
import 'package:malou/shared/services/keyword/keyword.provider.dart';
import 'package:malou/shared/services/keyword/keywords.types.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/date/date_formatter.widget.dart';
import 'package:malou/shared/widgets/image/cached_image.widget.dart';
import 'package:malou/shared/widgets/score-gauge/score_gauge.widget.dart';
import 'package:provider/provider.dart';

class ReviewReply extends StatefulWidget {
  const ReviewReply(
      {super.key,
      required this.review,
      required this.listMode,
      required this.restaurant,
      this.onEditReply});

  final Review review;
  final bool listMode;
  final Restaurant restaurant;
  final Function(Comment reply)? onEditReply;
  @override
  State<ReviewReply> createState() => _ReviewReplyState();
}

class _ReviewReplyState extends State<ReviewReply> {
  late KeywordProvider keywordProvider;
  late QueryResult<KeywordsScoreProcessResult,
      ComputeKeywordsScoreAndTipsParams> computeKeywordsScoreAndTipsQuery;

  @override
  void initState() {
    super.initState();
    keywordProvider = Provider.of<KeywordProvider>(context, listen: false);
    computeKeywordsScoreAndTipsQuery =
        useQuery(keywordProvider.computeKeywordsScoreAndTips);

    if (widget.review.key != PlatformKeys.malouPrivate &&
        (widget.review.comments.last.keywordAnalysis == null ||
            widget.review.comments.last.keywordAnalysis?.score == null)) {
      computeKeywordsScoreAndTipsQuery.execute((
        restaurant: widget.restaurant,
        textToCompute: widget.review.comments.last.text,
        textType: widget.review.getKeywordScoreTextType(),
        lang: widget.review.lang,
        reviewerName: widget.review.reviewer?.displayName,
        responseTime: widget.review.getResponseTime(),
        isLowRatedReview: widget.review.isLowRatedReview(),
        reviewText: widget.review.text,
        reviewerNameValidation: widget.review.reviewerNameValidation
      ));
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    return Column(
      children: [
        const SizedBox(height: 15),
        Row(
          children: [
            Flexible(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: Colors.white,
                ),
                height: 2,
              ),
            )
          ],
        ),
        const SizedBox(height: 15),
        if (!widget.listMode) ...[
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            Row(
              children: [
                CachedImage(
                    height: 50,
                    width: 50,
                    radius: BorderRadius.circular(50),
                    url: widget.restaurant.logo
                        ?.getUrlForSize(preferedSize: MediaSize.small)),
                const SizedBox(width: 10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: size.width * 0.40,
                      child: Text(widget.restaurant.name,
                          style: MalouTextStyles(context)
                              .text14Bold
                              .copyWith(color: MalouColors.textTitle),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          softWrap: false),
                    ),
                    if (widget.review.updatedAt != null)
                      DateFormatter(
                          date: widget.review.updatedAt!,
                          style: MalouTextStyles(context)
                              .text12Italic
                              .copyWith(color: MalouColors.textBody)),
                  ],
                )
              ],
            ),
            if (widget.review.canRepliesBeModified()) ...[
              MaterialButton(
                onPressed: () => widget.onEditReply != null
                    ? widget.onEditReply!(widget.review.comments.last)
                    : null,
                color: Colors.white,
                shape: const CircleBorder(),
                elevation: 0,
                highlightElevation: 0,
                minWidth: 36,
                height: 36,
                padding: EdgeInsets.zero,
                child: const Icon(
                  MalouIcons.edit,
                  color: MalouColors.primary,
                  size: 23,
                ),
              )
            ]
          ]),
        ],
        const SizedBox(height: 15),
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          if (widget.review.key != PlatformKeys.malouPrivate) ...[
            StreamBuilder<KeywordsScoreProcessResult?>(
                stream: computeKeywordsScoreAndTipsQuery.data.stream,
                builder: (context, snapshot) {
                  final savedScore =
                      widget.review.comments.last.keywordAnalysis?.score;
                  final computedScore = snapshot.data?.score;

                  return ScoreGauge(score: savedScore ?? computedScore ?? 0);
                })
          ],
          if (widget.review.wasAnsweredAutomatically) ...[
            Container(
              // center
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: MalouColors.purpleLight,
              ),
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                child: Row(
                  children: [
                    const Icon(
                      MalouIcons.eclair,
                      color: MalouColors.primary,
                      size: 26,
                    ),
                    Text(
                      Translation.of(context).autoReply,
                      style: MalouTextStyles(context)
                          .text10
                          .copyWith(color: Colors.white),
                    )
                  ],
                ),
              ),
            )
          ]
        ]),
        if (widget.listMode &&
            widget.review.canHaveMultipleReplies() &&
            widget.review.comments.length > 1) ...[
          const SizedBox(height: 15),
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              Translation.of(context).nbReplies(
                  widget.review.comments.length, widget.review.comments.length),
              style: MalouTextStyles(context)
                  .text12Bold
                  .copyWith(color: MalouColors.textTitle),
            ),
          )
        ],
        const SizedBox(height: 15),
        Align(
          alignment: Alignment.centerLeft,
          child: Text(
            widget.review.comments.last.text,
            style: MalouTextStyles(context)
                .text12
                .copyWith(color: MalouColors.textBody),
            overflow: TextOverflow.ellipsis,
            maxLines: widget.listMode ? 4 : 100,
            softWrap: false,
          ),
        ),
      ],
    );
  }
}
