import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/lang_tag.service.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

class ReviewTranslateButton extends StatelessWidget {
  final Stream<bool> isFetchingStream;
  final VoidCallback onTranslate;
  final LangTagService langTagService;
  final String languageName;

  const ReviewTranslateButton({
    Key? key,
    required this.isFetchingStream,
    required this.onTranslate,
    required this.langTagService,
    required this.languageName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
      stream: isFetchingStream,
      builder: (context, snapshot) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Opacity(
              opacity: snapshot.data == true ? 0.5 : 1,
              child: GestureDetector(
                onTap: onTranslate,
                child: Text(
                  Translation.of(context)
                      .translateInLang(langTagService.getLangTag(languageName)),
                  style: MalouTextStyles(context)
                      .text12Bold
                      .copyWith(color: MalouColors.primary),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
