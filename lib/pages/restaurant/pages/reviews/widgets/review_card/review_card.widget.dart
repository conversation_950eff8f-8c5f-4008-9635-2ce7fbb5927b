import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/posts/post_creation/tabs/post_medias/widgets/post_media_picker/widgets/widgets/widgets/video_media_square.widget.dart';
import 'package:malou/pages/restaurant/pages/reviews/widgets/review_card/widgets/review_original_text_btn.widget.dart';
import 'package:malou/pages/restaurant/pages/reviews/widgets/review_card/widgets/review_reply.widget.dart';
import 'package:malou/pages/restaurant/pages/reviews/widgets/review_card/widgets/review_translate_btn.widget.dart';
import 'package:malou/shared/helpers/heap.service.dart';
import 'package:malou/shared/helpers/lang_tag.service.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/helpers/use_query.dart';
import 'package:malou/shared/helpers/utils.dart';
import 'package:malou/shared/models/keywords/keyword.model.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/models/message.model.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/models/restaurant.model.dart';
import 'package:malou/shared/models/reviews.model.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/review/review.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/avatar/avatar.widget.dart';
import 'package:malou/shared/widgets/date/date_formatter.widget.dart';
import 'package:malou/shared/widgets/image/cached_image.widget.dart';
import 'package:malou/shared/widgets/pages/medias_preview.widget.dart';
import 'package:malou/shared/widgets/stars/stars.widget.dart';
import 'package:provider/provider.dart';

class ReviewCard extends StatefulWidget {
  final Review review;
  bool displaySemanticAnalyses;
  final bool listMode;
  final bool displayRestaurantHeader;
  final Restaurant restaurant;
  final List<RestaurantKeyword> keywords;
  final Function(Comment reply)? onEditReply;
  ReviewCard(
      {super.key,
      required this.review,
      this.displaySemanticAnalyses = false,
      required this.restaurant,
      required this.keywords,
      this.onEditReply,
      this.listMode = true,
      this.displayRestaurantHeader = false});

  @override
  State<ReviewCard> createState() => _ReviewCardState();
}

class _ReviewCardState extends State<ReviewCard> {
  List<SegmentAnalysisWithTextSpan> textSegmentsFromSemanticAnalyses = [];
  List<ChipAnalysis> chips = [];
  late RestaurantProvider restaurantProvider;
  late UserProvider userProvider;
  late LanguageProvider languageProvider;
  late ReviewProvider reviewProvider;
  late QueryResult<Review, TranslateReviewTextFromAIParams>
      translateReviewTextQuery;

  String reviewText = "";

  @override
  void initState() {
    super.initState();
    restaurantProvider =
        Provider.of<RestaurantProvider>(context, listen: false);
    userProvider = Provider.of<UserProvider>(context, listen: false);
    languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    reviewProvider = Provider.of<ReviewProvider>(context, listen: false);
    widget.review.removeUnwantedTagsAndSentimentsFromAnalyses();
    textSegmentsFromSemanticAnalyses =
        widget.review.buildTextSegmentsFromSemanticAnalyses();
    chips = widget.review.getUniqueChipsFromReviewAnalyses();
    translateReviewTextQuery = useQuery(
      reviewProvider.translateReviewFromAI,
      onSuccess: (review, params, p2) => {
        setState(() {
          widget.displaySemanticAnalyses = false;
          widget.review.setTranslations(review.translations!);
          widget.review.initTranslatedText(languageProvider.language);
        })
      },
    );

    setState(() {
      widget.review.initTranslatedText(languageProvider.language);
    });
  }

  void colorizeSegment(String tag, String sentiment) {
    for (SegmentAnalysisWithTextSpan segmentAnalysisWithTextSpan
        in textSegmentsFromSemanticAnalyses) {
      if (segmentAnalysisWithTextSpan.segmentAnalysis.tag == tag &&
          segmentAnalysisWithTextSpan.segmentAnalysis.sentiment == sentiment) {
        if (segmentAnalysisWithTextSpan.active) {
          segmentAnalysisWithTextSpan.textSpan = TextSpan(
              text: segmentAnalysisWithTextSpan.textSpan.text,
              style: MalouTextStyles(context)
                  .text12
                  .copyWith(color: MalouColors.textBody));
          segmentAnalysisWithTextSpan.active = false;
        } else {
          segmentAnalysisWithTextSpan.textSpan = TextSpan(
              text: segmentAnalysisWithTextSpan.textSpan.text,
              style: MalouTextStyles(context).text12.copyWith(
                  color: segmentAnalysisWithTextSpan.segmentAnalysis
                      .getColorFromSentiment()));
          segmentAnalysisWithTextSpan.active = true;
        }
      } else {
        segmentAnalysisWithTextSpan.textSpan = TextSpan(
            text: segmentAnalysisWithTextSpan.textSpan.text,
            style: MalouTextStyles(context)
                .text12
                .copyWith(color: MalouColors.textBody));
        segmentAnalysisWithTextSpan.active = false;
      }
    }

    if (textSegmentsFromSemanticAnalyses
        .every((element) => element.active == false)) {
      textSegmentsFromSemanticAnalyses =
          widget.review.buildTextSegmentsFromSemanticAnalyses();
    }

    setState(() {
      textSegmentsFromSemanticAnalyses = textSegmentsFromSemanticAnalyses;
    });
  }

  void setOpacityToChips(ChipAnalysis currentChip) {
    if (currentChip.active) {
      chips = widget.review.getUniqueChipsFromReviewAnalyses();
    } else {
      for (var chip in chips) {
        chip.opacity = 0.5;
        chip.active = false;
      }
      currentChip.opacity = 1;
      currentChip.active = true;
    }
    setState(() {
      chips = chips;
    });
  }

  Widget displayReplies(
      Review review,
      Restaurant restaurant,
      List<RestaurantKeyword> keywords,
      bool listMode,
      final Function(Comment reply)? onEditReply) {
    if (listMode) {
      return ReviewReply(
          listMode: listMode,
          restaurant: restaurant,
          review: review,
          onEditReply: onEditReply);
    }

    switch (review.key) {
      case PlatformKeys.facebook:
      case PlatformKeys.zenchef:
        List<Review> reviews = review.comments
            .map((comment) => review.copyWith(
                  comments: [comment],
                ))
            .toList();
        return Column(
          children: [
            ...reviews
                .map((review) => ReviewReply(
                    review: review,
                    restaurant: restaurant,
                    listMode: listMode,
                    onEditReply: onEditReply))
                .toList(),
          ],
        );
      default:
        return ReviewReply(
            listMode: listMode,
            restaurant: restaurant,
            review: review,
            onEditReply: onEditReply);
    }
  }

  void translateReviewText() {
    if (widget.review.text == null) {
      return;
    }
    if (widget.review.hasTranslation(languageProvider.language)) {
      setState(() {
        widget.displaySemanticAnalyses = false;
        widget.review.initTranslatedText(languageProvider.language);
      });
    } else {
      translateReviewTextQuery.execute((
        lang: languageProvider.language.name,
        text: widget.review.text!,
        restaurantId: widget.restaurant.id,
        reviewId: widget.review.id,
        isPrivateReview: widget.review.isPrivateReview()
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final LangTagService langTagService =
        LangTagService(Translation.of(context));

    var attachments = widget.review.socialAttachments!.length > 3
        ? widget.review.socialAttachments!.sublist(0, 3)
        : widget.review.socialAttachments;

    return GestureDetector(
      onTap: widget.listMode
          ? () {
              HeapService.instance.track(
                  'review_clicked',
                  {
                    'reviewId': widget.review.id,
                  },
                  context: context);
              context.push(
                  '/restaurant/${widget.restaurant.id}/reviews/${widget.review.id}',
                  extra: {
                    'replyMethod': widget.review.getReplyMethod(),
                  });
            }
          : null,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: widget.review.hasReply()
              ? MalouColors.backgroundDark
              : Colors.white,
          border: Border.all(color: MalouColors.borderPrimary, width: 1),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Column(
          children: [
            if (widget.displayRestaurantHeader)
              Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(vertical: 21, horizontal: 22),
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(10),
                    topRight: Radius.circular(10),
                  ),
                  color: MalouColors.backgroundLight,
                  border:
                      Border.all(color: MalouColors.borderPrimary, width: 1),
                ),
                child: Row(children: [
                  CachedImage(
                    height: 35,
                    width: 35,
                    url: restaurantProvider.currentRestaurant!.logo
                        ?.getUrlForSize(preferedSize: MediaSize.small),
                    radius: BorderRadius.circular(100),
                  ),
                  const SizedBox(width: 20),
                  Column(
                    children: [
                      SizedBox(
                        width: size.width * 0.6,
                        child: Text(restaurantProvider.currentRestaurant!.name,
                            style: MalouTextStyles(context)
                                .text15Semibold
                                .copyWith(color: MalouColors.textTitle),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            softWrap: false),
                      ),
                      SizedBox(
                        width: size.width * 0.6,
                        child: Text(
                            restaurantProvider.currentRestaurant!
                                .getFullFormattedAddress(context: context),
                            style: MalouTextStyles(context).text10.copyWith(
                                  color: MalouColors.textBody,
                                ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            softWrap: false),
                      )
                    ],
                  )
                ]),
              ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 27),
              child:
                  Column(mainAxisAlignment: MainAxisAlignment.start, children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        AvatarPlatform(
                          url: widget.review.reviewer?.profilePhotoUrl ?? "",
                          platformKey: widget.review.key,
                        ),
                        const SizedBox(width: 10),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                              width: size.width * 0.40,
                              child: Text(widget.review.displayName(context),
                                  style: MalouTextStyles(context)
                                      .text14Bold
                                      .copyWith(color: MalouColors.textTitle),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                  softWrap: false),
                            ),
                            DateFormatter(
                                date: widget.review.socialCreatedAt,
                                style: MalouTextStyles(context)
                                    .text12Italic
                                    .copyWith(color: MalouColors.textBody)),
                          ],
                        )
                      ],
                    ),
                    _buildReviewStateButton(context)
                  ],
                ),
                const SizedBox(height: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                        padding: const EdgeInsets.symmetric(vertical: 5),
                        child: StarsWidget(
                          stars: 5,
                          starFilled: widget.review.rating ?? 0,
                          size: widget.listMode ? 26 : 16,
                        ))
                  ],
                ),
                if (widget.review.text != null &&
                    widget.review.text!.isNotEmpty) ...[
                  SizedBox(height: widget.listMode ? 20 : 10),
                  SizedBox(
                    child: Align(
                        alignment: Alignment.centerLeft,
                        child: widget.displaySemanticAnalyses &&
                                textSegmentsFromSemanticAnalyses.isNotEmpty
                            ? RichText(
                                text: TextSpan(
                                    style: MalouTextStyles(context)
                                        .text12
                                        .copyWith(color: MalouColors.textBody),
                                    children: textSegmentsFromSemanticAnalyses
                                        .map((child) => child.textSpan)
                                        .toList()))
                            : Text(
                                parseHtmlString(
                                    widget.review.translatedText ?? ""),
                                style: MalouTextStyles(context)
                                    .text12
                                    .copyWith(color: MalouColors.textBody),
                                overflow: TextOverflow.ellipsis,
                                maxLines: widget.listMode ? 4 : 100,
                                softWrap: false,
                              )),
                  ),
                ],
                if (!widget.review
                    .hasRightTranslation(languageProvider.language)) ...[
                  if (widget.review.reviewNeedTranslation(
                      languageProvider.language,
                      widget.displaySemanticAnalyses)) ...[
                    const SizedBox(height: 15),
                    ReviewTranslateButton(
                        isFetchingStream: translateReviewTextQuery.isFetching,
                        onTranslate: translateReviewText,
                        langTagService: langTagService,
                        languageName: languageProvider.language.name),
                  ] else ...[
                    const SizedBox(height: 15),
                    ReviewOriginalTextBtn(
                      langTagService: langTagService,
                      review: widget.review,
                      setOriginalReviewText: () {
                        setState(() {
                          widget.review.setOriginalReviewText();
                        });
                      },
                    )
                  ]
                ],
                if (widget.displaySemanticAnalyses &&
                    widget.review.text != null) ...[
                  const SizedBox(height: 15),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                          color: widget.review.hasReply()
                              ? Colors.white
                              : MalouColors.borderPrimary,
                          width: 1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 15),
                      child: Column(children: [
                        Row(children: [
                          Text(
                            Translation.of(context).semanticAnalyse,
                            style: MalouTextStyles(context)
                                .text10Bold
                                .copyWith(color: MalouColors.textTitle),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 4,
                            softWrap: false,
                          ),
                        ]),
                        if (widget.review.isReviewHasBeenAnalysed()) ...[
                          Align(
                            alignment: Alignment.centerLeft,
                            child: Wrap(
                              spacing: 10,
                              children: [
                                for (var chip in chips)
                                  if (chip.tag != "not_analyzed") ...[
                                    GestureDetector(
                                      onTap: () {
                                        colorizeSegment(
                                            chip.tag, chip.sentiment);
                                        setOpacityToChips(chip);
                                      },
                                      child: Chip(
                                        elevation: 0,
                                        side: BorderSide.none,
                                        // radius
                                        shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(20)),
                                        padding: const EdgeInsets.all(8),
                                        backgroundColor:
                                            chip.sentiment.toLowerCase() ==
                                                    "negative"
                                                ? MalouColors.pinkAccent
                                                    .withOpacity(chip.opacity)
                                                : MalouColors.success
                                                    .withOpacity(chip.opacity),
                                        shadowColor: Colors.black,
                                        label: Text(
                                          capitalize(chip.translate(context)),
                                          style: MalouTextStyles(context)
                                              .text10
                                              .copyWith(color: Colors.white),
                                        ), //Text
                                      ),
                                    ),
                                  ]
                              ],
                            ),
                          )
                        ] else ...[
                          Align(
                            alignment: Alignment.centerLeft,
                            child: Padding(
                              padding: const EdgeInsets.only(top: 10),
                              child: Text(
                                widget.review
                                    .explainSemanticAnalysisStatus(context),
                                style: MalouTextStyles(context)
                                    .text10Italic
                                    .copyWith(color: MalouColors.textBody),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 4,
                                softWrap: false,
                              ),
                            ),
                          )
                        ]
                      ]),
                    ),
                  ),
                ],
                if (widget.review.hasReply()) ...[
                  displayReplies(widget.review, widget.restaurant,
                      widget.keywords, widget.listMode, widget.onEditReply)
                ],
              ]),
            ),
            if (widget.review.socialAttachments != null &&
                widget.review.socialAttachments!.isNotEmpty) ...[
              SizedBox(
                  height: 73,
                  width: double.infinity,
                  child: Row(
                    children: [
                      for (var i = 0; i < attachments!.length; i++) ...[
                        Expanded(
                          child: Hero(
                            tag: "${widget.review.id}-$i",
                            child: GestureDetector(
                              onTap: () => Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => MediasPreviewPage(
                                        attachments: widget
                                            .review.socialAttachments!
                                            .map((attachment) {
                                          return Attachment(
                                              type: attachment.type,
                                              url: attachment.urls.original ??
                                                  attachment.urls.small ??
                                                  "");
                                        }).toList(),
                                        initialPage: i,
                                        tag: "${widget.review.id}-$i")),
                              ),
                              child: Stack(
                                children: [
                                  widget.review.socialAttachments![i].type ==
                                          'photo'
                                      ? CachedImage(
                                          url: widget.review
                                              .getSocialAttachmentUrl(i),
                                          radius: BorderRadius.only(
                                            bottomLeft: i == 0
                                                ? const Radius.circular(10)
                                                : const Radius.circular(0),
                                            bottomRight:
                                                i == attachments.length - 1
                                                    ? const Radius.circular(10)
                                                    : const Radius.circular(0),
                                          ),
                                          height: 73)
                                      : VideoMediaSquare(
                                          url: widget.review
                                              .getSocialAttachmentUrl(i),
                                          radius: BorderRadius.only(
                                            bottomLeft: i == 0
                                                // idk why but for video we need to set a radius of 0.1 instead of 10, otherwise its too big
                                                ? const Radius.circular(0.1)
                                                : const Radius.circular(0),
                                            bottomRight:
                                                i == attachments.length - 1
                                                    ? const Radius.circular(0.1)
                                                    : const Radius.circular(0),
                                          )),
                                  if (i == 2 &&
                                      widget.review.socialAttachments!.length >
                                          3) ...[
                                    Positioned(
                                        child: Container(
                                      alignment: Alignment.center,
                                      width: double.infinity,
                                      height: double.infinity,
                                      decoration: BoxDecoration(
                                        color: MalouColors.textTitle
                                            .withOpacity(0.5),
                                        borderRadius: const BorderRadius.only(
                                          bottomRight: Radius.circular(10),
                                        ),
                                      ),
                                      child: Text(
                                          "+${widget.review.socialAttachments!.length - 3}",
                                          style: MalouTextStyles(context)
                                              .text20Bold
                                              .copyWith(color: Colors.white)),
                                    ))
                                  ]
                                ],
                              ),
                            ),
                          ),
                        ),
                        if (i != widget.review.socialAttachments!.length) ...[
                          const SizedBox(width: 1),
                        ]
                      ],
                    ],
                  ))
            ],
            if (!widget.review.hasReply() &&
                widget.review.isAnswerableAndHasReplyTimeConstraint()) ...[
              widget.review.canBeAnswered()
                  ? Container(
                      decoration: const BoxDecoration(
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(10),
                          bottomRight: Radius.circular(10),
                        ),
                        color: MalouColors.backgroundDark,
                      ),
                      height: 44,
                      width: double.infinity,
                      // center
                      alignment: Alignment.center,
                      child: Text(
                        "${widget.review.getDaysLeftToReply()} ${Translation.of(context).daysLeftToReply}",
                        style: MalouTextStyles(context)
                            .text12Semibold
                            .copyWith(color: MalouColors.primary),
                      ))
                  : Container(
                      decoration: const BoxDecoration(
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(10),
                          bottomRight: Radius.circular(10),
                        ),
                        color: MalouColors.backgroundDark,
                      ),
                      height: 44,
                      width: double.infinity,
                      // center
                      alignment: Alignment.center,
                      child: Text(
                        Translation.of(context).timeLimitReplyReviewExceeded,
                        style: MalouTextStyles(context)
                            .text10Italic
                            .copyWith(color: MalouColors.textBody),
                      ))
            ]
          ],
        ),
      ),
    );
  }

  MaterialButton _buildReviewStateButton(BuildContext context) {
    if (widget.review.hasReply()) {
      return MaterialButton(
        onPressed: () => context.push(
            '/restaurant/${widget.restaurant.id}/reviews/${widget.review.id}',
            extra: {
              'replyMethod': widget.review.getReplyMethod(),
            }),
        shape: const CircleBorder(),
        disabledColor: widget.review.isReplyPendingStatus()
            ? MalouColors.warningLight
            : MalouColors.green,
        elevation: 0,
        highlightElevation: 0,
        minWidth: 36,
        height: 36,
        padding: EdgeInsets.zero,
        color: widget.review.isReplyPendingStatus()
            ? MalouColors.warningLight
            : MalouColors.green,
        child: Icon(
          MalouIcons.check,
          color: widget.review.isReplyPendingStatus()
              ? MalouColors.warning
              : MalouColors.success,
          size: 23,
        ),
      );
    } else {
      return MaterialButton(
        onPressed: widget.listMode
            ? () => context.push(
                    '/restaurant/${widget.restaurant.id}/reviews/${widget.review.id}',
                    extra: {
                      'replyMethod': widget.review.getReplyMethod(),
                    })
            : null,
        shape: const CircleBorder(),
        disabledColor: Colors.transparent,
        elevation: 0,
        highlightElevation: 0,
        minWidth: 36,
        height: 36,
        padding: EdgeInsets.zero,
        color: MalouColors.backgroundDark,
        child: widget.listMode
            ? const Icon(
                MalouIcons.arrowRight,
                color: MalouColors.primary,
                size: 20,
              )
            : Container(),
      );
    }
  }
}
