import 'package:flutter/material.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/widgets/stars/stars.widget.dart';
import 'package:skeleton_loader/skeleton_loader.dart';

class ReviewsCardSkeleton extends StatelessWidget {
  final int nbItems;
  const ReviewsCardSkeleton({super.key, this.nbItems = 1});

  @override
  Widget build(BuildContext context) {
    return SkeletonLoader(
      builder: Container(
        decoration: BoxDecoration(
          border: Border.all(color: MalouColors.borderPrimary, width: 1),
          borderRadius: BorderRadius.circular(10),
        ),
        margin: const EdgeInsets.only(bottom: 20),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  const CircleAvatar(
                    backgroundColor: MalouColors.backgroundDark,
                    radius: 30,
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Container(
                            width: double.infinity,
                            height: 20,
                            decoration: BoxDecoration(
                              color: MalouColors.backgroundDark,
                              borderRadius: BorderRadius.circular(20),
                            )),
                        const SizedBox(height: 10),
                        Container(
                            width: 100,
                            height: 20,
                            decoration: BoxDecoration(
                              color: MalouColors.backgroundDark,
                              borderRadius: BorderRadius.circular(20),
                            )),
                      ],
                    ),
                  ),
                ],
              ),
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 5),
                child: StarsWidget(
                  stars: 5,
                  starFilled: 5,
                  size: 26,
                ),
              ),
              const SizedBox(height: 10),
              Container(
                  width: double.infinity,
                  height: 50,
                  decoration: BoxDecoration(
                    color: MalouColors.backgroundDark,
                    borderRadius: BorderRadius.circular(20),
                  ))
            ],
          ),
        ),
      ),
      items: nbItems,
      highlightColor: MalouColors.borderPrimary,
      baseColor: MalouColors.backgroundDark,
      direction: SkeletonDirection.ltr,
    );
  }
}