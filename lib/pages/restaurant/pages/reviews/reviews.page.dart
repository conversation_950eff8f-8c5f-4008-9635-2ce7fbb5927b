import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:go_router/go_router.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/reviews/widgets/review_filters/review_filters.widget.dart';
import 'package:malou/pages/restaurant/pages/reviews/widgets/review_card/review_card.widget.dart';
import 'package:malou/pages/restaurant/pages/reviews/widgets/skeleton_reviews_card.widget.dart';
import 'package:malou/pages/router.dart';
import 'package:malou/shared/helpers/heap.service.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/helpers/modal_bottom_sheet.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/models/restaurant.model.dart';
import 'package:malou/shared/models/reviews.model.dart';
import 'package:malou/shared/models/reviews_filters.model.dart';
import 'package:malou/shared/models/user.model.dart';
import 'package:malou/shared/services/keyword/keyword.provider.dart';
import 'package:malou/shared/services/platform/platform.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/review/review.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/errors/error_no_platforms_connected.widget.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum ReviewsTabs { unanswered, urgent, all }

class ReviewsPage extends StatefulWidget {
  final String? selectedReviewId;
  const ReviewsPage({super.key, this.selectedReviewId});

  @override
  State<ReviewsPage> createState() => _ReviewsPageState();
}

class _ReviewsPageState extends State<ReviewsPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late RestaurantProvider restaurantProvider;
  late ReviewProvider reviewProvider;
  late UserProvider userProvider;
  late String? selectedReviewId;
  late AnimationController _syncAnimationController;

  BehaviorSubject<String?> killSub = BehaviorSubject();

  StreamSubscription<Restaurant?>? currentRestaurantWatcherSub;

  @override
  void initState() {
    selectedReviewId = widget.selectedReviewId;
    _syncAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    )..repeat();
    super.initState();

    _tabController = TabController(length: 3, vsync: this);
    restaurantProvider =
        Provider.of<RestaurantProvider>(context, listen: false);
    reviewProvider = Provider.of<ReviewProvider>(context, listen: false);
    userProvider = Provider.of<UserProvider>(context, listen: false);

    reviewProvider.filters.add(getReviewsFiltersPresetFromIndexTab(0));

    _listenToRestaurantChange();
    _listenToFiltersChange();

    reviewProvider.setTotalUrgentReviews();
    reviewProvider.setTotalUnansweredReviews();
    reviewProvider.setTotalReviews();

    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        reviewProvider.currentIndexReviewsTab.add(_tabController.index);
        reviewProvider.filters
            .add(getReviewsFiltersPresetFromIndexTab(_tabController.index));
      }
      var organization = userProvider.getOrganizationById(
          restaurantProvider.currentRestaurant!.organizationId ?? '');
      HeapService.instance.track('restaurant_reviews_tab_changed', {
        'restaurantId': restaurantProvider.currentRestaurant!.id,
        'restaurant':
            '${restaurantProvider.currentRestaurant!.name} - ${restaurantProvider.currentRestaurant!.getFullFormattedAddress()}',
        'restaurantOrganisation': organization?.name,
        'tab': _tabController.index
      });
    });

    _listenToCurrentRestaurantReviewsWatcher();

    reviewProvider.pagingController.addPageRequestListener(_fetchPage);

    if (selectedReviewId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        context.push(
            '/restaurant/${restaurantProvider.currentRestaurant!.id}/reviews/$selectedReviewId',
            extra: {
              'replyMethod': ReplyMethod.comment
            }).then((value) => setState(() {
              selectedReviewId = null;
            }));
      });
    }
  }

  void _listenToCurrentRestaurantReviewsWatcher() {
    currentRestaurantWatcherSub?.cancel();
    currentRestaurantWatcherSub = reviewProvider
        .currentRestaurantReviewsWatcher.value
        .takeUntil(killSub)
        .listen((restaurant) {
      if (restaurant == null) {
        reviewProvider.pagingController.refresh();
      }
    });
  }

  ReviewsFilters getReviewsFiltersPresetFromIndexTab(int index) {
    ReviewsTabs idx = ReviewsTabs.values[index];
    switch (idx) {
      case ReviewsTabs.unanswered:
        return reviewProvider.filters.value.copyWith(
          notAnswered: true,
          answered: false,
          ratings: ['0', '1', '2', '3', '4', '5'],
          pending: false,
          answerable: true,
          privatePlatforms: [PrivatePlatform.campaign],
        );
      case ReviewsTabs.urgent:
        return reviewProvider.filters.value.copyWith(
            notAnswered: true,
            answered: false,
            ratings: ['1', '2', '3'],
            pending: false,
            answerable: true,
            privatePlatforms: [PrivatePlatform.campaign]);
      default:
        return reviewProvider.filters.value.copyWith(
          answered: true,
          notAnswered: true,
          pending: true,
          showPrivate: true,
          withText: true,
          withoutText: true,
          archived: false,
          unarchived: true,
          ratings: ['0', '1', '2', '3', '4', '5'],
          sortOrder: -1,
          sortBy: 'date',
          platforms: PlatformKeys.values,
          privatePlatforms: PrivatePlatform.values,
          answerable: false,
        );
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _syncAnimationController.dispose();
    killSub.add('kill');
    reviewProvider.pagingController.removePageRequestListener(_fetchPage);
    reviewProvider.currentIndexReviewsTab.add(0);
    super.dispose();
  }

  void _listenToRestaurantChange() {
    restaurantProvider.addListener(() {
      if (restaurantProvider.currentRestaurant != null) {
        reviewProvider.pagingController.refresh();
        _listenToCurrentRestaurantReviewsWatcher();
        reviewProvider.setTotalUrgentReviews();
        reviewProvider.setTotalUnansweredReviews();
        reviewProvider.setTotalReviews();
      }
    });
  }

  void _listenToFiltersChange() {
    reviewProvider.filters.takeUntil(killSub).listen((value) {
      SharedPreferences.getInstance().then((preferences) {
        preferences.setString(
            'reviewsFilters', jsonEncode(reviewProvider.filters.value));
      });
      reviewProvider.pagingController.refresh();
      reviewProvider.setTotalUrgentReviews();
      reviewProvider.setTotalUnansweredReviews();
      reviewProvider.setTotalReviews();
    });
  }

  void _fetchPage(int pageKey) {
    reviewProvider.getReviews(pageKey).then((value) {
      final isLastPage = value.reviews.length < 20;

      if (!mounted) {
        return;
      }

      if (isLastPage) {
        reviewProvider.pagingController.appendLastPage(value.reviews);
      } else {
        final nextPageKey = 1 + pageKey;
        reviewProvider.pagingController.appendPage(value.reviews, nextPageKey);
      }
    });
  }

  void syncReviews(String restaurantId) async {
    var organization = userProvider.getOrganizationById(
        restaurantProvider.currentRestaurant!.organizationId ?? '');
    HeapService.instance.track('restaurant_reviews_sync', {
      'restaurantId': restaurantProvider.currentRestaurant!.id,
      'restaurant':
          '${restaurantProvider.currentRestaurant!.name} - ${restaurantProvider.currentRestaurant!.getFullFormattedAddress()}',
      'restaurantOrganisation': organization?.name,
    });
    // just to trigger loader directly
    reviewProvider.currentRestaurantReviewsWatcher.value.add(
      // set current state null in case it has been already set
      restaurantProvider.currentRestaurant!
          .copyWith(currentState: CurrentState(reviews: null)),
    );

    await reviewProvider.startWatchingReviewsSynchronization(
        restaurantId, reviewProvider.currentRestaurantReviewsWatcher);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer4<ReviewProvider, PlatformProvider, UserProvider,
            KeywordProvider>(
        builder: (context, reviewProvider, platformProvider, userProvider,
            keywordProvider, child) {
      if (platformProvider.platforms$.value.isEmpty) {
        return StatefulBuilder(
          builder: ((context, setState) => ErroNoPlatfromsConnected(
                restaurantId: restaurantProvider.currentRestaurant!.id,
              )),
        );
      }

      bool displaySemanticAnalyses = false;
      UserRestaurant userRestaurant = userProvider.currentUser!
          .getUserRestaurantByRestaurantId(
              restaurantProvider.currentRestaurant!.id);
      if (userRestaurant.displaySemanticAnalyses != null &&
          userRestaurant.displaySemanticAnalyses!) {
        displaySemanticAnalyses = true;
      }

      return Padding(
        padding: const EdgeInsets.only(left: 20, right: 20),
        child: RefreshIndicator(
          onRefresh: () async {
            reviewProvider.pagingController.refresh();
          },
          child: CustomScrollView(
            slivers: [
              SliverList(
                  delegate: SliverChildListDelegate([
                Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(Translation.of(context).reviews,
                          style: MalouTextStyles(context)
                              .text20Semibold
                              .copyWith(color: MalouColors.textTitle)),
                      Row(
                        children: [
                          MaterialButton(
                            onPressed: () {
                              var organization = userProvider
                                  .getOrganizationById(restaurantProvider
                                          .currentRestaurant!.organizationId ??
                                      '');
                              HeapService.instance
                                  .track('restaurant_reviews_filter', {
                                'restaurantId':
                                    restaurantProvider.currentRestaurant!.id,
                                'restaurant':
                                    '${restaurantProvider.currentRestaurant!.name} - ${restaurantProvider.currentRestaurant!.getFullFormattedAddress()}',
                                'restaurantOrganisation': organization?.name,
                              });
                              AppBottomModal.showTinyModalBottomSheet<bool>(
                                  rootNavigatorKey.currentContext!,
                                  ReviewFiltersWidget(
                                      displayAnalyzeReviews:
                                          displaySemanticAnalyses),
                                  afterClosed: (value) {
                                HeapService.instance.track(
                                    'restaurant_reviews_filter_applied', {
                                  'restaurantId':
                                      restaurantProvider.currentRestaurant!.id,
                                  'display_semantic_analyses': value,
                                  'restaurant':
                                      '${restaurantProvider.currentRestaurant!.name} - ${restaurantProvider.currentRestaurant!.getFullFormattedAddress()}',
                                  'restaurantOrganisation': organization?.name,
                                });
                                userProvider.setDisplaySemanticAnalyses(value,
                                    restaurantProvider.currentRestaurant!.id);
                              });
                            },
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                            elevation: 0,
                            highlightElevation: 0,
                            minWidth: 36,
                            height: 36,
                            padding: EdgeInsets.zero,
                            color: MalouColors.backgroundDark,
                            child: const Icon(
                              MalouIcons.filter,
                              color: MalouColors.primary,
                              size: 20,
                            ),
                          ),
                          StreamBuilder<Restaurant?>(
                              stream: reviewProvider
                                  .currentRestaurantReviewsWatcher.value.stream,
                              builder: (context, snapshot) {
                                bool isSyncNotRunning = !reviewProvider
                                        .currentRestaurantReviewsWatcher
                                        .value
                                        .hasValue ||
                                    snapshot.data == null;
                                return Opacity(
                                  opacity: isSyncNotRunning ? 1 : 0.5,
                                  child: MaterialButton(
                                      onPressed: isSyncNotRunning
                                          ? () {
                                              syncReviews(restaurantProvider
                                                  .currentRestaurant!.id);
                                            }
                                          : null,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      elevation: 0,
                                      highlightElevation: 0,
                                      minWidth: 36,
                                      height: 36,
                                      padding: EdgeInsets.zero,
                                      color: MalouColors.backgroundDark,
                                      child: isSyncNotRunning
                                          ? const Icon(
                                              MalouIcons.sync,
                                              color: MalouColors.primary,
                                              size: 20,
                                            )
                                          : RotationTransition(
                                              turns: Tween(begin: 0.0, end: 1.0)
                                                  .animate(
                                                      _syncAnimationController),
                                              child: const Icon(
                                                MalouIcons.sync,
                                                color: MalouColors.primary,
                                                size: 20,
                                              ),
                                            )),
                                );
                              })
                        ],
                      ),
                    ]),
                const SizedBox(height: 16),
                Container(
                  decoration: BoxDecoration(
                    color: MalouColors.borderPrimary,
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: TabBar(
                      isScrollable: false,
                      overlayColor: WidgetStateProperty.all(Colors.transparent),
                      indicatorSize: TabBarIndicatorSize.tab,
                      dividerColor: Colors.transparent,
                      controller: _tabController,
                      padding: EdgeInsets.zero,
                      indicatorPadding: EdgeInsets.zero,
                      labelPadding: EdgeInsets.zero,
                      unselectedLabelColor: MalouColors.textBody,
                      labelStyle: MalouTextStyles(context).text12Semibold,
                      labelColor: Colors.white,
                      indicator: BoxDecoration(
                        gradient: MalouColors.gradientFive,
                        borderRadius: BorderRadius.circular(30),
                      ),
                      tabs: [
                        Tab(
                            iconMargin: const EdgeInsets.all(10),
                            child: StreamBuilder<int?>(
                              stream: reviewProvider.unAnswered.stream,
                              builder: (context, snapshot) {
                                if (snapshot.hasData) {
                                  return Text(
                                    "${Translation.of(context).toCheck} (${snapshot.data! > 9 ? "+9" : snapshot.data!})",
                                  );
                                }
                                return const SizedBox();
                              },
                            )),
                        Tab(
                            child: StreamBuilder<int?>(
                          stream: reviewProvider.urgent.stream,
                          builder: (context, snapshot) {
                            if (snapshot.hasData) {
                              return Text(
                                "${Translation.of(context).urgent} (${snapshot.data! > 9 ? "+9" : snapshot.data!})",
                              );
                            }
                            return const SizedBox();
                          },
                        )),
                        Tab(
                            child: StreamBuilder<int?>(
                          stream: reviewProvider.total.stream,
                          builder: (context, snapshot) {
                            if (snapshot.hasData) {
                              return Text(
                                "${Translation.of(context).all} (${snapshot.data! > 9 ? "+9" : snapshot.data!})",
                              );
                            }
                            return const SizedBox();
                          },
                        )),
                      ]),
                ),
                const SizedBox(height: 20)
              ])),
              SlidableAutoCloseBehavior(
                child: PagedSliverList<int, Review>.separated(
                    pagingController: reviewProvider.pagingController,
                    builderDelegate: PagedChildBuilderDelegate<Review>(
                        itemBuilder: (context, review, index) {
                          return ReviewCard(
                            key: ValueKey(review.id),
                            review: review,
                            displaySemanticAnalyses: displaySemanticAnalyses,
                            restaurant: restaurantProvider.currentRestaurant!,
                            keywords: keywordProvider.selectedKeywords.value,
                          );
                        },
                        animateTransitions: true,
                        newPageProgressIndicatorBuilder: (context) =>
                            const Center(
                              child: CircularProgressIndicator(
                                color: MalouColors.primary,
                              ),
                            ),
                        noItemsFoundIndicatorBuilder: (context) => Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Image.asset(
                                  IllustrationsPathResolver.resolve(
                                      Illustrations.burgerFlag),
                                  height: 80,
                                ),
                                const SizedBox(height: 20),
                                Text(Translation.of(context).noReviews,
                                    style:
                                        MalouTextStyles(context).text14Italic),
                              ],
                            ),
                        firstPageProgressIndicatorBuilder: (context) =>
                            const SizedBox(
                                height: 100,
                                child: ReviewsCardSkeleton(
                                  nbItems: 2,
                                ))),
                    separatorBuilder: (context, index) =>
                        const SizedBox(height: 10)),
              ),
            ],
          ),
        ),
      );
    });
  }
}
