// ignore: library_prefixes
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/messages/widgets/single_message.widget.dart';
import 'package:malou/pages/router.dart';
import 'package:malou/shared/helpers/constant.dart';
import 'package:malou/shared/helpers/heap.service.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/helpers/modal_bottom_sheet.dart';
import 'package:malou/shared/helpers/time.dart';
import 'package:malou/shared/models/conversation.model.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/models/message.model.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/models/quick_text.model.dart';
import 'package:malou/shared/models/template.model.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/services/medias/medias.service.dart';
import 'package:malou/shared/services/messaging/messaging.filters.dart';
import 'package:malou/shared/services/messaging/messaging.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/appbar/appbar_back.widget.dart';
import 'package:malou/shared/widgets/avatar/avatar.widget.dart';
import 'package:malou/shared/widgets/chips/quick_text_chips_list.widget.dart';
import 'package:malou/shared/widgets/forms/fixed_bottom_form_screen.widget.dart';
import 'package:malou/shared/widgets/media_picker/media_picker_modal.widget.dart';
import 'package:malou/shared/widgets/pages/camera_preview.widget.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';
import 'package:malou/shared/widgets/templates_picker/templates_picker.widget.dart';
import 'package:malou/shared/widgets/toasts/toast.widget.dart';
import 'package:malou/shared/widgets/toasts/toasts.service.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:skeleton_loader/skeleton_loader.dart';

class ConversationPage extends StatefulWidget {
  final String selectedConversationId;
  final MessagingProvider messagingProvider;
  const ConversationPage(
      {super.key,
      required this.messagingProvider,
      required this.selectedConversationId});

  @override
  State<ConversationPage> createState() => _ConversationPageState();
}

class _ConversationPageState extends State<ConversationPage>
    with TickerProviderStateMixin {
  late UserProvider userProvider;
  late LanguageProvider languageProvider;
  late AnimationController _animationController;
  late Animation<double> _animation;
  late MessageDTO baseMessage;
  late RestaurantProvider restaurantProvider;
  final TextEditingController _textEditingController = TextEditingController();
  bool isSendButtonDisabled = true;
  bool loadingPage = true;
  late PlatformDetails platformDetails;
  late ConversationWithMessages currentCWM;

  ScrollController textFieldScrollController = ScrollController();
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  List<QuickText> quickTexts = [];
  String defaultAvatar =
      'https://api.dicebear.com/7.x/initials/svg?seed=unknown.png';
  String? dicebearAvatar;
  String? avatarUrl;
  PlatformKeys? platformKey;

  @override
  void initState() {
    super.initState();
    restaurantProvider =
        Provider.of<RestaurantProvider>(context, listen: false);

    userProvider = Provider.of<UserProvider>(context, listen: false);
    languageProvider = Provider.of<LanguageProvider>(context, listen: false);

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    _textEditingController.addListener(() {
      setState(() {
        isSendButtonDisabled = _textEditingController.text.isEmpty;

        if (textFieldScrollController.hasClients) {
          textFieldScrollController.animateTo(
              textFieldScrollController.position.maxScrollExtent,
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeIn);
        }
      });
    });

    if (widget.messagingProvider.conversationsWithMessages.isEmpty ||
        widget.messagingProvider.currentCWM() == null) {
      widget.messagingProvider
          .getConversation(widget.selectedConversationId)
          .then((value) => setState(() {
                currentCWM = value;
                initConversationPagingController(value);
                loadingPage = false;
              }));
    } else {
      currentCWM = widget.messagingProvider.currentCWM()!;
      initConversationPagingController(widget.messagingProvider.currentCWM()!);
      loadingPage = false;
    }

    Future.delayed(const Duration(milliseconds: 500)).then((value) => widget
            .messagingProvider
            .updateConversation(widget.selectedConversationId, {
          'status': ConversationStatus.READ.name,
          'unreadConversationCount': 0,
        }));
  }

  void initConversationPagingController(ConversationWithMessages cwm) {
    widget.messagingProvider
        .addPagingControllerForConversation(cwm.conversation.id);

    setState(() {
      platformDetails = AppConstant.getDetails(
          parsePlatformKey(cwm.conversation.key) as PlatformKeys);

      if (widget.messagingProvider.currentCWM() != null) {
        dicebearAvatar =
            'https://api.dicebear.com/7.x/initials/svg?seed=${cwm.getConversationSenderName()}.png';
        avatarUrl = widget.messagingProvider
                .currentCWM()!
                .conversation
                .userInfo
                .profilePictureUrl ??
            dicebearAvatar;
        platformKey = PlatformKeys.values.byName(cwm.conversation.key);
      }
    });

    _listenToPageChange(cwm);

    baseMessage = MessageDTO(
        socialConversationId: cwm.conversation.socialConversationId,
        key: cwm.conversation.key,
        conversationId: cwm.conversation.id,
        malouAuthorId: userProvider.currentUser!.id,
        malouAuthor: userProvider.currentUser!,
        userInfo: cwm.conversation.userInfo,
        isFromRestaurant: true);
  }

  List<QuickText> initQuickTexts(ConversationWithMessages cwm) {
    return [
      {
        'key': 'phone',
        'displayKey':
            '@${Translation.of(rootNavigatorKey.currentContext!).restaurantPhone}',
        'content': ''
      },
      {
        'key': 'address',
        'displayKey':
            '@${Translation.of(rootNavigatorKey.currentContext!).restaurantAddress}',
        'content': ''
      },
      {
        'key': 'regular_hours',
        'displayKey':
            '@${Translation.of(rootNavigatorKey.currentContext!).restaurantOpeningHours}',
        'content': ''
      },
      {
        'key': 'website',
        'displayKey':
            '@${Translation.of(rootNavigatorKey.currentContext!).restaurantWebsite}',
        'content': ''
      },
      {
        'key': 'menuUrl',
        'displayKey':
            '@${Translation.of(rootNavigatorKey.currentContext!).restaurantMenu}',
        'content': ''
      },
      {
        'key': 'client_name',
        'displayKey':
            '@${Translation.of(rootNavigatorKey.currentContext!).clientName}',
        'content': '',
      },
    ]
        .map((e) => QuickText(
            key: e['key'] ?? '',
            displayKey: e['displayKey'] ?? '',
            content: setQuickTextsContent(
                e['key'] ?? '', languageProvider.language.name, cwm)))
        .toList(growable: false);
  }

  @override
  Widget build(BuildContext context) {
    quickTexts = initQuickTexts(currentCWM);
    final width = MediaQuery.of(context).size.width;

    Size size = MediaQuery.of(context).size;
    return loadingPage
        ? const LoadingConversationPageShimmer()
        : GestureDetector(
            child: Scaffold(
              backgroundColor: Colors.white,
              appBar: AppBarArrowBack(
                height: 70,
                actions: [
                  Expanded(
                    child: Row(
                      children: [
                        const SizedBox(width: 60),
                        AvatarPlatform(
                          factorSize: 0.8,
                          url: avatarUrl ?? defaultAvatar,
                          platformKey: platformKey ?? PlatformKeys.instagram,
                          fallbackUrl: dicebearAvatar,
                        ),
                        const SizedBox(
                          width: 20,
                        ),
                        SizedBox(
                          width: size.width * 0.6,
                          child: Text(
                            widget.messagingProvider
                                .currentCWM()!
                                .getConversationSenderName(),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: MalouTextStyles(context)
                                .text20Semibold
                                .copyWith(color: MalouColors.textTitle),
                          ),
                        )
                      ],
                    ),
                  )
                ],
              ),
              body: SafeArea(
                child: Consumer<MessagingProvider>(
                    builder: (context, messagingProvider, child) {
                  return Column(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () => FocusScope.of(context).unfocus(),
                          child: SmartRefresher(
                            controller: _refreshController,
                            header: const WaterDropHeader(
                              complete: Text(""),
                              idleIcon: Icon(
                                MalouIcons.spinner,
                                size: 20,
                                color: MalouColors.primary,
                              ),
                              waterDropColor: Colors.transparent,
                              refresh: CircularSpinner(
                                size: 20,
                                color: MalouColors.primary,
                              ),
                            ),
                            enablePullDown: true,
                            enablePullUp: false,
                            onRefresh: () async {
                              setState(() {
                                var pagingController = messagingProvider
                                    .getPagingControllerForConversation(widget
                                        .messagingProvider
                                        .currentCWM()!
                                        .conversation
                                        .id);
                                widget.messagingProvider
                                    .currentCWM()!
                                    .messages = [];
                                pagingController.nextPageKey = 0;
                                pagingController.itemList!.clear();
                                pagingController.refresh();
                                Future.delayed(
                                        const Duration(milliseconds: 500))
                                    .then((value) =>
                                        _refreshController.refreshCompleted());
                              });
                            },
                            child: PagedListView(
                                reverse: true,
                                padding: const EdgeInsets.all(12),
                                pagingController: messagingProvider
                                    .getPagingControllerForConversation(widget
                                        .messagingProvider
                                        .currentCWM()!
                                        .conversation
                                        .id),
                                builderDelegate:
                                    PagedChildBuilderDelegate<Message>(
                                        itemBuilder: (context, message, index) {
                                  var allMessages = messagingProvider
                                      .getPagingControllerForConversation(widget
                                          .messagingProvider
                                          .currentCWM()!
                                          .conversation
                                          .id)
                                      .itemList!;
                                  return SingleMessage(
                                    restaurantProvider: restaurantProvider,
                                    messagingProvider: widget.messagingProvider,
                                    isNextMessageFromSameSender: index > 0
                                        ? allMessages[index - 1]
                                                .isFromRestaurant ==
                                            message.isFromRestaurant
                                        : false,
                                    message: message,
                                    profilePictureUrl: widget.messagingProvider
                                        .currentCWM()!
                                        .conversation
                                        .userInfo
                                        .profilePictureUrl,
                                    platformDetails: platformDetails,
                                    shouldDisplayMessagesNewStartDate:
                                        index == allMessages.length - 1 ||
                                            message.compareSocialCreatedAt(
                                                allMessages[index + 1]),
                                  );
                                })),
                          ),
                        ),
                      ),
                      if (widget.messagingProvider
                              .currentCWM()!
                              .conversation
                              .key ==
                          PlatformKeys.gmb.name)
                        Container(
                          width: width * 0.8,
                          padding: const EdgeInsets.all(8),
                          child: Text(
                            Translation.of(context).gmbMessagesNotAvailable,
                            textAlign: TextAlign.center,
                            style: MalouTextStyles(context).text12,
                          ),
                        )
                      else
                        FixedBottomFormScreen(
                            quickTextsList: QuickTextsList(
                                quickTexts: quickTexts,
                                textEditingController: _textEditingController),
                            leftCustomIcon: buildLeftButtonActions(context),
                            controller: _textEditingController,
                            maxLines: 7,
                            keyboardType: TextInputType.multiline,
                            hint: Translation.of(context).writeAnswerReview,
                            minLines: 1,
                            showBorders: true,
                            onTap: () => _animationController.reverse(),
                            onSubmit:
                                !isSendButtonDisabled ? sendMessage : null)
                    ],
                  );
                }),
              ),
            ),
          );
  }

// --------------------------------------------------------------------------------------------
// Widget builders
// --------------------------------------------------------------------------------------------
  Row buildLeftButtonActions(BuildContext context) {
    return Row(
      children: [
        SizeTransition(
          axis: Axis.horizontal,
          sizeFactor: Tween<double>(
            begin: 1,
            end: 0,
          ).animate(_animation),
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 0),
              end: const Offset(-0.5, 0),
            ).animate(_animation),
            child: FadeTransition(
              opacity: Tween<double>(
                begin: 1,
                end: 0,
              ).animate(_animation),
              child: IconButton(
                constraints: const BoxConstraints(),
                onPressed: () => setState(() {
                  _animationController.forward();
                }),
                icon: const Icon(
                  Icons.add_circle_outline,
                  size: 20,
                  color: MalouColors.primary,
                ),
              ),
            ),
          ),
        ),
        SizeTransition(
          axis: Axis.horizontal,
          sizeFactor: _animation,
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(-0.5, 0),
              end: const Offset(0, 0),
            ).animate(_animation),
            child: FadeTransition(
                opacity: _animation,
                child: Row(
                  children: [
                    IconButton(
                      constraints: const BoxConstraints(),
                      onPressed: () => openCamera(context),
                      icon: const Icon(
                        MalouIcons.photographie,
                        size: 20,
                        color: MalouColors.primary,
                      ),
                    ),
                    IconButton(
                      constraints: const BoxConstraints(),
                      onPressed: () {
                        AppBottomModal.showAppBottomModal<List<Media>>(
                          context,
                          const MediaPickerModal(),
                          initExpanded: true,
                          afterClosed: (result) => sendMedias(result),
                        );
                      },
                      icon: const Icon(
                        MalouIcons.images,
                        size: 20,
                        color: MalouColors.primary,
                      ),
                    ),
                    IconButton(
                      constraints: const BoxConstraints(),
                      onPressed: openTemplatesPicker,
                      icon: const Icon(
                        Icons.bookmarks_outlined,
                        size: 20,
                        color: MalouColors.primary,
                      ),
                    ),
                  ],
                )),
          ),
        ),
      ],
    );
  }

  Future<dynamic> openCamera(BuildContext context) async {
    final result = await PhotoManager.requestPermissionExtend();
    if (!result.isAuth) {
      PhotoManager.openSetting().then((value) {
        PhotoManager.requestPermissionExtend().then((value) {
          if (!value.isAuth) {
            ToastsService.instance.add(
                Toast(type: ToastType.error, message: 'Permission denied'));
            return;
          }
        });
      });
    }
    return Navigator.push(context, MaterialPageRoute(builder: (context) {
      return CameraPreviewPage(
        onSelectImage: sendMediaFromCamera,
      );
    }));
  }

// --------------------------------------------------------------------------------------------
// Other methods
// --------------------------------------------------------------------------------------------
  void openTemplatesPicker() {
    AppBottomModal.showAppBottomModalV2<Template>(
        context,
        TemplatesPicker(
          restaurantId: restaurantProvider.currentRestaurant!.id,
          type: TemplateType.MESSAGE,
        ), afterClosed: (result) {
      _textEditingController.text = replaceVariables(result.text);
      _animationController.reverse();
    });
  }

  String setQuickTextsContent(
      String key, String currentLang, ConversationWithMessages cwm) {
    switch (key) {
      case 'client_name':
        return cwm.conversation.userInfo.displayName!;
      case 'address':
        return restaurantProvider.currentRestaurant!.getFullFormattedAddress();
      case 'regular_hours':
        return generateTextFromHours(
            restaurantProvider.currentRestaurant!.regularHours,
            currentLang,
            Translation.of(context));
      case 'phone':
        return restaurantProvider.currentRestaurant!.phone.toString();
      default:
        return restaurantProvider.currentRestaurant!.toMap()[key] ?? '';
    }
  }

  String replaceVariables(String text) {
    String newText = text;
    for (var element in quickTexts) {
      newText = newText.replaceAll('[{${element.key}}]', element.content);
    }
    return newText;
  }

  void _listenToPageChange(ConversationWithMessages cwm) {
    var currentConversationWithMessages = cwm;

    const pageSize = 10;

    widget.messagingProvider.getPagingControllerForConversation(
        currentConversationWithMessages.conversation.id);

    widget.messagingProvider
        .getPagingControllerForConversation(
            currentConversationWithMessages.conversation.id)
        .addPageRequestListener((pageKey) async {
      try {
        if (pageKey == 0 && currentConversationWithMessages.messages.isEmpty) {
          final newItems =
              await widget.messagingProvider.loadMoreMessages(pageKey);
          final isLastPage = newItems.length < pageSize;
          if (isLastPage) {
            widget.messagingProvider
                .getPagingControllerForConversation(
                    currentConversationWithMessages.conversation.id)
                .appendLastPage(newItems.reversed.toList());
          } else {
            final nextPageKey = pageKey + 1;
            widget.messagingProvider
                .getPagingControllerForConversation(
                    currentConversationWithMessages.conversation.id)
                .appendPage(newItems.reversed.toList(), nextPageKey);
          }
        } else if (pageKey == 0 &&
            currentConversationWithMessages.messages.length < pageSize) {
          widget.messagingProvider
              .getPagingControllerForConversation(
                  currentConversationWithMessages.conversation.id)
              .appendLastPage(
                  currentConversationWithMessages.messages.reversed.toList());
          return;
        } else if (pageKey == 0 &&
            currentConversationWithMessages.messages.length >= pageSize) {
          widget.messagingProvider
              .getPagingControllerForConversation(
                  currentConversationWithMessages.conversation.id)
              .appendPage(
                  currentConversationWithMessages.messages.reversed.toList(),
                  pageKey + 1);
        } else {
          final newItems =
              await widget.messagingProvider.loadMoreMessages(pageKey);
          final isLastPage = newItems.length < pageSize;
          if (isLastPage) {
            widget.messagingProvider
                .getPagingControllerForConversation(
                    currentConversationWithMessages.conversation.id)
                .appendLastPage(newItems.reversed.toList());
          } else {
            final nextPageKey = pageKey + 1;
            widget.messagingProvider
                .getPagingControllerForConversation(
                    currentConversationWithMessages.conversation.id)
                .appendPage(newItems.reversed.toList(), nextPageKey);
          }
        }
      } catch (e) {
        widget.messagingProvider
            .getPagingControllerForConversation(
                currentConversationWithMessages.conversation.id)
            .error = e;
      }
    });
  }

  Future<void> sendMediaFromCamera(XFile file) async {
    final bytes = await file.readAsBytes();
    // transforme XFile to AssetEntity
    AssetEntity assetEntity =
        await PhotoManager.editor.saveImage(bytes, filename: file.name);
    sendMedias([
      Media(
          isDeviceMedia: true,
          category: '',
          userId: '',
          restaurantId: '',
          id: assetEntity.id,
          type: assetEntity.type == AssetType.image
              ? 'image'
              : MediaType.video.name,
          urls: {'original': ''},
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          format: '',
          postIds: [],
          sizes: {},
          socialId: '',
          assetEntity: assetEntity,
          tagIds: [])
    ]);
  }

  Future<void> sendMessage() async {
    var organization = userProvider.getOrganizationById(
        restaurantProvider.currentRestaurant!.organizationId ?? '');
    if (_textEditingController.text.isNotEmpty) {
      HeapService.instance.track('message_sent', {
        'restaurantId': restaurantProvider.currentRestaurant!.id,
        'restaurant':
            '${restaurantProvider.currentRestaurant!.name} - ${restaurantProvider.currentRestaurant!.getFullFormattedAddress()}',
        'restaurantOrganisation': organization?.name,
        'conversation_id': widget.messagingProvider
            .currentCWM()!
            .conversation
            .socialConversationId,
      });
      MessageDTO message = baseMessage.copyWith(
        text: _textEditingController.text,
        socialCreatedAt: DateTime.now().toIso8601String(),
      );
      Message newMessage = Message.fromMessageDTO(message);
      // update provider value
      widget.messagingProvider.addNewMessageToConversation(newMessage);

      widget.messagingProvider
          .getPagingControllerForConversation(
              widget.messagingProvider.currentCWM()!.conversation.id)
          .itemList!
          .insert(0, newMessage);

      _textEditingController.clear();
      widget.messagingProvider
          .sendMessage(message, restaurantProvider.currentRestaurant!.id)
          .then((value) {
        int newMessageIdx = widget.messagingProvider
            .getPagingControllerForConversation(
                widget.messagingProvider.currentCWM()!.conversation.id)
            .itemList!
            .indexWhere((element) => element.id == newMessage.id);
        if (newMessageIdx.isNegative) {
          return;
        }
        widget.messagingProvider
            .getPagingControllerForConversation(
                widget.messagingProvider.currentCWM()!.conversation.id)
            .itemList![newMessageIdx] = value;

        widget.messagingProvider
            .replaceMessageInConversationByIndex(value, newMessage.id);
      });
    }
  }

  Future<void> sendMedias(List<Media> medias) async {
    // check if platform is instagram, send each attachment as a message, else send all attachments as a single message
    for (var media in medias) {
      MessageDTO messageDTO = baseMessage.copyWith(
        socialCreatedAt: DateTime.now().toIso8601String(),
        attachments: [
          Attachment(
              type: media.type == MediaType.photo.name || media.type == 'image'
                  ? 'image'
                  : MediaType.video.name,
              url: media.urls!['original'] ?? '',
              assetEntity: media.assetEntity,
              isDeviceMedia: media.isDeviceMedia)
        ],
      );
      Message newMessage = Message.fromMessageDTO(messageDTO);
      // update provider value
      widget.messagingProvider.addNewMessageToConversation(newMessage);

      widget.messagingProvider
          .getPagingControllerForConversation(
              widget.messagingProvider.currentCWM()!.conversation.id)
          .itemList!
          .insert(0, newMessage);

      // check if the medias have already been uploaded to the server
      if (media.restaurantId!.isEmpty) {
        try {
          MediasService.uploadAssetEntity(
            restaurantId: widget.messagingProvider
                .currentCWM()!
                .conversation
                .restaurantId,
            assetEntity: media.assetEntity!,
          ).then((value) {
            Media newMedia = value.data!;
            messageDTO.attachments = [
              Attachment(
                  url: newMedia.urls!['original']!,
                  type: newMedia.type == MediaType.photo.name
                      ? 'image'
                      : MediaType.video.name)
            ];
            widget.messagingProvider
                .sendMessage(
                    messageDTO, restaurantProvider.currentRestaurant!.id)
                .then((value) {
              int newMessageIdx = widget.messagingProvider
                  .getPagingControllerForConversation(
                      widget.messagingProvider.currentCWM()!.conversation.id)
                  .itemList!
                  .indexWhere((element) => element.id == newMessage.id);

              widget.messagingProvider
                  .getPagingControllerForConversation(
                      widget.messagingProvider.currentCWM()!.conversation.id)
                  .itemList![newMessageIdx] = value;

              // update provider value
              widget.messagingProvider
                  .replaceMessageInConversationByIndex(value, newMessage.id);
            });
          });
        } catch (e) {
          int oldMessageIdx = widget.messagingProvider
              .getPagingControllerForConversation(
                  widget.messagingProvider.currentCWM()!.conversation.id)
              .itemList!
              .indexWhere((element) => element.id == newMessage.id);
          widget.messagingProvider
                  .getPagingControllerForConversation(
                      widget.messagingProvider.currentCWM()!.conversation.id)
                  .itemList![oldMessageIdx] =
              newMessage.copyWith(status: MessageStatus.ERROR);

          // update provider value
          widget.messagingProvider.replaceMessageInConversationByIndex(
              newMessage.copyWith(status: MessageStatus.ERROR), newMessage.id);
        }
      } else {
        widget.messagingProvider
            .sendMessage(messageDTO, restaurantProvider.currentRestaurant!.id)
            .then((value) {
          int newMessageIdx = widget.messagingProvider
              .getPagingControllerForConversation(
                  widget.messagingProvider.currentCWM()!.conversation.id)
              .itemList!
              .indexWhere((element) => element.id == newMessage.id);

          widget.messagingProvider
              .getPagingControllerForConversation(
                  widget.messagingProvider.currentCWM()!.conversation.id)
              .itemList![newMessageIdx] = value;

          // update provider value
          widget.messagingProvider
              .replaceMessageInConversationByIndex(value, newMessage.id);
        });
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _textEditingController.dispose();
    widget.messagingProvider.removePagingControllerForConversation(
        widget.messagingProvider.currentCWM()!.conversation.id);
    super.dispose();
  }
}

class LoadingConversationPageShimmer extends StatelessWidget {
  const LoadingConversationPageShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBarArrowBack(
          height: 70,
          actions: [
            Expanded(
              child: Row(
                children: [
                  const SizedBox(width: 60),
                  SizedBox(
                    height: 50,
                    width: 50,
                    child: SkeletonLoader(
                      highlightColor: MalouColors.backgroundLight,
                      baseColor: MalouColors.backgroundDark,
                      direction: SkeletonDirection.ltr,
                      items: 1,
                      builder: Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: MalouColors.backgroundDark,
                          borderRadius: BorderRadius.circular(50),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(
                    width: 20,
                  ),
                  SizedBox(
                    height: 25,
                    width: 120,
                    child: SkeletonLoader(
                      highlightColor: MalouColors.backgroundLight,
                      baseColor: MalouColors.backgroundDark,
                      direction: SkeletonDirection.ltr,
                      items: 1,
                      builder: Container(
                        width: 120,
                        height: 25,
                        decoration: BoxDecoration(
                          color: MalouColors.backgroundDark,
                          borderRadius: BorderRadius.circular(50),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
        body: const Center(
          child: CircularSpinner(
            size: 50,
            color: MalouColors.primary,
          ),
        ));
  }
}
