import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/messages/widgets/conversations_list.widget.dart';
import 'package:malou/pages/restaurant/pages/messages/widgets/platform_disconnected_warning.widget.dart';
import 'package:malou/shared/helpers/heap.service.dart';
import 'package:malou/shared/models/conversation.model.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/services/messaging/messaging.provider.dart';
import 'package:malou/shared/services/platform/platform.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/errors/error_no_platforms_connected.widget.dart';
import 'package:provider/provider.dart';

class MessagesRootPage extends StatefulWidget {
  final String? selectedConversationId;
  const MessagesRootPage({super.key, this.selectedConversationId});

  @override
  State<MessagesRootPage> createState() => _MessagesRootPageState();
}

const int pageSize = 20;

class _MessagesRootPageState extends State<MessagesRootPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late RestaurantProvider restaurantProvider;
  late MessagingProvider messagingProvider;
  late PlatformProvider platformProvider;

  int selectedTabIndex = 0;

  String errorMessage = '';

  List<String> missingPlatforms = [];

  bool isWarningVisible = true;

  bool areAnyPlatformsConnected = false;

  final PagingController<int, ConversationWithMessages>
      conversationsPagingController = PagingController(firstPageKey: 0);

  @override
  void initState() {
    super.initState();
    restaurantProvider =
        Provider.of<RestaurantProvider>(context, listen: false);
    platformProvider = Provider.of<PlatformProvider>(context, listen: false);
    messagingProvider = Provider.of<MessagingProvider>(context, listen: false);

    conversationsPagingController
        .addPageRequestListener(_listenToScrollNewPage);
    _listenToPlatformsChange();
    _initTabControllerAndListenToTabChange();
    _listenToConversationArchived();
    _listenToNewMessageEvents();
  }

  @override
  void dispose() {
    conversationsPagingController
        .removePageRequestListener(_listenToScrollNewPage);
    _tabController.dispose();
    // remove the listener from the stream
    messagingProvider.messageStream.drain();
    messagingProvider.conversationStream.drain();
    restaurantProvider.restaurantSubject$.drain();
    super.dispose();
  }

  void _setMissingPlatformKey(PlatformProvider platformProvider) {
    Set<String> connectedMessagingPlatfromsKeys = platformProvider
        .connectedMessagingPlatforms$.value
        .where((element) => element.isValid == true)
        .map((e) => e.key)
        .toSet();
    Set<String> messagingPlatformsKeys =
        platformProvider.socialNetworkPlatformsKeys.map((e) => e.name).toSet();
    missingPlatforms = messagingPlatformsKeys
        .difference(connectedMessagingPlatfromsKeys)
        .where((element) =>
            element.isNotEmpty && element != PlatformKeys.mapstr.name)
        .toList();
  }

  void _listenToNewMessageEvents() {
    messagingProvider.messageStream.listen((streamData) {
      switch (streamData.event) {
        case MessageEvent.messageReceived:
          setState(() {
            if (conversationsPagingController.itemList!.isNotEmpty) {
              conversationsPagingController.itemList!.sort((a, b) {
                if (a.getLastMessage() == null || b.getLastMessage() == null) {
                  return 0;
                }
                return b
                    .getLastMessage()!
                    .createdAt
                    .compareTo(a.getLastMessage()!.createdAt);
              });
            }
          });
          break;
        default:
      }
    });
  }

  void _listenToScrollNewPage(int pageKey) async {
    try {
      final newItems = await messagingProvider.getConversationsWithMessages(
          restaurantProvider.currentRestaurant!.id,
          platformProvider.connectedMessagingPlatforms$.value
              .map((messagingPlatform) =>
                  MessagingPlatformKeys.values.byName(messagingPlatform.key))
              .toList(),
          page: pageKey,
          currentTab: selectedTabIndex,
          pageSize: pageSize);
      final isLastPage = newItems.length < pageSize;
      if (isLastPage) {
        conversationsPagingController.appendLastPage(newItems);
      } else {
        final nextPageKey = pageKey + 1;

        conversationsPagingController.appendPage(newItems, nextPageKey);
      }
      messagingProvider.conversationsWithMessages =
          conversationsPagingController.itemList ?? [];
    } catch (e) {
      conversationsPagingController.error = e;
    }
  }

  void _initTabControllerAndListenToTabChange() {
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(() => setState(() {
          selectedTabIndex = _tabController.index;
          if (_tabController.indexIsChanging) {
            conversationsPagingController.refresh();
            HeapService.instance.track(
                'conversations_tab_changed',
                {
                  'tab': _tabController.index,
                },
                context: context);
          }
        }));
  }

  void _listenToPlatformsChange() {
    platformProvider.connectedMessagingPlatforms$.listen((value) {
      conversationsPagingController.refresh();
    });
  }

  void _listenToConversationArchived() {
    messagingProvider.conversationStream.listen((ev) {
      setState(() {
        if (ev.event == ConversationEvent.conversationArchived) {
          conversationsPagingController.itemList!.removeWhere((element) =>
              element.conversation.id ==
              ev.conversationWithMessages.conversation.id);
        } else {
          conversationsPagingController.itemList!.insert(
              ev.previousElemenntIndex ?? 0, ev.conversationWithMessages);
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<Platform>>(
        stream: platformProvider.connectedSocialNetworksPlatforms$,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          bool areAnyMessagingPlatformConnected =
              platformProvider.connectedMessagingPlatforms$.value.isNotEmpty;

          if (!areAnyMessagingPlatformConnected) {
            return Center(
                child: ErroNoPlatfromsConnected(
              restaurantId: restaurantProvider.currentRestaurant!.id,
            ));
          }

          if (snapshot.hasData) {
            areAnyPlatformsConnected = snapshot.data!.isNotEmpty;
            _setMissingPlatformKey(platformProvider);
          }

          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15),
            child: RefreshIndicator(
              onRefresh: () async {
                conversationsPagingController.refresh();
              },
              child: CustomScrollView(
                slivers: [
                  buildHeaderAndTabBar(),
                  ConversationsList(
                    conversationsPagingController:
                        conversationsPagingController,
                  )
                ],
              ),
            ),
          );
        });
  }

  Widget buildHeaderAndTabBar() {
    return SliverList(
        delegate: SliverChildListDelegate([
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(Translation.of(context).messaging,
              style: MalouTextStyles(context).text20Bold),
        ],
      ),
      const SizedBox(height: 10),
      PlatformDisconnectedWarning(
        isWarningVisible: isWarningVisible,
        missingPlatforms: missingPlatforms,
        restaurantId: restaurantProvider.currentRestaurant!.id,
        onDismiss: () => setState(() => isWarningVisible = false),
      ),
      Container(
        padding: EdgeInsets.zero,
        decoration: BoxDecoration(
          color: MalouColors.borderPrimary,
          borderRadius: BorderRadius.circular(30),
        ),
        child: TabBar(
            padding: EdgeInsets.zero,
            labelPadding: EdgeInsets.zero,
            indicatorPadding: EdgeInsets.zero,
            indicatorWeight: 0,
            dividerColor: Colors.transparent,
            unselectedLabelColor: MalouColors.textBody,
            labelStyle: MalouTextStyles(context).text12Semibold,
            labelColor: Colors.white,
            overlayColor: WidgetStateProperty.all(Colors.transparent),
            indicatorSize: TabBarIndicatorSize.tab,
            indicatorColor: Colors.transparent,
            controller: _tabController,
            indicator: BoxDecoration(
              gradient: MalouColors.gradientFive,
              borderRadius: BorderRadius.circular(30),
            ),
            tabs: [
              Tab(
                text: Translation.of(context).all,
              ),
              Tab(
                text: Translation.of(context).favorite,
              ),
              Tab(
                text: Translation.of(context).archived,
              ),
            ]),
      )
    ]));
  }
}
