import 'package:flutter/material.dart';
import 'package:malou/app_config.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/utils.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:url_launcher/url_launcher.dart';

class PlatformDisconnectedWarning extends StatelessWidget {
  final List<String> missingPlatforms;
  final bool isWarningVisible;
  final VoidCallback onDismiss;
  final String restaurantId;
  const PlatformDisconnectedWarning({
    super.key,
    required this.restaurantId,
    required this.onDismiss,
    required this.missingPlatforms,
    this.isWarningVisible = true,
  });

  @override
  Widget build(BuildContext context) {
    if (missingPlatforms.isEmpty || !isWarningVisible) {
      return Container();
    }
    String missingPlatformsFromatted = missingPlatforms.length > 1
        ? missingPlatforms
            .map((e) => capitalize(e))
            .join(' ${Translation.of(context).and} ')
        : missingPlatforms.first;
    return Container(
      padding: const EdgeInsets.all(14.0),
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: MalouColors.backgroundDark),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: MediaQuery.of(context).size.width * 0.6,
                child: Text(
                  capitalize(missingPlatforms.length > 1
                      ? Translation.of(context).manyPlatformDisconnectedWarning(
                          missingPlatformsFromatted)
                      : Translation.of(context).platformDisconnectedWarning(
                          missingPlatformsFromatted)),
                  style: MalouTextStyles(context)
                      .text13
                      .copyWith(color: MalouColors.textTitle),
                ),
              ),
              const SizedBox(height: 5),
              GestureDetector(
                onTap: _onRedirectToPlatforms,
                child: Text(
                  Translation.of(context).reconnectPlatform(
                      missingPlatforms.length > 1
                          ? ''
                          : missingPlatformsFromatted),
                  style: MalouTextStyles(context).text14Semibold.copyWith(
                        color: MalouColors.primary,
                      ),
                ),
              ),
            ],
          ),
          GestureDetector(
            onTap: onDismiss,
            child: const Icon(
              Icons.close,
              color: MalouColors.primary,
            ),
          )
        ],
      ),
    );
  }

  void _onRedirectToPlatforms() async {
    Uri uri = Uri.parse(
        '${AppConfig.shared.webAppUri}/restaurants/$restaurantId/settings/platforms/connection');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }
}
