import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/constant.dart';
import 'package:malou/shared/helpers/utils.dart';
import 'package:malou/shared/models/conversation.model.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/services/messaging/messaging.filters.dart';
import 'package:malou/shared/services/messaging/messaging.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/snackbars/snackbar.dart';
import 'package:timeago/timeago.dart' as timeago;

class ConversationTile extends StatefulWidget {
  final ConversationWithMessages conversationWithMessages;
  final MessagingProvider messagingProvider;
  final int idx;

  const ConversationTile(
      {super.key,
      required this.conversationWithMessages,
      required this.idx,
      required this.messagingProvider});

  @override
  State<ConversationTile> createState() => _ConversationTileState();
}

class _ConversationTileState extends State<ConversationTile> {
  bool hasImageError = false;

  @override
  Widget build(BuildContext context) {
    final avatarUrl =
        'https://api.dicebear.com/7.x/initials/svg?seed=${widget.conversationWithMessages.getConversationSenderName()}.png';
    PlatformDetails platformDetails = AppConstant.getDetails(
        parsePlatformKey(widget.conversationWithMessages.conversation.key)
            as PlatformKeys);
    return Slidable(
      groupTag: '0',
      key: ValueKey(widget.idx),
      closeOnScroll: false,
      endActionPane: ActionPane(
        extentRatio: 0.6,
        dragDismissible: false,
        // A motion is a widget used to control how the pane animates.
        motion: const ScrollMotion(),
        children: [
          CustomSlidableAction(
            flex: 3,
            padding: EdgeInsets.zero,
            onPressed: archiveConversation,
            child: Container(
                margin: const EdgeInsets.only(left: 20),
                decoration: const BoxDecoration(
                  color: MalouColors.primary,
                  borderRadius: BorderRadius.all(Radius.circular(10)),
                ),
                child: Center(
                  child: Text(
                    widget.conversationWithMessages.conversation.archived
                        ? Translation.of(context).unarchive
                        : Translation.of(context).archive,
                    style: MalouTextStyles(context)
                        .text13Semibold
                        .copyWith(color: MalouColors.borderPrimary),
                  ),
                )),
          ),
          CustomSlidableAction(
            flex: 2,
            padding: EdgeInsets.zero,
            onPressed: setConversationStatus,
            child: Container(
                margin: const EdgeInsets.only(left: 5),
                decoration: const BoxDecoration(
                  color: MalouColors.primary,
                  borderRadius: BorderRadius.all(Radius.circular(10)),
                ),
                child: Center(
                  child: Text(
                    widget.conversationWithMessages.isConversationRead()
                        ? Translation.of(context).unread
                        : Translation.of(context).read,
                    style: MalouTextStyles(context)
                        .text13Semibold
                        .copyWith(color: MalouColors.borderPrimary),
                  ),
                )),
          ),
        ],
      ),
      child: SizedBox(
        width: double.infinity,
        child: ListTile(
          contentPadding: EdgeInsets.zero,
          leading: CircleAvatar(
              radius: 22,
              backgroundColor: platformDetails.borderColor,
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  if (!hasImageError &&
                      widget.conversationWithMessages.conversation.userInfo
                              .profilePictureUrl !=
                          null)
                    CircleAvatar(
                      radius: 20,
                      backgroundImage: CachedNetworkImageProvider(widget
                          .conversationWithMessages
                          .conversation
                          .userInfo
                          .profilePictureUrl!),
                      onBackgroundImageError: (exception, stackTrace) {
                        Future.delayed(const Duration(milliseconds: 100), () {
                          setState(() {
                            hasImageError = true;
                          });
                        });
                      },
                    ),
                  if (hasImageError ||
                      widget.conversationWithMessages.conversation.userInfo
                              .profilePictureUrl ==
                          null)
                    ClipRRect(
                        borderRadius: BorderRadius.circular(100),
                        child: SvgPicture.network(avatarUrl, width: 40)),
                  Positioned(
                    bottom: -5,
                    right: -5,
                    child: CircleAvatar(
                      radius: 10,
                      child: Container(
                        decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            image: DecorationImage(
                                image:
                                    AssetImage(platformDetails.resolveLogo()))),
                      ),
                    ),
                  ),
                ],
              )),
          title: Text(
              widget.conversationWithMessages.getConversationSenderName(),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: widget.conversationWithMessages.isConversationRead()
                  ? MalouTextStyles(context)
                      .text15Semibold
                      .copyWith(color: MalouColors.textTitle)
                  : MalouTextStyles(context)
                      .text15Bold
                      .copyWith(color: MalouColors.textTitle)),
          subtitle: Text(widget.conversationWithMessages.getLastMessageText(),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: widget.conversationWithMessages.isConversationRead()
                  ? MalouTextStyles(context)
                      .text13
                      .copyWith(color: MalouColors.textBody70)
                  : MalouTextStyles(context)
                      .text13Bold
                      .copyWith(color: MalouColors.textBody70)),
          trailing: Padding(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                if (widget.conversationWithMessages.getLastMessage() != null)
                  Text(
                      capitalize(timeago.format(
                          widget.conversationWithMessages
                              .getLastMessage()!
                              .createdAt,
                          locale: Intl.getCurrentLocale())),
                      style:
                          widget.conversationWithMessages.isConversationRead()
                              ? MalouTextStyles(context).text10
                              : MalouTextStyles(context).text10Bold),
                const SizedBox(height: 5),
                widget.conversationWithMessages.isConversationRead()
                    ? Container(
                        width: 1,
                      )
                    : CircleAvatar(
                        radius: 5,
                        child: Container(
                          decoration: const BoxDecoration(
                            gradient: MalouColors.gradientFive,
                            shape: BoxShape.circle,
                          ),
                        ))
              ],
            ),
          ),
        ),
      ),
    );
  }

  void setConversationStatus(_, {bool isUndo = false}) {
    setState(() {
      widget.conversationWithMessages.conversation.status =
          widget.conversationWithMessages.conversation.status ==
                  ConversationStatus.READ
              ? ConversationStatus.UNREAD
              : ConversationStatus.READ;
    });
    widget.messagingProvider
        .updateConversation(widget.conversationWithMessages.conversation.id, {
      'status': widget.conversationWithMessages.isConversationRead()
          ? ConversationStatus.READ.name
          : ConversationStatus.UNREAD.name,
    });
    if (!isUndo) {
      showConfirmationSnackbar(
          () => setConversationStatus(
                null,
                isUndo: true,
              ),
          label: widget.conversationWithMessages.conversation.status ==
                  ConversationStatus.READ
              ? Translation.of(context).markedAsRead
              : Translation.of(context).markedAsUnread);
    }
  }

  void archiveConversation(_,
      {ConversationWithMessages? conversationWithMessages,
      bool isUndo = false}) {
    conversationWithMessages ??= widget.conversationWithMessages;
    if (isUndo) {
      int currentIndex = widget.messagingProvider.conversationsWithMessages
          .indexWhere((element) =>
              element.conversation.id ==
              conversationWithMessages!.conversation.id);
      widget.messagingProvider.conversationStreamController.add(
          ConversationChangeEvent(
              conversationWithMessages: conversationWithMessages,
              event: !conversationWithMessages.conversation.archived
                  ? ConversationEvent.conversationArchived
                  : ConversationEvent.conversationUnarchived,
              previousElemenntIndex: currentIndex));
    } else {
      widget.messagingProvider.conversationStreamController.add(
          ConversationChangeEvent(
              conversationWithMessages: conversationWithMessages,
              event: ConversationEvent.conversationArchived));
    }

    widget.messagingProvider
        .updateConversation(conversationWithMessages.conversation.id, {
      'archived': !conversationWithMessages.conversation.archived,
    });
    if (!isUndo) {
      showConfirmationSnackbar(
        () => archiveConversation(null,
            isUndo: true, conversationWithMessages: conversationWithMessages),
        label: conversationWithMessages.conversation.archived
            ? Translation.of(context).conversationArchived
            : Translation.of(context).conversationUnarchived,
      );
    }
  }

  void showConfirmationSnackbar(undoFunction, {required String label}) {
    MalouSnackbars.showInfoSnackbar(
        context: context, undoFunction: undoFunction, label: label);
  }
}
