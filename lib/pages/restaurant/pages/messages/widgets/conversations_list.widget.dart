import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:go_router/go_router.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';

import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/pages/messages/widgets/conversation_tile.widget.dart';
import 'package:malou/shared/helpers/heap.service.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/models/conversation.model.dart';
import 'package:malou/shared/services/messaging/messaging.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';
import 'package:provider/provider.dart';
import 'package:skeleton_loader/skeleton_loader.dart';

class ConversationsList extends StatefulWidget {
  final PagingController<int, ConversationWithMessages>
      conversationsPagingController;
  const ConversationsList({
    super.key,
    required this.conversationsPagingController,
  });

  @override
  State<ConversationsList> createState() => _ConversationsListState();
}

class _ConversationsListState extends State<ConversationsList> {
  @override
  Widget build(BuildContext context) {
    return Consumer3<MessagingProvider, RestaurantProvider, UserProvider>(
        builder: (context, messagingProvider, restaurantProvider, userProvider,
            child) {
      return SlidableAutoCloseBehavior(
        child: PagedSliverList(
          pagingController: widget.conversationsPagingController,
          builderDelegate: PagedChildBuilderDelegate<ConversationWithMessages>(
            animateTransitions: true,
            itemBuilder: (context, conversationWithMessages, index) =>
                GestureDetector(
              onTap: () {
                HeapService.instance.track(
                    'conversation_clicked',
                    {
                      'conversationId':
                          conversationWithMessages.conversation.id,
                    },
                    context: context);
                context.push(
                    '/restaurant/${conversationWithMessages.conversation.restaurantId}/conversations/conversation/${conversationWithMessages.conversation.id}',
                    extra: {
                      'conversationId': conversationWithMessages.conversation.id
                    }).then((value) {});
              },
              child: ConversationTile(
                conversationWithMessages: conversationWithMessages,
                messagingProvider: messagingProvider,
                idx: index,
              ),
            ),
            firstPageErrorIndicatorBuilder: (context) =>
                Column(mainAxisAlignment: MainAxisAlignment.center, children: [
              Image.asset(
                IllustrationsPathResolver.resolve(Illustrations.cook),
                height: 80,
              ),
              const SizedBox(height: 20),
              Text(
                Translation.of(context).anErrorOccured,
                style: MalouTextStyles(context).text14Italic,
              ),
            ]),
            noItemsFoundIndicatorBuilder: (context) => Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  IllustrationsPathResolver.resolve(Illustrations.cook),
                  height: 80,
                ),
                const SizedBox(height: 20),
                Text(Translation.of(context).noMessagesYet,
                    style: MalouTextStyles(context).text14Italic),
              ],
            ),
            newPageProgressIndicatorBuilder: (context) => const CircularSpinner(
              size: 15,
            ),
            firstPageProgressIndicatorBuilder: (context) =>
                const SizedBox(height: 100, child: ConversationListSkeleton()),
          ),
        ),
      );
    });
  }
}

class ConversationListSkeleton extends StatelessWidget {
  const ConversationListSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return SkeletonLoader(
      builder: Container(
        margin: const EdgeInsets.only(top: 20),
        child: Row(
          children: <Widget>[
            const CircleAvatar(
              backgroundColor: MalouColors.backgroundDark,
              radius: 30,
            ),
            const SizedBox(width: 10),
            Expanded(
              child: Column(
                children: <Widget>[
                  Container(
                      width: double.infinity,
                      height: 10,
                      decoration: BoxDecoration(
                        color: MalouColors.backgroundDark,
                        borderRadius: BorderRadius.circular(5),
                      )),
                  const SizedBox(height: 10),
                  Container(
                      width: double.infinity,
                      height: 50,
                      decoration: BoxDecoration(
                        color: MalouColors.backgroundDark,
                        borderRadius: BorderRadius.circular(10),
                      )),
                ],
              ),
            ),
          ],
        ),
      ),
      items: 5,
      highlightColor: MalouColors.borderPrimary,
      baseColor: MalouColors.backgroundDark,
      direction: SkeletonDirection.ltr,
    );
  }
}
