import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/widgets/avatar/avatar.widget.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/helpers/utils.dart';
import 'package:malou/shared/models/message.model.dart';
import 'package:malou/shared/models/message_user_info.model.dart';
import 'package:malou/shared/models/platform.model.dart';
import 'package:malou/shared/services/messaging/messaging.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';
import 'package:malou/shared/widgets/audio/audio_message.widget.dart';
import 'package:malou/shared/widgets/pages/medias_preview.widget.dart';
import 'package:photo_manager_image_provider/photo_manager_image_provider.dart';
import 'package:uuid/uuid.dart';
import 'package:video_player/video_player.dart';

class SingleMessage extends StatefulWidget {
  final Message message;
  final String? profilePictureUrl;
  final PlatformDetails platformDetails;
  final bool isNextMessageFromSameSender;
  final bool shouldDisplayMessagesNewStartDate;
  final MessagingProvider messagingProvider;
  final RestaurantProvider restaurantProvider;
  const SingleMessage({
    super.key,
    required this.message,
    required this.profilePictureUrl,
    required this.platformDetails,
    required this.isNextMessageFromSameSender,
    required this.shouldDisplayMessagesNewStartDate,
    required this.messagingProvider,
    required this.restaurantProvider,
  });

  @override
  State<SingleMessage> createState() => _SingleMessageState();
}

class _SingleMessageState extends State<SingleMessage> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 5),
      child: Column(
        crossAxisAlignment: widget.message.isFromRestaurant
            ? CrossAxisAlignment.end
            : CrossAxisAlignment.start,
        children: [
          widget.shouldDisplayMessagesNewStartDate
              ? Container(
                  alignment: Alignment.center,
                  margin: const EdgeInsets.symmetric(vertical: 10),
                  child: Text(widget.message.getMessageDate(),
                      style: MalouTextStyles(context).text10))
              : Container(),
          GestureDetector(
            onDoubleTap: widget.message.key == PlatformKeys.instagram.name
                ? addReaction
                : null,
            child: !widget.message.isFromRestaurant
                ? ExternalSingleMessage(
                    restaurantProvider: widget.restaurantProvider,
                    messagingProvider: widget.messagingProvider,
                    isNextMessageFromSameSender:
                        widget.isNextMessageFromSameSender,
                    message: widget.message,
                    platformDetails: widget.platformDetails,
                    profilePictureUrl: widget.profilePictureUrl)
                : OwnSingleMessage(
                    restaurantProvider: widget.restaurantProvider,
                    message: widget.message,
                    messagingProvider: widget.messagingProvider),
          ),
        ],
      ),
    );
  }

  void addReaction() {
    widget.messagingProvider.addReactionToMessage(
        widget.message, widget.restaurantProvider.currentRestaurant!.id);
    setState(() {
      widget.message.reactions.add(Reaction(
          reactionType: 'any',
          userInfo: UserInfo(displayName: '', profilePictureUrl: '')));
    });
  }
}

class OwnSingleMessage extends StatefulWidget {
  final RestaurantProvider restaurantProvider;
  final MessagingProvider messagingProvider;
  const OwnSingleMessage({
    super.key,
    required this.message,
    required this.messagingProvider,
    required this.restaurantProvider,
  });

  final Message message;

  @override
  State<OwnSingleMessage> createState() => _OwnSingleMessageState();
}

class _OwnSingleMessageState extends State<OwnSingleMessage> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            widget.message.status == MessageStatus.ERROR
                ? Container(
                    margin: const EdgeInsets.only(right: 5),
                    child: const Icon(Icons.error_outline_rounded,
                        color: MalouColors.error, size: 20),
                  )
                : Container(),
            Stack(
              clipBehavior: Clip.none,
              children: [
                Container(
                  constraints: BoxConstraints(
                      maxWidth: MediaQuery.of(context).size.width * 0.7),
                  decoration: BoxDecoration(
                    color: widget.message.isFromRestaurant
                        ? widget.message.story != null ||
                                (widget.message.attachments != null &&
                                    widget.message.attachments!.isNotEmpty)
                            ? Colors.transparent
                            : MalouColors.backgroundDark
                        : Colors.white,
                    borderRadius: BorderRadius.circular(10),
                    border: widget.message.story != null ||
                            (widget.message.attachments != null &&
                                widget.message.attachments!.isNotEmpty)
                        ? null
                        : Border.all(color: MalouColors.backgroundDark),
                  ),
                  child: widget.message.isUnsupportedAttachment
                      ? TextMessage(
                          isUnsupportedAttachment: true,
                          message: widget.message.copyWith(
                              text: Translation.of(context)
                                  .unsupportedAttachment))
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            widget.message.story != null
                                ? StoryPreview(
                                    story: widget.message.story!,
                                    message: widget.message,
                                  )
                                : Container(
                                    width: 0,
                                  ),
                            widget.message.attachments != null &&
                                    widget.message.attachments!.isNotEmpty
                                ? MediaMessage(
                                    attachments: widget.message.attachments!,
                                    message: widget.message,
                                  )
                                : widget.message.text != null &&
                                        widget.message.text!.isNotEmpty
                                    ? TextMessage(message: widget.message)
                                    : Container(),
                          ],
                        ),
                ),
                widget.message.reactions.isNotEmpty
                    ? ReactionContainer(
                        messagingProvider: widget.messagingProvider,
                        message: widget.message,
                        onUnReact: () {
                          widget.messagingProvider.removeReactionFromMessage(
                              widget.message,
                              widget.restaurantProvider.currentRestaurant!.id);
                          setState(() {
                            widget.message.reactions.removeLast();
                          });
                        },
                      )
                    : Container()
              ],
            ),
            const SizedBox(
              width: 5,
            ),
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              width: widget.message.status == MessageStatus.PENDING ? 10 : 0,
              height: 10,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: MalouColors.backgroundDark,
              ),
            )
          ],
        ),
        widget.message.status == MessageStatus.ERROR
            ? Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    Translation.of(context).sendError,
                    style: MalouTextStyles(context)
                        .text12
                        .copyWith(color: MalouColors.error),
                  ),
                  TextButton(
                      style: TextButton.styleFrom(
                          foregroundColor: MalouColors.error),
                      onPressed: retrySendMessage,
                      child: Text(Translation.of(context).retry,
                          style: MalouTextStyles(context).text12Bold.copyWith(
                                color: MalouColors.error,
                              )))
                ],
              )
            : Container(),
      ],
    );
  }

  Future<void> retrySendMessage() async {
    MessageDTO messageDTO = MessageDTO.fromMessage(widget.message);
    setState(() {
      widget.message.status = MessageStatus.PENDING;
    });
    widget.messagingProvider
        .sendMessage(
            messageDTO, widget.restaurantProvider.currentRestaurant!.id)
        .then((value) {
      setState(() {
        widget.message.status = MessageStatus.DELIVERED;
      });
    }).catchError((e) {
      setState(() {
        widget.message.status = MessageStatus.ERROR;
      });
    });
  }
}

class ExternalSingleMessage extends StatefulWidget {
  const ExternalSingleMessage({
    super.key,
    required this.isNextMessageFromSameSender,
    required this.message,
    required this.platformDetails,
    required this.profilePictureUrl,
    required this.messagingProvider,
    required this.restaurantProvider,
  });

  final bool isNextMessageFromSameSender;
  final Message message;
  final PlatformDetails platformDetails;
  final String? profilePictureUrl;
  final MessagingProvider messagingProvider;
  final RestaurantProvider restaurantProvider;

  @override
  State<ExternalSingleMessage> createState() => _ExternalSingleMessageState();
}

class _ExternalSingleMessageState extends State<ExternalSingleMessage> {
  @override
  Widget build(BuildContext context) {
    final String fallbackUrl =
        'https://api.dicebear.com/7.x/initials/svg?seed=${widget.message.userInfo.displayName}.png';
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        widget.isNextMessageFromSameSender
            ? Container(
                width: 30,
              )
            : Padding(
                padding: const EdgeInsets.only(bottom: 4.0),
                child: Avatar(
                  factorSize: 0.6,
                  url: widget.profilePictureUrl ?? fallbackUrl,
                  fallbackUrl: fallbackUrl,
                ),
              ),
        const SizedBox(
          width: 10,
        ),
        Stack(
          clipBehavior: Clip.none,
          children: [
            widget.message.isUnsupportedAttachment
                ? TextMessage(
                    isUnsupportedAttachment: true,
                    message: widget.message.copyWith(
                        text: Translation.of(context).unsupportedAttachment))
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      widget.message.story != null
                          ? StoryPreview(
                              story: widget.message.story!,
                              message: widget.message,
                            )
                          : Container(),
                      widget.message.attachments != null &&
                              widget.message.attachments!.isNotEmpty
                          ? MediaMessage(
                              attachments: widget.message.attachments!,
                              message: widget.message,
                            )
                          : widget.message.text != null &&
                                  widget.message.text!.isNotEmpty
                              ? TextMessage(message: widget.message)
                              : Container(),
                    ],
                  ),
            widget.message.reactions.isNotEmpty
                ? ReactionContainer(
                    messagingProvider: widget.messagingProvider,
                    message: widget.message,
                    onUnReact: () {
                      widget.messagingProvider.removeReactionFromMessage(
                          widget.message,
                          widget.restaurantProvider.currentRestaurant!.id);
                      setState(() {
                        widget.message.reactions.removeLast();
                      });
                    },
                  )
                : Container()
          ],
        ),
      ],
    );
  }
}

class TextMessage extends StatelessWidget {
  const TextMessage({
    super.key,
    required this.message,
    this.isUnsupportedAttachment = false,
  });
  final bool isUnsupportedAttachment;
  final Message message;

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints:
          BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.7),
      padding: const EdgeInsets.all(14),
      decoration: BoxDecoration(
        color: message.isFromRestaurant
            ? MalouColors.backgroundDark
            : Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: MalouColors.backgroundDark),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            message.text ?? '',
            style: isUnsupportedAttachment
                ? MalouTextStyles(context).text10Italic
                : MalouTextStyles(context)
                    .text13
                    .copyWith(color: MalouColors.textTitle),
          ),
          // text element at the end of the crossAxis
          const SizedBox(
            height: 3,
          ),
          Text(
            message.displayMessageSentAt(),
            style: MalouTextStyles(context).text10,
          ),
        ],
      ),
    );
  }
}

class ReactionContainer extends StatelessWidget {
  final MessagingProvider messagingProvider;
  final Message message;
  final VoidCallback onUnReact;
  const ReactionContainer({
    super.key,
    required this.messagingProvider,
    required this.message,
    required this.onUnReact,
  });

  @override
  Widget build(BuildContext context) {
    return buildPositionned(
      message.isFromRestaurant,
      GestureDetector(
        onTap: onUnReact,
        child: Container(
          padding: const EdgeInsets.all(4.0),
          decoration: BoxDecoration(
            color: MalouColors.backgroundLight,
            borderRadius: BorderRadius.circular(10),
          ),
          child: const Center(
            child: Text('❤️', style: TextStyle(fontSize: 12)),
          ),
        ),
      ),
    );
  }

  Widget buildPositionned(bool isFromRestaurant, Widget child) {
    return isFromRestaurant
        ? Positioned(
            bottom: -10,
            left: 0,
            child: child,
          )
        : Positioned(bottom: -10, right: 0, child: child);
  }
}

class MediaMessage extends StatelessWidget {
  final List<Attachment> attachments;
  final Message message;
  const MediaMessage(
      {super.key, required this.attachments, required this.message});

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints:
          BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.7),
      child: Wrap(
        spacing: 5.0,
        direction: Axis.horizontal,
        runSpacing: 5.0,
        children: [
          for (var attachment in attachments) ...[
            if (attachment.type == 'image')
              ImageMessage(
                  message: message,
                  attachment: attachment,
                  isSmallDisplay: attachments.length > 1)
            else if (attachment.type == 'audio')
              AudioMessage(
                attachment: attachment,
                message: message,
              )
            else
              VideoMessage(
                attachment: attachment,
                message: message,
              )
          ]
        ],
      ),
    );
  }
}

class ImageMessage extends StatefulWidget {
  const ImageMessage({
    super.key,
    required this.attachment,
    required this.isSmallDisplay,
    required this.message,
  });

  final Attachment attachment;
  final bool isSmallDisplay;
  final Message message;

  @override
  State<ImageMessage> createState() => _ImageMessageState();
}

class _ImageMessageState extends State<ImageMessage> {
  bool errorLoadingImage = false;
  @override
  Widget build(BuildContext context) {
    var tagUniqueId = const Uuid().v4();
    return errorLoadingImage
        ? TextMessage(
            message: widget.message
                .copyWith(text: Translation.of(context).unavailableMessage))
        : Hero(
            tag: tagUniqueId,
            child: GestureDetector(
              onTap: widget.message.status == MessageStatus.PENDING
                  ? null
                  : () => Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => MediasPreviewPage(
                                attachments: [widget.attachment],
                                tag: tagUniqueId)),
                      ),
              child: Container(
                constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width * 0.3,
                    maxHeight: MediaQuery.of(context).size.height *
                        (widget.isSmallDisplay ? 0.2 : 0.3)),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  image: widget.attachment.isDeviceMedia
                      ? DecorationImage(
                          fit: BoxFit.cover,
                          image: AssetEntityImageProvider(
                            widget.attachment.assetEntity!,
                          ))
                      : DecorationImage(
                          image: CachedNetworkImageProvider(
                            widget.attachment.url,
                            errorListener: (e) {
                              setState(() {
                                errorLoadingImage = true;
                              });
                            },
                          ),
                          fit: BoxFit.cover,
                        ),
                ),
              ),
            ),
          );
  }
}

class VideoMessage extends StatefulWidget {
  final Attachment attachment;
  final Message message;
  const VideoMessage(
      {super.key, required this.attachment, required this.message});

  @override
  State<VideoMessage> createState() => _VideoMessageState();
}

class _VideoMessageState extends State<VideoMessage> {
  late VideoPlayerController _controller;
  bool errorLoadingVideo = false;

  @override
  void initState() {
    super.initState();

    _controller =
        VideoPlayerController.networkUrl(Uri.parse(widget.attachment.url))
          ..initialize().then((_) {
            setState(() {
              _controller.setLooping(true);
            });
          }).catchError((e) {
            if (mounted) {
              setState(() {
                errorLoadingVideo = true;
              });
            }
          });
  }

  @override
  Widget build(BuildContext context) {
    var tagUniqueId = const Uuid().v4();
    return Hero(
        tag: tagUniqueId,
        child: GestureDetector(
          onTap: () => Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => MediasPreviewPage(
                    attachments: [widget.attachment], tag: tagUniqueId)),
          ),
          child: Container(
            constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.3,
                maxHeight: MediaQuery.of(context).size.height * 0.3),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
            ),
            child: _controller.value.isInitialized
                ? errorLoadingVideo
                    ? TextMessage(
                        message: widget.message.copyWith(
                            text: Translation.of(context).messageNotAvailable))
                    : AspectRatio(
                        aspectRatio: _controller.value.aspectRatio,
                        child: Stack(
                          fit: StackFit.expand,
                          children: [
                            ClipRRect(
                                borderRadius: BorderRadius.circular(10),
                                child: VideoPlayer(_controller)),
                            Center(
                              child: Container(
                                decoration: BoxDecoration(
                                    color: Colors.black.withOpacity(0.4),
                                    shape: BoxShape.circle),
                                child: IconButton(
                                  onPressed: () => Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                          builder: (context) =>
                                              MediasPreviewPage(attachments: [
                                                widget.attachment
                                              ], tag: tagUniqueId))),
                                  icon: const Icon(
                                    MalouIcons.play,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                            Align(
                              alignment: Alignment.bottomRight,
                              child: Container(
                                margin: const EdgeInsets.all(5),
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                    color: Colors.black.withOpacity(0.5),
                                    borderRadius: BorderRadius.circular(10)),
                                child: Text(
                                    formatDuration(_controller.value.duration),
                                    style: MalouTextStyles(context)
                                        .text10Semibold
                                        .copyWith(color: Colors.white)),
                              ),
                            )
                          ],
                        ))
                : const CircularSpinner(
                    color: MalouColors.primary,
                  ),
          ),
        ));
  }
}

class AudioMessage extends StatelessWidget {
  final Message message;
  final Attachment attachment;
  const AudioMessage(
      {super.key, required this.attachment, required this.message});

  @override
  Widget build(BuildContext context) {
    return VoiceMessage(
      me: false,
      audioSrc: attachment.url,
      contactBgColor: MalouColors.primary,
      contactPlayIconBgColor: Colors.transparent,
      contactPlayIconColor: Colors.white,
      showDuration: false,
      contactFgColor: Colors.white,
      contactCircleColor: Colors.white,
      formatDuration: (duration) => formatDuration(duration),
    );
  }
}

class StoryPreview extends StatefulWidget {
  final Story story;
  final Message message;
  const StoryPreview({super.key, required this.story, required this.message});

  @override
  State<StoryPreview> createState() => _StoryPreviewState();
}

class _StoryPreviewState extends State<StoryPreview> {
  bool isLoadingType = true;
  bool isErrorLoadingVideo = false;
  bool isStoryExpired = false;

  @override
  void initState() {
    super.initState();

    isStoryExpired = widget.message.socialCreatedAt
        .isBefore(DateTime.now().subtract(const Duration(hours: 24)));
    if (widget.story.storySocialUrl == null) {
      setState(() {
        isLoadingType = false;
        isErrorLoadingVideo = true;
      });
      return;
    } else {
      getMediaTypeFromUrl(widget.story.storySocialUrl!).then((value) {
        setState(() {
          widget.story.storyMediaType = value;
          isLoadingType = false;
        });
      }).catchError((err) {
        setState(() {
          isLoadingType = false;
          isErrorLoadingVideo = true;
        });
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return isStoryExpired
        ? Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
                border: widget.message.isFromRestaurant
                    ? const Border(
                        right: BorderSide(
                            color: MalouColors.backgroundDark, width: 1))
                    : const Border(
                        left: BorderSide(
                            color: MalouColors.backgroundDark, width: 1))),
            child: Text(
              Translation.of(context).storyExpired,
              style: MalouTextStyles(context).text10Italic,
            ),
          )
        : Column(
            crossAxisAlignment: widget.message.isFromRestaurant
                ? CrossAxisAlignment.end
                : CrossAxisAlignment.start,
            children: [
              Text(
                widget.message.isFromRestaurant
                    ? widget.story.storyType == StoryType.mention.name
                        ? Translation.of(context).youMentionnedInStory
                        : Translation.of(context).youAnsweredAStory
                    : widget.story.storyType == StoryType.mention.name
                        ? Translation.of(context).mentionnedYouInStory
                        : Translation.of(context).answeredYourStory,
                style: MalouTextStyles(context).text10Italic,
              ),
              const SizedBox(
                height: 5,
              ),
              Container(
                padding: const EdgeInsets.only(left: 10),
                decoration: BoxDecoration(
                    border: widget.message.isFromRestaurant
                        ? const Border(
                            right: BorderSide(
                                color: MalouColors.backgroundDark, width: 1))
                        : const Border(
                            left: BorderSide(
                                color: MalouColors.backgroundDark, width: 1))),
                child: isLoadingType
                    ? const CircularSpinner(
                        size: 20,
                        color: MalouColors.primary,
                      )
                    : isErrorLoadingVideo
                        ? TextMessage(
                            message: widget.message.copyWith(
                                text:
                                    Translation.of(context).messageNotAvailable,
                                isFromRestaurant:
                                    widget.message.isFromRestaurant),
                          )
                        : MediaMessage(
                            attachments: [
                              Attachment(
                                  type: widget.story.storyMediaType!,
                                  url: widget.story.storySocialUrl!)
                            ],
                            message: widget.message,
                          ),
              ),
              const SizedBox(
                height: 10,
              ),
            ],
          );
  }
}
