import 'package:flutter/material.dart';
import 'package:flutter_app_badge/flutter_app_badge.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/restaurant/widgets/restaurant_app_bar.widget.dart';
import 'package:malou/shared/helpers/bottom_navigation_item.dart';
import 'package:malou/shared/helpers/heap.service.dart';
import 'package:malou/shared/models/restaurant.model.dart';
import 'package:malou/shared/services/hashtags/hashtags.provider.dart';
import 'package:malou/shared/services/keyword/keyword.provider.dart';
import 'package:malou/shared/services/messaging/messaging.provider.dart';
import 'package:malou/shared/services/organization/organization.provider.dart';
import 'package:malou/shared/services/platform/platform.provider.dart';
import 'package:malou/shared/services/restaurant-ai-settings/restaurant_ai_setting.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/review/review.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/widgets/layout/bottom_navigation/bottom_navigation.widget.dart';
import 'package:malou/shared/widgets/toasts/toast.widget.dart';
import 'package:malou/shared/widgets/toasts/toasts.service.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';

class RestaurantPage extends StatefulWidget {
  final Widget child;

  const RestaurantPage({super.key, required this.child});

  @override
  State<RestaurantPage> createState() => _RestaurantPageState();
}

class _RestaurantPageState extends State<RestaurantPage> {
  late RestaurantProvider restaurantProvider;
  late ReviewProvider reviewProvider;
  late MessagingProvider messagingProvider;
  late KeywordProvider keywordProvider;
  late UserProvider userProvider;
  late OrganizationProvider organizationProvider;
  late PlatformProvider platformProvider;
  late RestaurantAiSettingsProvider restaurantAiSettingsProvider;

  bool isSliddingRestaurantsOpen = false;

  BehaviorSubject<String?> killSub = BehaviorSubject();

  @override
  void initState() {
    super.initState();
    FlutterAppBadge.count(0);
    reviewProvider = Provider.of<ReviewProvider>(context, listen: false);
    restaurantProvider =
        Provider.of<RestaurantProvider>(context, listen: false);
    messagingProvider = Provider.of<MessagingProvider>(context, listen: false);
    userProvider = Provider.of<UserProvider>(context, listen: false);
    keywordProvider = Provider.of<KeywordProvider>(context, listen: false);
    organizationProvider =
        Provider.of<OrganizationProvider>(context, listen: false);
    platformProvider = Provider.of<PlatformProvider>(context, listen: false);
    restaurantAiSettingsProvider =
        Provider.of<RestaurantAiSettingsProvider>(context, listen: false);

    restaurantProvider.selectedBottomAppBarIndex = 0;

    if (reviewProvider.watchers[restaurantProvider.currentRestaurant?.id] !=
        null) {
      reviewProvider.currentRestaurantReviewsWatcher =
          reviewProvider.watchers[restaurantProvider.currentRestaurant?.id]!;
    }

    _listenToRestaurantChange();

    _listenCurrentRestaurantReviewsWatcher();

    reviewProvider.addListener(() {
      _listenCurrentRestaurantReviewsWatcher();
    });

    _loadKeywords();
    _loadRestaurantAiSettings();
    _setOrganization();
    Provider.of<HashtagsProvider>(context, listen: false)
        .getRestaurantHashtags(restaurantProvider.currentRestaurant!);

    if (mounted) {
      _redirectDependingOnRestaurantTypeAndRoute();
    }

    HeapService.instance.init(userProvider);
  }

  void _redirectDependingOnRestaurantTypeAndRoute() {
    restaurantProvider.restaurantSubject$
        .pairwise()
        .where((data) => data.isNotEmpty && data.length == 2)
        .listen((data) {
      var previous = data.first;
      var now = data.last;
      if (now == null) return;
      if (previous == null) return;
      if (!mounted) {
        return;
      }
      var path =
          GoRouter.of(context).routeInformationProvider.value.uri.toString();

      if (previous.isBrandBusiness() != now.isBrandBusiness()) {
        // this technique is not very type safe
        // we should name our routes to be able to get them from the enum BottomNavigationItem
        var currentBarItem = BottomNavigationItem.values
            .firstWhere((element) => path.contains(element.name));

        var tabsRoutes = restaurantProvider.currentRestaurant!
            .getBottomNavigationItemsAvailable();

        var index = tabsRoutes.indexOf(currentBarItem);

        if (index == -1) {
          _onItemTapped(0);
          return;
        }

        setState(() {
          restaurantProvider.selectedBottomAppBarIndex = index;
        });
      }
    });
  }

  void _listenToRestaurantChange() {
    restaurantProvider.addListener(() {
      if (restaurantProvider.currentRestaurant != null) {
        var restaurant = restaurantProvider.currentRestaurant!;
        killSub.add('kill');
        killSub = BehaviorSubject();
        if (reviewProvider.watchers[restaurant.id] != null) {
          reviewProvider.currentRestaurantReviewsWatcher =
              reviewProvider.watchers[restaurant.id]!;
        } else {
          reviewProvider.currentRestaurantReviewsWatcher = Watcher<Restaurant>(
              timer: null, value: BehaviorSubject<Restaurant?>());
        }
        _loadKeywords();
        _loadRestaurantAiSettings();
        _setOrganization();
        _listenCurrentRestaurantReviewsWatcher();
        platformProvider.getPlatformsForRestaurant(restaurant.id).then((value) {
          messagingProvider.initializeMessaging(
              restaurantProvider, platformProvider);
        });

        reviewProvider.addListener(() {
          _listenCurrentRestaurantReviewsWatcher();
        });
      }
    });
  }

  void _listenCurrentRestaurantReviewsWatcher() {
    reviewProvider.currentRestaurantReviewsWatcher.value
        .takeUntil(killSub)
        .listen((restaurant) {
      displayToastAboutFetchingReviews(restaurant);
    });
  }

  void displayToastAboutFetchingReviews(Restaurant? restaurant) {
    if (restaurant != null && restaurant.currentState?.reviews != null) {
      if (restaurant.currentState!.done()) {
        if (restaurant.currentState!.getPlatformsErrosNames().isEmpty) {
          ToastsService.instance.add(Toast(
              type: ToastType.success,
              message: Translation.of(context).reviewsSyncSuccess));
        } else {
          ToastsService.instance.add(Toast(
              type: ToastType.error,
              message:
                  '${Translation.of(context).reviewsSyncError} ${restaurant.currentState?.getPlatformsErrosNames().join(', ') ?? 'unknown error'}'));
        }
      }
    }
  }

  void _loadKeywords() {
    keywordProvider.loadKeywords();
  }

  void _loadRestaurantAiSettings() {
    restaurantAiSettingsProvider
        .fetchAndSetAiSettings(restaurantProvider.currentRestaurant!.id);
  }

  void _setOrganization() {
    if (restaurantProvider.currentRestaurant?.organizationId == null) return;
    organizationProvider.fetchAndSetOrganization(
        restaurantProvider.currentRestaurant!.organizationId!);
  }

  @override
  void dispose() {
    killSub.add('kill');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (isSliddingRestaurantsOpen) {
          setState(() {
            isSliddingRestaurantsOpen = false;
          });
          return false;
        }
        context.go('/restaurants');
        return false;
      },
      child: Scaffold(
          backgroundColor: Colors.white,
          appBar: const RestaurantAppBar(),
          body: SafeArea(
            child: Column(
              children: [
                SizedBox(
                    height: MediaQuery.of(context).size.height * 0.765,
                    child: widget.child),
              ],
            ),
          ),
          bottomNavigationBar: BottomNavigation(
            onItemTapped: _onItemTapped,
            restaurantProvider: restaurantProvider,
          )),
    );
  }

  void _onItemTapped(int index) {
    var tabsRoutes = restaurantProvider.currentRestaurant!
        .getBottomNavigationItemsAvailable();
    if (index == restaurantProvider.selectedBottomAppBarIndex) return;
    final route = tabsRoutes[index].name;
    context
        .go('/restaurant/${restaurantProvider.currentRestaurant!.id}/$route');

    setState(() {
      restaurantProvider.selectedBottomAppBarIndex = index;
    });
    var organization = userProvider.getOrganizationById(
        restaurantProvider.currentRestaurant!.organizationId ?? '');
    HeapService.instance.track('bottom-navigation-bar-$route', {
      'restaurantId': restaurantProvider.currentRestaurant!.id,
      'restaurant':
          '${restaurantProvider.currentRestaurant!.name} - ${restaurantProvider.currentRestaurant!.getFullFormattedAddress()}',
      'restaurantOrganisation': organization?.name,
      'route': route
    });
  }
}
