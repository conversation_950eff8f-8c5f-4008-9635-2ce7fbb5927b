import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/heap.service.dart';
import 'package:malou/shared/helpers/illustrations.dart';
import 'package:malou/shared/helpers/modal_bottom_sheet.dart';
import 'package:malou/shared/helpers/use_query.dart';
import 'package:malou/shared/models/media.model.dart';
import 'package:malou/shared/models/restaurant.model.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/inputs/text_search.widget.dart';
import 'package:malou/shared/widgets/image/cached_image.widget.dart';
import 'package:malou/shared/widgets/toasts/toast.widget.dart';
import 'package:malou/shared/widgets/toasts/toasts.service.dart';
import 'package:provider/provider.dart';

class RestaurantAppBar extends StatefulWidget implements PreferredSizeWidget {
  const RestaurantAppBar({super.key});

  @override
  State<RestaurantAppBar> createState() => _RestaurantAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(50);
}

class _RestaurantAppBarState extends State<RestaurantAppBar> {
  final TextEditingController _searchController = TextEditingController();
  late RestaurantProvider restaurantProvider;
  late QueryResult<Restaurant?, String> getRestaurantBydIdQuery;
  final PagingController<int, Restaurant> _restaurantsPagingController =
      PagingController(firstPageKey: 0);

  @override
  void initState() {
    restaurantProvider =
        Provider.of<RestaurantProvider>(context, listen: false);
    getRestaurantBydIdQuery = useQuery(restaurantProvider.getRestaurantById,
        onError: (error, params, onProcessResult) {
      ToastsService.instance.add(Toast(
          type: ToastType.error,
          message: Translation.of(context).unknownError));
    }, onSuccess: (result, params, onProcessResult) {
      if (result != null) {
        restaurantProvider.currentRestaurant = result;
      }
    });
    _restaurantsPagingController.addPageRequestListener((pageKey) {
      final items = restaurantProvider.getPagedRestaurants(pageKey);
      if (items.isNotEmpty) {
        final isLastPage = items.length < 10;
        if (isLastPage) {
          _restaurantsPagingController.appendLastPage(items);
        } else {
          final nextPageKey = pageKey + 1;
          _restaurantsPagingController.appendPage(items, nextPageKey);
        }
      } else {
        _restaurantsPagingController.appendLastPage(items);
      }
    });
    super.initState();
  }

  void _onSearchTextChanged(
      String text, RestaurantProvider restaurantProvider) {
    if (text.isEmpty) {
      _searchController.clear();
      restaurantProvider.filteredUserRestaurants
          .add(restaurantProvider.userRestaurants);
      return;
    }
    restaurantProvider.filteredUserRestaurants.add(restaurantProvider
        .userRestaurants
        .where((element) =>
            element.name.toLowerCase().contains(text.toLowerCase()))
        .toList());
  }

  Widget _createSliddingRestaurants() {
    var size = MediaQuery.of(context).size;
    return Consumer<RestaurantProvider>(
        builder: (context, restaurantProvider, child) {
      var filteredRestaurants = restaurantProvider.filteredUserRestaurants;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(Translation.of(context).your_businesses,
              style: MalouTextStyles(context)
                  .text20Semibold
                  .copyWith(color: MalouColors.textTitle)),
          const SizedBox(height: 20),
          TextSearch(
              controller: _searchController,
              onChanged: (val) => _onSearchTextChanged(val, restaurantProvider),
              onClearText: () => _onSearchTextChanged('', restaurantProvider)),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.only(bottom: 70),
            height: size.height * 0.7,
            child: StreamBuilder(
                stream: filteredRestaurants,
                builder: (context, snapshot) {
                  if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                    if (snapshot.data?.length ==
                        restaurantProvider.userRestaurants.length) {
                      _restaurantsPagingController.refresh();
                      return _createRestaurantsList(
                          snapshot.data!, restaurantProvider, size);
                    }
                    return ListView.builder(
                        itemCount: snapshot.data!.length,
                        itemBuilder: (context, index) {
                          final restaurant = snapshot.data![index];
                          return RestaurantItem(
                              restaurantProvider: restaurantProvider,
                              getRestaurantBydIdQuery: getRestaurantBydIdQuery,
                              restaurant: restaurant,
                              logo: restaurant.logo?.getUrlForSize(
                                  preferedSize: MediaSize.small));
                        });
                  }
                  return _noSearchResults(context);
                }),
          ),
        ],
      );
    });
  }

  Widget _createRestaurantsList(List<Restaurant> filteredRestaurants,
      RestaurantProvider restaurantProvider, Size size) {
    return PagedListView(
      pagingController: _restaurantsPagingController,
      cacheExtent: 1,
      builderDelegate: PagedChildBuilderDelegate(
        itemBuilder: (context, item, index) {
          var restaurant = filteredRestaurants[index];
          var logo =
              restaurant.logo?.getUrlForSize(preferedSize: MediaSize.small);
          return RestaurantItem(
              restaurantProvider: restaurantProvider,
              getRestaurantBydIdQuery: getRestaurantBydIdQuery,
              restaurant: restaurant,
              logo: logo);
        },
      ),
    );
  }

  Widget _noSearchResults(BuildContext context) {
    return Center(
      child: Column(
        children: [
          Image.asset(
              height: 100,
              IllustrationsPathResolver.resolve(Illustrations.cook)),
          const SizedBox(height: 20),
          Text(Translation.of(context).noResults,
              style: MalouTextStyles(context).text14Bold),
          const SizedBox(height: 10),
          Text(Translation.of(context).try_another_search,
              style: MalouTextStyles(context).text12Italic),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Consumer2<RestaurantProvider, UserProvider>(
      builder: (consumerContext, restaurantProvider, userProvider, child) {
        return AppBar(
          backgroundColor: Colors.white,
          automaticallyImplyLeading: false,
          elevation: 0,
          title:
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            Row(
              children: [
                GestureDetector(
                  onTap: () {
                    HeapService.instance.track('go_to_restaurants_list', {});
                    restaurantProvider.selectedBottomAppBarIndex = 0;
                    context.go('/restaurants');
                  },
                  child: CachedImage(
                      height: 30,
                      width: 30,
                      radius: BorderRadius.circular(50),
                      url: restaurantProvider.currentRestaurant?.logo
                          ?.getUrlForSize(preferedSize: MediaSize.small)),
                ),
                const SizedBox(width: 10),
                GestureDetector(
                    onTap: () {
                      HeapService.instance.track('open_restaurants_list', {});
                      AppBottomModal.showAppBottomModalV2(
                          context, _createSliddingRestaurants(),
                          initExpanded: true, initialChildSize: 0.8);
                    },
                    child: Row(
                      children: [
                        SizedBox(
                          width: size.width * 0.6,
                          child: Text(
                              "${restaurantProvider.currentRestaurant?.name} ${restaurantProvider.currentRestaurant?.getFullFormattedAddress(prefix: ' - ', context: context)}",
                              style: MalouTextStyles(context)
                                  .text15Semibold
                                  .copyWith(color: MalouColors.textBody),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              softWrap: false),
                        ),
                        const Icon(
                          Icons.expand_more,
                          color: MalouColors.primary,
                          size: 20,
                        )
                      ],
                    ))
              ],
            ),
            IconButton(
              icon: const Icon(Icons.settings_outlined),
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              color: MalouColors.primary,
              onPressed: () {
                HeapService.instance.track('go_to_settings', {
                  'restaurantId': restaurantProvider.currentRestaurant?.id,
                  'restaurantOrganization': userProvider.getOrganizationById(
                      restaurantProvider.currentRestaurant?.organizationId ??
                          ''),
                  'restaurant': restaurantProvider.currentRestaurant
                      ?.getRestaurantNameWithFormattedAddress()
                });
                context.push('/settings');
              },
            )
          ]),
        );
      },
    );
  }
}

class RestaurantItem extends StatelessWidget {
  const RestaurantItem({
    super.key,
    required this.getRestaurantBydIdQuery,
    required this.restaurant,
    required this.logo,
    required this.restaurantProvider,
  });

  final QueryResult<Restaurant?, String> getRestaurantBydIdQuery;
  final Restaurant restaurant;
  final String? logo;
  final RestaurantProvider restaurantProvider;

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    return GestureDetector(
      onTap: () {
        getRestaurantBydIdQuery.execute(restaurant.id);
        context.pop();
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 10),
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15),
            color: restaurantProvider.currentRestaurant!.id == restaurant.id
                ? MalouColors.backgroundDark
                : Colors.white),
        child: Row(
          children: [
            CachedImage(
              height: 30,
              width: 30,
              url: logo,
              radius: BorderRadius.circular(10),
            ),
            const SizedBox(width: 10),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: size.width * 0.7,
                  child: Text(restaurant.name,
                      style: MalouTextStyles(context)
                          .text15Semibold
                          .copyWith(color: MalouColors.textTitle),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      softWrap: false),
                ),
                SizedBox(
                  width: size.width * 0.7,
                  child:
                      Text(restaurant.getFullFormattedAddress(context: context),
                          style: MalouTextStyles(context).text14.copyWith(
                                color: MalouColors.textBody,
                              ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          softWrap: false),
                )
              ],
            ),
          ],
        ),
      ),
    );
  }
}
