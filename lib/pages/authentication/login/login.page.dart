import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/authentication/forgot_password/forgot_password.page.dart';
import 'package:malou/pages/authentication/widgets/login_text_field.widget.dart';
import 'package:malou/shared/helpers/utils.dart';
import 'package:malou/shared/services/auth/auth.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:malou/shared/widgets/buttons/round_button.widget.dart';
import 'package:provider/provider.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'dart:async';

import 'login.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> with TickerProviderStateMixin {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  final _emailTooltipKey = GlobalKey<TooltipState>();
  final _passwordTooltipKey = GlobalKey<TooltipState>();
  final _emailFieldKey = GlobalKey<FormFieldState>();
  final _passwordFieldKey = GlobalKey<FormFieldState>();
  String? emailErrorMessage;
  String? passwordErrorMessage;
  bool isPasswordObscured = false;
  bool isSubmitting = false;

  late AnimationController _fadeAnimationController;
  late AnimationController _slideAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  late StreamSubscription<bool> keyboardSubscription;
  bool isKeyboardVisible = false;

  late AuthProvider authProvider;
  late UserProvider userProvider;
  late RestaurantProvider restaurantProvider;

  bool isFormValid = false;

  @override
  void initState() {
    super.initState();
    authProvider = Provider.of<AuthProvider>(context, listen: false);
    userProvider = Provider.of<UserProvider>(context, listen: false);
    restaurantProvider =
        Provider.of<RestaurantProvider>(context, listen: false);
    _fadeAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    );

    _emailController.addListener(updateFormValidity);
    _passwordController.addListener(updateFormValidity);

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _fadeAnimationController,
        curve: const Interval(0.0, 0.5, curve: Curves.ease),
      ),
    );

    _slideAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.0),
      end: const Offset(0.0, -0.32),
    ).animate(CurvedAnimation(
      parent: _slideAnimationController,
      curve: Curves.easeInOut,
    ));

    Login.checkForSessionAndRedirect(authProvider, userProvider, context,
        _fadeAnimationController, _slideAnimationController);

    var keyboardVisibilityController = KeyboardVisibilityController();

    keyboardSubscription =
        keyboardVisibilityController.onChange.listen((bool visible) {
      setState(() {
        isKeyboardVisible = visible;
      });
    });

    authProvider.getCredentialFromStorage().then((value) => {
          _emailController.text = value.$1 ?? '',
          _passwordController.text = value.$2 ?? '',
          updateFormValidity()
        });
  }

  @override
  void dispose() {
    keyboardSubscription.cancel();
    _fadeAnimationController.dispose();
    _slideAnimationController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _emailController.removeListener(updateFormValidity);
    _passwordController.removeListener(updateFormValidity);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    MalouTextStyles textStyles = MalouTextStyles(context);
    Size size = MediaQuery.of(context).size;

    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: Stack(
          children: [
            Container(
              height: size.height,
              width: size.width,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 50),
              decoration: const BoxDecoration(
                gradient: MalouColors.gradientOne,
              ),
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: Column(
                  // center
                  children: [
                    SizedBox(
                      height: isKeyboardVisible
                          ? size.height * 0.175
                          : size.height * 0.2,
                    ),
                    buildWelcomeText(textStyles),
                    SizedBox(
                      height: size.height * 0.05,
                    ),
                    buildLoginForm(
                        textStyles,
                        (email) => Login.emailValidatorFn(email),
                        (password) => Login.passwordValidatorFn(password!),
                        size),
                    Expanded(
                        child: Column(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                          buildForgotPassword(),
                          SizedBox(height: size.height * 0.03),
                        ]))
                  ],
                ),
              ),
            ),
            buildMainLogo(),
          ],
        ),
      ),
    );
  }

  void updateFormValidity() {
    final valid = Login.checkIsFormValid(_emailController, _passwordController);
    if (isFormValid != valid) {
      setState(() {
        isFormValid = valid;
      });
    }
  }

  TextButton buildForgotPassword() {
    return TextButton(
      onPressed: () => Navigator.of(context).push(
        PageRouteBuilder(
          pageBuilder: (context, _, __) => const ForgotPasswordPage(),
          transitionDuration: Duration.zero,
          reverseTransitionDuration: Duration.zero,
        ),
      ),
      child: Text(Translation.of(context).forgotPassword,
          style: MalouTextStyles(context)
              .text10Semibold
              .copyWith(color: MalouColors.whiteLight)),
    );
  }

  Widget buildBottomSection(Size size) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Align(
        alignment: Alignment.bottomCenter,
        child: Container(
          height: size.height * 0.08,
          width: size.width,
          decoration: BoxDecoration(
            color: Colors.white.withAlpha(30),
          ),
        ),
      ),
    );
  }

  SlideTransition buildMainLogo() {
    return SlideTransition(
      position: _slideAnimation,
      child: Center(
        child: SvgPicture.asset(
          'assets/logos/malou.svg',
          semanticsLabel: 'My SVG Image',
        ),
      ),
    );
  }

  Text buildWelcomeText(MalouTextStyles textStyles) {
    return Text(
      Translation.of(context).loginPageSubtitle,
      style: textStyles.text20Semibold.copyWith(
        color: MalouColors.whiteLight,
      ),
      textAlign: TextAlign.center,
    );
  }

  Opacity buildLoginForm(
      MalouTextStyles textStyles,
      String? Function(String? email) emailValidatorFn,
      String? Function(String? password) passwordValidatorfn,
      Size size) {
    return Opacity(
      opacity: isSubmitting ? 0.5 : 1,
      child: Form(
          key: _formKey,
          child: Column(children: [
            LoginTextField(
              fieldKey: _emailFieldKey,
              keyboardType: TextInputType.emailAddress,
              enabled: !isSubmitting,
              textInputAction: TextInputAction.next,
              onChanged: debounceTime((_) {
                if (_emailController.text.isNotEmpty) {
                  setState(() {
                    emailErrorMessage = emailValidatorFn(_emailController.text);
                    _emailFieldKey.currentState?.validate();
                    if (emailErrorMessage != null) {
                      Future.delayed(
                          const Duration(milliseconds: 50),
                          () => _emailTooltipKey.currentState
                              ?.ensureTooltipVisible());
                    }
                  });
                } else {
                  setState(() {
                    emailErrorMessage = null;
                    // set email error message to null
                    _emailFieldKey.currentState?.validate();
                  });
                }
              }),
              suffixIcon: emailErrorMessage != null
                  ? Tooltip(
                      decoration: BoxDecoration(
                        color: MalouColors.textTitle,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      key: _emailTooltipKey,
                      textStyle: textStyles.text13.copyWith(
                        color: Colors.white,
                      ),
                      message: emailErrorMessage,
                      child: IconButton(
                        onPressed: () => _emailTooltipKey.currentState!
                            .ensureTooltipVisible(),
                        icon: const Icon(
                          Icons.warning_amber_outlined,
                          color: Colors.white,
                        ),
                      ),
                    )
                  : null,
              validator: emailValidatorFn,
              controller: _emailController,
              label: Translation.of(context).email,
              hint: '<EMAIL>',
            ),
            SizedBox(height: size.height * 0.03),
            LoginTextField(
                onFieldSubmitted: (val) {
                  if (Login.checkIsFormValid(
                      _emailController, _passwordController)) {
                    Login.submitForm(
                        context,
                        _formKey,
                        _emailController,
                        _passwordController,
                        authProvider,
                        isSubmitting,
                        setState,
                        (bool isSubmitting) => this.isSubmitting = isSubmitting,
                        (BuildContext context) => context.go('/restaurants'));
                  }
                },
                fieldKey: _passwordFieldKey,
                keyboardType: TextInputType.visiblePassword,
                enabled: !isSubmitting,
                obscureText: !isPasswordObscured,
                onChanged: debounceTime((_) {
                  if (_passwordController.text.isNotEmpty) {
                    setState(() {
                      passwordErrorMessage = null;
                      // set email error message to null
                      _passwordFieldKey.currentState?.validate();
                    });
                  } else {
                    setState(() {
                      passwordErrorMessage =
                          passwordValidatorfn(_passwordController.text);
                      _passwordFieldKey.currentState?.validate();
                      if (passwordErrorMessage != null) {
                        Future.delayed(
                            const Duration(milliseconds: 50),
                            () => _passwordTooltipKey.currentState
                                ?.ensureTooltipVisible());
                      }
                    });
                  }
                }),
                validator: passwordValidatorfn,
                controller: _passwordController,
                label: Translation.of(context).password,
                suffixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    passwordErrorMessage == null
                        ? Container()
                        : Tooltip(
                            decoration: BoxDecoration(
                              color: MalouColors.textTitle,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            key: _passwordTooltipKey,
                            textStyle: textStyles.text13.copyWith(
                              color: Colors.white,
                            ),
                            message: passwordErrorMessage,
                            child: IconButton(
                              onPressed: () => _passwordTooltipKey.currentState!
                                  .ensureTooltipVisible(),
                              icon: const Icon(
                                Icons.warning_amber_outlined,
                                color: Colors.white,
                              ),
                            ),
                          ),
                    IconButton(
                      onPressed: () => setState(
                          () => isPasswordObscured = !isPasswordObscured),
                      icon: isPasswordObscured
                          ? const Icon(Icons.visibility_off_outlined,
                              color: Colors.white)
                          : const Icon(
                              Icons.visibility_outlined,
                              color: Colors.white,
                            ),
                    ),
                  ],
                ),
                hint: Translation.of(context).enterPassword),
            SizedBox(
                height:
                    isKeyboardVisible ? size.height * 0.03 : size.height * 0.1),
            RoundButton(
                key: const Key('login_submit_button'),
                isSubmitting: isSubmitting,
                onPressed: () => Login.submitForm(
                    context,
                    _formKey,
                    _emailController,
                    _passwordController,
                    authProvider,
                    isSubmitting,
                    setState,
                    (bool isSubmitting) => this.isSubmitting = isSubmitting,
                    (BuildContext context) => context.go('/restaurants')),
                isFormValid: isFormValid,
                icon: Icons.arrow_forward),
          ])),
    );
  }
}
