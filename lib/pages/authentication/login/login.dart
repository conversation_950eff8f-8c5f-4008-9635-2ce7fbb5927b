// ignore_for_file: use_build_context_synchronously

import 'package:email_validator/email_validator.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/router.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/helpers/heap.service.dart';
import 'package:malou/shared/services/auth/auth.model.dart';
import 'package:malou/shared/services/auth/auth.provider.dart';
import 'package:malou/shared/services/connectivity/connectivity.service.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/widgets/errors/error_dialog.widget.dart';
import 'package:malou/shared/widgets/spinners/circular_spinner.widget.dart';

class Login {
  static String clarifyError(ApiResult res, {Translation? translation}) {
    translation ??= Translation.of(rootNavigatorKey.currentContext!);

    if (res.statusCode == 404) {
      return translation.invalidCredentials;
    } else if (res.statusCode == 503) {
      return translation.noInternetConnection;
    } else {
      return res.message;
    }
  }

  static void checkForSessionAndRedirect(
      AuthProvider authProvider,
      UserProvider userProvider,
      BuildContext context,
      AnimationController fadeAnimationController,
      AnimationController slideAnimationController) {
    Future.wait([
      ConnectivityService().isConnectedToInternet(),
      Future.delayed(const Duration(seconds: 2))
    ]).then((values) {
      if (values[0] == false) {
        context.go('/no-internet');
        return false;
      }

      return authProvider.checkForSession().then((sessionFound) {
        if (sessionFound) {
          authProvider.initializeUserInformation();
        }
        return sessionFound;
      });
    }).then((isConnected) {
      if (isConnected == true) {
        HeapService.instance.init(userProvider);
        context.go('/restaurants');
      } else {
        fadeAnimationController.forward();
        slideAnimationController.forward();
      }
    });
  }

  static String? emailValidatorFn(String? email, {Translation? translation}) {
    translation ??= Translation.of(rootNavigatorKey.currentContext!);
    return EmailValidator.validate(email ?? '')
        ? null
        : translation.invalidEmailMessage;
  }

  static String? passwordValidatorFn(String? password,
      {Translation? translation}) {
    translation ??= Translation.of(rootNavigatorKey.currentContext!);
    return password!.isEmpty ? translation.invalidPassword : null;
  }

  static bool checkIsFormValid(TextEditingController emailController,
      TextEditingController passwordController) {
    return EmailValidator.validate(emailController.value.text) &&
        passwordController.value.text.isNotEmpty;
  }

  static void submitForm(
    BuildContext context,
    GlobalKey<FormState> formKey,
    TextEditingController emailController,
    TextEditingController passwordController,
    AuthProvider authProvider,
    bool isSubmitting,
    void Function(void Function() fn) setState,
    Function(bool) isSubmittingCallback,
    Function(BuildContext) redirectToNextScreen,
  ) async {
    authProvider.saveCredentialsInStorage(
        emailController.text, passwordController.text);
    // Shows spinner
    showDialog(
        context: context,
        barrierDismissible: false,
        barrierColor: Colors.transparent,
        builder: (BuildContext context) {
          return const CircularSpinner(
            key: Key('login_submit_loader'),
          );
        });

    if (formKey.currentState!.validate()) {
      setState(() => isSubmitting = true);

      try {
        ApiResult<AuthResponse> res = await authProvider
            .login(AuthPayload(
                email: emailController.text, password: passwordController.text))
            .then((value) {
          Navigator.of(context).pop();
          return value;
        });

        if (res.error && context.mounted) {
          Login.showErrorDialog(context, Login.clarifyError(res));
        } else {
          await redirectToNextScreen(context);
        }

        setState(() => isSubmitting = false);
      } catch (e) {
        context.pop();
        setState(() => isSubmitting = false);
        Login.showErrorDialog(
            context, Login.clarifyError(ApiResult(message: e.toString())));
        return;
      }
      return;
    }
    context.pop();
  }

  static void showErrorDialog(BuildContext context, String errorMessage) {
    showDialog(
        context: context,
        barrierDismissible: true,
        builder: (context) => ErrorDialog(
            key: const Key('login_error_dialog'), errorMessage: errorMessage));
  }
}
