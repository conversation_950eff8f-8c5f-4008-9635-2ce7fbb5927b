import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/widgets/buttons/round_button.widget.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';

class ForgotPasswordEmailSent extends StatefulWidget {
  final String email;
  const ForgotPasswordEmailSent({super.key, required this.email});

  @override
  State<ForgotPasswordEmailSent> createState() =>
      _ForgotPasswordEmailSentState();
}

class _ForgotPasswordEmailSentState extends State<ForgotPasswordEmailSent> {
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: Container(
        width: size.width,
        height: size.height,
        decoration: const BoxDecoration(gradient: MalouColors.gradientOne),
        padding: const EdgeInsets.symmetric(horizontal: 60, vertical: 50),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              'assets/logos/malou.svg',
            ),
            const SizedBox(
              height: 120,
            ),
            Text(
              Translation.of(context).forgot_password_sent,
              textAlign: TextAlign.center,
              style: MalouTextStyles(context).text20Bold.copyWith(
                  fontSize: 18,
                  color: Colors.white.withOpacity(0.7),
                  height: 1.5),
            ),
            const SizedBox(
              height: 20,
            ),
            Text(
              Translation.of(context)
                  .forgot_password_has_been_sent(widget.email),
              textAlign: TextAlign.center,
              style: MalouTextStyles(context)
                  .text14
                  .copyWith(color: MalouColors.whiteLight, height: 1.5),
            ),
            const SizedBox(
              height: 80,
            ),
            RoundButton(
              key: const Key('forgot_password_email_sent_button'),
              icon: Icons.done,
              onPressed: () {
                context.go('/login');
              },
            )
          ],
        ),
      ),
    );
  }
}
