import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/authentication/login/login.dart';
import 'package:malou/shared/helpers/malou_icons.dart';
import 'package:malou/shared/helpers/utils.dart';
import 'package:malou/shared/services/auth/auth.provider.dart';
import 'package:malou/shared/widgets/buttons/round_button.widget.dart';
import 'package:malou/pages/authentication/widgets/login_text_field.widget.dart';
import 'package:malou/shared/theme/theme_colors.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:malou/shared/theme/theme_texts_styles.dart';
import 'package:provider/provider.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'dart:async';

class ForgotPasswordPage extends StatefulWidget {
  const ForgotPasswordPage({super.key});

  @override
  State<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends State<ForgotPasswordPage> {
  final TextEditingController _emailController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  final _emailTooltipKey = GlobalKey<TooltipState>();
  final _emailFieldKey = GlobalKey<FormFieldState>();
  String? emailErrorMessage;
  bool isSubmitting = false;
  bool isFormValid = false;
  late AuthProvider authProvider;

  late StreamSubscription<bool> keyboardSubscription;
  bool isKeyboardVisible = false;

  @override
  void initState() {
    super.initState();
    authProvider = Provider.of<AuthProvider>(context, listen: false);
    var keyboardVisibilityController = KeyboardVisibilityController();

    keyboardSubscription =
        keyboardVisibilityController.onChange.listen((bool visible) {
      setState(() {
        isKeyboardVisible = visible;
      });
    });
  }

  @override
  void dispose() {
    keyboardSubscription.cancel();
    super.dispose();
  }

  void submitForm() {
    setState(() => isSubmitting = true);

    authProvider.forgotPassword(_emailController.text).then((value) {
      context.go('/forgot_password_email_sent', extra: {
        'email': _emailController.text,
      });
    });
  }

  bool checkIsFormValid() {
    return _formKey.currentState!.validate();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: Container(
          width: size.width,
          height: size.height,
          decoration: const BoxDecoration(gradient: MalouColors.gradientOne),
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 50),
          child: ListView(
            children: [
              Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      RoundButton(
                        key: const Key('forgot_password_close_button'),
                        icon: MalouIcons.cross,
                        onPressed: () => Navigator.of(context).pop(),
                        width: 50,
                        height: 50,
                      ),
                    ],
                  ),
                  SizedBox(height: size.height * 0.1),
                  SvgPicture.asset(
                    'assets/logos/malou.svg',
                  ),
                  SizedBox(height: size.height * 0.05),
                  Text(
                    Translation.of(context).set_email,
                    style: MalouTextStyles(context)
                        .text20Bold
                        .copyWith(fontSize: 18, color: MalouColors.whiteLight),
                  ),
                  SizedBox(height: size.height * 0.01),
                  SizedBox(
                    width: 250,
                    child: Text(
                      Translation.of(context).forgot_password_explanation,
                      textAlign: TextAlign.center,
                      style: MalouTextStyles(context)
                          .text14
                          .copyWith(color: MalouColors.whiteLight),
                    ),
                  ),
                  SizedBox(
                    height: size.height * 0.05,
                  ),
                  Form(
                    onChanged: () => setState(() {
                      isFormValid = checkIsFormValid();
                    }),
                    key: _formKey,
                    child: Column(
                      children: [
                        LoginTextField(
                          fieldKey: _emailFieldKey,
                          keyboardType: TextInputType.emailAddress,
                          enabled: !isSubmitting,
                          onChanged: debounceTime((_) {
                            if (_emailController.text.isNotEmpty) {
                              setState(() {
                                emailErrorMessage = Login.emailValidatorFn(
                                    _emailController.text);
                                _emailFieldKey.currentState?.validate();
                                if (emailErrorMessage != null) {
                                  Future.delayed(
                                      const Duration(milliseconds: 50),
                                      () => _emailTooltipKey.currentState
                                          ?.ensureTooltipVisible());
                                }
                              });
                            } else {
                              setState(() {
                                emailErrorMessage = null;
                                // set email error message to null
                                _emailFieldKey.currentState?.validate();
                              });
                            }
                          }),
                          suffixIcon: emailErrorMessage != null
                              ? Tooltip(
                                  decoration: BoxDecoration(
                                    color: MalouColors.textTitle,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  key: _emailTooltipKey,
                                  textStyle:
                                      MalouTextStyles(context).text13.copyWith(
                                            color: Colors.white,
                                          ),
                                  message: emailErrorMessage,
                                  child: IconButton(
                                    onPressed: () => _emailTooltipKey
                                        .currentState!
                                        .ensureTooltipVisible(),
                                    icon: const Icon(
                                      Icons.warning_amber_outlined,
                                      color: Colors.white,
                                    ),
                                  ),
                                )
                              : null,
                          validator: (email) => Login.emailValidatorFn(email),
                          controller: _emailController,
                          label: Translation.of(context).email,
                          hint: '<EMAIL>',
                        ),
                        SizedBox(
                          height: isKeyboardVisible
                              ? size.height * 0.025
                              : size.height * 0.1,
                        ),
                        RoundButton(
                          key: const Key('forgot_password_submit_button'),
                          isSubmitting: isSubmitting,
                          onPressed: submitForm,
                          isFormValid: isFormValid,
                          icon: Icons.arrow_forward,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
