import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:malou/shared/theme/theme_colors.dart';

class LoginTextField extends StatelessWidget {
  final TextEditingController controller;
  final GlobalKey<FormFieldState>? fieldKey;
  final String label;
  final String hint;
  final bool obscureText;
  final bool enabled;
  final Widget? suffixIcon;
  final TextInputType keyboardType;
  final FocusNode? focusNode;
  final TextInputAction? textInputAction;
  final String? Function(String?)? validator;
  final void Function(String?)? onChanged;
  final void Function(String)? onFieldSubmitted;

  const LoginTextField(
      {super.key,
      required this.controller,
      required this.label,
      required this.hint,
      required this.keyboardType,
      this.suffixIcon,
      this.validator,
      this.onChanged,
      this.obscureText = false,
      this.enabled = true,
      this.focusNode,
      this.onFieldSubmitted,
      this.fieldKey,
      this.textInputAction});

  @override
  Widget build(BuildContext context) {
    return TextForm<PERSON>ield(
      focusNode: focusNode,
      key: fieldKey,
      textInputAction: textInputAction,
      onSaved: (value) => controller.text = value ?? '',
      onFieldSubmitted: onFieldSubmitted,
      autocorrect: false,
      keyboardType: keyboardType,
      enabled: enabled,
      validator: validator,
      controller: controller,
      onChanged: onChanged,
      obscureText: obscureText,
      decoration: InputDecoration(
        disabledBorder: const OutlineInputBorder(
          borderSide: BorderSide(
              color: Colors.white, width: 1, style: BorderStyle.solid),
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
        errorStyle: GoogleFonts.poppins()
            .copyWith(color: Colors.transparent, fontSize: 0),
        suffixIcon: suffixIcon,
        floatingLabelBehavior: FloatingLabelBehavior.always,
        enabledBorder: const OutlineInputBorder(
          borderSide: BorderSide(
              color: Colors.white, width: 1, style: BorderStyle.solid),
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
        labelText: label,
        hintText: hint,
        labelStyle: GoogleFonts.poppins().copyWith(
          color: Colors.white,
          fontSize: 14,
        ),
        focusColor: Colors.white,
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(
              color: Colors.white, width: 1, style: BorderStyle.solid),
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
        hintStyle: GoogleFonts.poppins().copyWith(
            color: MalouColors.whiteLight,
            fontSize: 12,
            fontWeight: FontWeight.w400,
            fontStyle: FontStyle.italic),
        border: const OutlineInputBorder(
          borderSide: BorderSide(
              color: Colors.white, width: 1, style: BorderStyle.solid),
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
      ),
      style: GoogleFonts.poppins().copyWith(
        color: Colors.white,
        fontSize: 14,
      ),
    );
  }
}
