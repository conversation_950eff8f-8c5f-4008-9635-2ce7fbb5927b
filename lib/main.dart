// ignore_for_file: unused_import, library_prefixes

import 'dart:async';
import 'dart:io';

import 'package:device_preview/device_preview.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:malou/app_config.dart';
import 'package:malou/error_logger.dart';
import 'package:malou/firebase_options_development.dart'
    as DefaultFirebaseOptionsDevelopment;
import 'package:malou/firebase_options_production.dart'
    as DefaultFirebaseOptionsProduction;
import 'package:malou/pages/router.dart';
import 'package:malou/shared/helpers/experimentation.service.dart';
import 'package:malou/shared/models/organization.model.dart';
import 'package:malou/shared/services/ai/ai.service.dart';
import 'package:malou/shared/services/connectivity/connectivity.provider.dart';
import 'package:malou/shared/services/feedbacks/feedbacks.provider.dart';
import 'package:malou/shared/services/hashtags/hashtags.provider.dart';
import 'package:malou/shared/services/keyword/keyword.provider.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/services/medias/medias.provider.dart';
import 'package:malou/shared/services/messaging/messaging.provider.dart';
import 'package:malou/shared/services/organization/organization.provider.dart';
import 'package:malou/shared/services/platform/platform.provider.dart';
import 'package:malou/shared/services/posts/posts.provider.dart';
import 'package:malou/shared/services/posts/posts.service.dart';
import 'package:malou/shared/services/posts/seo/seo-posts.provider.dart';
import 'package:malou/shared/services/posts/social-posts/social-posts.provider.dart';
import 'package:malou/shared/services/posts/stories/stories.provider.dart';
import 'package:malou/shared/services/restaurant-ai-settings/restaurant_ai_setting.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/review/review.provider.dart';
import 'package:malou/shared/services/templates/templates.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/theme/main_theme.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:upgrader/upgrader.dart';

import 'generated/l10n.dart';
import 'shared/services/auth/auth.provider.dart';

void main() async {
  runZonedGuarded<Future<void>>(() async {
    WidgetsFlutterBinding.ensureInitialized();
    final originalOnError = FlutterError.onError;

    FlutterError.onError = (FlutterErrorDetails errorDetails) async {
      Zone.current
          .handleUncaughtError(errorDetails.exception, errorDetails.stack!);
      originalOnError!(errorDetails);
    };

    AppConfig.create(
      appName: const String.fromEnvironment('APP_NAME'),
      baseUrl: const String.fromEnvironment('API_URI'),
      pusherKey: const String.fromEnvironment('PUSHER_KEY'),
      webAppUri: const String.fromEnvironment('WEB_URI'),
    );
    final FirebaseOptions firebaseOptions =
        const String.fromEnvironment('CURRENT_ENVIRONNEMENT') == 'development'
            ? DefaultFirebaseOptionsDevelopment
                .DefaultFirebaseOptions.currentPlatform
            : DefaultFirebaseOptionsProduction
                .DefaultFirebaseOptions.currentPlatform;

    await Firebase.initializeApp(options: firebaseOptions);

    SharedPreferences preferences = await SharedPreferences.getInstance();

    timeago.setLocaleMessages(
        preferences.getString('language') ?? Platform.localeName.split('_')[0],
        timeago.FrMessages());
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );

    final experimentationService = ExperimentationService.instance;
    await experimentationService.initialize(null, null);

    if (const String.fromEnvironment('IS_RUNNING_ON_MY_COMPUTER') == 'false') {
      final shouldForceUpdate =
          experimentationService.isOn('kill-switch-mobile-force-update');

      await SentryFlutter.init(
        (options) async {
          options.dsn =
              'https://<EMAIL>/4505481857269760';
          options.tracesSampleRate = 1.0;
          options.enableAutoSessionTracking = true;
          PackageInfo packageInfo = await PackageInfo.fromPlatform();
          options.release = packageInfo.version;
          options.beforeSend = (event, hint) {
            // do not send errors from development
            if (const String.fromEnvironment('CURRENT_ENVIRONNEMENT') ==
                'development') {
              return null;
            }
            return event;
          } as BeforeSendCallback?;
        },
        appRunner: () => runApp(UpgradeAlert(
            navigatorKey: rootNavigatorKey,
            upgrader: Upgrader(
              showIgnore: !shouldForceUpdate,
              dialogStyle: Platform.isAndroid
                  ? UpgradeDialogStyle.material
                  : UpgradeDialogStyle.cupertino,
              canDismissDialog: !shouldForceUpdate,
              showLater: !shouldForceUpdate,
              durationUntilAlertAgain: const Duration(hours: 24),
            ),
            child: MyApp(preferences: preferences))),
      );
    } else {
      runApp(DevicePreview(
          enabled: false,
          builder: (context) => MyApp(preferences: preferences)));
    }
  }, (dynamic error, stackTrace) async {
    logFlutterSystemError(error, stackTrace);
  });
}

class MyApp extends StatelessWidget with WidgetsBindingObserver {
  final SharedPreferences preferences;
  MyApp({super.key, required this.preferences});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      /* 
        This is for providing a global state to the app
        so we can access it from anywhere in the app
      */
      providers: [
        ChangeNotifierProvider(create: (_) => ConnectivityProvider()),
        ChangeNotifierProvider(create: (_) => UserProvider()),
        ChangeNotifierProvider(create: (_) => RestaurantProvider()),
        ChangeNotifierProvider(create: (_) => TemplatesProvider()),
        ChangeNotifierProvider(create: (_) => OrganizationProvider()),
        ChangeNotifierProvider(create: (_) => FeedbacksProvider()),
        ChangeNotifierProvider(create: (_) => HashtagsProvider()),
        ChangeNotifierProvider(create: (_) => RestaurantAiSettingsProvider()),
        ChangeNotifierProvider(
            create: (context) => LanguageProvider(preferences: preferences)),
        ChangeNotifierProxyProvider2<UserProvider, RestaurantProvider,
            AuthProvider>(
          update: (context, userProvider, restaurantProvider, authProvider) =>
              AuthProvider(
                  userProvider: userProvider,
                  restaurantProvider: restaurantProvider),
          create: (context) => AuthProvider(),
        ),
        ChangeNotifierProxyProvider<RestaurantProvider, PlatformProvider>(
          update: (context, restaurantProvider, platformProvider) {
            if (platformProvider == null) {
              return PlatformProvider(restaurantProvider: restaurantProvider);
            }
            platformProvider.restaurantProvider = restaurantProvider;
            if (restaurantProvider.currentRestaurant != null) {
              platformProvider.getPlatformsForRestaurant(
                  restaurantProvider.currentRestaurant!.id);
            }
            return platformProvider;
          },
          create: (context) => PlatformProvider(),
        ),
        ChangeNotifierProxyProvider2<RestaurantProvider, PlatformProvider,
            MessagingProvider>(
          update: (context, restaurantProvider, platformProvider,
              messagingProvider) {
            if (messagingProvider == null) {
              return MessagingProvider(
                  restaurantProvider: restaurantProvider,
                  platformProvider: platformProvider);
            }
            return messagingProvider;
          },
          create: (context) => MessagingProvider(),
        ),
        ChangeNotifierProxyProvider2<RestaurantProvider, PlatformProvider,
            PostsProvider>(
          update:
              (context, restaurantProvider, platformProvider, postsProvider) {
            if (postsProvider == null) {
              return PostsProvider(postsService: PostsService());
            }
            return postsProvider;
          },
          create: (context) => PostsProvider(),
        ),
        ChangeNotifierProxyProvider2<RestaurantProvider, PlatformProvider,
            SocialPostsProvider>(
          update: (context, restaurantProvider, platformProvider,
              socialPostsProvider) {
            if (socialPostsProvider == null) {
              return SocialPostsProvider(postsService: PostsService());
            }
            return socialPostsProvider;
          },
          create: (context) =>
              SocialPostsProvider(postsService: PostsService()),
        ),
        ChangeNotifierProxyProvider2<RestaurantProvider, PlatformProvider,
            SeoPostsProvider>(
          update: (context, restaurantProvider, platformProvider,
              seoPostsProvider) {
            if (seoPostsProvider == null) {
              return SeoPostsProvider(postsService: PostsService());
            }
            return seoPostsProvider;
          },
          create: (context) => SeoPostsProvider(postsService: PostsService()),
        ),
        ChangeNotifierProxyProvider2<RestaurantProvider, PlatformProvider,
            StoriesProvider>(
          update:
              (context, restaurantProvider, platformProvider, storiesProvider) {
            if (storiesProvider == null) {
              return StoriesProvider(postsService: PostsService());
            }
            return storiesProvider;
          },
          create: (context) => StoriesProvider(postsService: PostsService()),
        ),
        ChangeNotifierProxyProvider<RestaurantProvider, MediasProvider>(
          update: (context, restaurantProvider, platformProvider) =>
              MediasProvider(restaurantProvider: restaurantProvider),
          create: (context) => MediasProvider(),
        ),
        ChangeNotifierProxyProvider2<RestaurantProvider, PlatformProvider,
            ReviewProvider>(
          update:
              (context, restaurantProvider, platformProvider, reviewProvider) {
            if (reviewProvider == null) {
              return ReviewProvider(
                  preferences: preferences,
                  restaurantProvider: restaurantProvider,
                  platformProvider: platformProvider);
            }
            reviewProvider.platformProvider = platformProvider;
            reviewProvider.restaurantProvider = restaurantProvider;
            return reviewProvider;
          },
          create: (context) => ReviewProvider(preferences: preferences),
        ),
        ChangeNotifierProxyProvider3<RestaurantProvider, LanguageProvider,
            RestaurantAiSettingsProvider, KeywordProvider>(
          update: (context, restaurantProvider, languageProvider,
              restaurantAiSettingsProvider, keywordProvider) {
            if (keywordProvider == null) {
              return KeywordProvider(
                  restaurantProvider: restaurantProvider,
                  languageProvider: languageProvider,
                  restaurantAiSettingsProvider: restaurantAiSettingsProvider);
            }
            keywordProvider.restaurantProvider = restaurantProvider;
            keywordProvider.languageProvider = languageProvider;
            keywordProvider.restaurantAiSettingsProvider =
                restaurantAiSettingsProvider;
            return keywordProvider;
          },
          create: (context) => KeywordProvider(),
        ),
      ],
      child: Consumer<LanguageProvider>(
        builder: (context, languageProvider, child) {
          return MaterialApp.router(
            builder: DevicePreview.appBuilder,
            locale: Locale(languageProvider.language.name),
            localizationsDelegates: const [
              Translation.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: Translation.supportedLocales,
            title: 'Malou',
            theme: MainTheme.theme,
            routerConfig: router,
          );
        },
      ),
    );
  }
}
