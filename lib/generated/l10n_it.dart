import 'package:intl/intl.dart' as intl;

import 'l10n.dart';

// ignore_for_file: type=lint

/// The translations for Italian (`it`).
class TranslationIt extends Translation {
  TranslationIt([String locale = 'it']) : super(locale);

  @override
  String get aboutToDeleteMessage => 'Stai per eliminare il messaggio.';

  @override
  String get aboutToSignOut => 'Stai per disconnetterti';

  @override
  String get activate => 'Attivare';

  @override
  String get actives => 'Attivi';

  @override
  String get add => 'Aggiungi';

  @override
  String get addARestaurant => 'Aggiungi un ristorante';

  @override
  String get addCoupon => 'Un codice promozionale o un coupon';

  @override
  String get addDraft => 'Crea una bozza';

  @override
  String get addFIrstPost => 'Crea il mio primo post';

  @override
  String get addHashtags => 'Aggiungi degli hashtag';

  @override
  String get addImageToUse => 'Aggiungi un\'immagine per utilizzare questa funzionalità';

  @override
  String get addInfosAboutOffer => 'Aggiungi informazioni sull\'offerta';

  @override
  String get addLinkToOffer => 'Un link all\'offerta';

  @override
  String get addLocation => 'Aggiungi una posizione';

  @override
  String get addNow => 'Aggiungi ora';

  @override
  String get addSeoPost => 'post Google';

  @override
  String get addSocialPost => 'Post social media';

  @override
  String get addTitle => 'Aggiungi un titolo';

  @override
  String get addYourFirstRestaurant => 'Aggiungi il tuo primo ristorante per godere appieno di MalouApp';

  @override
  String get aDish => 'un piatto ';

  @override
  String get advice => 'un consiglio ';

  @override
  String get af => 'Afrikaans';

  @override
  String get aIcreditsBackSoon => 'Le risposte proposte saranno nuovamente disponibili al momento del rinnovo dei crediti.';

  @override
  String get aiPostCaptionFormfieldHint => 'Esempio: il nostro morbido al cioccolato perfetto per concludere il pasto con una nota cremosa con un tono divertente';

  @override
  String get aiPostCaptionFormfieldLabel => 'Genera un post su...';

  @override
  String get all => 'Tutto';

  @override
  String get am => 'Amarico';

  @override
  String get analyzeReviewsNotAvailable => 'L\'analisi semantica non è disponibile per questo esercizio.';

  @override
  String get and => ' e';

  @override
  String get anErrorOccured => 'Si è verificato un errore';

  @override
  String get announceAnEvent => 'Annuncia un evento ';

  @override
  String get answeredYourStory => 'Ha risposto alla tua storia';

  @override
  String get aPlace => 'un luogo';

  @override
  String get ar => 'Arabo';

  @override
  String get archive => 'Archivio';

  @override
  String get archived => 'Archiviato';

  @override
  String get areYouSureYouWantToDeleteThisPost => 'Vuoi eliminare questo post?';

  @override
  String get areYouSureYouWantToDeleteThisPostPublished => 'Vuoi eliminare questo post? Sarà rimosso anche dalle piattaforme su cui è stato pubblicato.';

  @override
  String get areYouSureYouWantToDeleteThisStory => 'Vuoi eliminare questa storia?';

  @override
  String get artificialIntelligence => 'Intelligenza artificiale';

  @override
  String get askForDemo => 'Richiedi una demo';

  @override
  String get atmosphere => 'atmosfera';

  @override
  String get authorizeCamera => 'Consenti l\'accesso alla mia fotocamera';

  @override
  String get authorizeMedias => 'Consenti l\'accesso ai miei media';

  @override
  String get autoReply => 'Risposta automatica';

  @override
  String get az => 'Azero';

  @override
  String get be => 'Bielorusso';

  @override
  String get bg => 'Bulgaro';

  @override
  String get bn => 'Bengalese';

  @override
  String get book => 'Prenota';

  @override
  String get brandAccount => 'Account del marchio';

  @override
  String get bs => 'Bosniaco';

  @override
  String get businessName => 'Nome dell\'esercizio';

  @override
  String get buy => 'Compra';

  @override
  String get byDefault => ' (Per impostazione predefinita)';

  @override
  String get ca => 'Catalano';

  @override
  String get call => 'Chiama';

  @override
  String get cameraNoAccessDescription => 'Questo ti permetterà di inviare foto dalla tua fotocamera.';

  @override
  String get cameraNoAccessTitle => 'MalouApp non ha accesso alla tua fotocamera';

  @override
  String get cancel => 'Annulla';

  @override
  String get cancelEdit => 'Annulla modifiche';

  @override
  String get cannotFindYourLang => 'Non trovi la tua lingua?';

  @override
  String get cant_edit_medias_post_published => '';

  @override
  String get cantAnalyzeImage => 'Impossibile analizzare l\'immagine';

  @override
  String get cantAnalyzeVideos => 'Impossibile analizzare i video';

  @override
  String get cantEditMediaPublished => 'Non puoi modificare i media di un articolo già pubblicato.';

  @override
  String get carrouselForbiddenOnGooGle => 'Non puoi pubblicare un carosello su Google';

  @override
  String get carrouselForbiddenOnGoogleMessage => 'Non puoi pubblicare un carosello su Google';

  @override
  String get carrouselMediasLimitReachedMessage => 'Non puoi selezionare più di 10 media';

  @override
  String get ceb => 'Cebuano';

  @override
  String get changeLanguage => 'Cambia lingua';

  @override
  String get chooseActionButton => 'Scegli un pulsante di azione';

  @override
  String get clientName => 'Nome cliente';

  @override
  String closed_day(String closedDay) {
    return 'chiuso il $closedDay, ';
  }

  @override
  String closed_days(String endDay, String startDay) {
    return 'chiuso dal $startDay al $endDay, ';
  }

  @override
  String get co => 'Corso';

  @override
  String get commentsTab => 'Note';

  @override
  String get completeCaptionToGenerateHashtagsFromAi => 'Completa la didascalia del tuo post in modo che l\'IA possa generare gli hashtag';

  @override
  String get concernedRatings => 'Valutazioni delle recensioni interessate';

  @override
  String get concernsOnlyNegativeReviews => 'Riguarda solo le recensioni negative senza risposta da più di 48 ore';

  @override
  String get conditionOffer => 'Condizioni dell\'offerta';

  @override
  String get conditionsOfOffer => 'Condizioni dell\'offerta';

  @override
  String get confirm => 'Confermare';

  @override
  String get connectGoogle => 'Collega il mio account Google';

  @override
  String get connectMyPlatforms => 'Collega le mie piattaforme';

  @override
  String get connectSocialNetworks => 'Collega i miei account dei social media';

  @override
  String get contactMalouOps => 'Contatta il tuo referente Malou se non trovi ciò che stai cercando nella nostra FAQ 🤗.';

  @override
  String get conversation => 'Messaggistica';

  @override
  String get conversationArchived => 'Conversazione archiviata';

  @override
  String get conversationUnarchived => 'Conversazione non archiviata';

  @override
  String get courier_to_restaurant => 'Consegna';

  @override
  String get cover => 'Copertina';

  @override
  String get create_reel => 'Creare un Reel';

  @override
  String get createCompletionError => 'Si è verificato un errore durante la generazione dell\'IA, riprova.';

  @override
  String get createDraft => 'Creare una bozza';

  @override
  String get cs => 'Ceco';

  @override
  String get customer_to_restaurant => 'Cliente';

  @override
  String get customerReviews => 'Recensioni';

  @override
  String get customized => 'Personalizzato ';

  @override
  String get cy => 'Gallese';

  @override
  String get da => 'Danese';

  @override
  String dailyUnansweredReviewsNotificationText(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count nuove recensioni ricevute',
      one: '1 nuova recensione ricevuta',
    );
    return '⭐️ Eh, $_temp0';
  }

  @override
  String dailyUnreadMessagesNotificationText(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count nuovi messaggi',
      one: '1 nuovo messaggio',
    );
    return '💬 Eh, $_temp0';
  }

  @override
  String get date => 'Data';

  @override
  String get dateOf => 'Data di';

  @override
  String get daysLeftToReply => ' giorni rimasti per rispondere';

  @override
  String get de => 'Tedesco';

  @override
  String get delete => 'Elimina';

  @override
  String get deleteFeedbackMessageError => 'Errore durante l\'eliminazione del feedback, riprova.';

  @override
  String get describeDish => 'Il nostro nuovo menu con ';

  @override
  String get describeGiveaway => 'Un prossimo evento con ';

  @override
  String get descriptionTab => 'Descrizione';

  @override
  String get displayAnalyzeReviews => 'Mostra analisi semantica';

  @override
  String get displayOnlyNeverPosted => 'Mostra solo i media mai pubblicati';

  @override
  String get dontForgetToAnswer => 'Non dimenticare di rispondere da Malouapp.';

  @override
  String get dontLetYourCustomersWithoutAnswer => 'Non lasciare i tuoi clienti senza risposta, visita Malouapp.';

  @override
  String get draft => 'Bozza';

  @override
  String get drafts => 'Bozze';

  @override
  String get duplicate => 'Duplica';

  @override
  String get eachTime => 'Ogni volta';

  @override
  String get edit => 'Modifica';

  @override
  String get editWithAI => 'Modifica con l\'IA';

  @override
  String get el => 'Greco';

  @override
  String get email => 'Email';

  @override
  String get emailSelected => 'Dall\'email';

  @override
  String get emptyDeviceMedias => 'Non hai ancora media nella tua galleria';

  @override
  String get emptyGalleryMedias => 'Non hai ancora media nella tua galleria';

  @override
  String get emptyPosts => 'Nessun post qui';

  @override
  String get emptySocialPosts => 'Non hai ancora collegato Malouapp ai tuoi account dei social media... ma puoi già creare bozze';

  @override
  String get en => 'Inglese';

  @override
  String get enableNotifications => 'Abilita le notifiche';

  @override
  String get ended => 'Completato';

  @override
  String get enterPassword => 'Inserisci la tua password';

  @override
  String get eo => 'Esperanto';

  @override
  String get errorCreatingPost => 'Errore durante la creazione del post';

  @override
  String get errorPublishing => 'Errore durante la pubblicazione';

  @override
  String get errorRequired => 'Campo obbligatorio';

  @override
  String get es => 'Spagnolo';

  @override
  String get et => 'Estone';

  @override
  String get eu => 'Basco';

  @override
  String get expeditiousness => 'tempo di attesa';

  @override
  String get fa => 'Persiano';

  @override
  String get favorite => 'Preferiti';

  @override
  String feedbackMarkAsClosed(String name) {
    return '$name ha chiuso la conversazione';
  }

  @override
  String feedbackMarkAsReopen(String name) {
    return '$name ha riaperto la conversazione';
  }

  @override
  String get feedbacks => 'Feedback';

  @override
  String get feedbackYouTag => '(Tu)';

  @override
  String get fi => 'Finlandese';

  @override
  String get filter => 'Filtri';

  @override
  String get food => 'cibo';

  @override
  String get forgot_password_explanation => 'Ti invieremo un link per reimpostare la tua password';

  @override
  String forgot_password_has_been_sent(String email) {
    return 'All\'indirizzo fornito:\n$email';
  }

  @override
  String get forgot_password_sent => 'È stata inviata una email di reset della password';

  @override
  String get forgotPassword => 'Hai dimenticato la password?';

  @override
  String get fr => 'Francese';

  @override
  String get frequency => 'Frequenza';

  @override
  String get friday => 'Venerdì';

  @override
  String get from => 'da';

  @override
  String get fy => 'Frisone';

  @override
  String get ga => 'Irlandese';

  @override
  String get gd => 'Gaelico scozzese';

  @override
  String get general => 'Generale';

  @override
  String get generate => 'Genera';

  @override
  String get generateListOfHashtags => 'Genera una lista di hashtag';

  @override
  String get generatePostPrefixText => 'Genera un post su ';

  @override
  String get generateReviewReply => 'Générer une réponse à un avis';

  @override
  String get generateReviewReplyPlaceholder => 'Bonjour, merci pour votre note ... ';

  @override
  String get giveAdvice => 'Dare consigli';

  @override
  String get giveaway => 'Un concorso di un mese per gli abbonati che mettono mi piace al post e taggano un amico nei commenti, con in palio ';

  @override
  String get gl => 'Galiziano';

  @override
  String get gmbMessagesNotAvailable => 'Google ha disabilitato definitivamente la sua funzionalità di messaggistica, non è più possibile inviare messaggi in questa conversazione';

  @override
  String get google => 'Google';

  @override
  String get googleConnectedButNoPosts => 'Non hai ancora collegato Malouapp al tuo account Google... ma puoi già creare bozze!';

  @override
  String get gu => 'Gujarati';

  @override
  String get ha => 'Hausa';

  @override
  String get hashtagAdvice1 => 'Tra 3 e 6 hashtag';

  @override
  String get hashtagAdvice2 => '3 o più categorie\n variano gli hashtag da un post all\'altro';

  @override
  String get hashtags => 'Hashtag';

  @override
  String get hashtagsCategories => 'Categorie degli hashtag';

  @override
  String get hashtagsDisplay => 'Visualizzazione degli hashtag';

  @override
  String get hashtagsRequiredForAI => 'Non hai una lista di hashtag.';

  @override
  String get haw => 'Hawaiano';

  @override
  String get he => 'Ebraico';

  @override
  String get hi => 'Hindi';

  @override
  String get hide_my_keywords => 'Nascondere le altre parole chiave';

  @override
  String get hmn => 'Hmong';

  @override
  String get home => 'Home';

  @override
  String get hour => 'Ora';

  @override
  String get hr => 'Croato';

  @override
  String get ht => 'Creolo haitiano';

  @override
  String get hu => 'Ungherese';

  @override
  String get hy => 'Armeno';

  @override
  String get hygiene => 'igiene';

  @override
  String get ia => '(IA)';

  @override
  String get iaGenerationExampleHintText => 'Esempio: Genera un post sul nostro nuovo piatto di burrata al pistacchio con un tono formale';

  @override
  String get id => 'Indonesiano';

  @override
  String get ig => 'Igbo';

  @override
  String get includeHashtagOfTheRestaurant => 'Includere l\'hashtag dell\'azienda';

  @override
  String get includeReviewsWithAutoAnswer => 'Includi recensioni con risposta automatica';

  @override
  String get inFirstComment => 'Come primo commento';

  @override
  String get inLegend => 'Nella didascalia';

  @override
  String get invalidCredentials => 'Verifica le tue credenziali di accesso';

  @override
  String get invalidEmailMessage => 'Inserisci un indirizzo email valido';

  @override
  String get invalidLink => 'Link non valido';

  @override
  String get invalidPassword => 'Inserisci la tua password';

  @override
  String get it => 'Italiano';

  @override
  String get iw => 'Ebraico';

  @override
  String get ja => 'Giapponese';

  @override
  String get jw => 'Giavanese';

  @override
  String get ka => 'Georgiano';

  @override
  String get keywords => 'Parole chiave';

  @override
  String get keywords_score => 'Punteggio';

  @override
  String get keywordsAdvices => 'Consigli sulle parole chiave';

  @override
  String get keywordScoreAdviceResponseTime => 'Risposta entro 72 ore';

  @override
  String get keywordScoreAdviceRestaurantName => 'Aggiungi il nome del ristorante';

  @override
  String get keywordScoreAdviceReviewerName => 'Includi il nome del cliente';

  @override
  String get keywordScoreAdviceSimpleBricksNumber => 'Utilizza parole chiave : ';

  @override
  String get keywordScoreAdviceSimpleBricksVariety => 'Varietà di parole chiave :';

  @override
  String get keywordScoreAdviceSimpleBricksVarietyTip => '(Usa specialità, una posizione o una categoria)';

  @override
  String get keywordScoreAdviceSorry => 'Scuse';

  @override
  String get keywordScoreAdviceSorryTip => '(scusa, rammarico...)';

  @override
  String get keywordScoreAdviceTextLength => 'Numero minimo di caratteri:';

  @override
  String get kk => 'Kazako';

  @override
  String get km => 'Khmer';

  @override
  String get kn => 'Kannada';

  @override
  String get ko => 'Coreano';

  @override
  String get ku => 'Curdo (kurmanji)';

  @override
  String get ky => 'Kirghiso';

  @override
  String get la => 'Latino';

  @override
  String get landsquape => 'paesaggio';

  @override
  String get languages => 'Lingue';

  @override
  String get lastSevenDays => 'Ultimi 7 giorni';

  @override
  String get lastSixmonths => 'Ultimi 6 mesi';

  @override
  String get lastThirtyDays => '\n(Ultimi 30 giorni)';

  @override
  String get lastThirtyDays_date_range => 'Ultimi 30 giorni';

  @override
  String get lastThreeMonths => 'Ultimi 3 mesi';

  @override
  String get lastTwelveMonths => 'Ultimi 12 mesi';

  @override
  String get later => 'Dopo';

  @override
  String get lb => 'Lussemburghese';

  @override
  String get learnMore => 'Scopri di più';

  @override
  String get leave => 'Lascia?';

  @override
  String get leaveAndLoseModifications => 'Abbandona e perdi le modifiche';

  @override
  String get legalMentions => 'Menzioni legali';

  @override
  String get legalMentions1 => 'I- Disposizioni chiaramente stabilite a seguito del lancio di un\'applicazione.';

  @override
  String get legalMentions1Details => 'Il lancio di un\'applicazione su un mercato virtuale presuppone la regolarizzazione legale delle note legali da visualizzare per l\'utente, delle condizioni generali d\'uso.';

  @override
  String get legalMentionsA => 'A- Menzioni legali da rispettare.';

  @override
  String get legalMentionsADetails => 'Prima di considerare l\'idea di lanciare un\'applicazione sul mercato, alcune procedure legali devono essere rispettate: quindi, la loro preparazione deve essere effettuata prima di qualsiasi commercializzazione del prodotto. Questo è il caso innanzitutto delle note legali. Queste sono obbligatorie perché consentono all\'utente dell\'applicazione di avere i dettagli di contatto del publisher dell\'applicazione, ovvero il suo sviluppatore o la persona che sponsorizza lo sviluppo di un\'applicazione, attraverso uno standard aperto. Queste note legali devono quindi essere disponibili all\'utente attraverso la sua descrizione senza dover scaricare, a pagamento o gratuitamente, quest\'ultima. La mancata ottemperanza a questa obbligazione di note legali espone il publisher dell\'applicazione a una pena di un anno di reclusione e a una multa di 75.000 euro. Le informazioni da indicare sono le seguenti. In primo luogo, il publisher dell\'applicazione deve includere la propria denominazione sociale, sede legale, numero di telefono, numero di registrazione nel registro del commercio e delle società o registro delle imprese, l\'importo del capitale sociale, il nome del direttore e quello del co-direttore dell\'applicazione. Inoltre, le note legali devono includere il nome, la denominazione sociale o la ragione sociale, l\'indirizzo e il numero di telefono';

  @override
  String get legalMentionsTitle => 'Menzioni legali';

  @override
  String get legendLabel => 'Didascalia *';

  @override
  String get linkOfOffer => 'Link dell\'offerta';

  @override
  String get lo => 'Lao';

  @override
  String get loginPageSubtitle => 'Digitale al servizio della crescita degli esercizi';

  @override
  String get lt => 'Lituano';

  @override
  String get lv => 'Lettone';

  @override
  String get mainHashtag => 'Hashtag principale';

  @override
  String get malouGallery => 'Galleria Malou';

  @override
  String get manageNotifs => 'Gestisci le notifiche che ricevi tu stesso 🔔.';

  @override
  String get mandatoryField => 'Campo obbligatorio';

  @override
  String manyPlatformDisconnectedWarning(String platform) {
    return '$platform sono stati disconnessi';
  }

  @override
  String get markedAsRead => 'Conversazione segnata come letta';

  @override
  String get markedAsUnread => 'Conversazione segnata come non letta';

  @override
  String get markFeebackMessagesAsClosed => 'Segna conversazione come chiusa';

  @override
  String get mediasAccessDescription => 'Questo ti permetterà di inviare foto e video dal tuo telefono.';

  @override
  String get mediasTab => 'Media';

  @override
  String get mentionnedYouInStory => 'Ti ha menzionato in una storia';

  @override
  String get messageNotAvailable => 'Messaggio non disponibile';

  @override
  String get messages => 'Messaggi';

  @override
  String get messaging => 'Messaggistica';

  @override
  String get mg => 'Malgascio';

  @override
  String get mi => 'Maori';

  @override
  String get midnight => 'mezzanotte';

  @override
  String minOccurences(int minOccurences) {
    return '(minimo $minOccurences)';
  }

  @override
  String get mk => 'Macedone';

  @override
  String get ml => 'Malayalam';

  @override
  String get mn => 'Mongolo';

  @override
  String get monday => 'Lunedì';

  @override
  String get mr => 'Marathi';

  @override
  String get ms => 'Malese';

  @override
  String get mt => 'Maltese';

  @override
  String get my => 'Birmano (birmanie)';

  @override
  String get myFirstName => 'Il mio nome';

  @override
  String get myMedias => 'I miei Media';

  @override
  String get myName => 'Il mio nome';

  @override
  String get myTemplates => 'I miei modelli';

  @override
  String get name => 'nome';

  @override
  String nbReplies(Object nbReplies, Object nbReviews) {
    return 'Risposta ($nbReplies/$nbReviews)';
  }

  @override
  String get ne => 'Nepalese';

  @override
  String get needIdeas => 'Hai bisogno di idee?';

  @override
  String get newReview => 'Nuova recensione';

  @override
  String get nl => 'Olandese';

  @override
  String get no => 'Norvegese';

  @override
  String get no_results => 'Nessun risultato';

  @override
  String get noAccessToMedias => 'La “MalouApp” non ha accesso ai tuoi media';

  @override
  String get noAccountQuestion => 'Non hai un account?';

  @override
  String get noFeedbacksYet => 'Nessun feedback';

  @override
  String get noInternetConnection => 'Non sei connesso a Internet';

  @override
  String get noKeywordsYet => 'Non hai ancora parole chiave';

  @override
  String get noMediaInGallery => 'Non hai ancora nessun file multimediale nella tua galleria';

  @override
  String get noMessagesYet => 'Non hai ancora messaggi';

  @override
  String get noMoreItemsInTheList => 'Scorri verso l\'alto, non c\'è nient\'altro da vedere';

  @override
  String get noMoreScheduledPosts => 'Non hai più post pianificati';

  @override
  String get noon => 'mezzogiorno';

  @override
  String get nop => 'Non';

  @override
  String get noRestaurantForYourResearch => 'Nessun esercizio corrisponde alla tua ricerca.';

  @override
  String get noRestaurantQuestion => 'Beh, non hai un esercizio?';

  @override
  String get noResults => 'Nessun risultato';

  @override
  String get noReviews => 'Non hai recensioni dei clienti';

  @override
  String get noSocialNetworksConnected => 'Non hai ancora collegato Malouapp ai tuoi account dei social media... ma puoi già creare bozze!';

  @override
  String get noSocialPosts => 'Nessun post qui';

  @override
  String get noStories => 'Nessuna storia trovata... Non temere, vai avanti!';

  @override
  String get notAnsweredMessages => 'Messaggi senza risposta';

  @override
  String get notAnsweredReviews => 'Recensioni senza risposta';

  @override
  String get noTemplates => 'Non hai ancora modelli';

  @override
  String noTemplatesDetails(Object entity) {
    return 'Inizia aggiungendone alcuni su MalouApp per rispondere più velocemente alla tua $entity.';
  }

  @override
  String get notEnoughCreditToMakeAiApiCall => 'Non hai più abbastanza credito per effettuare questa azione.';

  @override
  String get notifCenter => 'Centro notifiche';

  @override
  String get notificationsDisabledDescription => 'Attivare le notifiche ti permetterà di essere avvisato/a quando hai un\'azione da compiere.';

  @override
  String get notificationsDisabledTitle => 'Notifiche disattivate';

  @override
  String get now => 'Ora';

  @override
  String numberOfHashtags(int hashtagsLength) {
    return 'Numero di hashtag: $hashtagsLength/10';
  }

  @override
  String get ny => 'Chichewa';

  @override
  String get object => 'Oggetto';

  @override
  String get onceADay => 'Una volta al giorno';

  @override
  String get onceADayWithParenthesis => ' (1 volta al giorno)';

  @override
  String get onlyAvailableInstagram => 'Disponibile solo su Instagram';

  @override
  String get onlyAvailableOnMapstr => 'Disponibile solo su Mapstr';

  @override
  String get onlyAvailableOnMeta => 'Disponibile solo su Facebook e Instagram';

  @override
  String get onlyForGoogleReviews => 'Riguarda solo le recensioni di Google, le altre piattaforme vengono recuperate solo una volta al giorno.';

  @override
  String get onlyVisibleByAdmin => 'Visibile solo dagli amministratori';

  @override
  String get onlyVisibleByAdmins => 'Visibile solo dagli amministratori';

  @override
  String open_day(String closeTime, String openDay, String openTime) {
    return 'aperto il $openDay dalle $openTime alle $closeTime, ';
  }

  @override
  String open_day_more(String closeTime2, String openTime2) {
    return 'e dalle $openTime2 alle $closeTime2, ';
  }

  @override
  String open_days(String closeTime, String endDay, String openTime, String startDay) {
    return 'aperto dal $startDay al $endDay dalle $openTime alle $closeTime, ';
  }

  @override
  String get optimize => 'Ottimizza';

  @override
  String get or => 'Odia';

  @override
  String get order => 'Ordine';

  @override
  String orderDetails(String amount, String orderCount) {
    return 'Ordine di $amount per la $orderCount volta';
  }

  @override
  String get oupsSeeNothing => 'Ops, non vediamo niente';

  @override
  String get overallExperience => 'Complessivo';

  @override
  String get pa => 'Punjabi';

  @override
  String get participate => 'Partecipa';

  @override
  String get password => 'Password';

  @override
  String get period => 'Periodo';

  @override
  String get pl => 'Polacco';

  @override
  String platformDisconnectedWarning(String platform) {
    return '$platform è stata disconnessa';
  }

  @override
  String get platformsDisconnected => 'Non hai piattaforme collegate';

  @override
  String get pleaseCreateHashtagsFromYourSettings => 'Crea almeno uno nelle impostazioni del tuo ristorante.';

  @override
  String get portrait => 'Ritratto';

  @override
  String get postCreated => 'Post creato con successo';

  @override
  String get postDeleted => 'Post eliminato con successo';

  @override
  String get postNotFound => 'Il post non può essere trovato';

  @override
  String get postOnPlatforms => 'Pubblica su piattaforme';

  @override
  String get posts => 'Post';

  @override
  String get postsSyncError => 'Errore di sincronizzazione';

  @override
  String get postsSyncSuccess => 'Sincronizzazione completata con successo';

  @override
  String get postSuccessfullyModified => 'Post modificata con successo';

  @override
  String get postsUpdated => 'Post aggiornati con successo';

  @override
  String get postTopic => 'Tipo di post';

  @override
  String get postTopicEvent => 'Evento';

  @override
  String get postTopicNew => 'Nuovo';

  @override
  String get postTopicOffer => 'Offerta';

  @override
  String get presentAPlace => 'Presenta un luogo ';

  @override
  String get price => 'prezzo';

  @override
  String get probablyDeleted => 'Probabilmente è stato cancellato';

  @override
  String get processing => 'Elaborazione in corso...';

  @override
  String get promoOrCoupon => 'Codice promozionale o buono';

  @override
  String get proposals => 'Propositions';

  @override
  String get ps => 'Pashto';

  @override
  String get pt => 'Portoghese';

  @override
  String get publicationDate => 'Data di pubblicazione';

  @override
  String get publicationError => 'Errore di pubblicazione';

  @override
  String get publish => 'Pubblica';

  @override
  String get published => 'Pubblicato';

  @override
  String get publishing => 'Post in corso...';

  @override
  String get purchaseAIPlus => 'Passa all\'opzione AI Plus per un accesso illimitato all\'IA';

  @override
  String get pursue => 'Continuare?';

  @override
  String get pursueEdit => 'Continua le modifiche';

  @override
  String get question => 'Hai domande?';

  @override
  String get questionsAnswers => 'Domande frequenti';

  @override
  String get read => 'Leggi';

  @override
  String receivedAMedia(String mediaType) {
    return 'Hai ricevuto un $mediaType';
  }

  @override
  String get receivedAStory => 'Hai ricevuto una storia';

  @override
  String get receivedReviews => 'Recensioni ricevute';

  @override
  String get recommandations => 'Raccomandazioni';

  @override
  String get recommandationsForUse => 'Istruzioni per l\'uso';

  @override
  String reconnectPlatform(String missingPlatform) {
    return 'Riconnetti $missingPlatform';
  }

  @override
  String get reconnectToInternet => 'Riconnettersi a una rete per accedere a questa pagina.';

  @override
  String get refreshed => 'Aggiornato!';

  @override
  String get refreshPage => 'Aggiorna la pagina';

  @override
  String remainingAICredits(int nbCredits) {
    return '$nbCredits crediti rimanenti';
  }

  @override
  String remainingAICreditsFormField(int nbCredits) {
    return '$nbCredits/500';
  }

  @override
  String remarksNumber(int count) {
    return '$count feedback';
  }

  @override
  String get reply => 'Rispondi';

  @override
  String get replyByMail => 'Rispondi via email';

  @override
  String get restaurantAddress => 'Indirizzo';

  @override
  String get restaurantMenu => 'Menu';

  @override
  String get restaurantName => 'Nome del ristorante';

  @override
  String get restaurantOpeningHours => 'Orari di apertura';

  @override
  String get restaurantPhone => 'Telefono';

  @override
  String get restaurantWebsite => 'Sito web';

  @override
  String get retry => 'Riprova';

  @override
  String get review => 'Recensioni';

  @override
  String get reviewAnalyseFailed => 'La recensione non ha potuto essere analizzata';

  @override
  String get reviewAnalysesRunning => 'L\'analisi è in corso';

  @override
  String get reviewAnalysesUnknownStatus => 'Stato sconosciuto per l\'analisi semantica';

  @override
  String get reviewAnalyzedError => 'Errore durante l\'analisi della recensione';

  @override
  String get reviewAnalyzedNoResult => 'Questa recensione non ha una categoria associata a un sentimento.';

  @override
  String get reviewAnalyzedTooOld => 'La recensione è troppo vecchia per essere analizzata.';

  @override
  String get reviewDoesNotExistAnymore => 'Sembra che questa recensione sia stata eliminata dall\'utente.';

  @override
  String get reviewFrom => 'Da...';

  @override
  String get reviewNotYetAnalyzed => 'Questa recensione non è ancora stata analizzata. Potrai vedere il risultato entro 24 ore.';

  @override
  String get reviewReplyError => 'Ops! La tua risposta non ha potuto essere inviata, riprova più tardi.';

  @override
  String get reviewReplySentSuccessfully => 'La tua risposta è stata inviata con successo! ';

  @override
  String get reviews => 'Recensioni dei clienti';

  @override
  String get reviewScores => 'Punteggi delle recensioni rilevanti';

  @override
  String get reviewsRating => 'Valutazione media delle recensioni';

  @override
  String get reviewsSyncError => 'Errore di sincronizzazione per le piattaforme: ';

  @override
  String get reviewsSyncSuccess => 'Sincronizzazione recensioni completata con successo';

  @override
  String get reviewTemplates => 'Modèles d\'avis';

  @override
  String get ro => 'Rumeno';

  @override
  String get ru => 'Russo';

  @override
  String get saturday => 'Sabato';

  @override
  String get savedLinks => 'Link salvati';

  @override
  String get saveDraft => 'Salva come bozza';

  @override
  String get saveModifications => 'Salva le modifiche';

  @override
  String get savePost => 'Salva post';

  @override
  String get schedule => 'Programma';

  @override
  String get scheduled => 'Pianificato';

  @override
  String get sd => 'Sindhi';

  @override
  String get search => 'Ricerca';

  @override
  String get searchPlace => 'Cerca un luogo';

  @override
  String get see => 'Vedi';

  @override
  String see_other_keywords(int noRevelantBricksLength) {
    return 'Vedi le mie altre parole chiave $noRevelantBricksLength';
  }

  @override
  String get seeFeed => 'Vedi il feed';

  @override
  String get seeList => 'Vedi la lista';

  @override
  String get seeListOfHashtags => 'Vedi la lista degli hashtag';

  @override
  String get seeMenu => 'Leggi il menu';

  @override
  String seeOriginalReviewText(String lang) {
    return 'Visualizza l\'originale (in $lang)';
  }

  @override
  String get semanticAnalyse => 'Analisi semantica';

  @override
  String get sendError => 'Errore nell\'invio.';

  @override
  String get sendPromotion => 'Inviare offerta';

  @override
  String sendPromotionOf(String amount) {
    return 'Inviare offerta de : $amount';
  }

  @override
  String sentAMedia(String mediaType) {
    return 'Hai inviato un $mediaType';
  }

  @override
  String get service => 'servizio';

  @override
  String get set_email => 'Inserisci la tua email';

  @override
  String get settings => 'Impostazioni';

  @override
  String get showInFeed => 'Mostra nel feed di Instagram';

  @override
  String get si => 'Singalese';

  @override
  String get signOut => 'Esci';

  @override
  String get signup => 'Registrati';

  @override
  String get sk => 'Slovacco';

  @override
  String get sl => 'Sloveno';

  @override
  String get sm => 'Samoano';

  @override
  String get sn => 'Shona';

  @override
  String get so => 'Somalo';

  @override
  String get socialNetworks => 'Social Media';

  @override
  String get somethingWentWrong => 'Ops, qualcosa è andato storto';

  @override
  String get sq => 'Albanese';

  @override
  String get square => 'Quadrato';

  @override
  String get sr => 'Serbo';

  @override
  String get st => 'Sesotho';

  @override
  String get stories => 'Storie';

  @override
  String get storyDeleted => 'Storia cancellata con successo';

  @override
  String get storyExpired => 'Storia scaduta';

  @override
  String get su => 'Sundanese';

  @override
  String get sunday => 'Domenica';

  @override
  String get sv => 'Svedese';

  @override
  String get sw => 'Swahili';

  @override
  String get ta => 'Tamil';

  @override
  String get tagAccounts => 'Tagga gli account';

  @override
  String get te => 'Telugu';

  @override
  String get tg => 'Tagiko';

  @override
  String get th => 'Tailandese';

  @override
  String get the => 'il';

  @override
  String get theEvent => 'l\'evento';

  @override
  String get theEventPostGenerationAI => 'l\'evento ';

  @override
  String get theOffer => 'l\'offerta';

  @override
  String get thursday => 'Giovedì';

  @override
  String get timeLimitReplyReviewExceeded => 'Hai superato la scadenza per rispondere alla recensione';

  @override
  String get titleLabel => 'Titolo *';

  @override
  String get tl => 'Filippino';

  @override
  String get to => 'a';

  @override
  String get toCheck => 'Per trattare';

  @override
  String get todo => 'Da fare';

  @override
  String get tooManyNotif => 'Troppe notifiche, ne vuoi meno?';

  @override
  String totem(String chipName) {
    return 'Totem $chipName';
  }

  @override
  String get totemDisplayName => 'Totem';

  @override
  String get tr => 'Turco';

  @override
  String get translate => 'Traduci';

  @override
  String get translated => 'Tradotto -';

  @override
  String translateInLang(String lang) {
    return 'Traduci in $lang';
  }

  @override
  String get try_another_search => 'Prova un\'altra ricerca';

  @override
  String get tryAgain => 'Riprova';

  @override
  String get tryAgainLater => 'Riprova più tardi';

  @override
  String get tuesday => 'Martedì';

  @override
  String get typeAndLocalisation => 'Tipo e posizione del tuo locale';

  @override
  String get typeAndLocalisationExample => '(es: ItalianoParigi14)';

  @override
  String get typeYourLang => 'Digita la tua lingua';

  @override
  String get typeYourText => 'Digita il tuo testo';

  @override
  String get ug => 'Uiguro';

  @override
  String get uk => 'Ucraino';

  @override
  String get unAnswered => 'Senza risposta';

  @override
  String get unansweredReviews => 'Recensioni senza risposta';

  @override
  String get unarchive => 'Disarchivia';

  @override
  String get unauthorized => 'Non sei autorizzato a eseguire questa azione.';

  @override
  String get unavailableMessage => 'Messaggio non disponibile';

  @override
  String get undetermined => 'Indeterminato';

  @override
  String get unknownError => 'Si è verificato un errore sconosciuto';

  @override
  String get unread => 'Non letto';

  @override
  String get unreadMessages => 'Conversazioni non lette';

  @override
  String get unsupportedAttachment => 'Il formato dell\'allegato non è supportato.';

  @override
  String get updateFeedbackMessageError => 'Errore durante l\'aggiornamento del feedback, riprova.';

  @override
  String get ur => 'Urdu';

  @override
  String get urgent => 'Urgente';

  @override
  String get useImageForPostGeneration => 'Usa l\'immagine per generare la didascalia';

  @override
  String get uz => 'Uzbeco';

  @override
  String get validate => 'Convalida';

  @override
  String get varietyOfHashtags => 'Varietà di hashtag';

  @override
  String get vi => 'Vietnamita';

  @override
  String get video => 'Video';

  @override
  String get videosForbiddenOnMapstr => 'I video non sono consentiti su Mapstr';

  @override
  String get videosInCarrouselForbiddenOnFacebook => 'Non puoi pubblicare un carosello contenente video su Facebook';

  @override
  String get videosOnGoogleForbiddenMessage => 'Non puoi pubblicare video su Google';

  @override
  String get videoTooShortMessage => 'Non puoi pubblicare video inferiori a 3 secondi';

  @override
  String get waitingForImageAnalysis => 'In attesa dell\'analisi dell\'immagine';

  @override
  String get we_are => 'Siamo ';

  @override
  String get wednesday => 'Mercoledì';

  @override
  String get whatsNew => 'Cosa c\'è di nuovo?';

  @override
  String get writeAnswerReview => 'Scrivi la tua risposta';

  @override
  String get writeLegendHere => 'Scrivi la tua didascalia qui o ';

  @override
  String get writeMessage => 'Scrivi un messaggio';

  @override
  String get writePostWithAi => 'Scrivi un post con l\'IA';

  @override
  String get writeTitleHere => 'Scrivi il titolo qui...';

  @override
  String get writeYourFeedback => 'Scrivi un feedback';

  @override
  String get xh => 'Xhosa';

  @override
  String get year => 'Ultimi 12 mesi';

  @override
  String get yi => 'Yiddish';

  @override
  String get yo => 'Yoruba';

  @override
  String get youAnsweredAStory => 'Hai risposto a una storia';

  @override
  String get youCanSelectOnlyOne => 'Puoi selezionare solo un\'immagine';

  @override
  String get youCanSelectOnlyOnevideo => 'Puoi aggiungere solo un video a un Reel';

  @override
  String get youCanSelectVideosOnly => 'Puoi selezionare solo video';

  @override
  String get youMentionnedInStory => 'Sei stato menzionato nella tua storia';

  @override
  String get your_businesses => 'I tuoi locali';

  @override
  String get youreDisconnectedFromInternet => 'Sembra che tu sia disconnesso da Internet.';

  @override
  String get yourHashtags => 'Gli hashtag';

  @override
  String get youWillLoseDraft => 'Se esci, perderai questa bozza di post.';

  @override
  String get youWillLoseYourModifications => 'Perderai le tue modifiche.';

  @override
  String get zu => 'Zulù';
}
