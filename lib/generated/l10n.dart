import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'l10n_en.dart';
import 'l10n_es.dart';
import 'l10n_fr.dart';
import 'l10n_it.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of Translation
/// returned by `Translation.of(context)`.
///
/// Applications need to include `Translation.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'generated/l10n.dart';
///
/// return MaterialApp(
///   localizationsDelegates: Translation.localizationsDelegates,
///   supportedLocales: Translation.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the Translation.supportedLocales
/// property.
abstract class Translation {
  Translation(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static Translation of(BuildContext context) {
    return Localizations.of<Translation>(context, Translation)!;
  }

  static const LocalizationsDelegate<Translation> delegate = _TranslationDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('es'),
    Locale('fr'),
    Locale('it')
  ];

  /// No description provided for @aboutToDeleteMessage.
  ///
  /// In fr, this message translates to:
  /// **'Vous êtes sur le point de supprimer ce message.'**
  String get aboutToDeleteMessage;

  /// No description provided for @aboutToSignOut.
  ///
  /// In fr, this message translates to:
  /// **'Vous êtes sur le point de vous déconnecter'**
  String get aboutToSignOut;

  /// No description provided for @activate.
  ///
  /// In fr, this message translates to:
  /// **'Activer'**
  String get activate;

  /// No description provided for @actives.
  ///
  /// In fr, this message translates to:
  /// **'Actives'**
  String get actives;

  /// No description provided for @add.
  ///
  /// In fr, this message translates to:
  /// **'Ajouter'**
  String get add;

  /// No description provided for @addARestaurant.
  ///
  /// In fr, this message translates to:
  /// **'Ajouter un établissement'**
  String get addARestaurant;

  /// No description provided for @addCoupon.
  ///
  /// In fr, this message translates to:
  /// **'Un code promo ou un coupon'**
  String get addCoupon;

  /// No description provided for @addDraft.
  ///
  /// In fr, this message translates to:
  /// **'Créer un brouillon'**
  String get addDraft;

  /// No description provided for @addFIrstPost.
  ///
  /// In fr, this message translates to:
  /// **'Créer mon premier post'**
  String get addFIrstPost;

  /// No description provided for @addHashtags.
  ///
  /// In fr, this message translates to:
  /// **'Ajouter des hashtags'**
  String get addHashtags;

  /// No description provided for @addImageToUse.
  ///
  /// In fr, this message translates to:
  /// **'Ajouter une image pour utiliser cette fonctionnalité'**
  String get addImageToUse;

  /// No description provided for @addInfosAboutOffer.
  ///
  /// In fr, this message translates to:
  /// **'Ajouter des informations sur l\'offre'**
  String get addInfosAboutOffer;

  /// No description provided for @addLinkToOffer.
  ///
  /// In fr, this message translates to:
  /// **'Un lien pour profiter de l\'offre'**
  String get addLinkToOffer;

  /// No description provided for @addLocation.
  ///
  /// In fr, this message translates to:
  /// **'Ajouter un lieu'**
  String get addLocation;

  /// No description provided for @addNow.
  ///
  /// In fr, this message translates to:
  /// **'Ajouter maintenant'**
  String get addNow;

  /// No description provided for @addSeoPost.
  ///
  /// In fr, this message translates to:
  /// **'Post Google'**
  String get addSeoPost;

  /// No description provided for @addSocialPost.
  ///
  /// In fr, this message translates to:
  /// **'Post RS'**
  String get addSocialPost;

  /// No description provided for @addTitle.
  ///
  /// In fr, this message translates to:
  /// **'Ajouter un titre'**
  String get addTitle;

  /// No description provided for @addYourFirstRestaurant.
  ///
  /// In fr, this message translates to:
  /// **'Ajoutez votre premier établissement pour\nprofiter pleinement de la MalouApp'**
  String get addYourFirstRestaurant;

  /// No description provided for @aDish.
  ///
  /// In fr, this message translates to:
  /// **'Notre nouveau menu avec '**
  String get aDish;

  /// No description provided for @advice.
  ///
  /// In fr, this message translates to:
  /// **'d\'un conseil '**
  String get advice;

  /// No description provided for @af.
  ///
  /// In fr, this message translates to:
  /// **'Afrikaans'**
  String get af;

  /// No description provided for @aIcreditsBackSoon.
  ///
  /// In fr, this message translates to:
  /// **'Les propositions de réponses seront à nouveau disponibles lors du renouvellement de vos crédits.'**
  String get aIcreditsBackSoon;

  /// No description provided for @aiPostCaptionFormfieldHint.
  ///
  /// In fr, this message translates to:
  /// **'notre moelleux au chocolat parfait pour finir son repas'**
  String get aiPostCaptionFormfieldHint;

  /// No description provided for @aiPostCaptionFormfieldLabel.
  ///
  /// In fr, this message translates to:
  /// **'Générer un post à propos de...'**
  String get aiPostCaptionFormfieldLabel;

  /// No description provided for @all.
  ///
  /// In fr, this message translates to:
  /// **'Tous'**
  String get all;

  /// No description provided for @am.
  ///
  /// In fr, this message translates to:
  /// **'Amharique'**
  String get am;

  /// No description provided for @analyzeReviewsNotAvailable.
  ///
  /// In fr, this message translates to:
  /// **'L’analyse sémantique n’est pas disponible pour cet établissement.'**
  String get analyzeReviewsNotAvailable;

  /// No description provided for @and.
  ///
  /// In fr, this message translates to:
  /// **' et'**
  String get and;

  /// No description provided for @anErrorOccured.
  ///
  /// In fr, this message translates to:
  /// **'Une erreur est survenue'**
  String get anErrorOccured;

  /// No description provided for @announceAnEvent.
  ///
  /// In fr, this message translates to:
  /// **'Un événement à venir avec'**
  String get announceAnEvent;

  /// No description provided for @answeredYourStory.
  ///
  /// In fr, this message translates to:
  /// **'A répondu à votre story'**
  String get answeredYourStory;

  /// No description provided for @aPlace.
  ///
  /// In fr, this message translates to:
  /// **'d\'un lieu'**
  String get aPlace;

  /// No description provided for @ar.
  ///
  /// In fr, this message translates to:
  /// **'Arabe'**
  String get ar;

  /// No description provided for @archive.
  ///
  /// In fr, this message translates to:
  /// **'Archiver'**
  String get archive;

  /// No description provided for @archived.
  ///
  /// In fr, this message translates to:
  /// **'Archivés'**
  String get archived;

  /// No description provided for @areYouSureYouWantToDeleteThisPost.
  ///
  /// In fr, this message translates to:
  /// **'Voulez-vous supprimer ce post ?'**
  String get areYouSureYouWantToDeleteThisPost;

  /// No description provided for @areYouSureYouWantToDeleteThisPostPublished.
  ///
  /// In fr, this message translates to:
  /// **'Voulez-vous supprimer ce post ?\n Il sera également supprimé des plateformes sur lesquelles il a été posté.'**
  String get areYouSureYouWantToDeleteThisPostPublished;

  /// No description provided for @areYouSureYouWantToDeleteThisStory.
  ///
  /// In fr, this message translates to:
  /// **'Voulez vous supprimer cette story ?'**
  String get areYouSureYouWantToDeleteThisStory;

  /// No description provided for @artificialIntelligence.
  ///
  /// In fr, this message translates to:
  /// **'Intelligence Artificielle'**
  String get artificialIntelligence;

  /// No description provided for @askForDemo.
  ///
  /// In fr, this message translates to:
  /// **'Demander une démo'**
  String get askForDemo;

  /// No description provided for @atmosphere.
  ///
  /// In fr, this message translates to:
  /// **'ambiance'**
  String get atmosphere;

  /// No description provided for @authorizeCamera.
  ///
  /// In fr, this message translates to:
  /// **'Autoriser l\'accès à l\'appareil photo'**
  String get authorizeCamera;

  /// No description provided for @authorizeMedias.
  ///
  /// In fr, this message translates to:
  /// **'Autoriser l\'accès à mes médias'**
  String get authorizeMedias;

  /// No description provided for @autoReply.
  ///
  /// In fr, this message translates to:
  /// **'Réponse automatique'**
  String get autoReply;

  /// No description provided for @az.
  ///
  /// In fr, this message translates to:
  /// **'Azéri'**
  String get az;

  /// No description provided for @be.
  ///
  /// In fr, this message translates to:
  /// **'Biélorusse'**
  String get be;

  /// No description provided for @bg.
  ///
  /// In fr, this message translates to:
  /// **'Bulgare'**
  String get bg;

  /// No description provided for @bn.
  ///
  /// In fr, this message translates to:
  /// **'Bengali'**
  String get bn;

  /// No description provided for @book.
  ///
  /// In fr, this message translates to:
  /// **'Réserver'**
  String get book;

  /// No description provided for @brandAccount.
  ///
  /// In fr, this message translates to:
  /// **'Compte marque'**
  String get brandAccount;

  /// No description provided for @bs.
  ///
  /// In fr, this message translates to:
  /// **'Bosniaque'**
  String get bs;

  /// No description provided for @businessName.
  ///
  /// In fr, this message translates to:
  /// **'Nom de l\'établissement'**
  String get businessName;

  /// No description provided for @buy.
  ///
  /// In fr, this message translates to:
  /// **'Acheter'**
  String get buy;

  /// No description provided for @byDefault.
  ///
  /// In fr, this message translates to:
  /// **' (Par défaut)'**
  String get byDefault;

  /// No description provided for @ca.
  ///
  /// In fr, this message translates to:
  /// **'Catalan'**
  String get ca;

  /// No description provided for @call.
  ///
  /// In fr, this message translates to:
  /// **'Appeler'**
  String get call;

  /// No description provided for @cameraNoAccessDescription.
  ///
  /// In fr, this message translates to:
  /// **'Ceci vous permettra d’envoyer des photos depuis votre appareil photo.'**
  String get cameraNoAccessDescription;

  /// No description provided for @cameraNoAccessTitle.
  ///
  /// In fr, this message translates to:
  /// **'La “MalouApp” n’a pas accès à votre appareil photo'**
  String get cameraNoAccessTitle;

  /// No description provided for @cancel.
  ///
  /// In fr, this message translates to:
  /// **'Annuler'**
  String get cancel;

  /// No description provided for @cancelEdit.
  ///
  /// In fr, this message translates to:
  /// **'Annuler les modifications'**
  String get cancelEdit;

  /// No description provided for @cannotFindYourLang.
  ///
  /// In fr, this message translates to:
  /// **'Vous ne trouvez pas votre langue ?'**
  String get cannotFindYourLang;

  /// No description provided for @cant_edit_medias_post_published.
  ///
  /// In fr, this message translates to:
  /// **'Vous ne pouvez pas modifier les médias d\'un post déjà publié'**
  String get cant_edit_medias_post_published;

  /// No description provided for @cantAnalyzeImage.
  ///
  /// In fr, this message translates to:
  /// **'Impossible d\'analyser l\'image'**
  String get cantAnalyzeImage;

  /// No description provided for @cantAnalyzeVideos.
  ///
  /// In fr, this message translates to:
  /// **'Impossible d\'analyser les vidéos'**
  String get cantAnalyzeVideos;

  /// No description provided for @cantEditMediaPublished.
  ///
  /// In fr, this message translates to:
  /// **'Vous ne pouvez pas modifier les médias d\'un post déjà publié'**
  String get cantEditMediaPublished;

  /// No description provided for @carrouselForbiddenOnGooGle.
  ///
  /// In fr, this message translates to:
  /// **'Vous ne pouvez pas publier de carrousel sur Google'**
  String get carrouselForbiddenOnGooGle;

  /// No description provided for @carrouselForbiddenOnGoogleMessage.
  ///
  /// In fr, this message translates to:
  /// **'Vous ne pouvez pas publier de carrousel sur Google'**
  String get carrouselForbiddenOnGoogleMessage;

  /// No description provided for @carrouselMediasLimitReachedMessage.
  ///
  /// In fr, this message translates to:
  /// **'Vous ne pouvez pas séléctionner plus de 10 médias'**
  String get carrouselMediasLimitReachedMessage;

  /// No description provided for @ceb.
  ///
  /// In fr, this message translates to:
  /// **'Cebuano'**
  String get ceb;

  /// No description provided for @changeLanguage.
  ///
  /// In fr, this message translates to:
  /// **'Changer la langue'**
  String get changeLanguage;

  /// No description provided for @chooseActionButton.
  ///
  /// In fr, this message translates to:
  /// **'Choisir un bouton d\'action'**
  String get chooseActionButton;

  /// No description provided for @clientName.
  ///
  /// In fr, this message translates to:
  /// **'Nom du client'**
  String get clientName;

  /// No description provided for @closed_day.
  ///
  /// In fr, this message translates to:
  /// **'fermés le {closedDay}, '**
  String closed_day(String closedDay);

  /// No description provided for @closed_days.
  ///
  /// In fr, this message translates to:
  /// **'fermés du {startDay} au {endDay}, '**
  String closed_days(String endDay, String startDay);

  /// No description provided for @co.
  ///
  /// In fr, this message translates to:
  /// **'Corse'**
  String get co;

  /// No description provided for @commentsTab.
  ///
  /// In fr, this message translates to:
  /// **'Remarques'**
  String get commentsTab;

  /// No description provided for @completeCaptionToGenerateHashtagsFromAi.
  ///
  /// In fr, this message translates to:
  /// **'Completez la légende de votre post pour que l\'IA puisse générer des hashtags'**
  String get completeCaptionToGenerateHashtagsFromAi;

  /// No description provided for @concernedRatings.
  ///
  /// In fr, this message translates to:
  /// **'Notes des avis concernés'**
  String get concernedRatings;

  /// No description provided for @concernsOnlyNegativeReviews.
  ///
  /// In fr, this message translates to:
  /// **'Concerne les avis négatifs sans réponse depuis plus de 48h'**
  String get concernsOnlyNegativeReviews;

  /// No description provided for @conditionOffer.
  ///
  /// In fr, this message translates to:
  /// **'Conditions de l\'offre'**
  String get conditionOffer;

  /// No description provided for @conditionsOfOffer.
  ///
  /// In fr, this message translates to:
  /// **'Les conditions de l\'offre'**
  String get conditionsOfOffer;

  /// No description provided for @confirm.
  ///
  /// In fr, this message translates to:
  /// **'Confirmer'**
  String get confirm;

  /// No description provided for @connectGoogle.
  ///
  /// In fr, this message translates to:
  /// **'Connecter mon compte Google'**
  String get connectGoogle;

  /// No description provided for @connectMyPlatforms.
  ///
  /// In fr, this message translates to:
  /// **'Connecter mes plateformes'**
  String get connectMyPlatforms;

  /// No description provided for @connectSocialNetworks.
  ///
  /// In fr, this message translates to:
  /// **'Connecter mes réseaux sociaux'**
  String get connectSocialNetworks;

  /// No description provided for @contactMalouOps.
  ///
  /// In fr, this message translates to:
  /// **'Contactez votre référent(e) Malou si vous ne trouvez pas votre bonheur dans notre FAQ 🤗.'**
  String get contactMalouOps;

  /// No description provided for @conversation.
  ///
  /// In fr, this message translates to:
  /// **'Messagerie'**
  String get conversation;

  /// No description provided for @conversationArchived.
  ///
  /// In fr, this message translates to:
  /// **'Conversation archivée'**
  String get conversationArchived;

  /// No description provided for @conversationUnarchived.
  ///
  /// In fr, this message translates to:
  /// **'Conversation désarchivée'**
  String get conversationUnarchived;

  /// No description provided for @courier_to_restaurant.
  ///
  /// In fr, this message translates to:
  /// **'Livreur'**
  String get courier_to_restaurant;

  /// No description provided for @cover.
  ///
  /// In fr, this message translates to:
  /// **'Couverture'**
  String get cover;

  /// No description provided for @create_reel.
  ///
  /// In fr, this message translates to:
  /// **'Créer un Reel'**
  String get create_reel;

  /// No description provided for @createCompletionError.
  ///
  /// In fr, this message translates to:
  /// **'Une erreur est survenue lors de la génération IA, veuillez réessayer.'**
  String get createCompletionError;

  /// No description provided for @createDraft.
  ///
  /// In fr, this message translates to:
  /// **'Créer un brouillon'**
  String get createDraft;

  /// No description provided for @cs.
  ///
  /// In fr, this message translates to:
  /// **'Tchèque'**
  String get cs;

  /// No description provided for @customer_to_restaurant.
  ///
  /// In fr, this message translates to:
  /// **'Client'**
  String get customer_to_restaurant;

  /// No description provided for @customerReviews.
  ///
  /// In fr, this message translates to:
  /// **'Avis clients'**
  String get customerReviews;

  /// No description provided for @customized.
  ///
  /// In fr, this message translates to:
  /// **'Personnalisé '**
  String get customized;

  /// No description provided for @cy.
  ///
  /// In fr, this message translates to:
  /// **'Gallois'**
  String get cy;

  /// No description provided for @da.
  ///
  /// In fr, this message translates to:
  /// **'Danois'**
  String get da;

  /// No description provided for @dailyUnansweredReviewsNotificationText.
  ///
  /// In fr, this message translates to:
  /// **'⭐️ Psst, {count, plural, one {1 nouvel avis reçu} other {{count} nouveaux avis reçus}}'**
  String dailyUnansweredReviewsNotificationText(int count);

  /// No description provided for @dailyUnreadMessagesNotificationText.
  ///
  /// In fr, this message translates to:
  /// **'💬 Psst, {count, plural, one {1 nouveau message} other {{count} nouveaux messages}}'**
  String dailyUnreadMessagesNotificationText(int count);

  /// No description provided for @date.
  ///
  /// In fr, this message translates to:
  /// **'Date'**
  String get date;

  /// No description provided for @dateOf.
  ///
  /// In fr, this message translates to:
  /// **'Date de'**
  String get dateOf;

  /// No description provided for @daysLeftToReply.
  ///
  /// In fr, this message translates to:
  /// **' jours restants pour répondre'**
  String get daysLeftToReply;

  /// No description provided for @de.
  ///
  /// In fr, this message translates to:
  /// **'Allemand'**
  String get de;

  /// No description provided for @delete.
  ///
  /// In fr, this message translates to:
  /// **'Supprimer'**
  String get delete;

  /// No description provided for @deleteFeedbackMessageError.
  ///
  /// In fr, this message translates to:
  /// **'Erreur lors de la suppression du message'**
  String get deleteFeedbackMessageError;

  /// No description provided for @describeDish.
  ///
  /// In fr, this message translates to:
  /// **'Notre nouveau menu avec '**
  String get describeDish;

  /// No description provided for @describeGiveaway.
  ///
  /// In fr, this message translates to:
  /// **'Un événement à venir avec '**
  String get describeGiveaway;

  /// No description provided for @descriptionTab.
  ///
  /// In fr, this message translates to:
  /// **'Description'**
  String get descriptionTab;

  /// No description provided for @displayAnalyzeReviews.
  ///
  /// In fr, this message translates to:
  /// **'Afficher l\'analyse sémantique'**
  String get displayAnalyzeReviews;

  /// No description provided for @displayOnlyNeverPosted.
  ///
  /// In fr, this message translates to:
  /// **'Afficher uniquement les médias jamais postés'**
  String get displayOnlyNeverPosted;

  /// No description provided for @dontForgetToAnswer.
  ///
  /// In fr, this message translates to:
  /// **'N’oubliez pas d’y répondre depuis la Malouapp.'**
  String get dontForgetToAnswer;

  /// No description provided for @dontLetYourCustomersWithoutAnswer.
  ///
  /// In fr, this message translates to:
  /// **'Ne laissez pas vos clients sans réponse, rendez-vous sur la Malouapp.'**
  String get dontLetYourCustomersWithoutAnswer;

  /// No description provided for @draft.
  ///
  /// In fr, this message translates to:
  /// **'Brouillon'**
  String get draft;

  /// No description provided for @drafts.
  ///
  /// In fr, this message translates to:
  /// **'Brouillons'**
  String get drafts;

  /// No description provided for @duplicate.
  ///
  /// In fr, this message translates to:
  /// **'Dupliquer'**
  String get duplicate;

  /// No description provided for @eachTime.
  ///
  /// In fr, this message translates to:
  /// **'A chaque fois'**
  String get eachTime;

  /// No description provided for @edit.
  ///
  /// In fr, this message translates to:
  /// **'Modifier'**
  String get edit;

  /// No description provided for @editWithAI.
  ///
  /// In fr, this message translates to:
  /// **'Modifier avec l\'IA'**
  String get editWithAI;

  /// No description provided for @el.
  ///
  /// In fr, this message translates to:
  /// **'Grec'**
  String get el;

  /// No description provided for @email.
  ///
  /// In fr, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @emailSelected.
  ///
  /// In fr, this message translates to:
  /// **'Mail d\'envoi'**
  String get emailSelected;

  /// No description provided for @emptyDeviceMedias.
  ///
  /// In fr, this message translates to:
  /// **'Vous n\'avez pas encore de médias dans votre galerie'**
  String get emptyDeviceMedias;

  /// No description provided for @emptyGalleryMedias.
  ///
  /// In fr, this message translates to:
  /// **'Vous n\'avez pas encore de médias dans votre galerie'**
  String get emptyGalleryMedias;

  /// No description provided for @emptyPosts.
  ///
  /// In fr, this message translates to:
  /// **'Pas de post référencé par ici'**
  String get emptyPosts;

  /// No description provided for @emptySocialPosts.
  ///
  /// In fr, this message translates to:
  /// **'Vous n’avez pas connecté la Malouapp à vos réseaux sociaux... mais vous pouvez déjà créer des brouillons'**
  String get emptySocialPosts;

  /// No description provided for @en.
  ///
  /// In fr, this message translates to:
  /// **'Anglais'**
  String get en;

  /// No description provided for @enableNotifications.
  ///
  /// In fr, this message translates to:
  /// **'Activer les notifications'**
  String get enableNotifications;

  /// No description provided for @ended.
  ///
  /// In fr, this message translates to:
  /// **'Terminées'**
  String get ended;

  /// No description provided for @enterPassword.
  ///
  /// In fr, this message translates to:
  /// **'Saisir votre mot de passe'**
  String get enterPassword;

  /// No description provided for @eo.
  ///
  /// In fr, this message translates to:
  /// **'Espéranto'**
  String get eo;

  /// No description provided for @errorCreatingPost.
  ///
  /// In fr, this message translates to:
  /// **'Une erreur est survenue lors de la création du post'**
  String get errorCreatingPost;

  /// No description provided for @errorPublishing.
  ///
  /// In fr, this message translates to:
  /// **'Erreur lors de la publication'**
  String get errorPublishing;

  /// No description provided for @errorRequired.
  ///
  /// In fr, this message translates to:
  /// **'Champ requis'**
  String get errorRequired;

  /// No description provided for @es.
  ///
  /// In fr, this message translates to:
  /// **'Espagnol'**
  String get es;

  /// No description provided for @et.
  ///
  /// In fr, this message translates to:
  /// **'Estonien'**
  String get et;

  /// No description provided for @eu.
  ///
  /// In fr, this message translates to:
  /// **'Basque'**
  String get eu;

  /// No description provided for @expeditiousness.
  ///
  /// In fr, this message translates to:
  /// **'attente'**
  String get expeditiousness;

  /// No description provided for @fa.
  ///
  /// In fr, this message translates to:
  /// **'Persan'**
  String get fa;

  /// No description provided for @favorite.
  ///
  /// In fr, this message translates to:
  /// **'Favoris'**
  String get favorite;

  /// No description provided for @feedbackMarkAsClosed.
  ///
  /// In fr, this message translates to:
  /// **'{name} a marqué la conversation comme traitée'**
  String feedbackMarkAsClosed(String name);

  /// No description provided for @feedbackMarkAsReopen.
  ///
  /// In fr, this message translates to:
  /// **'{name} a réouvert la conversation'**
  String feedbackMarkAsReopen(String name);

  /// No description provided for @feedbacks.
  ///
  /// In fr, this message translates to:
  /// **'Remarques'**
  String get feedbacks;

  /// No description provided for @feedbackYouTag.
  ///
  /// In fr, this message translates to:
  /// **'(Vous)'**
  String get feedbackYouTag;

  /// No description provided for @fi.
  ///
  /// In fr, this message translates to:
  /// **'Finnois'**
  String get fi;

  /// No description provided for @filter.
  ///
  /// In fr, this message translates to:
  /// **'Filtres'**
  String get filter;

  /// No description provided for @food.
  ///
  /// In fr, this message translates to:
  /// **'cuisine'**
  String get food;

  /// No description provided for @forgot_password_explanation.
  ///
  /// In fr, this message translates to:
  /// **'Nous vous enverrons un lien pour réinitialiser votre mot de passe'**
  String get forgot_password_explanation;

  /// No description provided for @forgot_password_has_been_sent.
  ///
  /// In fr, this message translates to:
  /// **'A l’adresse indiquée : \n {email}'**
  String forgot_password_has_been_sent(String email);

  /// No description provided for @forgot_password_sent.
  ///
  /// In fr, this message translates to:
  /// **'Un mail de réinitialisation vous a été envoyé'**
  String get forgot_password_sent;

  /// No description provided for @forgotPassword.
  ///
  /// In fr, this message translates to:
  /// **'Mot de passe oublié ?'**
  String get forgotPassword;

  /// No description provided for @fr.
  ///
  /// In fr, this message translates to:
  /// **'Français'**
  String get fr;

  /// No description provided for @frequency.
  ///
  /// In fr, this message translates to:
  /// **'Fréquence'**
  String get frequency;

  /// No description provided for @friday.
  ///
  /// In fr, this message translates to:
  /// **'Vendredi'**
  String get friday;

  /// No description provided for @from.
  ///
  /// In fr, this message translates to:
  /// **'Du'**
  String get from;

  /// No description provided for @fy.
  ///
  /// In fr, this message translates to:
  /// **'Frison'**
  String get fy;

  /// No description provided for @ga.
  ///
  /// In fr, this message translates to:
  /// **'Irlandais'**
  String get ga;

  /// No description provided for @gd.
  ///
  /// In fr, this message translates to:
  /// **'Gaélique écossais'**
  String get gd;

  /// No description provided for @general.
  ///
  /// In fr, this message translates to:
  /// **'Général'**
  String get general;

  /// No description provided for @generate.
  ///
  /// In fr, this message translates to:
  /// **'Générer'**
  String get generate;

  /// No description provided for @generateListOfHashtags.
  ///
  /// In fr, this message translates to:
  /// **'Générer une liste de hashtags'**
  String get generateListOfHashtags;

  /// No description provided for @generatePostPrefixText.
  ///
  /// In fr, this message translates to:
  /// **'Générer un post à propos '**
  String get generatePostPrefixText;

  /// No description provided for @generateReviewReply.
  ///
  /// In fr, this message translates to:
  /// **'Générer une réponse à un avis'**
  String get generateReviewReply;

  /// No description provided for @generateReviewReplyPlaceholder.
  ///
  /// In fr, this message translates to:
  /// **'Bonjour, merci pour votre note ... '**
  String get generateReviewReplyPlaceholder;

  /// No description provided for @giveAdvice.
  ///
  /// In fr, this message translates to:
  /// **'Donner un conseil'**
  String get giveAdvice;

  /// No description provided for @giveaway.
  ///
  /// In fr, this message translates to:
  /// **'Un jeu concours pendant un mois pour les abonnés qui likent le post et taguent un ami en commentaire, avec pour cadeau '**
  String get giveaway;

  /// No description provided for @gl.
  ///
  /// In fr, this message translates to:
  /// **'Galicien'**
  String get gl;

  /// No description provided for @gmbMessagesNotAvailable.
  ///
  /// In fr, this message translates to:
  /// **'Google a désactivé définitivement sa fonctionnalité de messagerie, vous ne pouvez plus envoyer de message dans cette conversation'**
  String get gmbMessagesNotAvailable;

  /// No description provided for @google.
  ///
  /// In fr, this message translates to:
  /// **'Google'**
  String get google;

  /// No description provided for @googleConnectedButNoPosts.
  ///
  /// In fr, this message translates to:
  /// **'Vous n’avez pas connecté la Malouapp à votre compte Google... mais vous pouvez déjà créer des brouillons !'**
  String get googleConnectedButNoPosts;

  /// No description provided for @gu.
  ///
  /// In fr, this message translates to:
  /// **'Gujarati'**
  String get gu;

  /// No description provided for @ha.
  ///
  /// In fr, this message translates to:
  /// **'Haoussa'**
  String get ha;

  /// No description provided for @hashtagAdvice1.
  ///
  /// In fr, this message translates to:
  /// **'Entre 3 et 6 hashtags'**
  String get hashtagAdvice1;

  /// No description provided for @hashtagAdvice2.
  ///
  /// In fr, this message translates to:
  /// **'3 catégories ou +\nVariez les hashtags d\'un post à l\'autre'**
  String get hashtagAdvice2;

  /// No description provided for @hashtags.
  ///
  /// In fr, this message translates to:
  /// **'Hashtags'**
  String get hashtags;

  /// No description provided for @hashtagsCategories.
  ///
  /// In fr, this message translates to:
  /// **'Catégories de hashtags'**
  String get hashtagsCategories;

  /// No description provided for @hashtagsDisplay.
  ///
  /// In fr, this message translates to:
  /// **'Affichage des hashtags'**
  String get hashtagsDisplay;

  /// No description provided for @hashtagsRequiredForAI.
  ///
  /// In fr, this message translates to:
  /// **'Vous n\'avez pas de liste de hashtags.'**
  String get hashtagsRequiredForAI;

  /// No description provided for @haw.
  ///
  /// In fr, this message translates to:
  /// **'Hawaïen'**
  String get haw;

  /// No description provided for @he.
  ///
  /// In fr, this message translates to:
  /// **'Hébreu'**
  String get he;

  /// No description provided for @hi.
  ///
  /// In fr, this message translates to:
  /// **'Hindi'**
  String get hi;

  /// No description provided for @hide_my_keywords.
  ///
  /// In fr, this message translates to:
  /// **'Cacher mes autres mots-clés'**
  String get hide_my_keywords;

  /// No description provided for @hmn.
  ///
  /// In fr, this message translates to:
  /// **'Hmong'**
  String get hmn;

  /// No description provided for @home.
  ///
  /// In fr, this message translates to:
  /// **'Accueil'**
  String get home;

  /// No description provided for @hour.
  ///
  /// In fr, this message translates to:
  /// **'Heure'**
  String get hour;

  /// No description provided for @hr.
  ///
  /// In fr, this message translates to:
  /// **'Croate'**
  String get hr;

  /// No description provided for @ht.
  ///
  /// In fr, this message translates to:
  /// **'Créole haïtien'**
  String get ht;

  /// No description provided for @hu.
  ///
  /// In fr, this message translates to:
  /// **'Hongrois'**
  String get hu;

  /// No description provided for @hy.
  ///
  /// In fr, this message translates to:
  /// **'Arménien'**
  String get hy;

  /// No description provided for @hygiene.
  ///
  /// In fr, this message translates to:
  /// **'hygiène'**
  String get hygiene;

  /// No description provided for @ia.
  ///
  /// In fr, this message translates to:
  /// **'(IA)'**
  String get ia;

  /// No description provided for @iaGenerationExampleHintText.
  ///
  /// In fr, this message translates to:
  /// **'Exemple : Générer un post à propos de notre nouveau plat de burrata à a pistache avec un ton formel'**
  String get iaGenerationExampleHintText;

  /// No description provided for @id.
  ///
  /// In fr, this message translates to:
  /// **'Indonésien'**
  String get id;

  /// No description provided for @ig.
  ///
  /// In fr, this message translates to:
  /// **'Igbo'**
  String get ig;

  /// No description provided for @includeHashtagOfTheRestaurant.
  ///
  /// In fr, this message translates to:
  /// **'Inclure le hashtag de l\'établissement'**
  String get includeHashtagOfTheRestaurant;

  /// No description provided for @includeReviewsWithAutoAnswer.
  ///
  /// In fr, this message translates to:
  /// **'Inclure les avis avec réponse auto.'**
  String get includeReviewsWithAutoAnswer;

  /// No description provided for @inFirstComment.
  ///
  /// In fr, this message translates to:
  /// **'En premier commentaire'**
  String get inFirstComment;

  /// No description provided for @inLegend.
  ///
  /// In fr, this message translates to:
  /// **'Dans la légende'**
  String get inLegend;

  /// No description provided for @invalidCredentials.
  ///
  /// In fr, this message translates to:
  /// **'Veuillez vérifier vos identifiants\nde connexion'**
  String get invalidCredentials;

  /// No description provided for @invalidEmailMessage.
  ///
  /// In fr, this message translates to:
  /// **'Veuillez saisir une adresse email valide'**
  String get invalidEmailMessage;

  /// No description provided for @invalidLink.
  ///
  /// In fr, this message translates to:
  /// **'Lien invalide'**
  String get invalidLink;

  /// No description provided for @invalidPassword.
  ///
  /// In fr, this message translates to:
  /// **'Veuillez saisir votre mot de passe'**
  String get invalidPassword;

  /// No description provided for @it.
  ///
  /// In fr, this message translates to:
  /// **'Italien'**
  String get it;

  /// No description provided for @iw.
  ///
  /// In fr, this message translates to:
  /// **'Hébreu'**
  String get iw;

  /// No description provided for @ja.
  ///
  /// In fr, this message translates to:
  /// **'Japonais'**
  String get ja;

  /// No description provided for @jw.
  ///
  /// In fr, this message translates to:
  /// **'Javanais'**
  String get jw;

  /// No description provided for @ka.
  ///
  /// In fr, this message translates to:
  /// **'Géorgien'**
  String get ka;

  /// No description provided for @keywords.
  ///
  /// In fr, this message translates to:
  /// **'Mots-clés'**
  String get keywords;

  /// No description provided for @keywords_score.
  ///
  /// In fr, this message translates to:
  /// **'Score '**
  String get keywords_score;

  /// No description provided for @keywordsAdvices.
  ///
  /// In fr, this message translates to:
  /// **'Conseils d\'utilisation'**
  String get keywordsAdvices;

  /// No description provided for @keywordScoreAdviceResponseTime.
  ///
  /// In fr, this message translates to:
  /// **'Réponse en moins de 72h'**
  String get keywordScoreAdviceResponseTime;

  /// No description provided for @keywordScoreAdviceRestaurantName.
  ///
  /// In fr, this message translates to:
  /// **'Inclure le nom de l\'etablissement'**
  String get keywordScoreAdviceRestaurantName;

  /// No description provided for @keywordScoreAdviceReviewerName.
  ///
  /// In fr, this message translates to:
  /// **'Inclure le nom du client'**
  String get keywordScoreAdviceReviewerName;

  /// No description provided for @keywordScoreAdviceSimpleBricksNumber.
  ///
  /// In fr, this message translates to:
  /// **'Utiliser des mots-clés : '**
  String get keywordScoreAdviceSimpleBricksNumber;

  /// No description provided for @keywordScoreAdviceSimpleBricksVariety.
  ///
  /// In fr, this message translates to:
  /// **'Variété des mots-clés : '**
  String get keywordScoreAdviceSimpleBricksVariety;

  /// No description provided for @keywordScoreAdviceSimpleBricksVarietyTip.
  ///
  /// In fr, this message translates to:
  /// **'(Essayez des spécialités, une localisation ou une categorie)'**
  String get keywordScoreAdviceSimpleBricksVarietyTip;

  /// No description provided for @keywordScoreAdviceSorry.
  ///
  /// In fr, this message translates to:
  /// **'Mot d\'excuse'**
  String get keywordScoreAdviceSorry;

  /// No description provided for @keywordScoreAdviceSorryTip.
  ///
  /// In fr, this message translates to:
  /// **'(navré, désolé, regret ...)'**
  String get keywordScoreAdviceSorryTip;

  /// No description provided for @keywordScoreAdviceTextLength.
  ///
  /// In fr, this message translates to:
  /// **'Nombre de caractères minimum :'**
  String get keywordScoreAdviceTextLength;

  /// No description provided for @kk.
  ///
  /// In fr, this message translates to:
  /// **'Kazakh'**
  String get kk;

  /// No description provided for @km.
  ///
  /// In fr, this message translates to:
  /// **'Khmer'**
  String get km;

  /// No description provided for @kn.
  ///
  /// In fr, this message translates to:
  /// **'Kannada'**
  String get kn;

  /// No description provided for @ko.
  ///
  /// In fr, this message translates to:
  /// **'Coréen'**
  String get ko;

  /// No description provided for @ku.
  ///
  /// In fr, this message translates to:
  /// **'Kurde (kurmanji)'**
  String get ku;

  /// No description provided for @ky.
  ///
  /// In fr, this message translates to:
  /// **'Kirghize'**
  String get ky;

  /// No description provided for @la.
  ///
  /// In fr, this message translates to:
  /// **'Latin'**
  String get la;

  /// No description provided for @landsquape.
  ///
  /// In fr, this message translates to:
  /// **'Paysage'**
  String get landsquape;

  /// No description provided for @languages.
  ///
  /// In fr, this message translates to:
  /// **'Langues'**
  String get languages;

  /// No description provided for @lastSevenDays.
  ///
  /// In fr, this message translates to:
  /// **'7 derniers jours'**
  String get lastSevenDays;

  /// No description provided for @lastSixmonths.
  ///
  /// In fr, this message translates to:
  /// **'6 derniers mois'**
  String get lastSixmonths;

  /// No description provided for @lastThirtyDays.
  ///
  /// In fr, this message translates to:
  /// **'\n(30 derniers jours)'**
  String get lastThirtyDays;

  /// No description provided for @lastThirtyDays_date_range.
  ///
  /// In fr, this message translates to:
  /// **'30 derniers jours'**
  String get lastThirtyDays_date_range;

  /// No description provided for @lastThreeMonths.
  ///
  /// In fr, this message translates to:
  /// **'3 derniers mois'**
  String get lastThreeMonths;

  /// No description provided for @lastTwelveMonths.
  ///
  /// In fr, this message translates to:
  /// **'12 derniers mois'**
  String get lastTwelveMonths;

  /// No description provided for @later.
  ///
  /// In fr, this message translates to:
  /// **'Plus tard'**
  String get later;

  /// No description provided for @lb.
  ///
  /// In fr, this message translates to:
  /// **'Luxembourgeois'**
  String get lb;

  /// No description provided for @learnMore.
  ///
  /// In fr, this message translates to:
  /// **'En savoir plus'**
  String get learnMore;

  /// No description provided for @leave.
  ///
  /// In fr, this message translates to:
  /// **'Quitter ?'**
  String get leave;

  /// No description provided for @leaveAndLoseModifications.
  ///
  /// In fr, this message translates to:
  /// **'Quitter et perdre mes modifications'**
  String get leaveAndLoseModifications;

  /// No description provided for @legalMentions.
  ///
  /// In fr, this message translates to:
  /// **'Mentions légales'**
  String get legalMentions;

  /// No description provided for @legalMentions1.
  ///
  /// In fr, this message translates to:
  /// **'I- Des dispositions clairement énoncées en aval du lancement d\'une application.'**
  String get legalMentions1;

  /// No description provided for @legalMentions1Details.
  ///
  /// In fr, this message translates to:
  /// **'Lancer une application sur un marché virtuel suppose en aval la régularisation juridique des mentions légales à afficher pour l\'utilisateur, des conditions générales d\'utilisation.'**
  String get legalMentions1Details;

  /// No description provided for @legalMentionsA.
  ///
  /// In fr, this message translates to:
  /// **'A- Les mentions légales à respecter.'**
  String get legalMentionsA;

  /// No description provided for @legalMentionsADetails.
  ///
  /// In fr, this message translates to:
  /// **'Avant même de songer à l\'idée de lancer une application sur le marché, certaines procédures juridiques doivent être respectées : de ce fait, leur préparation doit s\'effectuer avant toute commercialisation du produit. Il en va dans un premier temps des mentions légales. Ces dernières sont obligatoires puisqu\'elles permettent à l\'utilisateur de l\'application de disposer des coordonnées de l\'éditeur de l\'application, à savoir son développeur ou la personne qui est commanditaire du développement d\'une application, par le biais d\'un standard ouvert. Ces mentions légales devront ainsi être disponibles pour l\'utilisateur par le biais de son descriptif sans qu\'il ait eu à télécharger, de manière payante ou gratuite, cette dernière. Le non-respect de cette obligation des mentions légales fait encourir à l\'éditeur de l\'application une peine d\'un an d\'emprisonnement et de 75 000 euros d\'amende. Les informations à indiquer sont les suivantes. Dans un premier temps, l\'éditeur de l\'application doit y faire figurer sa dénomination sociale, son siège social, son numéro de téléphone, son numéro d\'inscription au registre du commerce et des sociétés ou au répertoire des métiers, le montant de son capital social, le nom du directeur et celui du codirecteur de l\'application. De plus, les mentions légales doivent faire figurer le nom, la dénomination sociale ou la raison sociale, l\'adresse et le numéro de téléphone'**
  String get legalMentionsADetails;

  /// No description provided for @legalMentionsTitle.
  ///
  /// In fr, this message translates to:
  /// **'Mentions légales'**
  String get legalMentionsTitle;

  /// No description provided for @legendLabel.
  ///
  /// In fr, this message translates to:
  /// **'Légende *'**
  String get legendLabel;

  /// No description provided for @linkOfOffer.
  ///
  /// In fr, this message translates to:
  /// **'Lien de l\'offre'**
  String get linkOfOffer;

  /// No description provided for @lo.
  ///
  /// In fr, this message translates to:
  /// **'Laotien'**
  String get lo;

  /// No description provided for @loginPageSubtitle.
  ///
  /// In fr, this message translates to:
  /// **'Le digital au service de la croissance des établissements'**
  String get loginPageSubtitle;

  /// No description provided for @lt.
  ///
  /// In fr, this message translates to:
  /// **'Lituanien'**
  String get lt;

  /// No description provided for @lv.
  ///
  /// In fr, this message translates to:
  /// **'Letton'**
  String get lv;

  /// No description provided for @mainHashtag.
  ///
  /// In fr, this message translates to:
  /// **'Hashtag de marque'**
  String get mainHashtag;

  /// No description provided for @malouGallery.
  ///
  /// In fr, this message translates to:
  /// **'Galerie Malou'**
  String get malouGallery;

  /// No description provided for @manageNotifs.
  ///
  /// In fr, this message translates to:
  /// **'Gérez vous-même les notifications que vous recevez 🔔.'**
  String get manageNotifs;

  /// No description provided for @mandatoryField.
  ///
  /// In fr, this message translates to:
  /// **'Champ obligatoire'**
  String get mandatoryField;

  /// No description provided for @manyPlatformDisconnectedWarning.
  ///
  /// In fr, this message translates to:
  /// **'{platform} ont été déconnectés'**
  String manyPlatformDisconnectedWarning(String platform);

  /// No description provided for @markedAsRead.
  ///
  /// In fr, this message translates to:
  /// **'Conversation marquée comme lue'**
  String get markedAsRead;

  /// No description provided for @markedAsUnread.
  ///
  /// In fr, this message translates to:
  /// **'Conversation marquée comme non lue'**
  String get markedAsUnread;

  /// No description provided for @markFeebackMessagesAsClosed.
  ///
  /// In fr, this message translates to:
  /// **'Marquer la conversation comme traitée'**
  String get markFeebackMessagesAsClosed;

  /// No description provided for @mediasAccessDescription.
  ///
  /// In fr, this message translates to:
  /// **'Ceci vous permettra d’envoyer des photos et vidéos depuis votre téléphone.'**
  String get mediasAccessDescription;

  /// No description provided for @mediasTab.
  ///
  /// In fr, this message translates to:
  /// **'Médias'**
  String get mediasTab;

  /// No description provided for @mentionnedYouInStory.
  ///
  /// In fr, this message translates to:
  /// **'Vous a mentionné dans une story'**
  String get mentionnedYouInStory;

  /// No description provided for @messageNotAvailable.
  ///
  /// In fr, this message translates to:
  /// **'Message indisponible'**
  String get messageNotAvailable;

  /// No description provided for @messages.
  ///
  /// In fr, this message translates to:
  /// **'Messages'**
  String get messages;

  /// No description provided for @messaging.
  ///
  /// In fr, this message translates to:
  /// **'Messagerie'**
  String get messaging;

  /// No description provided for @mg.
  ///
  /// In fr, this message translates to:
  /// **'Malgache'**
  String get mg;

  /// No description provided for @mi.
  ///
  /// In fr, this message translates to:
  /// **'Maori'**
  String get mi;

  /// No description provided for @midnight.
  ///
  /// In fr, this message translates to:
  /// **'minuit'**
  String get midnight;

  /// No description provided for @minOccurences.
  ///
  /// In fr, this message translates to:
  /// **'(minimum {minOccurences})'**
  String minOccurences(int minOccurences);

  /// No description provided for @mk.
  ///
  /// In fr, this message translates to:
  /// **'Macédonien'**
  String get mk;

  /// No description provided for @ml.
  ///
  /// In fr, this message translates to:
  /// **'Malayalam'**
  String get ml;

  /// No description provided for @mn.
  ///
  /// In fr, this message translates to:
  /// **'Mongol'**
  String get mn;

  /// No description provided for @monday.
  ///
  /// In fr, this message translates to:
  /// **'Lundi'**
  String get monday;

  /// No description provided for @mr.
  ///
  /// In fr, this message translates to:
  /// **'Marathi'**
  String get mr;

  /// No description provided for @ms.
  ///
  /// In fr, this message translates to:
  /// **'Malais'**
  String get ms;

  /// No description provided for @mt.
  ///
  /// In fr, this message translates to:
  /// **'Maltais'**
  String get mt;

  /// No description provided for @my.
  ///
  /// In fr, this message translates to:
  /// **'Birman (birmanie)'**
  String get my;

  /// No description provided for @myFirstName.
  ///
  /// In fr, this message translates to:
  /// **'Mon prénom'**
  String get myFirstName;

  /// No description provided for @myMedias.
  ///
  /// In fr, this message translates to:
  /// **'Pellicule'**
  String get myMedias;

  /// No description provided for @myName.
  ///
  /// In fr, this message translates to:
  /// **'Mon nom'**
  String get myName;

  /// No description provided for @myTemplates.
  ///
  /// In fr, this message translates to:
  /// **'Mes modèles de réponses'**
  String get myTemplates;

  /// No description provided for @name.
  ///
  /// In fr, this message translates to:
  /// **'name'**
  String get name;

  /// No description provided for @nbReplies.
  ///
  /// In fr, this message translates to:
  /// **'Réponse ({nbReplies}/{nbReviews})'**
  String nbReplies(Object nbReplies, Object nbReviews);

  /// No description provided for @ne.
  ///
  /// In fr, this message translates to:
  /// **'Népalais'**
  String get ne;

  /// No description provided for @needIdeas.
  ///
  /// In fr, this message translates to:
  /// **'Idées'**
  String get needIdeas;

  /// No description provided for @newReview.
  ///
  /// In fr, this message translates to:
  /// **'Nouvel avis'**
  String get newReview;

  /// No description provided for @nl.
  ///
  /// In fr, this message translates to:
  /// **'Néerlandais'**
  String get nl;

  /// No description provided for @no.
  ///
  /// In fr, this message translates to:
  /// **'Norvégien'**
  String get no;

  /// No description provided for @no_results.
  ///
  /// In fr, this message translates to:
  /// **'Aucun résultat'**
  String get no_results;

  /// No description provided for @noAccessToMedias.
  ///
  /// In fr, this message translates to:
  /// **'La “MalouApp” n’a pas accès à vos médias'**
  String get noAccessToMedias;

  /// No description provided for @noAccountQuestion.
  ///
  /// In fr, this message translates to:
  /// **'Vous n\'avez pas de compte ?'**
  String get noAccountQuestion;

  /// No description provided for @noFeedbacksYet.
  ///
  /// In fr, this message translates to:
  /// **'Vous n\'avez pas de remarques\npour le moment'**
  String get noFeedbacksYet;

  /// No description provided for @noInternetConnection.
  ///
  /// In fr, this message translates to:
  /// **'Vous n’êtes pas connecté(e) à internet'**
  String get noInternetConnection;

  /// No description provided for @noKeywordsYet.
  ///
  /// In fr, this message translates to:
  /// **'Vous ne possédez pas encore de mots-clés'**
  String get noKeywordsYet;

  /// No description provided for @noMediaInGallery.
  ///
  /// In fr, this message translates to:
  /// **'Vous n\'avez pas encore de médias dans votre galerie'**
  String get noMediaInGallery;

  /// No description provided for @noMessagesYet.
  ///
  /// In fr, this message translates to:
  /// **'Vous n\'avez pas encore de message'**
  String get noMessagesYet;

  /// No description provided for @noMoreItemsInTheList.
  ///
  /// In fr, this message translates to:
  /// **'Remontez il n\'y a plus rien à voir'**
  String get noMoreItemsInTheList;

  /// No description provided for @noMoreScheduledPosts.
  ///
  /// In fr, this message translates to:
  /// **'Aucun post programmé'**
  String get noMoreScheduledPosts;

  /// No description provided for @noon.
  ///
  /// In fr, this message translates to:
  /// **'midi'**
  String get noon;

  /// No description provided for @nop.
  ///
  /// In fr, this message translates to:
  /// **'Non'**
  String get nop;

  /// No description provided for @noRestaurantForYourResearch.
  ///
  /// In fr, this message translates to:
  /// **'Aucun établissement ne correspond à\nvotre recherche.'**
  String get noRestaurantForYourResearch;

  /// No description provided for @noRestaurantQuestion.
  ///
  /// In fr, this message translates to:
  /// **'Bah alors, on a pas détablissement ?'**
  String get noRestaurantQuestion;

  /// No description provided for @noResults.
  ///
  /// In fr, this message translates to:
  /// **'Aucun résultat'**
  String get noResults;

  /// No description provided for @noReviews.
  ///
  /// In fr, this message translates to:
  /// **'Vous n\'avez pas d\'avis client'**
  String get noReviews;

  /// No description provided for @noSocialNetworksConnected.
  ///
  /// In fr, this message translates to:
  /// **'Vous n’avez pas connecté la Malouapp à vos réseaux sociaux... mais vous pouvez déjà créer des brouillons !'**
  String get noSocialNetworksConnected;

  /// No description provided for @noSocialPosts.
  ///
  /// In fr, this message translates to:
  /// **'Pas de post référencé par ici'**
  String get noSocialPosts;

  /// No description provided for @noStories.
  ///
  /// In fr, this message translates to:
  /// **'Aucune story trouvée.. N\'ayez pas peur, lancez-vous !'**
  String get noStories;

  /// No description provided for @notAnsweredMessages.
  ///
  /// In fr, this message translates to:
  /// **'Messages non répondus'**
  String get notAnsweredMessages;

  /// No description provided for @notAnsweredReviews.
  ///
  /// In fr, this message translates to:
  /// **'Avis non répondus'**
  String get notAnsweredReviews;

  /// No description provided for @noTemplates.
  ///
  /// In fr, this message translates to:
  /// **'Vous n\'avez pas de modèle de réponse'**
  String get noTemplates;

  /// No description provided for @noTemplatesDetails.
  ///
  /// In fr, this message translates to:
  /// **'Commencez par en ajouter sur la MalouApp pour répondre plus vite à vos {entity}.'**
  String noTemplatesDetails(Object entity);

  /// No description provided for @notEnoughCreditToMakeAiApiCall.
  ///
  /// In fr, this message translates to:
  /// **'Vous n’avez plus de crédit IA.'**
  String get notEnoughCreditToMakeAiApiCall;

  /// No description provided for @notifCenter.
  ///
  /// In fr, this message translates to:
  /// **'Centre de notifications'**
  String get notifCenter;

  /// No description provided for @notificationsDisabledDescription.
  ///
  /// In fr, this message translates to:
  /// **'Activer les notifications vous permettra d’être averti(e) lorsque vous avez une action à faire.'**
  String get notificationsDisabledDescription;

  /// No description provided for @notificationsDisabledTitle.
  ///
  /// In fr, this message translates to:
  /// **'Notifications désactivées'**
  String get notificationsDisabledTitle;

  /// No description provided for @now.
  ///
  /// In fr, this message translates to:
  /// **'Maintenant'**
  String get now;

  /// No description provided for @numberOfHashtags.
  ///
  /// In fr, this message translates to:
  /// **'Nombre de hashtag: {hashtagsLength}/10'**
  String numberOfHashtags(int hashtagsLength);

  /// No description provided for @ny.
  ///
  /// In fr, this message translates to:
  /// **'Chichewa'**
  String get ny;

  /// No description provided for @object.
  ///
  /// In fr, this message translates to:
  /// **'Objet'**
  String get object;

  /// No description provided for @onceADay.
  ///
  /// In fr, this message translates to:
  /// **'Une fois par jour'**
  String get onceADay;

  /// No description provided for @onceADayWithParenthesis.
  ///
  /// In fr, this message translates to:
  /// **' (1 fois / jour)'**
  String get onceADayWithParenthesis;

  /// No description provided for @onlyAvailableInstagram.
  ///
  /// In fr, this message translates to:
  /// **'Uniquement disponible sur Instagram'**
  String get onlyAvailableInstagram;

  /// No description provided for @onlyAvailableOnMapstr.
  ///
  /// In fr, this message translates to:
  /// **'Uniquement disponible sur Mapstr'**
  String get onlyAvailableOnMapstr;

  /// No description provided for @onlyAvailableOnMeta.
  ///
  /// In fr, this message translates to:
  /// **'Disponible uniquement sur Facebook et Instagram'**
  String get onlyAvailableOnMeta;

  /// No description provided for @onlyForGoogleReviews.
  ///
  /// In fr, this message translates to:
  /// **'Concerne seulement les avis google, les autres plateformes ne sont récupérés qu’une fois par jour.'**
  String get onlyForGoogleReviews;

  /// No description provided for @onlyVisibleByAdmin.
  ///
  /// In fr, this message translates to:
  /// **'Visible seulement par les admins'**
  String get onlyVisibleByAdmin;

  /// No description provided for @onlyVisibleByAdmins.
  ///
  /// In fr, this message translates to:
  /// **'Visible uniquement par les admins'**
  String get onlyVisibleByAdmins;

  /// No description provided for @open_day.
  ///
  /// In fr, this message translates to:
  /// **'ouvert le {openDay} de {openTime}h à {closeTime}h, '**
  String open_day(String closeTime, String openDay, String openTime);

  /// No description provided for @open_day_more.
  ///
  /// In fr, this message translates to:
  /// **'et de {openTime2}h à {closeTime2}h, '**
  String open_day_more(String closeTime2, String openTime2);

  /// No description provided for @open_days.
  ///
  /// In fr, this message translates to:
  /// **'ouvert du {startDay} au {endDay} de {openTime}h à {closeTime}h, '**
  String open_days(String closeTime, String endDay, String openTime, String startDay);

  /// No description provided for @optimize.
  ///
  /// In fr, this message translates to:
  /// **'Optimiser'**
  String get optimize;

  /// No description provided for @or.
  ///
  /// In fr, this message translates to:
  /// **'Odia'**
  String get or;

  /// No description provided for @order.
  ///
  /// In fr, this message translates to:
  /// **'Commander'**
  String get order;

  /// No description provided for @orderDetails.
  ///
  /// In fr, this message translates to:
  /// **'Commande de {amount} pour la {orderCount} fois'**
  String orderDetails(String amount, String orderCount);

  /// No description provided for @oupsSeeNothing.
  ///
  /// In fr, this message translates to:
  /// **'Oups, on n\'y voit rien'**
  String get oupsSeeNothing;

  /// No description provided for @overallExperience.
  ///
  /// In fr, this message translates to:
  /// **'general'**
  String get overallExperience;

  /// No description provided for @pa.
  ///
  /// In fr, this message translates to:
  /// **'Pendjabi'**
  String get pa;

  /// No description provided for @participate.
  ///
  /// In fr, this message translates to:
  /// **'Participer'**
  String get participate;

  /// No description provided for @password.
  ///
  /// In fr, this message translates to:
  /// **'Mot de passe'**
  String get password;

  /// No description provided for @period.
  ///
  /// In fr, this message translates to:
  /// **'Période'**
  String get period;

  /// No description provided for @pl.
  ///
  /// In fr, this message translates to:
  /// **'Polonais'**
  String get pl;

  /// No description provided for @platformDisconnectedWarning.
  ///
  /// In fr, this message translates to:
  /// **'{platform} a été déconnecté'**
  String platformDisconnectedWarning(String platform);

  /// No description provided for @platformsDisconnected.
  ///
  /// In fr, this message translates to:
  /// **'Vous n\'avez pas de plateformes connectées'**
  String get platformsDisconnected;

  /// No description provided for @pleaseCreateHashtagsFromYourSettings.
  ///
  /// In fr, this message translates to:
  /// **'Veuillez en créer une dans les paramètres de votre restaurant.'**
  String get pleaseCreateHashtagsFromYourSettings;

  /// No description provided for @portrait.
  ///
  /// In fr, this message translates to:
  /// **'Portrait'**
  String get portrait;

  /// No description provided for @postCreated.
  ///
  /// In fr, this message translates to:
  /// **'Post créé avec succès'**
  String get postCreated;

  /// No description provided for @postDeleted.
  ///
  /// In fr, this message translates to:
  /// **'Votre post a bien été supprimé'**
  String get postDeleted;

  /// No description provided for @postNotFound.
  ///
  /// In fr, this message translates to:
  /// **'Le post est introuvable'**
  String get postNotFound;

  /// No description provided for @postOnPlatforms.
  ///
  /// In fr, this message translates to:
  /// **'Poster sur les plateformes'**
  String get postOnPlatforms;

  /// No description provided for @posts.
  ///
  /// In fr, this message translates to:
  /// **'Posts'**
  String get posts;

  /// No description provided for @postsSyncError.
  ///
  /// In fr, this message translates to:
  /// **'Erreur de synchronisation'**
  String get postsSyncError;

  /// No description provided for @postsSyncSuccess.
  ///
  /// In fr, this message translates to:
  /// **'La synchronisation a été réalisée avec succès !'**
  String get postsSyncSuccess;

  /// No description provided for @postSuccessfullyModified.
  ///
  /// In fr, this message translates to:
  /// **'Post modifié avec succès'**
  String get postSuccessfullyModified;

  /// No description provided for @postsUpdated.
  ///
  /// In fr, this message translates to:
  /// **'Posts mis à jour'**
  String get postsUpdated;

  /// No description provided for @postTopic.
  ///
  /// In fr, this message translates to:
  /// **'Type de post'**
  String get postTopic;

  /// No description provided for @postTopicEvent.
  ///
  /// In fr, this message translates to:
  /// **'Evènement'**
  String get postTopicEvent;

  /// No description provided for @postTopicNew.
  ///
  /// In fr, this message translates to:
  /// **'Nouveauté'**
  String get postTopicNew;

  /// No description provided for @postTopicOffer.
  ///
  /// In fr, this message translates to:
  /// **'Offre'**
  String get postTopicOffer;

  /// No description provided for @presentAPlace.
  ///
  /// In fr, this message translates to:
  /// **'Présenter un lieu '**
  String get presentAPlace;

  /// No description provided for @price.
  ///
  /// In fr, this message translates to:
  /// **'prix'**
  String get price;

  /// No description provided for @probablyDeleted.
  ///
  /// In fr, this message translates to:
  /// **'Il a sûrement été supprimé'**
  String get probablyDeleted;

  /// No description provided for @processing.
  ///
  /// In fr, this message translates to:
  /// **'En cours de traitement ...'**
  String get processing;

  /// No description provided for @promoOrCoupon.
  ///
  /// In fr, this message translates to:
  /// **'Code promo ou coupon'**
  String get promoOrCoupon;

  /// No description provided for @proposals.
  ///
  /// In fr, this message translates to:
  /// **'Propositions'**
  String get proposals;

  /// No description provided for @ps.
  ///
  /// In fr, this message translates to:
  /// **'Pachto'**
  String get ps;

  /// No description provided for @pt.
  ///
  /// In fr, this message translates to:
  /// **'Portugais'**
  String get pt;

  /// No description provided for @publicationDate.
  ///
  /// In fr, this message translates to:
  /// **'Date de publication'**
  String get publicationDate;

  /// No description provided for @publicationError.
  ///
  /// In fr, this message translates to:
  /// **'Erreur de publication'**
  String get publicationError;

  /// No description provided for @publish.
  ///
  /// In fr, this message translates to:
  /// **'Publier'**
  String get publish;

  /// No description provided for @published.
  ///
  /// In fr, this message translates to:
  /// **'Publiés'**
  String get published;

  /// No description provided for @publishing.
  ///
  /// In fr, this message translates to:
  /// **'Post en cours de publication...'**
  String get publishing;

  /// No description provided for @purchaseAIPlus.
  ///
  /// In fr, this message translates to:
  /// **'Passez à l’option IA Plus pour accéder à L’IA en illimité'**
  String get purchaseAIPlus;

  /// No description provided for @pursue.
  ///
  /// In fr, this message translates to:
  /// **'Continuer ?'**
  String get pursue;

  /// No description provided for @pursueEdit.
  ///
  /// In fr, this message translates to:
  /// **'Poursuivre mes modifications'**
  String get pursueEdit;

  /// No description provided for @question.
  ///
  /// In fr, this message translates to:
  /// **'Une question ?'**
  String get question;

  /// No description provided for @questionsAnswers.
  ///
  /// In fr, this message translates to:
  /// **'Foire aux questions'**
  String get questionsAnswers;

  /// No description provided for @read.
  ///
  /// In fr, this message translates to:
  /// **'Lu'**
  String get read;

  /// No description provided for @receivedAMedia.
  ///
  /// In fr, this message translates to:
  /// **'Vous a envoyé une {mediaType}'**
  String receivedAMedia(String mediaType);

  /// No description provided for @receivedAStory.
  ///
  /// In fr, this message translates to:
  /// **'Vous avez reçu une story'**
  String get receivedAStory;

  /// No description provided for @receivedReviews.
  ///
  /// In fr, this message translates to:
  /// **'Avis reçus'**
  String get receivedReviews;

  /// No description provided for @recommandations.
  ///
  /// In fr, this message translates to:
  /// **'Recommandations '**
  String get recommandations;

  /// No description provided for @recommandationsForUse.
  ///
  /// In fr, this message translates to:
  /// **'Conseils d\'utilisation'**
  String get recommandationsForUse;

  /// No description provided for @reconnectPlatform.
  ///
  /// In fr, this message translates to:
  /// **'Reconnecter {missingPlatform}'**
  String reconnectPlatform(String missingPlatform);

  /// No description provided for @reconnectToInternet.
  ///
  /// In fr, this message translates to:
  /// **'Reconnectez-vous à un réseau afin d’accéder à cette page.'**
  String get reconnectToInternet;

  /// No description provided for @refreshed.
  ///
  /// In fr, this message translates to:
  /// **'Hop c\'est rafrachi !'**
  String get refreshed;

  /// No description provided for @refreshPage.
  ///
  /// In fr, this message translates to:
  /// **'Rafraichir la page'**
  String get refreshPage;

  /// No description provided for @remainingAICredits.
  ///
  /// In fr, this message translates to:
  /// **'{nbCredits} crédits restants'**
  String remainingAICredits(int nbCredits);

  /// No description provided for @remainingAICreditsFormField.
  ///
  /// In fr, this message translates to:
  /// **'{nbCredits}/500'**
  String remainingAICreditsFormField(int nbCredits);

  /// No description provided for @remarksNumber.
  ///
  /// In fr, this message translates to:
  /// **'{count, plural, one {1 remarque} other {{count} remarques}}'**
  String remarksNumber(int count);

  /// No description provided for @reply.
  ///
  /// In fr, this message translates to:
  /// **'Répondre'**
  String get reply;

  /// No description provided for @replyByMail.
  ///
  /// In fr, this message translates to:
  /// **'Répondre par mail'**
  String get replyByMail;

  /// No description provided for @restaurantAddress.
  ///
  /// In fr, this message translates to:
  /// **'Adresse'**
  String get restaurantAddress;

  /// No description provided for @restaurantMenu.
  ///
  /// In fr, this message translates to:
  /// **'Menu'**
  String get restaurantMenu;

  /// No description provided for @restaurantName.
  ///
  /// In fr, this message translates to:
  /// **'Nom Établissement'**
  String get restaurantName;

  /// No description provided for @restaurantOpeningHours.
  ///
  /// In fr, this message translates to:
  /// **'Horaires d\'ouverture'**
  String get restaurantOpeningHours;

  /// No description provided for @restaurantPhone.
  ///
  /// In fr, this message translates to:
  /// **'Téléphone'**
  String get restaurantPhone;

  /// No description provided for @restaurantWebsite.
  ///
  /// In fr, this message translates to:
  /// **'Site web'**
  String get restaurantWebsite;

  /// No description provided for @retry.
  ///
  /// In fr, this message translates to:
  /// **'Réessayer'**
  String get retry;

  /// No description provided for @review.
  ///
  /// In fr, this message translates to:
  /// **'Avis client'**
  String get review;

  /// No description provided for @reviewAnalyseFailed.
  ///
  /// In fr, this message translates to:
  /// **'Cet avis n\'a pas pu être analysé'**
  String get reviewAnalyseFailed;

  /// No description provided for @reviewAnalysesRunning.
  ///
  /// In fr, this message translates to:
  /// **'L\'analyse est en cours'**
  String get reviewAnalysesRunning;

  /// No description provided for @reviewAnalysesUnknownStatus.
  ///
  /// In fr, this message translates to:
  /// **'Status inconnu pour l\'analyse sémantique'**
  String get reviewAnalysesUnknownStatus;

  /// No description provided for @reviewAnalyzedError.
  ///
  /// In fr, this message translates to:
  /// **'Erreur pendant l\'analyse de la review'**
  String get reviewAnalyzedError;

  /// No description provided for @reviewAnalyzedNoResult.
  ///
  /// In fr, this message translates to:
  /// **'Cet avis n\'a pas de catégorie associé à un sentiment.'**
  String get reviewAnalyzedNoResult;

  /// No description provided for @reviewAnalyzedTooOld.
  ///
  /// In fr, this message translates to:
  /// **'L\'avis est trop ancien pour être analysé.'**
  String get reviewAnalyzedTooOld;

  /// No description provided for @reviewDoesNotExistAnymore.
  ///
  /// In fr, this message translates to:
  /// **'Il semblerait que cet avis ait été supprimé par l\'utilisateur.'**
  String get reviewDoesNotExistAnymore;

  /// No description provided for @reviewFrom.
  ///
  /// In fr, this message translates to:
  /// **'De la part de ...'**
  String get reviewFrom;

  /// No description provided for @reviewNotYetAnalyzed.
  ///
  /// In fr, this message translates to:
  /// **'Cet avis n\'a pas encore été analysé, vous pourrez voir le résultat dans un délai maximal de 24h.'**
  String get reviewNotYetAnalyzed;

  /// No description provided for @reviewReplyError.
  ///
  /// In fr, this message translates to:
  /// **'Oups ! Votre réponse n’a pas pu être envoyée, veuillez réessayer ultérieurement.'**
  String get reviewReplyError;

  /// No description provided for @reviewReplySentSuccessfully.
  ///
  /// In fr, this message translates to:
  /// **'Votre réponse a bien été envoyée ! '**
  String get reviewReplySentSuccessfully;

  /// No description provided for @reviews.
  ///
  /// In fr, this message translates to:
  /// **'Avis clients'**
  String get reviews;

  /// No description provided for @reviewScores.
  ///
  /// In fr, this message translates to:
  /// **'Notes des avis concernés'**
  String get reviewScores;

  /// No description provided for @reviewsRating.
  ///
  /// In fr, this message translates to:
  /// **'Note moyenne des avis'**
  String get reviewsRating;

  /// No description provided for @reviewsSyncError.
  ///
  /// In fr, this message translates to:
  /// **'Error de synchronisation pour les plateformes : '**
  String get reviewsSyncError;

  /// No description provided for @reviewsSyncSuccess.
  ///
  /// In fr, this message translates to:
  /// **'Synchronisation des avis terminée avec succès'**
  String get reviewsSyncSuccess;

  /// No description provided for @reviewTemplates.
  ///
  /// In fr, this message translates to:
  /// **'Modèles d\'avis'**
  String get reviewTemplates;

  /// No description provided for @ro.
  ///
  /// In fr, this message translates to:
  /// **'Roumain'**
  String get ro;

  /// No description provided for @ru.
  ///
  /// In fr, this message translates to:
  /// **'Russe'**
  String get ru;

  /// No description provided for @saturday.
  ///
  /// In fr, this message translates to:
  /// **'Samedi'**
  String get saturday;

  /// No description provided for @savedLinks.
  ///
  /// In fr, this message translates to:
  /// **'Liens enregistrés'**
  String get savedLinks;

  /// No description provided for @saveDraft.
  ///
  /// In fr, this message translates to:
  /// **'Enregistrer en brouillon'**
  String get saveDraft;

  /// No description provided for @saveModifications.
  ///
  /// In fr, this message translates to:
  /// **'Enregistrer les modifications'**
  String get saveModifications;

  /// No description provided for @savePost.
  ///
  /// In fr, this message translates to:
  /// **'Enregistrer le post'**
  String get savePost;

  /// No description provided for @schedule.
  ///
  /// In fr, this message translates to:
  /// **'Programmer'**
  String get schedule;

  /// No description provided for @scheduled.
  ///
  /// In fr, this message translates to:
  /// **'Programmés'**
  String get scheduled;

  /// No description provided for @sd.
  ///
  /// In fr, this message translates to:
  /// **'Sindhi'**
  String get sd;

  /// No description provided for @search.
  ///
  /// In fr, this message translates to:
  /// **'Rechercher'**
  String get search;

  /// No description provided for @searchPlace.
  ///
  /// In fr, this message translates to:
  /// **'Rechercher un lieu'**
  String get searchPlace;

  /// No description provided for @see.
  ///
  /// In fr, this message translates to:
  /// **'Voir'**
  String get see;

  /// No description provided for @see_other_keywords.
  ///
  /// In fr, this message translates to:
  /// **'Voir mes autres mots-clés {noRevelantBricksLength}'**
  String see_other_keywords(int noRevelantBricksLength);

  /// No description provided for @seeFeed.
  ///
  /// In fr, this message translates to:
  /// **'Voir le feed'**
  String get seeFeed;

  /// No description provided for @seeList.
  ///
  /// In fr, this message translates to:
  /// **'Voir la liste'**
  String get seeList;

  /// No description provided for @seeListOfHashtags.
  ///
  /// In fr, this message translates to:
  /// **'Voir la liste de mes hashtags'**
  String get seeListOfHashtags;

  /// No description provided for @seeMenu.
  ///
  /// In fr, this message translates to:
  /// **'Lire le menu'**
  String get seeMenu;

  /// No description provided for @seeOriginalReviewText.
  ///
  /// In fr, this message translates to:
  /// **'Voir l\'original (en {lang})'**
  String seeOriginalReviewText(String lang);

  /// No description provided for @semanticAnalyse.
  ///
  /// In fr, this message translates to:
  /// **'Analyse sémantique'**
  String get semanticAnalyse;

  /// No description provided for @sendError.
  ///
  /// In fr, this message translates to:
  /// **'Erreur d\'envoi.'**
  String get sendError;

  /// No description provided for @sendPromotion.
  ///
  /// In fr, this message translates to:
  /// **'Envoyer une compensation'**
  String get sendPromotion;

  /// No description provided for @sendPromotionOf.
  ///
  /// In fr, this message translates to:
  /// **'Envoyer une compensation : {amount}'**
  String sendPromotionOf(String amount);

  /// No description provided for @sentAMedia.
  ///
  /// In fr, this message translates to:
  /// **'Vous avez envoyé une {mediaType}'**
  String sentAMedia(String mediaType);

  /// No description provided for @service.
  ///
  /// In fr, this message translates to:
  /// **'service'**
  String get service;

  /// No description provided for @set_email.
  ///
  /// In fr, this message translates to:
  /// **'Entrez votre email'**
  String get set_email;

  /// No description provided for @settings.
  ///
  /// In fr, this message translates to:
  /// **'Paramètres'**
  String get settings;

  /// No description provided for @showInFeed.
  ///
  /// In fr, this message translates to:
  /// **'Afficher dans le feed Instagram'**
  String get showInFeed;

  /// No description provided for @si.
  ///
  /// In fr, this message translates to:
  /// **'Cinghalais'**
  String get si;

  /// No description provided for @signOut.
  ///
  /// In fr, this message translates to:
  /// **'Me déconnecter'**
  String get signOut;

  /// No description provided for @signup.
  ///
  /// In fr, this message translates to:
  /// **'S\'inscrire'**
  String get signup;

  /// No description provided for @sk.
  ///
  /// In fr, this message translates to:
  /// **'Slovaque'**
  String get sk;

  /// No description provided for @sl.
  ///
  /// In fr, this message translates to:
  /// **'Slovène'**
  String get sl;

  /// No description provided for @sm.
  ///
  /// In fr, this message translates to:
  /// **'Samoan'**
  String get sm;

  /// No description provided for @sn.
  ///
  /// In fr, this message translates to:
  /// **'Shona'**
  String get sn;

  /// No description provided for @so.
  ///
  /// In fr, this message translates to:
  /// **'Somali'**
  String get so;

  /// No description provided for @socialNetworks.
  ///
  /// In fr, this message translates to:
  /// **'RS'**
  String get socialNetworks;

  /// No description provided for @somethingWentWrong.
  ///
  /// In fr, this message translates to:
  /// **'Oops, something went wrong'**
  String get somethingWentWrong;

  /// No description provided for @sq.
  ///
  /// In fr, this message translates to:
  /// **'Albanais'**
  String get sq;

  /// No description provided for @square.
  ///
  /// In fr, this message translates to:
  /// **'Carré'**
  String get square;

  /// No description provided for @sr.
  ///
  /// In fr, this message translates to:
  /// **'Serbe'**
  String get sr;

  /// No description provided for @st.
  ///
  /// In fr, this message translates to:
  /// **'Sesotho'**
  String get st;

  /// No description provided for @stories.
  ///
  /// In fr, this message translates to:
  /// **'Stories'**
  String get stories;

  /// No description provided for @storyDeleted.
  ///
  /// In fr, this message translates to:
  /// **'Votre story a bien été supprimé'**
  String get storyDeleted;

  /// No description provided for @storyExpired.
  ///
  /// In fr, this message translates to:
  /// **'Story expirée'**
  String get storyExpired;

  /// No description provided for @su.
  ///
  /// In fr, this message translates to:
  /// **'Soudanais'**
  String get su;

  /// No description provided for @sunday.
  ///
  /// In fr, this message translates to:
  /// **'Dimanche'**
  String get sunday;

  /// No description provided for @sv.
  ///
  /// In fr, this message translates to:
  /// **'Suédois'**
  String get sv;

  /// No description provided for @sw.
  ///
  /// In fr, this message translates to:
  /// **'Swahili'**
  String get sw;

  /// No description provided for @ta.
  ///
  /// In fr, this message translates to:
  /// **'Tamoul'**
  String get ta;

  /// No description provided for @tagAccounts.
  ///
  /// In fr, this message translates to:
  /// **'Taguer des comptes'**
  String get tagAccounts;

  /// No description provided for @te.
  ///
  /// In fr, this message translates to:
  /// **'Télougou'**
  String get te;

  /// No description provided for @tg.
  ///
  /// In fr, this message translates to:
  /// **'Tadjik'**
  String get tg;

  /// No description provided for @th.
  ///
  /// In fr, this message translates to:
  /// **'Thaï'**
  String get th;

  /// No description provided for @the.
  ///
  /// In fr, this message translates to:
  /// **'le'**
  String get the;

  /// No description provided for @theEvent.
  ///
  /// In fr, this message translates to:
  /// **'l\'événement'**
  String get theEvent;

  /// No description provided for @theEventPostGenerationAI.
  ///
  /// In fr, this message translates to:
  /// **'de l\'événement '**
  String get theEventPostGenerationAI;

  /// No description provided for @theOffer.
  ///
  /// In fr, this message translates to:
  /// **'l\'offre'**
  String get theOffer;

  /// No description provided for @thursday.
  ///
  /// In fr, this message translates to:
  /// **'Jeudi'**
  String get thursday;

  /// No description provided for @timeLimitReplyReviewExceeded.
  ///
  /// In fr, this message translates to:
  /// **'Vous avez dépassé la date limite pour répondre à l’avis'**
  String get timeLimitReplyReviewExceeded;

  /// No description provided for @titleLabel.
  ///
  /// In fr, this message translates to:
  /// **'Titre *'**
  String get titleLabel;

  /// No description provided for @tl.
  ///
  /// In fr, this message translates to:
  /// **'Filipino'**
  String get tl;

  /// No description provided for @to.
  ///
  /// In fr, this message translates to:
  /// **'Au'**
  String get to;

  /// No description provided for @toCheck.
  ///
  /// In fr, this message translates to:
  /// **'À traiter'**
  String get toCheck;

  /// No description provided for @todo.
  ///
  /// In fr, this message translates to:
  /// **'À faire'**
  String get todo;

  /// No description provided for @tooManyNotif.
  ///
  /// In fr, this message translates to:
  /// **'Trop de notifications, pas assez ?'**
  String get tooManyNotif;

  /// No description provided for @totem.
  ///
  /// In fr, this message translates to:
  /// **'Totem {chipName}'**
  String totem(String chipName);

  /// No description provided for @totemDisplayName.
  ///
  /// In fr, this message translates to:
  /// **'Totem'**
  String get totemDisplayName;

  /// No description provided for @tr.
  ///
  /// In fr, this message translates to:
  /// **'Turc'**
  String get tr;

  /// No description provided for @translate.
  ///
  /// In fr, this message translates to:
  /// **'Traduire'**
  String get translate;

  /// No description provided for @translated.
  ///
  /// In fr, this message translates to:
  /// **'Traduit -'**
  String get translated;

  /// No description provided for @translateInLang.
  ///
  /// In fr, this message translates to:
  /// **'Traduire en {lang}'**
  String translateInLang(String lang);

  /// No description provided for @try_another_search.
  ///
  /// In fr, this message translates to:
  /// **'Essayez une autre recherche'**
  String get try_another_search;

  /// No description provided for @tryAgain.
  ///
  /// In fr, this message translates to:
  /// **'Réessayer'**
  String get tryAgain;

  /// No description provided for @tryAgainLater.
  ///
  /// In fr, this message translates to:
  /// **'Please try again later'**
  String get tryAgainLater;

  /// No description provided for @tuesday.
  ///
  /// In fr, this message translates to:
  /// **'Mardi'**
  String get tuesday;

  /// No description provided for @typeAndLocalisation.
  ///
  /// In fr, this message translates to:
  /// **'Type et lieu '**
  String get typeAndLocalisation;

  /// No description provided for @typeAndLocalisationExample.
  ///
  /// In fr, this message translates to:
  /// **'(ex :  ItalienParis14)'**
  String get typeAndLocalisationExample;

  /// No description provided for @typeYourLang.
  ///
  /// In fr, this message translates to:
  /// **'Tapez votre langue'**
  String get typeYourLang;

  /// No description provided for @typeYourText.
  ///
  /// In fr, this message translates to:
  /// **'Tapez votre texte'**
  String get typeYourText;

  /// No description provided for @ug.
  ///
  /// In fr, this message translates to:
  /// **'Ouïghour'**
  String get ug;

  /// No description provided for @uk.
  ///
  /// In fr, this message translates to:
  /// **'Ukrainien'**
  String get uk;

  /// No description provided for @unAnswered.
  ///
  /// In fr, this message translates to:
  /// **'Non répondus'**
  String get unAnswered;

  /// No description provided for @unansweredReviews.
  ///
  /// In fr, this message translates to:
  /// **'Avis non répondus'**
  String get unansweredReviews;

  /// No description provided for @unarchive.
  ///
  /// In fr, this message translates to:
  /// **'Désarchiver'**
  String get unarchive;

  /// No description provided for @unauthorized.
  ///
  /// In fr, this message translates to:
  /// **'Vous n\'avez pas les autorisations pour effectuer cette action.'**
  String get unauthorized;

  /// No description provided for @unavailableMessage.
  ///
  /// In fr, this message translates to:
  /// **'Message indisponible'**
  String get unavailableMessage;

  /// No description provided for @undetermined.
  ///
  /// In fr, this message translates to:
  /// **'Indéterminé'**
  String get undetermined;

  /// No description provided for @unknownError.
  ///
  /// In fr, this message translates to:
  /// **'Erreur inconnue'**
  String get unknownError;

  /// No description provided for @unread.
  ///
  /// In fr, this message translates to:
  /// **'Non lu'**
  String get unread;

  /// No description provided for @unreadMessages.
  ///
  /// In fr, this message translates to:
  /// **'Messages non lus'**
  String get unreadMessages;

  /// No description provided for @unsupportedAttachment.
  ///
  /// In fr, this message translates to:
  /// **'Le format de la pièce jointe n\'est pas supporté.'**
  String get unsupportedAttachment;

  /// No description provided for @updateFeedbackMessageError.
  ///
  /// In fr, this message translates to:
  /// **'Erreur lors de la mise à jour du message, veuillez réessayer'**
  String get updateFeedbackMessageError;

  /// No description provided for @ur.
  ///
  /// In fr, this message translates to:
  /// **'Ourdou'**
  String get ur;

  /// No description provided for @urgent.
  ///
  /// In fr, this message translates to:
  /// **'Urgent'**
  String get urgent;

  /// No description provided for @useImageForPostGeneration.
  ///
  /// In fr, this message translates to:
  /// **'Utiliser l\'image pour générer la legende'**
  String get useImageForPostGeneration;

  /// No description provided for @uz.
  ///
  /// In fr, this message translates to:
  /// **'Ouzbek'**
  String get uz;

  /// No description provided for @validate.
  ///
  /// In fr, this message translates to:
  /// **'Valider'**
  String get validate;

  /// No description provided for @varietyOfHashtags.
  ///
  /// In fr, this message translates to:
  /// **'Variété des hashtags'**
  String get varietyOfHashtags;

  /// No description provided for @vi.
  ///
  /// In fr, this message translates to:
  /// **'Vietnamien'**
  String get vi;

  /// No description provided for @video.
  ///
  /// In fr, this message translates to:
  /// **'Vidéo'**
  String get video;

  /// No description provided for @videosForbiddenOnMapstr.
  ///
  /// In fr, this message translates to:
  /// **'Les vidéos ne sont pas autorisées sur Mapstr'**
  String get videosForbiddenOnMapstr;

  /// No description provided for @videosInCarrouselForbiddenOnFacebook.
  ///
  /// In fr, this message translates to:
  /// **'Vous ne pouvez pas publier de carrousel contenant des vidéos sur Facebook'**
  String get videosInCarrouselForbiddenOnFacebook;

  /// No description provided for @videosOnGoogleForbiddenMessage.
  ///
  /// In fr, this message translates to:
  /// **'Vous ne pouvez pas publier de vidéos sur Google'**
  String get videosOnGoogleForbiddenMessage;

  /// No description provided for @videoTooShortMessage.
  ///
  /// In fr, this message translates to:
  /// **'Vous ne pouvez pas publier de vidéos de moins de 3 seconds'**
  String get videoTooShortMessage;

  /// No description provided for @waitingForImageAnalysis.
  ///
  /// In fr, this message translates to:
  /// **'En attente de l\'analyse de l\'image'**
  String get waitingForImageAnalysis;

  /// No description provided for @we_are.
  ///
  /// In fr, this message translates to:
  /// **'Nous sommes '**
  String get we_are;

  /// No description provided for @wednesday.
  ///
  /// In fr, this message translates to:
  /// **'Mercredi'**
  String get wednesday;

  /// No description provided for @whatsNew.
  ///
  /// In fr, this message translates to:
  /// **'Quoi de nouveau ?'**
  String get whatsNew;

  /// No description provided for @writeAnswerReview.
  ///
  /// In fr, this message translates to:
  /// **'Écrire votre réponse'**
  String get writeAnswerReview;

  /// No description provided for @writeLegendHere.
  ///
  /// In fr, this message translates to:
  /// **'Ecrivez votre légende ici ou '**
  String get writeLegendHere;

  /// No description provided for @writeMessage.
  ///
  /// In fr, this message translates to:
  /// **'Écrire un message'**
  String get writeMessage;

  /// No description provided for @writePostWithAi.
  ///
  /// In fr, this message translates to:
  /// **'Écrire un post avec l\'IA'**
  String get writePostWithAi;

  /// No description provided for @writeTitleHere.
  ///
  /// In fr, this message translates to:
  /// **'Ecrivez votre titre ici...'**
  String get writeTitleHere;

  /// No description provided for @writeYourFeedback.
  ///
  /// In fr, this message translates to:
  /// **'Écrire une remarque'**
  String get writeYourFeedback;

  /// No description provided for @xh.
  ///
  /// In fr, this message translates to:
  /// **'Xhosa'**
  String get xh;

  /// No description provided for @year.
  ///
  /// In fr, this message translates to:
  /// **'12 derniers mois'**
  String get year;

  /// No description provided for @yi.
  ///
  /// In fr, this message translates to:
  /// **'Yiddish'**
  String get yi;

  /// No description provided for @yo.
  ///
  /// In fr, this message translates to:
  /// **'Yoruba'**
  String get yo;

  /// No description provided for @youAnsweredAStory.
  ///
  /// In fr, this message translates to:
  /// **'Vous avez répondu à une story'**
  String get youAnsweredAStory;

  /// No description provided for @youCanSelectOnlyOne.
  ///
  /// In fr, this message translates to:
  /// **'Vous ne pouvez séléctionner qu\'une seule image'**
  String get youCanSelectOnlyOne;

  /// No description provided for @youCanSelectOnlyOnevideo.
  ///
  /// In fr, this message translates to:
  /// **'Vous ne pouvez ajouter qu\'une seule vidéo sur un Reel'**
  String get youCanSelectOnlyOnevideo;

  /// No description provided for @youCanSelectVideosOnly.
  ///
  /// In fr, this message translates to:
  /// **'Vous ne pouvez séléctionner que des vidéos'**
  String get youCanSelectVideosOnly;

  /// No description provided for @youMentionnedInStory.
  ///
  /// In fr, this message translates to:
  /// **'Vous avez mentionné dans votre story'**
  String get youMentionnedInStory;

  /// No description provided for @your_businesses.
  ///
  /// In fr, this message translates to:
  /// **'Vos établissements'**
  String get your_businesses;

  /// No description provided for @youreDisconnectedFromInternet.
  ///
  /// In fr, this message translates to:
  /// **'Il semblerait que vous ne soyez plus connecté à internet.'**
  String get youreDisconnectedFromInternet;

  /// No description provided for @yourHashtags.
  ///
  /// In fr, this message translates to:
  /// **'Vos hashtags'**
  String get yourHashtags;

  /// No description provided for @youWillLoseDraft.
  ///
  /// In fr, this message translates to:
  /// **'Si vous quittez vous perdrez ce brouillon.'**
  String get youWillLoseDraft;

  /// No description provided for @youWillLoseYourModifications.
  ///
  /// In fr, this message translates to:
  /// **'Vous perdrez alors vos modifications.'**
  String get youWillLoseYourModifications;

  /// No description provided for @zu.
  ///
  /// In fr, this message translates to:
  /// **'Zoulou'**
  String get zu;
}

class _TranslationDelegate extends LocalizationsDelegate<Translation> {
  const _TranslationDelegate();

  @override
  Future<Translation> load(Locale locale) {
    return SynchronousFuture<Translation>(lookupTranslation(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'es', 'fr', 'it'].contains(locale.languageCode);

  @override
  bool shouldReload(_TranslationDelegate old) => false;
}

Translation lookupTranslation(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return TranslationEn();
    case 'es': return TranslationEs();
    case 'fr': return TranslationFr();
    case 'it': return TranslationIt();
  }

  throw FlutterError(
    'Translation.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
