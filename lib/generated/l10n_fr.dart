import 'package:intl/intl.dart' as intl;

import 'l10n.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class TranslationFr extends Translation {
  TranslationFr([String locale = 'fr']) : super(locale);

  @override
  String get aboutToDeleteMessage => 'Vous êtes sur le point de supprimer ce message.';

  @override
  String get aboutToSignOut => 'Vous êtes sur le point de vous déconnecter';

  @override
  String get activate => 'Activer';

  @override
  String get actives => 'Actives';

  @override
  String get add => 'Ajouter';

  @override
  String get addARestaurant => 'Ajouter un établissement';

  @override
  String get addCoupon => 'Un code promo ou un coupon';

  @override
  String get addDraft => 'Créer un brouillon';

  @override
  String get addFIrstPost => 'Créer mon premier post';

  @override
  String get addHashtags => 'Ajouter des hashtags';

  @override
  String get addImageToUse => 'Ajouter une image pour utiliser cette fonctionnalité';

  @override
  String get addInfosAboutOffer => 'Ajouter des informations sur l\'offre';

  @override
  String get addLinkToOffer => 'Un lien pour profiter de l\'offre';

  @override
  String get addLocation => 'Ajouter un lieu';

  @override
  String get addNow => 'Ajouter maintenant';

  @override
  String get addSeoPost => 'Post Google';

  @override
  String get addSocialPost => 'Post RS';

  @override
  String get addTitle => 'Ajouter un titre';

  @override
  String get addYourFirstRestaurant => 'Ajoutez votre premier établissement pour\nprofiter pleinement de la MalouApp';

  @override
  String get aDish => 'Notre nouveau menu avec ';

  @override
  String get advice => 'd\'un conseil ';

  @override
  String get af => 'Afrikaans';

  @override
  String get aIcreditsBackSoon => 'Les propositions de réponses seront à nouveau disponibles lors du renouvellement de vos crédits.';

  @override
  String get aiPostCaptionFormfieldHint => 'notre moelleux au chocolat parfait pour finir son repas';

  @override
  String get aiPostCaptionFormfieldLabel => 'Générer un post à propos de...';

  @override
  String get all => 'Tous';

  @override
  String get am => 'Amharique';

  @override
  String get analyzeReviewsNotAvailable => 'L’analyse sémantique n’est pas disponible pour cet établissement.';

  @override
  String get and => ' et';

  @override
  String get anErrorOccured => 'Une erreur est survenue';

  @override
  String get announceAnEvent => 'Un événement à venir avec';

  @override
  String get answeredYourStory => 'A répondu à votre story';

  @override
  String get aPlace => 'd\'un lieu';

  @override
  String get ar => 'Arabe';

  @override
  String get archive => 'Archiver';

  @override
  String get archived => 'Archivés';

  @override
  String get areYouSureYouWantToDeleteThisPost => 'Voulez-vous supprimer ce post ?';

  @override
  String get areYouSureYouWantToDeleteThisPostPublished => 'Voulez-vous supprimer ce post ?\n Il sera également supprimé des plateformes sur lesquelles il a été posté.';

  @override
  String get areYouSureYouWantToDeleteThisStory => 'Voulez vous supprimer cette story ?';

  @override
  String get artificialIntelligence => 'Intelligence Artificielle';

  @override
  String get askForDemo => 'Demander une démo';

  @override
  String get atmosphere => 'ambiance';

  @override
  String get authorizeCamera => 'Autoriser l\'accès à l\'appareil photo';

  @override
  String get authorizeMedias => 'Autoriser l\'accès à mes médias';

  @override
  String get autoReply => 'Réponse automatique';

  @override
  String get az => 'Azéri';

  @override
  String get be => 'Biélorusse';

  @override
  String get bg => 'Bulgare';

  @override
  String get bn => 'Bengali';

  @override
  String get book => 'Réserver';

  @override
  String get brandAccount => 'Compte marque';

  @override
  String get bs => 'Bosniaque';

  @override
  String get businessName => 'Nom de l\'établissement';

  @override
  String get buy => 'Acheter';

  @override
  String get byDefault => ' (Par défaut)';

  @override
  String get ca => 'Catalan';

  @override
  String get call => 'Appeler';

  @override
  String get cameraNoAccessDescription => 'Ceci vous permettra d’envoyer des photos depuis votre appareil photo.';

  @override
  String get cameraNoAccessTitle => 'La “MalouApp” n’a pas accès à votre appareil photo';

  @override
  String get cancel => 'Annuler';

  @override
  String get cancelEdit => 'Annuler les modifications';

  @override
  String get cannotFindYourLang => 'Vous ne trouvez pas votre langue ?';

  @override
  String get cant_edit_medias_post_published => 'Vous ne pouvez pas modifier les médias d\'un post déjà publié';

  @override
  String get cantAnalyzeImage => 'Impossible d\'analyser l\'image';

  @override
  String get cantAnalyzeVideos => 'Impossible d\'analyser les vidéos';

  @override
  String get cantEditMediaPublished => 'Vous ne pouvez pas modifier les médias d\'un post déjà publié';

  @override
  String get carrouselForbiddenOnGooGle => 'Vous ne pouvez pas publier de carrousel sur Google';

  @override
  String get carrouselForbiddenOnGoogleMessage => 'Vous ne pouvez pas publier de carrousel sur Google';

  @override
  String get carrouselMediasLimitReachedMessage => 'Vous ne pouvez pas séléctionner plus de 10 médias';

  @override
  String get ceb => 'Cebuano';

  @override
  String get changeLanguage => 'Changer la langue';

  @override
  String get chooseActionButton => 'Choisir un bouton d\'action';

  @override
  String get clientName => 'Nom du client';

  @override
  String closed_day(String closedDay) {
    return 'fermés le $closedDay, ';
  }

  @override
  String closed_days(String endDay, String startDay) {
    return 'fermés du $startDay au $endDay, ';
  }

  @override
  String get co => 'Corse';

  @override
  String get commentsTab => 'Remarques';

  @override
  String get completeCaptionToGenerateHashtagsFromAi => 'Completez la légende de votre post pour que l\'IA puisse générer des hashtags';

  @override
  String get concernedRatings => 'Notes des avis concernés';

  @override
  String get concernsOnlyNegativeReviews => 'Concerne les avis négatifs sans réponse depuis plus de 48h';

  @override
  String get conditionOffer => 'Conditions de l\'offre';

  @override
  String get conditionsOfOffer => 'Les conditions de l\'offre';

  @override
  String get confirm => 'Confirmer';

  @override
  String get connectGoogle => 'Connecter mon compte Google';

  @override
  String get connectMyPlatforms => 'Connecter mes plateformes';

  @override
  String get connectSocialNetworks => 'Connecter mes réseaux sociaux';

  @override
  String get contactMalouOps => 'Contactez votre référent(e) Malou si vous ne trouvez pas votre bonheur dans notre FAQ 🤗.';

  @override
  String get conversation => 'Messagerie';

  @override
  String get conversationArchived => 'Conversation archivée';

  @override
  String get conversationUnarchived => 'Conversation désarchivée';

  @override
  String get courier_to_restaurant => 'Livreur';

  @override
  String get cover => 'Couverture';

  @override
  String get create_reel => 'Créer un Reel';

  @override
  String get createCompletionError => 'Une erreur est survenue lors de la génération IA, veuillez réessayer.';

  @override
  String get createDraft => 'Créer un brouillon';

  @override
  String get cs => 'Tchèque';

  @override
  String get customer_to_restaurant => 'Client';

  @override
  String get customerReviews => 'Avis clients';

  @override
  String get customized => 'Personnalisé ';

  @override
  String get cy => 'Gallois';

  @override
  String get da => 'Danois';

  @override
  String dailyUnansweredReviewsNotificationText(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count nouveaux avis reçus',
      one: '1 nouvel avis reçu',
    );
    return '⭐️ Psst, $_temp0';
  }

  @override
  String dailyUnreadMessagesNotificationText(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count nouveaux messages',
      one: '1 nouveau message',
    );
    return '💬 Psst, $_temp0';
  }

  @override
  String get date => 'Date';

  @override
  String get dateOf => 'Date de';

  @override
  String get daysLeftToReply => ' jours restants pour répondre';

  @override
  String get de => 'Allemand';

  @override
  String get delete => 'Supprimer';

  @override
  String get deleteFeedbackMessageError => 'Erreur lors de la suppression du message';

  @override
  String get describeDish => 'Notre nouveau menu avec ';

  @override
  String get describeGiveaway => 'Un événement à venir avec ';

  @override
  String get descriptionTab => 'Description';

  @override
  String get displayAnalyzeReviews => 'Afficher l\'analyse sémantique';

  @override
  String get displayOnlyNeverPosted => 'Afficher uniquement les médias jamais postés';

  @override
  String get dontForgetToAnswer => 'N’oubliez pas d’y répondre depuis la Malouapp.';

  @override
  String get dontLetYourCustomersWithoutAnswer => 'Ne laissez pas vos clients sans réponse, rendez-vous sur la Malouapp.';

  @override
  String get draft => 'Brouillon';

  @override
  String get drafts => 'Brouillons';

  @override
  String get duplicate => 'Dupliquer';

  @override
  String get eachTime => 'A chaque fois';

  @override
  String get edit => 'Modifier';

  @override
  String get editWithAI => 'Modifier avec l\'IA';

  @override
  String get el => 'Grec';

  @override
  String get email => 'Email';

  @override
  String get emailSelected => 'Mail d\'envoi';

  @override
  String get emptyDeviceMedias => 'Vous n\'avez pas encore de médias dans votre galerie';

  @override
  String get emptyGalleryMedias => 'Vous n\'avez pas encore de médias dans votre galerie';

  @override
  String get emptyPosts => 'Pas de post référencé par ici';

  @override
  String get emptySocialPosts => 'Vous n’avez pas connecté la Malouapp à vos réseaux sociaux... mais vous pouvez déjà créer des brouillons';

  @override
  String get en => 'Anglais';

  @override
  String get enableNotifications => 'Activer les notifications';

  @override
  String get ended => 'Terminées';

  @override
  String get enterPassword => 'Saisir votre mot de passe';

  @override
  String get eo => 'Espéranto';

  @override
  String get errorCreatingPost => 'Une erreur est survenue lors de la création du post';

  @override
  String get errorPublishing => 'Erreur lors de la publication';

  @override
  String get errorRequired => 'Champ requis';

  @override
  String get es => 'Espagnol';

  @override
  String get et => 'Estonien';

  @override
  String get eu => 'Basque';

  @override
  String get expeditiousness => 'attente';

  @override
  String get fa => 'Persan';

  @override
  String get favorite => 'Favoris';

  @override
  String feedbackMarkAsClosed(String name) {
    return '$name a marqué la conversation comme traitée';
  }

  @override
  String feedbackMarkAsReopen(String name) {
    return '$name a réouvert la conversation';
  }

  @override
  String get feedbacks => 'Remarques';

  @override
  String get feedbackYouTag => '(Vous)';

  @override
  String get fi => 'Finnois';

  @override
  String get filter => 'Filtres';

  @override
  String get food => 'cuisine';

  @override
  String get forgot_password_explanation => 'Nous vous enverrons un lien pour réinitialiser votre mot de passe';

  @override
  String forgot_password_has_been_sent(String email) {
    return 'A l’adresse indiquée : \n $email';
  }

  @override
  String get forgot_password_sent => 'Un mail de réinitialisation vous a été envoyé';

  @override
  String get forgotPassword => 'Mot de passe oublié ?';

  @override
  String get fr => 'Français';

  @override
  String get frequency => 'Fréquence';

  @override
  String get friday => 'Vendredi';

  @override
  String get from => 'Du';

  @override
  String get fy => 'Frison';

  @override
  String get ga => 'Irlandais';

  @override
  String get gd => 'Gaélique écossais';

  @override
  String get general => 'Général';

  @override
  String get generate => 'Générer';

  @override
  String get generateListOfHashtags => 'Générer une liste de hashtags';

  @override
  String get generatePostPrefixText => 'Générer un post à propos ';

  @override
  String get generateReviewReply => 'Générer une réponse à un avis';

  @override
  String get generateReviewReplyPlaceholder => 'Bonjour, merci pour votre note ... ';

  @override
  String get giveAdvice => 'Donner un conseil';

  @override
  String get giveaway => 'Un jeu concours pendant un mois pour les abonnés qui likent le post et taguent un ami en commentaire, avec pour cadeau ';

  @override
  String get gl => 'Galicien';

  @override
  String get gmbMessagesNotAvailable => 'Google a désactivé définitivement sa fonctionnalité de messagerie, vous ne pouvez plus envoyer de message dans cette conversation';

  @override
  String get google => 'Google';

  @override
  String get googleConnectedButNoPosts => 'Vous n’avez pas connecté la Malouapp à votre compte Google... mais vous pouvez déjà créer des brouillons !';

  @override
  String get gu => 'Gujarati';

  @override
  String get ha => 'Haoussa';

  @override
  String get hashtagAdvice1 => 'Entre 3 et 6 hashtags';

  @override
  String get hashtagAdvice2 => '3 catégories ou +\nVariez les hashtags d\'un post à l\'autre';

  @override
  String get hashtags => 'Hashtags';

  @override
  String get hashtagsCategories => 'Catégories de hashtags';

  @override
  String get hashtagsDisplay => 'Affichage des hashtags';

  @override
  String get hashtagsRequiredForAI => 'Vous n\'avez pas de liste de hashtags.';

  @override
  String get haw => 'Hawaïen';

  @override
  String get he => 'Hébreu';

  @override
  String get hi => 'Hindi';

  @override
  String get hide_my_keywords => 'Cacher mes autres mots-clés';

  @override
  String get hmn => 'Hmong';

  @override
  String get home => 'Accueil';

  @override
  String get hour => 'Heure';

  @override
  String get hr => 'Croate';

  @override
  String get ht => 'Créole haïtien';

  @override
  String get hu => 'Hongrois';

  @override
  String get hy => 'Arménien';

  @override
  String get hygiene => 'hygiène';

  @override
  String get ia => '(IA)';

  @override
  String get iaGenerationExampleHintText => 'Exemple : Générer un post à propos de notre nouveau plat de burrata à a pistache avec un ton formel';

  @override
  String get id => 'Indonésien';

  @override
  String get ig => 'Igbo';

  @override
  String get includeHashtagOfTheRestaurant => 'Inclure le hashtag de l\'établissement';

  @override
  String get includeReviewsWithAutoAnswer => 'Inclure les avis avec réponse auto.';

  @override
  String get inFirstComment => 'En premier commentaire';

  @override
  String get inLegend => 'Dans la légende';

  @override
  String get invalidCredentials => 'Veuillez vérifier vos identifiants\nde connexion';

  @override
  String get invalidEmailMessage => 'Veuillez saisir une adresse email valide';

  @override
  String get invalidLink => 'Lien invalide';

  @override
  String get invalidPassword => 'Veuillez saisir votre mot de passe';

  @override
  String get it => 'Italien';

  @override
  String get iw => 'Hébreu';

  @override
  String get ja => 'Japonais';

  @override
  String get jw => 'Javanais';

  @override
  String get ka => 'Géorgien';

  @override
  String get keywords => 'Mots-clés';

  @override
  String get keywords_score => 'Score ';

  @override
  String get keywordsAdvices => 'Conseils d\'utilisation';

  @override
  String get keywordScoreAdviceResponseTime => 'Réponse en moins de 72h';

  @override
  String get keywordScoreAdviceRestaurantName => 'Inclure le nom de l\'etablissement';

  @override
  String get keywordScoreAdviceReviewerName => 'Inclure le nom du client';

  @override
  String get keywordScoreAdviceSimpleBricksNumber => 'Utiliser des mots-clés : ';

  @override
  String get keywordScoreAdviceSimpleBricksVariety => 'Variété des mots-clés : ';

  @override
  String get keywordScoreAdviceSimpleBricksVarietyTip => '(Essayez des spécialités, une localisation ou une categorie)';

  @override
  String get keywordScoreAdviceSorry => 'Mot d\'excuse';

  @override
  String get keywordScoreAdviceSorryTip => '(navré, désolé, regret ...)';

  @override
  String get keywordScoreAdviceTextLength => 'Nombre de caractères minimum :';

  @override
  String get kk => 'Kazakh';

  @override
  String get km => 'Khmer';

  @override
  String get kn => 'Kannada';

  @override
  String get ko => 'Coréen';

  @override
  String get ku => 'Kurde (kurmanji)';

  @override
  String get ky => 'Kirghize';

  @override
  String get la => 'Latin';

  @override
  String get landsquape => 'Paysage';

  @override
  String get languages => 'Langues';

  @override
  String get lastSevenDays => '7 derniers jours';

  @override
  String get lastSixmonths => '6 derniers mois';

  @override
  String get lastThirtyDays => '\n(30 derniers jours)';

  @override
  String get lastThirtyDays_date_range => '30 derniers jours';

  @override
  String get lastThreeMonths => '3 derniers mois';

  @override
  String get lastTwelveMonths => '12 derniers mois';

  @override
  String get later => 'Plus tard';

  @override
  String get lb => 'Luxembourgeois';

  @override
  String get learnMore => 'En savoir plus';

  @override
  String get leave => 'Quitter ?';

  @override
  String get leaveAndLoseModifications => 'Quitter et perdre mes modifications';

  @override
  String get legalMentions => 'Mentions légales';

  @override
  String get legalMentions1 => 'I- Des dispositions clairement énoncées en aval du lancement d\'une application.';

  @override
  String get legalMentions1Details => 'Lancer une application sur un marché virtuel suppose en aval la régularisation juridique des mentions légales à afficher pour l\'utilisateur, des conditions générales d\'utilisation.';

  @override
  String get legalMentionsA => 'A- Les mentions légales à respecter.';

  @override
  String get legalMentionsADetails => 'Avant même de songer à l\'idée de lancer une application sur le marché, certaines procédures juridiques doivent être respectées : de ce fait, leur préparation doit s\'effectuer avant toute commercialisation du produit. Il en va dans un premier temps des mentions légales. Ces dernières sont obligatoires puisqu\'elles permettent à l\'utilisateur de l\'application de disposer des coordonnées de l\'éditeur de l\'application, à savoir son développeur ou la personne qui est commanditaire du développement d\'une application, par le biais d\'un standard ouvert. Ces mentions légales devront ainsi être disponibles pour l\'utilisateur par le biais de son descriptif sans qu\'il ait eu à télécharger, de manière payante ou gratuite, cette dernière. Le non-respect de cette obligation des mentions légales fait encourir à l\'éditeur de l\'application une peine d\'un an d\'emprisonnement et de 75 000 euros d\'amende. Les informations à indiquer sont les suivantes. Dans un premier temps, l\'éditeur de l\'application doit y faire figurer sa dénomination sociale, son siège social, son numéro de téléphone, son numéro d\'inscription au registre du commerce et des sociétés ou au répertoire des métiers, le montant de son capital social, le nom du directeur et celui du codirecteur de l\'application. De plus, les mentions légales doivent faire figurer le nom, la dénomination sociale ou la raison sociale, l\'adresse et le numéro de téléphone';

  @override
  String get legalMentionsTitle => 'Mentions légales';

  @override
  String get legendLabel => 'Légende *';

  @override
  String get linkOfOffer => 'Lien de l\'offre';

  @override
  String get lo => 'Laotien';

  @override
  String get loginPageSubtitle => 'Le digital au service de la croissance des établissements';

  @override
  String get lt => 'Lituanien';

  @override
  String get lv => 'Letton';

  @override
  String get mainHashtag => 'Hashtag de marque';

  @override
  String get malouGallery => 'Galerie Malou';

  @override
  String get manageNotifs => 'Gérez vous-même les notifications que vous recevez 🔔.';

  @override
  String get mandatoryField => 'Champ obligatoire';

  @override
  String manyPlatformDisconnectedWarning(String platform) {
    return '$platform ont été déconnectés';
  }

  @override
  String get markedAsRead => 'Conversation marquée comme lue';

  @override
  String get markedAsUnread => 'Conversation marquée comme non lue';

  @override
  String get markFeebackMessagesAsClosed => 'Marquer la conversation comme traitée';

  @override
  String get mediasAccessDescription => 'Ceci vous permettra d’envoyer des photos et vidéos depuis votre téléphone.';

  @override
  String get mediasTab => 'Médias';

  @override
  String get mentionnedYouInStory => 'Vous a mentionné dans une story';

  @override
  String get messageNotAvailable => 'Message indisponible';

  @override
  String get messages => 'Messages';

  @override
  String get messaging => 'Messagerie';

  @override
  String get mg => 'Malgache';

  @override
  String get mi => 'Maori';

  @override
  String get midnight => 'minuit';

  @override
  String minOccurences(int minOccurences) {
    return '(minimum $minOccurences)';
  }

  @override
  String get mk => 'Macédonien';

  @override
  String get ml => 'Malayalam';

  @override
  String get mn => 'Mongol';

  @override
  String get modifiedOn => 'Modifié le';

  @override
  String get monday => 'Lundi';

  @override
  String get mr => 'Marathi';

  @override
  String get ms => 'Malais';

  @override
  String get mt => 'Maltais';

  @override
  String get my => 'Birman (birmanie)';

  @override
  String get myFirstName => 'Mon prénom';

  @override
  String get myMedias => 'Pellicule';

  @override
  String get myName => 'Mon nom';

  @override
  String get myTemplates => 'Mes modèles de réponses';

  @override
  String get name => 'name';

  @override
  String nbReplies(Object nbReplies, Object nbReviews) {
    return 'Réponse ($nbReplies/$nbReviews)';
  }

  @override
  String get ne => 'Népalais';

  @override
  String get needIdeas => 'Idées';

  @override
  String get newReview => 'Nouvel avis';

  @override
  String get nl => 'Néerlandais';

  @override
  String get no => 'Norvégien';

  @override
  String get no_results => 'Aucun résultat';

  @override
  String get noAccessToMedias => 'La “MalouApp” n’a pas accès à vos médias';

  @override
  String get noAccountQuestion => 'Vous n\'avez pas de compte ?';

  @override
  String get noFeedbacksYet => 'Vous n\'avez pas de remarques\npour le moment';

  @override
  String get noInternetConnection => 'Vous n’êtes pas connecté(e) à internet';

  @override
  String get noKeywordsYet => 'Vous ne possédez pas encore de mots-clés';

  @override
  String get noMediaInGallery => 'Vous n\'avez pas encore de médias dans votre galerie';

  @override
  String get noMessagesYet => 'Vous n\'avez pas encore de message';

  @override
  String get noMoreItemsInTheList => 'Remontez il n\'y a plus rien à voir';

  @override
  String get noMoreScheduledPosts => 'Aucun post programmé';

  @override
  String get noon => 'midi';

  @override
  String get nop => 'Non';

  @override
  String get noRestaurantForYourResearch => 'Aucun établissement ne correspond à\nvotre recherche.';

  @override
  String get noRestaurantQuestion => 'Bah alors, on a pas détablissement ?';

  @override
  String get noResults => 'Aucun résultat';

  @override
  String get noReviews => 'Vous n\'avez pas d\'avis client';

  @override
  String get noSocialNetworksConnected => 'Vous n’avez pas connecté la Malouapp à vos réseaux sociaux... mais vous pouvez déjà créer des brouillons !';

  @override
  String get noSocialPosts => 'Pas de post référencé par ici';

  @override
  String get noStories => 'Aucune story trouvée.. N\'ayez pas peur, lancez-vous !';

  @override
  String get notAnsweredMessages => 'Messages non répondus';

  @override
  String get notAnsweredReviews => 'Avis non répondus';

  @override
  String get noTemplates => 'Vous n\'avez pas de modèle de réponse';

  @override
  String noTemplatesDetails(Object entity) {
    return 'Commencez par en ajouter sur la MalouApp pour répondre plus vite à vos $entity.';
  }

  @override
  String get notEnoughCreditToMakeAiApiCall => 'Vous n’avez plus de crédit IA.';

  @override
  String get notifCenter => 'Centre de notifications';

  @override
  String get notificationsDisabledDescription => 'Activer les notifications vous permettra d’être averti(e) lorsque vous avez une action à faire.';

  @override
  String get notificationsDisabledTitle => 'Notifications désactivées';

  @override
  String get now => 'Maintenant';

  @override
  String numberOfHashtags(int hashtagsLength) {
    return 'Nombre de hashtag: $hashtagsLength/10';
  }

  @override
  String get ny => 'Chichewa';

  @override
  String get object => 'Objet';

  @override
  String get onceADay => 'Une fois par jour';

  @override
  String get onceADayWithParenthesis => ' (1 fois / jour)';

  @override
  String get onlyAvailableInstagram => 'Uniquement disponible sur Instagram';

  @override
  String get onlyAvailableOnMapstr => 'Uniquement disponible sur Mapstr';

  @override
  String get onlyAvailableOnMeta => 'Disponible uniquement sur Facebook et Instagram';

  @override
  String get onlyForGoogleReviews => 'Concerne seulement les avis google, les autres plateformes ne sont récupérés qu’une fois par jour.';

  @override
  String get onlyVisibleByAdmin => 'Visible seulement par les admins';

  @override
  String get onlyVisibleByAdmins => 'Visible uniquement par les admins';

  @override
  String open_day(String closeTime, String openDay, String openTime) {
    return 'ouvert le $openDay de ${openTime}h à ${closeTime}h, ';
  }

  @override
  String open_day_more(String closeTime2, String openTime2) {
    return 'et de ${openTime2}h à ${closeTime2}h, ';
  }

  @override
  String open_days(String closeTime, String endDay, String openTime, String startDay) {
    return 'ouvert du $startDay au $endDay de ${openTime}h à ${closeTime}h, ';
  }

  @override
  String get optimize => 'Optimiser';

  @override
  String get or => 'Odia';

  @override
  String get order => 'Commander';

  @override
  String orderDetails(String amount, String orderCount) {
    return 'Commande de $amount pour la $orderCount fois';
  }

  @override
  String get oupsSeeNothing => 'Oups, on n\'y voit rien';

  @override
  String get overallExperience => 'general';

  @override
  String get pa => 'Pendjabi';

  @override
  String get participate => 'Participer';

  @override
  String get password => 'Mot de passe';

  @override
  String get period => 'Période';

  @override
  String get pl => 'Polonais';

  @override
  String platformDisconnectedWarning(String platform) {
    return '$platform a été déconnecté';
  }

  @override
  String get platformsDisconnected => 'Vous n\'avez pas de plateformes connectées';

  @override
  String get pleaseCreateHashtagsFromYourSettings => 'Veuillez en créer une dans les paramètres de votre restaurant.';

  @override
  String get portrait => 'Portrait';

  @override
  String get postCreated => 'Post créé avec succès';

  @override
  String get postDeleted => 'Votre post a bien été supprimé';

  @override
  String get postNotFound => 'Le post est introuvable';

  @override
  String get postOnPlatforms => 'Poster sur les plateformes';

  @override
  String get posts => 'Posts';

  @override
  String get postsSyncError => 'Erreur de synchronisation';

  @override
  String get postsSyncSuccess => 'La synchronisation a été réalisée avec succès !';

  @override
  String get postSuccessfullyModified => 'Post modifié avec succès';

  @override
  String get postsUpdated => 'Posts mis à jour';

  @override
  String get postTopic => 'Type de post';

  @override
  String get postTopicEvent => 'Evènement';

  @override
  String get postTopicNew => 'Nouveauté';

  @override
  String get postTopicOffer => 'Offre';

  @override
  String get presentAPlace => 'Présenter un lieu ';

  @override
  String get price => 'prix';

  @override
  String get probablyDeleted => 'Il a sûrement été supprimé';

  @override
  String get processing => 'En cours de traitement ...';

  @override
  String get promoOrCoupon => 'Code promo ou coupon';

  @override
  String get proposals => 'Propositions';

  @override
  String get ps => 'Pachto';

  @override
  String get pt => 'Portugais';

  @override
  String get publicationDate => 'Date de publication';

  @override
  String get publicationError => 'Erreur de publication';

  @override
  String get publish => 'Publier';

  @override
  String get published => 'Publiés';

  @override
  String get publishing => 'Post en cours de publication...';

  @override
  String get purchaseAIPlus => 'Passez à l’option IA Plus pour accéder à L’IA en illimité';

  @override
  String get pursue => 'Continuer ?';

  @override
  String get pursueEdit => 'Poursuivre mes modifications';

  @override
  String get question => 'Une question ?';

  @override
  String get questionsAnswers => 'Foire aux questions';

  @override
  String get read => 'Lu';

  @override
  String receivedAMedia(String mediaType) {
    return 'Vous a envoyé une $mediaType';
  }

  @override
  String get receivedAStory => 'Vous avez reçu une story';

  @override
  String get receivedReviews => 'Avis reçus';

  @override
  String get recommandations => 'Recommandations ';

  @override
  String get recommandationsForUse => 'Conseils d\'utilisation';

  @override
  String reconnectPlatform(String missingPlatform) {
    return 'Reconnecter $missingPlatform';
  }

  @override
  String get reconnectToInternet => 'Reconnectez-vous à un réseau afin d’accéder à cette page.';

  @override
  String get refreshed => 'Hop c\'est rafrachi !';

  @override
  String get refreshPage => 'Rafraichir la page';

  @override
  String remainingAICredits(int nbCredits) {
    return '$nbCredits crédits restants';
  }

  @override
  String remainingAICreditsFormField(int nbCredits) {
    return '$nbCredits/500';
  }

  @override
  String remarksNumber(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count remarques',
      one: '1 remarque',
    );
    return '$_temp0';
  }

  @override
  String get reply => 'Répondre';

  @override
  String get replyByMail => 'Répondre par mail';

  @override
  String get restaurantAddress => 'Adresse';

  @override
  String get restaurantMenu => 'Menu';

  @override
  String get restaurantName => 'Nom Établissement';

  @override
  String get restaurantOpeningHours => 'Horaires d\'ouverture';

  @override
  String get restaurantPhone => 'Téléphone';

  @override
  String get restaurantWebsite => 'Site web';

  @override
  String get retry => 'Réessayer';

  @override
  String get review => 'Avis client';

  @override
  String get reviewAnalyseFailed => 'Cet avis n\'a pas pu être analysé';

  @override
  String get reviewAnalysesRunning => 'L\'analyse est en cours';

  @override
  String get reviewAnalysesUnknownStatus => 'Status inconnu pour l\'analyse sémantique';

  @override
  String get reviewAnalyzedError => 'Erreur pendant l\'analyse de la review';

  @override
  String get reviewAnalyzedNoResult => 'Cet avis n\'a pas de catégorie associé à un sentiment.';

  @override
  String get reviewAnalyzedTooOld => 'L\'avis est trop ancien pour être analysé.';

  @override
  String get reviewDoesNotExistAnymore => 'Il semblerait que cet avis ait été supprimé par l\'utilisateur.';

  @override
  String get reviewFrom => 'De la part de ...';

  @override
  String get reviewNotYetAnalyzed => 'Cet avis n\'a pas encore été analysé, vous pourrez voir le résultat dans un délai maximal de 24h.';

  @override
  String get reviewReplyError => 'Oups ! Votre réponse n’a pas pu être envoyée, veuillez réessayer ultérieurement.';

  @override
  String get reviewReplySentSuccessfully => 'Votre réponse a bien été envoyée ! ';

  @override
  String get reviews => 'Avis clients';

  @override
  String get reviewScores => 'Notes des avis concernés';

  @override
  String get reviewsRating => 'Note moyenne des avis';

  @override
  String get reviewsSyncError => 'Error de synchronisation pour les plateformes : ';

  @override
  String get reviewsSyncSuccess => 'Synchronisation des avis terminée avec succès';

  @override
  String get reviewTemplates => 'Modèles d\'avis';

  @override
  String get ro => 'Roumain';

  @override
  String get ru => 'Russe';

  @override
  String get saturday => 'Samedi';

  @override
  String get savedLinks => 'Liens enregistrés';

  @override
  String get saveDraft => 'Enregistrer en brouillon';

  @override
  String get saveModifications => 'Enregistrer les modifications';

  @override
  String get savePost => 'Enregistrer le post';

  @override
  String get schedule => 'Programmer';

  @override
  String get scheduled => 'Programmés';

  @override
  String get sd => 'Sindhi';

  @override
  String get search => 'Rechercher';

  @override
  String get searchPlace => 'Rechercher un lieu';

  @override
  String get see => 'Voir';

  @override
  String see_other_keywords(int noRevelantBricksLength) {
    return 'Voir mes autres mots-clés $noRevelantBricksLength';
  }

  @override
  String get seeFeed => 'Voir le feed';

  @override
  String get seeList => 'Voir la liste';

  @override
  String get seeListOfHashtags => 'Voir la liste de mes hashtags';

  @override
  String get seeMenu => 'Lire le menu';

  @override
  String seeOriginalReviewText(String lang) {
    return 'Voir l\'original (en $lang)';
  }

  @override
  String get semanticAnalyse => 'Analyse sémantique';

  @override
  String get sendError => 'Erreur d\'envoi.';

  @override
  String get sendPromotion => 'Envoyer une compensation';

  @override
  String sendPromotionOf(String amount) {
    return 'Envoyer une compensation : $amount';
  }

  @override
  String sentAMedia(String mediaType) {
    return 'Vous avez envoyé une $mediaType';
  }

  @override
  String get service => 'service';

  @override
  String get set_email => 'Entrez votre email';

  @override
  String get settings => 'Paramètres';

  @override
  String get showInFeed => 'Afficher dans le feed Instagram';

  @override
  String get si => 'Cinghalais';

  @override
  String get signOut => 'Me déconnecter';

  @override
  String get signup => 'S\'inscrire';

  @override
  String get sk => 'Slovaque';

  @override
  String get sl => 'Slovène';

  @override
  String get sm => 'Samoan';

  @override
  String get sn => 'Shona';

  @override
  String get so => 'Somali';

  @override
  String get socialNetworks => 'RS';

  @override
  String get somethingWentWrong => 'Oops, something went wrong';

  @override
  String get sq => 'Albanais';

  @override
  String get square => 'Carré';

  @override
  String get sr => 'Serbe';

  @override
  String get st => 'Sesotho';

  @override
  String get stories => 'Stories';

  @override
  String get storyDeleted => 'Votre story a bien été supprimé';

  @override
  String get storyExpired => 'Story expirée';

  @override
  String get su => 'Soudanais';

  @override
  String get sunday => 'Dimanche';

  @override
  String get sv => 'Suédois';

  @override
  String get sw => 'Swahili';

  @override
  String get ta => 'Tamoul';

  @override
  String get tagAccounts => 'Taguer des comptes';

  @override
  String get te => 'Télougou';

  @override
  String get tg => 'Tadjik';

  @override
  String get th => 'Thaï';

  @override
  String get the => 'le';

  @override
  String get theEvent => 'l\'événement';

  @override
  String get theEventPostGenerationAI => 'de l\'événement ';

  @override
  String get theOffer => 'l\'offre';

  @override
  String get thursday => 'Jeudi';

  @override
  String get timeLimitReplyReviewExceeded => 'Vous avez dépassé la date limite pour répondre à l’avis';

  @override
  String get titleLabel => 'Titre *';

  @override
  String get tl => 'Filipino';

  @override
  String get to => 'Au';

  @override
  String get toCheck => 'À traiter';

  @override
  String get todo => 'À faire';

  @override
  String get tooManyNotif => 'Trop de notifications, pas assez ?';

  @override
  String totem(String chipName) {
    return 'Totem $chipName';
  }

  @override
  String get totemDisplayName => 'Totem';

  @override
  String get tr => 'Turc';

  @override
  String get translate => 'Traduire';

  @override
  String get translated => 'Traduit -';

  @override
  String translateInLang(String lang) {
    return 'Traduire en $lang';
  }

  @override
  String get try_another_search => 'Essayez une autre recherche';

  @override
  String get tryAgain => 'Réessayer';

  @override
  String get tryAgainLater => 'Please try again later';

  @override
  String get tuesday => 'Mardi';

  @override
  String get typeAndLocalisation => 'Type et lieu ';

  @override
  String get typeAndLocalisationExample => '(ex :  ItalienParis14)';

  @override
  String get typeYourLang => 'Tapez votre langue';

  @override
  String get typeYourText => 'Tapez votre texte';

  @override
  String get ug => 'Ouïghour';

  @override
  String get uk => 'Ukrainien';

  @override
  String get unAnswered => 'Non répondus';

  @override
  String get unansweredReviews => 'Avis non répondus';

  @override
  String get unarchive => 'Désarchiver';

  @override
  String get unauthorized => 'Vous n\'avez pas les autorisations pour effectuer cette action.';

  @override
  String get unavailableMessage => 'Message indisponible';

  @override
  String get undetermined => 'Indéterminé';

  @override
  String get unknownError => 'Erreur inconnue';

  @override
  String get unread => 'Non lu';

  @override
  String get unreadMessages => 'Messages non lus';

  @override
  String get unsupportedAttachment => 'Le format de la pièce jointe n\'est pas supporté.';

  @override
  String get updateFeedbackMessageError => 'Erreur lors de la mise à jour du message, veuillez réessayer';

  @override
  String get ur => 'Ourdou';

  @override
  String get urgent => 'Urgent';

  @override
  String get useImageForPostGeneration => 'Utiliser l\'image pour générer la legende';

  @override
  String get uz => 'Ouzbek';

  @override
  String get validate => 'Valider';

  @override
  String get varietyOfHashtags => 'Variété des hashtags';

  @override
  String get vi => 'Vietnamien';

  @override
  String get video => 'Vidéo';

  @override
  String get videosForbiddenOnMapstr => 'Les vidéos ne sont pas autorisées sur Mapstr';

  @override
  String get videosInCarrouselForbiddenOnFacebook => 'Vous ne pouvez pas publier de carrousel contenant des vidéos sur Facebook';

  @override
  String get videosOnGoogleForbiddenMessage => 'Vous ne pouvez pas publier de vidéos sur Google';

  @override
  String get videoTooShortMessage => 'Vous ne pouvez pas publier de vidéos de moins de 3 seconds';

  @override
  String get waitingForImageAnalysis => 'En attente de l\'analyse de l\'image';

  @override
  String get we_are => 'Nous sommes ';

  @override
  String get wednesday => 'Mercredi';

  @override
  String get whatsNew => 'Quoi de nouveau ?';

  @override
  String get writeAnswerReview => 'Écrire votre réponse';

  @override
  String get writeLegendHere => 'Ecrivez votre légende ici ou ';

  @override
  String get writeMessage => 'Écrire un message';

  @override
  String get writePostWithAi => 'Écrire un post avec l\'IA';

  @override
  String get writeTitleHere => 'Ecrivez votre titre ici...';

  @override
  String get writeYourFeedback => 'Écrire une remarque';

  @override
  String get xh => 'Xhosa';

  @override
  String get year => '12 derniers mois';

  @override
  String get yi => 'Yiddish';

  @override
  String get yo => 'Yoruba';

  @override
  String get youAnsweredAStory => 'Vous avez répondu à une story';

  @override
  String get youCanSelectOnlyOne => 'Vous ne pouvez séléctionner qu\'une seule image';

  @override
  String get youCanSelectOnlyOnevideo => 'Vous ne pouvez ajouter qu\'une seule vidéo sur un Reel';

  @override
  String get youCanSelectVideosOnly => 'Vous ne pouvez séléctionner que des vidéos';

  @override
  String get youMentionnedInStory => 'Vous avez mentionné dans votre story';

  @override
  String get your_businesses => 'Vos établissements';

  @override
  String get youreDisconnectedFromInternet => 'Il semblerait que vous ne soyez plus connecté à internet.';

  @override
  String get yourHashtags => 'Vos hashtags';

  @override
  String get youWillLoseDraft => 'Si vous quittez vous perdrez ce brouillon.';

  @override
  String get youWillLoseYourModifications => 'Vous perdrez alors vos modifications.';

  @override
  String get zu => 'Zoulou';
}
