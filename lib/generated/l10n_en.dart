import 'package:intl/intl.dart' as intl;

import 'l10n.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class TranslationEn extends Translation {
  TranslationEn([String locale = 'en']) : super(locale);

  @override
  String get aboutToDeleteMessage => 'You\'re about to delete the message.';

  @override
  String get aboutToSignOut => 'You are about to sign out';

  @override
  String get activate => 'Activate';

  @override
  String get actives => 'Active';

  @override
  String get add => 'Add';

  @override
  String get addARestaurant => 'Add a restaurant';

  @override
  String get addCoupon => 'A promo code or a coupon';

  @override
  String get addDraft => 'Create a draft';

  @override
  String get addFIrstPost => 'Create my first post';

  @override
  String get addHashtags => 'Add hashtags';

  @override
  String get addImageToUse => 'Ajouter une image pour utiliser cette fonctionnalité';

  @override
  String get addInfosAboutOffer => 'Add information about the offer';

  @override
  String get addLinkToOffer => 'A link to the offer';

  @override
  String get addLocation => 'Add a location';

  @override
  String get addNow => 'Add now';

  @override
  String get addSeoPost => 'Google Post';

  @override
  String get addSocialPost => 'Social media post';

  @override
  String get addTitle => 'Add a title';

  @override
  String get addYourFirstRestaurant => 'Add your first restaurant to fully enjoy the MalouApp';

  @override
  String get aDish => 'Our new menu with ';

  @override
  String get advice => 'an advice ';

  @override
  String get af => 'Afrikaans';

  @override
  String get aIcreditsBackSoon => 'The proposed answers will be available again when your credits are renewed.';

  @override
  String get aiPostCaptionFormfieldHint => 'our perfect chocolate lava cake to finish your meal';

  @override
  String get aiPostCaptionFormfieldLabel => 'Generate a post about...';

  @override
  String get all => 'All';

  @override
  String get am => 'Amharic';

  @override
  String get analyzeReviewsNotAvailable => 'Semantic analysis is not available for this restaurant.';

  @override
  String get and => ' and';

  @override
  String get anErrorOccured => 'An error occurred';

  @override
  String get announceAnEvent => 'An upcoming event with';

  @override
  String get answeredYourStory => 'Replied to your story';

  @override
  String get aPlace => 'a place';

  @override
  String get ar => 'Arabic';

  @override
  String get archive => 'Archive';

  @override
  String get archived => 'Archived';

  @override
  String get areYouSureYouWantToDeleteThisPost => 'Do you want to delete this post?';

  @override
  String get areYouSureYouWantToDeleteThisPostPublished => 'Do you want to delete this post? It will also be removed from the platforms on which it was posted.';

  @override
  String get areYouSureYouWantToDeleteThisStory => 'Do you want to delete this story?';

  @override
  String get artificialIntelligence => 'Artificial Intelligence';

  @override
  String get askForDemo => 'Request a demo';

  @override
  String get atmosphere => 'atmosphere';

  @override
  String get authorizeCamera => 'Allow access to my camera';

  @override
  String get authorizeMedias => 'Allow access to my media';

  @override
  String get autoReply => 'Auto-reply';

  @override
  String get az => 'Azerbaijani';

  @override
  String get be => 'Belarusian';

  @override
  String get bg => 'Bulgarian';

  @override
  String get bn => 'Bengali';

  @override
  String get book => 'Book';

  @override
  String get brandAccount => 'Brand account';

  @override
  String get bs => 'Bosnian';

  @override
  String get businessName => 'Business name';

  @override
  String get buy => 'Buy';

  @override
  String get byDefault => ' (By default)';

  @override
  String get ca => 'Catalan';

  @override
  String get call => 'Call';

  @override
  String get cameraNoAccessDescription => 'This will allow you to send photos from your camera.';

  @override
  String get cameraNoAccessTitle => 'MalouApp does not have access to your camera';

  @override
  String get cancel => 'Cancel';

  @override
  String get cancelEdit => 'Cancel modifications';

  @override
  String get cannotFindYourLang => 'You cannot find your language?';

  @override
  String get cant_edit_medias_post_published => '';

  @override
  String get cantAnalyzeImage => 'Unable to analyze the image';

  @override
  String get cantAnalyzeVideos => 'Unable to analyze the videos';

  @override
  String get cantEditMediaPublished => 'You cannot modify the media of an article that has already been published.';

  @override
  String get carrouselForbiddenOnGooGle => 'You cannot publish a carousel on Google';

  @override
  String get carrouselForbiddenOnGoogleMessage => 'You cannot publish a carousel on Google';

  @override
  String get carrouselMediasLimitReachedMessage => 'You cannot select more than 10 media';

  @override
  String get ceb => 'Cebuano';

  @override
  String get changeLanguage => 'Change language';

  @override
  String get chooseActionButton => 'Choose an action button';

  @override
  String get clientName => 'Client name';

  @override
  String closed_day(String closedDay) {
    return 'closed on $closedDay, ';
  }

  @override
  String closed_days(String endDay, String startDay) {
    return 'closed from $startDay to $endDay, ';
  }

  @override
  String get co => 'Corsican';

  @override
  String get commentsTab => 'Remarques';

  @override
  String get completeCaptionToGenerateHashtagsFromAi => 'Complete the caption of your post so that the AI can generate hashtags';

  @override
  String get concernedRatings => 'Ratings of concerned reviews';

  @override
  String get concernsOnlyNegativeReviews => 'Only for negative reviews that have not been answered for more than 48hrs';

  @override
  String get conditionOffer => 'Terms of use';

  @override
  String get conditionsOfOffer => 'Terms of use';

  @override
  String get confirm => 'Confirm';

  @override
  String get connectGoogle => 'Connect my Google account';

  @override
  String get connectMyPlatforms => 'Connect my platforms';

  @override
  String get connectSocialNetworks => 'Connect my social media accounts';

  @override
  String get contactMalouOps => 'Contact your Malou reference if you can\'t find what you\'re looking for in our FAQ 🤗.';

  @override
  String get conversation => 'Messaging';

  @override
  String get conversationArchived => 'Conversation archived';

  @override
  String get conversationUnarchived => 'Conversation unarchived';

  @override
  String get courier_to_restaurant => 'Delivery';

  @override
  String get cover => 'Cover';

  @override
  String get create_reel => 'Create a reel';

  @override
  String get createCompletionError => 'An error occurred during AI generation, please try again.';

  @override
  String get createDraft => 'Create a draft';

  @override
  String get cs => 'Czech';

  @override
  String get customer_to_restaurant => 'Customer';

  @override
  String get customerReviews => 'Reviews';

  @override
  String get customized => 'Customized ';

  @override
  String get cy => 'Welsh';

  @override
  String get da => 'Danish';

  @override
  String dailyUnansweredReviewsNotificationText(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count new reviews received',
      one: '1 new review received',
    );
    return '⭐️ Psst, $_temp0';
  }

  @override
  String dailyUnreadMessagesNotificationText(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count new messages',
      one: '1 new message',
    );
    return '💬 Psst, $_temp0';
  }

  @override
  String get date => 'Date';

  @override
  String get dateOf => 'Date of';

  @override
  String get daysLeftToReply => ' days left to reply';

  @override
  String get de => 'German';

  @override
  String get delete => 'Delete';

  @override
  String get deleteFeedbackMessageError => 'Error during the deletion of the feedback, please try again.';

  @override
  String get describeDish => 'Our new menu with ';

  @override
  String get describeGiveaway => 'An upcoming event with ';

  @override
  String get descriptionTab => 'Description';

  @override
  String get displayAnalyzeReviews => 'Display semantic analysis';

  @override
  String get displayOnlyNeverPosted => 'Show only never posted media';

  @override
  String get dontForgetToAnswer => 'Don\'t forget to respond from the Malouapp.';

  @override
  String get dontLetYourCustomersWithoutAnswer => 'Don\'t leave your customers unanswered, visit the Malouapp.';

  @override
  String get draft => 'Draft';

  @override
  String get drafts => 'Drafts';

  @override
  String get duplicate => 'Duplicate';

  @override
  String get eachTime => 'Each time';

  @override
  String get edit => 'Edit';

  @override
  String get editWithAI => 'Edit with AI';

  @override
  String get el => 'Greek';

  @override
  String get email => 'Email';

  @override
  String get emailSelected => 'From email';

  @override
  String get emptyDeviceMedias => 'You don\'t have any media in your gallery yet';

  @override
  String get emptyGalleryMedias => 'You don\'t have any media in your gallery yet';

  @override
  String get emptyPosts => 'No posts here';

  @override
  String get emptySocialPosts => 'You haven\'t yet connected Malouapp to your social media accounts... but you can already create drafts';

  @override
  String get en => 'English';

  @override
  String get enableNotifications => 'Enable notifications';

  @override
  String get ended => 'Ended';

  @override
  String get enterPassword => 'Enter your password';

  @override
  String get eo => 'Esperanto';

  @override
  String get errorCreatingPost => 'Error occured while creating post';

  @override
  String get errorPublishing => 'Error during publication';

  @override
  String get errorRequired => 'Field required';

  @override
  String get es => 'Spanish';

  @override
  String get et => 'Estonian';

  @override
  String get eu => 'Basque';

  @override
  String get expeditiousness => 'wait time';

  @override
  String get fa => 'Persian';

  @override
  String get favorite => 'Favorites';

  @override
  String feedbackMarkAsClosed(String name) {
    return '$name has closed the conversation';
  }

  @override
  String feedbackMarkAsReopen(String name) {
    return '$name has reopened the conversation';
  }

  @override
  String get feedbacks => 'Feedback';

  @override
  String get feedbackYouTag => '(You)';

  @override
  String get fi => 'Finnish';

  @override
  String get filter => 'Filters';

  @override
  String get food => 'food';

  @override
  String get forgot_password_explanation => 'We will send you a link to reset your password';

  @override
  String forgot_password_has_been_sent(String email) {
    return 'To the provided address:\n$email';
  }

  @override
  String get forgot_password_sent => 'A password reset email has been sent to you';

  @override
  String get forgotPassword => 'Forgot password?';

  @override
  String get fr => 'French';

  @override
  String get frequency => 'Frequency';

  @override
  String get friday => 'Friday';

  @override
  String get from => 'from';

  @override
  String get fy => 'Frisian';

  @override
  String get ga => 'Irish';

  @override
  String get gd => 'Scots gaelic';

  @override
  String get general => 'General';

  @override
  String get generate => 'Generate';

  @override
  String get generateListOfHashtags => 'Generate a hashtag list';

  @override
  String get generatePostPrefixText => 'Generate a post about ';

  @override
  String get generateReviewReply => 'Generate a reply to a review';

  @override
  String get generateReviewReplyPlaceholder => 'Hello, thank you for your review ... ';

  @override
  String get giveAdvice => 'To give advice';

  @override
  String get giveaway => 'A one-month giveaway for subscribers who like the post and tag a friend in the comments, with a prize of ';

  @override
  String get gl => 'Galician';

  @override
  String get gmbMessagesNotAvailable => 'Google has permanently disabled its messaging feature, you can no longer send messages in this conversation';

  @override
  String get google => 'Google';

  @override
  String get googleConnectedButNoPosts => 'You haven\'t yet connected Malouapp to your Google account... but you can already create drafts!';

  @override
  String get gu => 'Gujarati';

  @override
  String get ha => 'Hausa';

  @override
  String get hashtagAdvice1 => 'Between 3 and 6 hashtags';

  @override
  String get hashtagAdvice2 => '3 or more categories\nVary the hashtags from one post to the next';

  @override
  String get hashtags => 'Hashtags';

  @override
  String get hashtagsCategories => 'Hashtag categories';

  @override
  String get hashtagsDisplay => 'Hashtag display';

  @override
  String get hashtagsRequiredForAI => 'You don\'t have a list of hashtags.';

  @override
  String get haw => 'Hawaiian';

  @override
  String get he => 'Hebrew';

  @override
  String get hi => 'Hindi';

  @override
  String get hide_my_keywords => 'Hide my other keywords';

  @override
  String get hmn => 'Hmong';

  @override
  String get home => 'Home';

  @override
  String get hour => 'Time';

  @override
  String get hr => 'Croatian';

  @override
  String get ht => 'Haitian creole';

  @override
  String get hu => 'Hungarian';

  @override
  String get hy => 'Armenian';

  @override
  String get hygiene => 'hygiene';

  @override
  String get ia => '(AI)';

  @override
  String get iaGenerationExampleHintText => 'Example : Generate a post about our new pistachio burrata dish with a formal tone';

  @override
  String get id => 'Indonesian';

  @override
  String get ig => 'Igbo';

  @override
  String get includeHashtagOfTheRestaurant => 'Include business hashtag';

  @override
  String get includeReviewsWithAutoAnswer => 'Include reviews with auto-answer';

  @override
  String get inFirstComment => 'In first comment';

  @override
  String get inLegend => 'In caption';

  @override
  String get invalidCredentials => 'Please check your login credentials';

  @override
  String get invalidEmailMessage => 'Please enter a valid email address';

  @override
  String get invalidLink => 'Invalid link';

  @override
  String get invalidPassword => 'Please enter your password';

  @override
  String get it => 'Italian';

  @override
  String get iw => 'Hebrew';

  @override
  String get ja => 'Japanese';

  @override
  String get jw => 'Javanese';

  @override
  String get ka => 'Georgian';

  @override
  String get keywords => 'Keywords';

  @override
  String get keywords_score => 'Score';

  @override
  String get keywordsAdvices => 'Keywords advices';

  @override
  String get keywordScoreAdviceResponseTime => 'Response within 72 hours';

  @override
  String get keywordScoreAdviceRestaurantName => 'Add restaurant name';

  @override
  String get keywordScoreAdviceReviewerName => 'Include client name';

  @override
  String get keywordScoreAdviceSimpleBricksNumber => 'Use keywords : ';

  @override
  String get keywordScoreAdviceSimpleBricksVariety => 'Keywords variety :';

  @override
  String get keywordScoreAdviceSimpleBricksVarietyTip => '(Use speciality, a location or a category)';

  @override
  String get keywordScoreAdviceSorry => 'Apologies';

  @override
  String get keywordScoreAdviceSorryTip => '(sorry, regret ...)';

  @override
  String get keywordScoreAdviceTextLength => 'Minimum character count:';

  @override
  String get kk => 'Kazakh';

  @override
  String get km => 'Khmer';

  @override
  String get kn => 'Kannada';

  @override
  String get ko => 'Korean';

  @override
  String get ku => 'Kurdish (kurmanji)';

  @override
  String get ky => 'Kyrgyz';

  @override
  String get la => 'Latin';

  @override
  String get landsquape => 'Landscape';

  @override
  String get languages => 'Languages';

  @override
  String get lastSevenDays => '7 last days';

  @override
  String get lastSixmonths => '6 last months';

  @override
  String get lastThirtyDays => '\n(Last 30 days)';

  @override
  String get lastThirtyDays_date_range => '30 last days';

  @override
  String get lastThreeMonths => '3 last months';

  @override
  String get lastTwelveMonths => '12 last months';

  @override
  String get later => 'Later';

  @override
  String get lb => 'Luxembourgish';

  @override
  String get learnMore => 'Learn more';

  @override
  String get leave => 'Leave?';

  @override
  String get leaveAndLoseModifications => 'Leave and lose my modifications';

  @override
  String get legalMentions => 'Legal Mentions';

  @override
  String get legalMentions1 => 'I- Clearly stated provisions following the launch of an application.';

  @override
  String get legalMentions1Details => 'Launching an application on a virtual market presupposes the legal regularization of the legal notices to be displayed for the user, the general conditions of use.';

  @override
  String get legalMentionsA => 'A- Legal mentions to be respected.';

  @override
  String get legalMentionsADetails => 'Even before considering the idea of launching an application on the market, certain legal procedures must be respected: therefore, their preparation must be carried out before any marketing of the product. This is the case first of all with legal notices. These are mandatory because they allow the application user to have the contact details of the application\'s publisher, namely its developer or the person who is the sponsor of the development of an application, through an open standard. These legal notices must thus be available to the user through its description without having to download, in a paid or free manner, the latter. Failure to comply with this obligation of legal notices exposes the application publisher to a penalty of one year\'s imprisonment and a fine of 75,000 euros. The information to be indicated is as follows. First, the application\'s publisher must include its corporate name, registered office, telephone number, registration number with the trade and companies register or directory of trades, the amount of its share capital, the name of the director and that of the co-director of the application. In addition, the legal notices must include the name, corporate name or business name, address and telephone number';

  @override
  String get legalMentionsTitle => 'Legal Mentions';

  @override
  String get legendLabel => 'Caption *';

  @override
  String get linkOfOffer => 'Link of the offer';

  @override
  String get lo => 'Lao';

  @override
  String get loginPageSubtitle => 'Digital at the service of restaurant growth';

  @override
  String get lt => 'Lithuanian';

  @override
  String get lv => 'Latvian';

  @override
  String get mainHashtag => 'Brand hashtag';

  @override
  String get malouGallery => 'Malou Gallery';

  @override
  String get manageNotifs => 'Manage the notifications you receive yourself 🔔.';

  @override
  String get mandatoryField => 'Required field';

  @override
  String manyPlatformDisconnectedWarning(String platform) {
    return '$platform have been disconnected';
  }

  @override
  String get markedAsRead => 'Conversation marked as read';

  @override
  String get markedAsUnread => 'Conversation marked as unread';

  @override
  String get markFeebackMessagesAsClosed => 'Mark conversation as closed';

  @override
  String get mediasAccessDescription => 'This will allow you to send photos and videos from your phone.';

  @override
  String get mediasTab => 'Media';

  @override
  String get mentionnedYouInStory => 'Mentioned you in a story';

  @override
  String get messageNotAvailable => 'Message not available';

  @override
  String get messages => 'Messages';

  @override
  String get messaging => 'Messaging';

  @override
  String get mg => 'Malagasy';

  @override
  String get mi => 'Maori';

  @override
  String get midnight => 'midnight';

  @override
  String minOccurences(int minOccurences) {
    return '(minimum $minOccurences)';
  }

  @override
  String get mk => 'Macedonian';

  @override
  String get ml => 'Malayalam';

  @override
  String get mn => 'Mongolian';

  @override
  String get modifiedOn => 'Modified on';

  @override
  String get monday => 'Monday';

  @override
  String get mr => 'Marathi';

  @override
  String get ms => 'Malay';

  @override
  String get mt => 'Maltese';

  @override
  String get my => 'Myanmar (burmese)';

  @override
  String get myFirstName => 'My first name';

  @override
  String get myMedias => 'My Media';

  @override
  String get myName => 'My name';

  @override
  String get myTemplates => 'My templates';

  @override
  String get name => 'name';

  @override
  String nbReplies(Object nbReplies, Object nbReviews) {
    return 'Reply ($nbReplies/$nbReviews)';
  }

  @override
  String get ne => 'Nepali';

  @override
  String get needIdeas => 'Need ideas?';

  @override
  String get newReview => 'New review';

  @override
  String get nl => 'Dutch';

  @override
  String get no => 'Norwegian';

  @override
  String get no_results => 'No results';

  @override
  String get noAccessToMedias => 'The “MalouApp” does not have access to your media';

  @override
  String get noAccountQuestion => 'Don\'t have an account?';

  @override
  String get noFeedbacksYet => 'No feedbacks';

  @override
  String get noInternetConnection => 'You are not connected to the internet';

  @override
  String get noKeywordsYet => 'You don\'t have configured any keywords yet';

  @override
  String get noMediaInGallery => 'You don\'t have any media in your gallery yet';

  @override
  String get noMessagesYet => 'You don\'t have any messages yet';

  @override
  String get noMoreItemsInTheList => 'Scroll up, there\'s nothing else to see';

  @override
  String get noMoreScheduledPosts => 'No post scheduled';

  @override
  String get noon => 'noon';

  @override
  String get nop => 'Non';

  @override
  String get noRestaurantForYourResearch => 'No restaurant matches your search.';

  @override
  String get noRestaurantQuestion => 'Well, don\'t you have an restaurant?';

  @override
  String get noResults => 'No results';

  @override
  String get noReviews => 'You don\'t have any reviews';

  @override
  String get noSocialNetworksConnected => 'You haven\'t yet connected Malouapp to your social media accounts... but you can already create drafts!';

  @override
  String get noSocialPosts => 'No posts here';

  @override
  String get noStories => 'No story found... Don\'t be afraid, go for it!';

  @override
  String get notAnsweredMessages => 'Unanswered messages';

  @override
  String get notAnsweredReviews => 'Unanswered reviews';

  @override
  String get noTemplates => 'You don\'t have any templates yet';

  @override
  String noTemplatesDetails(Object entity) {
    return 'Start by adding some on the MalouApp to respond faster to your $entity.';
  }

  @override
  String get notEnoughCreditToMakeAiApiCall => 'You no longer have enough credit to carry out this action.';

  @override
  String get notifCenter => 'Notification center';

  @override
  String get notificationsDisabledDescription => 'Enabling notifications will allow you to be alerted when you have an action to take.';

  @override
  String get notificationsDisabledTitle => 'Notifications disabled';

  @override
  String get now => 'Now';

  @override
  String numberOfHashtags(int hashtagsLength) {
    return 'Number of hashtag: $hashtagsLength/10';
  }

  @override
  String get ny => 'Chichewa';

  @override
  String get object => 'Object';

  @override
  String get onceADay => 'Once a day';

  @override
  String get onceADayWithParenthesis => ' (1 time / day)';

  @override
  String get onlyAvailableInstagram => 'Only available on Instagram';

  @override
  String get onlyAvailableOnMapstr => 'Only available on Mapstr';

  @override
  String get onlyAvailableOnMeta => 'Available only on Facebook and Instagram';

  @override
  String get onlyForGoogleReviews => 'Only concerns google reviews, other platforms are only retrieved once a day.';

  @override
  String get onlyVisibleByAdmin => 'Visible only by admins';

  @override
  String get onlyVisibleByAdmins => 'Visible only by admins';

  @override
  String open_day(String closeTime, String openDay, String openTime) {
    return 'open on $openDay from $openTime to $closeTime, ';
  }

  @override
  String open_day_more(String closeTime2, String openTime2) {
    return 'and from $openTime2 AM to $closeTime2 PM, ';
  }

  @override
  String open_days(String closeTime, String endDay, String openTime, String startDay) {
    return 'open from $startDay to $endDay from $openTime to $closeTime, ';
  }

  @override
  String get optimize => 'Optimize';

  @override
  String get or => 'Odia';

  @override
  String get order => 'Order';

  @override
  String orderDetails(String amount, String orderCount) {
    return 'Order of $amount for the $orderCount time';
  }

  @override
  String get oupsSeeNothing => 'Oops, we can\'t see anything';

  @override
  String get overallExperience => 'overall';

  @override
  String get pa => 'Punjabi';

  @override
  String get participate => 'Join';

  @override
  String get password => 'Password';

  @override
  String get period => 'Period';

  @override
  String get pl => 'Polish';

  @override
  String platformDisconnectedWarning(String platform) {
    return '$platform has been disconnected';
  }

  @override
  String get platformsDisconnected => 'You have no connected platforms';

  @override
  String get pleaseCreateHashtagsFromYourSettings => 'Please create at least one in your restaurant settings.';

  @override
  String get portrait => 'Portrait';

  @override
  String get postCreated => 'Post created successfully';

  @override
  String get postDeleted => 'Post deleted successfully';

  @override
  String get postNotFound => 'Post not found';

  @override
  String get postOnPlatforms => 'Post on platforms';

  @override
  String get posts => 'Posts';

  @override
  String get postsSyncError => 'Synchronization error';

  @override
  String get postsSyncSuccess => 'Synchronization completed successfully';

  @override
  String get postSuccessfullyModified => 'Post modifié avec succès';

  @override
  String get postsUpdated => 'Posts updated successfully';

  @override
  String get postTopic => 'Type of post';

  @override
  String get postTopicEvent => 'Event';

  @override
  String get postTopicNew => 'New';

  @override
  String get postTopicOffer => 'Offer';

  @override
  String get presentAPlace => 'Present a place ';

  @override
  String get price => 'price';

  @override
  String get probablyDeleted => 'Probably deleted';

  @override
  String get processing => 'Processing ...';

  @override
  String get promoOrCoupon => 'A discount code or coupon';

  @override
  String get proposals => 'Propositions';

  @override
  String get ps => 'Pashto';

  @override
  String get pt => 'Portuguese';

  @override
  String get publicationDate => 'Publication date';

  @override
  String get publicationError => 'Publication error';

  @override
  String get publish => 'Publish';

  @override
  String get published => 'Published';

  @override
  String get publishing => 'Post in progress...';

  @override
  String get purchaseAIPlus => 'Upgrade to the AI Plus option for unlimited access to AI';

  @override
  String get pursue => 'Continue?';

  @override
  String get pursueEdit => 'Pursue edit';

  @override
  String get question => 'Any questions?';

  @override
  String get questionsAnswers => 'FAQ';

  @override
  String get read => 'Read';

  @override
  String receivedAMedia(String mediaType) {
    return 'You received a $mediaType';
  }

  @override
  String get receivedAStory => 'You received a story';

  @override
  String get receivedReviews => 'Received reviews';

  @override
  String get recommandations => 'Recommendations';

  @override
  String get recommandationsForUse => 'Usage tips';

  @override
  String reconnectPlatform(String missingPlatform) {
    return 'Reconnect $missingPlatform';
  }

  @override
  String get reconnectToInternet => 'Reconnect to a network to access this page.';

  @override
  String get refreshed => 'Refreshed !';

  @override
  String get refreshPage => 'Refresh the page';

  @override
  String remainingAICredits(int nbCredits) {
    return '$nbCredits credits remaining';
  }

  @override
  String remainingAICreditsFormField(int nbCredits) {
    return '$nbCredits/500';
  }

  @override
  String remarksNumber(int count) {
    return '$count feedbacks';
  }

  @override
  String get reply => 'Reply';

  @override
  String get replyByMail => 'Reply by email';

  @override
  String get restaurantAddress => 'Address';

  @override
  String get restaurantMenu => 'Menu';

  @override
  String get restaurantName => 'Restaurant name';

  @override
  String get restaurantOpeningHours => 'Opening hours';

  @override
  String get restaurantPhone => 'Phone';

  @override
  String get restaurantWebsite => 'Website';

  @override
  String get retry => 'Retry';

  @override
  String get review => 'Review';

  @override
  String get reviewAnalyseFailed => 'Review could not be analyzed';

  @override
  String get reviewAnalysesRunning => 'The analysis is in progress';

  @override
  String get reviewAnalysesUnknownStatus => 'Unknown status for semantic analysis';

  @override
  String get reviewAnalyzedError => 'Error during review analysis';

  @override
  String get reviewAnalyzedNoResult => 'This review has no category associated with a feeling.';

  @override
  String get reviewAnalyzedTooOld => 'The review is too old to be analyzed.';

  @override
  String get reviewDoesNotExistAnymore => 'It seems that this review has been deleted by the user.';

  @override
  String get reviewFrom => 'From ...';

  @override
  String get reviewNotYetAnalyzed => 'This review has not yet been analyzed. You will be able to see the result within 24 hours.';

  @override
  String get reviewReplyError => 'Whoops ! Your reply could not be sent, please try again later.';

  @override
  String get reviewReplySentSuccessfully => 'Your reply has been sent successfully! ';

  @override
  String get reviews => 'Reviews';

  @override
  String get reviewScores => 'Scores of relevant reviews';

  @override
  String get reviewsRating => 'Average reviews rating';

  @override
  String get reviewsSyncError => 'Synchronization error for platforms: ';

  @override
  String get reviewsSyncSuccess => 'Reviews synchronization completed successfully';

  @override
  String get reviewTemplates => 'Modèles d\'avis';

  @override
  String get ro => 'Romanian';

  @override
  String get ru => 'Russian';

  @override
  String get saturday => 'Saturday';

  @override
  String get savedLinks => 'Saved links';

  @override
  String get saveDraft => 'Save as draft';

  @override
  String get saveModifications => 'Save modifications';

  @override
  String get savePost => 'Save post';

  @override
  String get schedule => 'Schedule';

  @override
  String get scheduled => 'Scheduled';

  @override
  String get sd => 'Sindhi';

  @override
  String get search => 'Search';

  @override
  String get searchPlace => 'Search a place';

  @override
  String get see => 'See';

  @override
  String see_other_keywords(int noRevelantBricksLength) {
    return 'See my other keywords $noRevelantBricksLength';
  }

  @override
  String get seeFeed => 'See feed';

  @override
  String get seeList => 'See list';

  @override
  String get seeListOfHashtags => 'See list of hashtags';

  @override
  String get seeMenu => 'menu';

  @override
  String seeOriginalReviewText(String lang) {
    return 'View the original (in $lang)';
  }

  @override
  String get semanticAnalyse => 'Semantic Analysis';

  @override
  String get sendError => 'Error sending.';

  @override
  String get sendPromotion => 'Send offer';

  @override
  String sendPromotionOf(String amount) {
    return 'Send offer of : $amount';
  }

  @override
  String sentAMedia(String mediaType) {
    return 'You sent a $mediaType';
  }

  @override
  String get service => 'service';

  @override
  String get set_email => 'Enter your email';

  @override
  String get settings => 'Settings';

  @override
  String get showInFeed => 'Show on Instagram feed';

  @override
  String get si => 'Sinhala';

  @override
  String get signOut => 'Sign me out';

  @override
  String get signup => 'Sign up';

  @override
  String get sk => 'Slovak';

  @override
  String get sl => 'Slovenian';

  @override
  String get sm => 'Samoan';

  @override
  String get sn => 'Shona';

  @override
  String get so => 'Somali';

  @override
  String get socialNetworks => 'Social Media';

  @override
  String get somethingWentWrong => 'Oops, something went wrong';

  @override
  String get sq => 'Albanian';

  @override
  String get square => 'Square';

  @override
  String get sr => 'Serbian';

  @override
  String get st => 'Sesotho';

  @override
  String get stories => 'Stories';

  @override
  String get storyDeleted => 'Story deleted successfully';

  @override
  String get storyExpired => 'Story expired';

  @override
  String get su => 'Sundanese';

  @override
  String get sunday => 'Sunday';

  @override
  String get sv => 'Swedish';

  @override
  String get sw => 'Swahili';

  @override
  String get ta => 'Tamil';

  @override
  String get tagAccounts => 'Tag accounts';

  @override
  String get te => 'Telugu';

  @override
  String get tg => 'Tajik';

  @override
  String get th => 'Thai';

  @override
  String get the => 'the';

  @override
  String get theEvent => 'the event';

  @override
  String get theEventPostGenerationAI => 'the event ';

  @override
  String get theOffer => 'the offer';

  @override
  String get thursday => 'Thursday';

  @override
  String get timeLimitReplyReviewExceeded => 'You have exceeded the deadline to respond to the review';

  @override
  String get titleLabel => 'Title *';

  @override
  String get tl => 'Filipino';

  @override
  String get to => 'to';

  @override
  String get toCheck => 'To check';

  @override
  String get todo => 'To do';

  @override
  String get tooManyNotif => 'Too many notifications, not enough?';

  @override
  String totem(String chipName) {
    return 'Totem $chipName';
  }

  @override
  String get totemDisplayName => 'Totem';

  @override
  String get tr => 'Turkish';

  @override
  String get translate => 'Translate';

  @override
  String get translated => 'Translated -';

  @override
  String translateInLang(String lang) {
    return 'Translate in $lang';
  }

  @override
  String get try_another_search => 'Try another search';

  @override
  String get tryAgain => 'Retry';

  @override
  String get tryAgainLater => 'Please try again later';

  @override
  String get tuesday => 'Tuesday';

  @override
  String get typeAndLocalisation => 'Type and location of your restaurant';

  @override
  String get typeAndLocalisationExample => '(e.g. ItalianParis14)';

  @override
  String get typeYourLang => 'Type your language';

  @override
  String get typeYourText => 'Type your text';

  @override
  String get ug => 'Uyghur';

  @override
  String get uk => 'Ukrainian';

  @override
  String get unAnswered => 'Unanswered';

  @override
  String get unansweredReviews => 'Unanswered reviews';

  @override
  String get unarchive => 'Unarchive';

  @override
  String get unauthorized => 'You are not authorized to perform this action.';

  @override
  String get unavailableMessage => 'Unavailable message';

  @override
  String get undetermined => 'Undetermined';

  @override
  String get unknownError => 'An unknown error occurred';

  @override
  String get unread => 'Unread';

  @override
  String get unreadMessages => 'Unread conversations';

  @override
  String get unsupportedAttachment => 'The attachment format is not supported.';

  @override
  String get updateFeedbackMessageError => 'Error during the update of the feedback, please try again.';

  @override
  String get ur => 'Urdu';

  @override
  String get urgent => 'Urgent';

  @override
  String get useImageForPostGeneration => 'Use the image to generate the caption';

  @override
  String get uz => 'Uzbek';

  @override
  String get validate => 'Confirm';

  @override
  String get varietyOfHashtags => 'Hashtags variety';

  @override
  String get vi => 'Vietnamese';

  @override
  String get video => 'Video';

  @override
  String get videosForbiddenOnMapstr => 'Videos are not allowed on Mapstr';

  @override
  String get videosInCarrouselForbiddenOnFacebook => 'You cannot publish a carousel containing videos on Facebook';

  @override
  String get videosOnGoogleForbiddenMessage => 'You can\'t publish videos on Google';

  @override
  String get videoTooShortMessage => 'You cannot post videos of less than 3 seconds long';

  @override
  String get waitingForImageAnalysis => 'Waiting for image analysis';

  @override
  String get we_are => 'We are ';

  @override
  String get wednesday => 'Wednesday';

  @override
  String get whatsNew => 'What\'s poppin ?';

  @override
  String get writeAnswerReview => 'Write your answer';

  @override
  String get writeLegendHere => 'Type your caption here or ';

  @override
  String get writeMessage => 'Write a message';

  @override
  String get writePostWithAi => 'Write a post with AI';

  @override
  String get writeTitleHere => 'Type your title here...';

  @override
  String get writeYourFeedback => 'Write a feedback';

  @override
  String get xh => 'Xhosa';

  @override
  String get year => '12 last months';

  @override
  String get yi => 'Yiddish';

  @override
  String get yo => 'Yoruba';

  @override
  String get youAnsweredAStory => 'You replied to a story';

  @override
  String get youCanSelectOnlyOne => 'You can only select one image';

  @override
  String get youCanSelectOnlyOnevideo => 'You can only add one video to a Reel';

  @override
  String get youCanSelectVideosOnly => 'You can only select videos';

  @override
  String get youMentionnedInStory => 'You mentioned in your story';

  @override
  String get your_businesses => 'Your restaurants';

  @override
  String get youreDisconnectedFromInternet => 'It seems that you are no longer connected to the internet.';

  @override
  String get yourHashtags => 'Your hashtags';

  @override
  String get youWillLoseDraft => 'You will lose your draft if you continue';

  @override
  String get youWillLoseYourModifications => 'You will then lose your changes.';

  @override
  String get zu => 'Zulu';
}
