import 'package:intl/intl.dart' as intl;

import 'l10n.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class TranslationEs extends Translation {
  TranslationEs([String locale = 'es']) : super(locale);

  @override
  String get aboutToDeleteMessage => 'Estás a punto de eliminar el mensaje.';

  @override
  String get aboutToSignOut => 'Estás a punto de cerrar sesión';

  @override
  String get activate => 'Activar';

  @override
  String get actives => 'Activos';

  @override
  String get add => 'Agregar';

  @override
  String get addARestaurant => 'Agregar un restaurante';

  @override
  String get addCoupon => 'Un código promocional o cupón';

  @override
  String get addDraft => 'Crear un borrador';

  @override
  String get addFIrstPost => 'Crear mi primera publicación';

  @override
  String get addHashtags => 'Agregar hashtags';

  @override
  String get addImageToUse => 'Agregar una imagen para usar esta funcionalidad';

  @override
  String get addInfosAboutOffer => 'Agregar información sobre la oferta';

  @override
  String get addLinkToOffer => 'Un enlace a la oferta';

  @override
  String get addLocation => 'Agregar una ubicación';

  @override
  String get addNow => 'Agregar ahora';

  @override
  String get addSeoPost => 'publicación Google';

  @override
  String get addSocialPost => 'Publicación redes sociales';

  @override
  String get addTitle => 'Agregar un título';

  @override
  String get addYourFirstRestaurant => 'Agrega tu primer restaurante para disfrutar completamente de MalouApp';

  @override
  String get aDish => ' un plato ';

  @override
  String get advice => ' un consejo ';

  @override
  String get af => 'Afrikáans';

  @override
  String get aIcreditsBackSoon => 'Las respuestas propuestas volverán a estar disponibles cuando se renueven sus créditos.';

  @override
  String get aiPostCaptionFormfieldHint => 'Ejemplo: nuestro delicioso pastel de chocolate perfecto para terminar la comida con un toque cremoso y un tono divertido';

  @override
  String get aiPostCaptionFormfieldLabel => 'Generar una publicación sobre...';

  @override
  String get all => 'Todo';

  @override
  String get am => 'Amárico';

  @override
  String get analyzeReviewsNotAvailable => 'El análisis semántico no está disponible para este establecimiento.';

  @override
  String get and => ' y';

  @override
  String get anErrorOccured => 'Se produjo un error';

  @override
  String get announceAnEvent => 'Anunciar un evento ';

  @override
  String get answeredYourStory => 'Respondió a tu historia';

  @override
  String get aPlace => ' un lugar';

  @override
  String get ar => 'Árabe';

  @override
  String get archive => 'Archivar';

  @override
  String get archived => 'Archivado';

  @override
  String get areYouSureYouWantToDeleteThisPost => '¿Seguro que quieres eliminar esta publicación?';

  @override
  String get areYouSureYouWantToDeleteThisPostPublished => '¿Seguro que quieres eliminar esta publicación? También se eliminará de las plataformas en las que se publicó.';

  @override
  String get areYouSureYouWantToDeleteThisStory => '¿Seguro que quieres eliminar esta historia?';

  @override
  String get artificialIntelligence => 'Inteligencia Artificial';

  @override
  String get askForDemo => 'Solicitar una demostración';

  @override
  String get atmosphere => 'atmósfera';

  @override
  String get authorizeCamera => 'Permitir acceso a mi cámara';

  @override
  String get authorizeMedias => 'Permitir acceso a mis medios';

  @override
  String get autoReply => 'Respuesta automática';

  @override
  String get az => 'Azerí';

  @override
  String get be => 'Bielorruso';

  @override
  String get bg => 'Búlgaro';

  @override
  String get bn => 'Bengalí';

  @override
  String get book => 'Reservar';

  @override
  String get brandAccount => 'Cuenta de marca';

  @override
  String get bs => 'Bosnio';

  @override
  String get businessName => 'Nombre del establecimiento';

  @override
  String get buy => 'Comprar';

  @override
  String get byDefault => ' (Por defecto)';

  @override
  String get ca => 'Catalán';

  @override
  String get call => 'Llamar';

  @override
  String get cameraNoAccessDescription => 'Esto te permitirá enviar fotos desde tu cámara.';

  @override
  String get cameraNoAccessTitle => 'MalouApp no tiene acceso a tu cámara';

  @override
  String get cancel => 'Cancelar';

  @override
  String get cancelEdit => 'Cancelar modificaciones';

  @override
  String get cannotFindYourLang => '¿No encuentras tu idioma?';

  @override
  String get cant_edit_medias_post_published => '';

  @override
  String get cantAnalyzeImage => 'No se puede analizar la imagen';

  @override
  String get cantAnalyzeVideos => 'No se pueden analizar los videos';

  @override
  String get cantEditMediaPublished => 'No puedes modificar los medios de un artículo que ya ha sido publicado.';

  @override
  String get carrouselForbiddenOnGooGle => 'No puedes publicar un carrusel en Google';

  @override
  String get carrouselForbiddenOnGoogleMessage => 'No puedes publicar un carrusel en Google';

  @override
  String get carrouselMediasLimitReachedMessage => 'No puedes seleccionar más de 10 medios';

  @override
  String get ceb => 'Cebuano';

  @override
  String get changeLanguage => 'Cambiar idioma';

  @override
  String get chooseActionButton => 'Elegir un botón de acción';

  @override
  String get clientName => 'Nombre del cliente';

  @override
  String closed_day(String closedDay) {
    return 'cerrado el $closedDay, ';
  }

  @override
  String closed_days(String endDay, String startDay) {
    return 'cerrado desde $startDay hasta $endDay, ';
  }

  @override
  String get co => 'Corso';

  @override
  String get commentsTab => 'Comentarios';

  @override
  String get completeCaptionToGenerateHashtagsFromAi => 'Completa la leyenda de tu publicación para que la IA pueda generar hashtags';

  @override
  String get concernedRatings => 'Calificaciones de comentarios relacionados';

  @override
  String get concernsOnlyNegativeReviews => 'Se refiere solo a reseñas negativas sin respuesta desde hace más de 48 horas';

  @override
  String get conditionOffer => 'Condiciones de la oferta';

  @override
  String get conditionsOfOffer => 'Condiciones de la oferta';

  @override
  String get confirm => 'Confirme';

  @override
  String get connectGoogle => 'Conectar mi cuenta de Google';

  @override
  String get connectMyPlatforms => 'Conectar mis plataformas';

  @override
  String get connectSocialNetworks => 'Conectar mis cuentas de redes sociales';

  @override
  String get contactMalouOps => 'Contacta a tu referencia de Malou si no encuentras lo que buscas en nuestra FAQ 🤗.';

  @override
  String get conversation => 'Mensajes';

  @override
  String get conversationArchived => 'Conversación archivada';

  @override
  String get conversationUnarchived => 'Conversación desarchivada';

  @override
  String get courier_to_restaurant => 'Entrega';

  @override
  String get cover => 'Portada';

  @override
  String get create_reel => 'Crear un Reel';

  @override
  String get createCompletionError => 'Se produjo un error durante la generación de la IA, inténtalo de nuevo.';

  @override
  String get createDraft => 'Crear un borrador';

  @override
  String get cs => 'Checo';

  @override
  String get customer_to_restaurant => 'Cliente';

  @override
  String get customerReviews => 'Reseñas';

  @override
  String get customized => 'Personalizado ';

  @override
  String get cy => 'Galés';

  @override
  String get da => 'Danés';

  @override
  String dailyUnansweredReviewsNotificationText(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count nuevas reseñas recibidas',
      one: '1 nueva reseña recibida',
    );
    return '⭐️ Psst, $_temp0';
  }

  @override
  String dailyUnreadMessagesNotificationText(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count nuevos mensajes',
      one: '1 nuevo mensaje',
    );
    return '💬 Psst, $_temp0';
  }

  @override
  String get date => 'Fecha';

  @override
  String get dateOf => 'Fecha de';

  @override
  String get daysLeftToReply => ' días para responder';

  @override
  String get de => 'Alemán';

  @override
  String get delete => 'Eliminar';

  @override
  String get deleteFeedbackMessageError => 'Error durante la eliminación del comentario, por favor inténtalo de nuevo.';

  @override
  String get describeDish => 'Nuestro nuevo menú con ';

  @override
  String get describeGiveaway => 'Un evento próximo con ';

  @override
  String get descriptionTab => 'Descripción';

  @override
  String get displayAnalyzeReviews => 'Mostrar análisis semántico';

  @override
  String get displayOnlyNeverPosted => 'Mostrar solo medios nunca publicados';

  @override
  String get dontForgetToAnswer => 'No olvides responder desde Malouapp.';

  @override
  String get dontLetYourCustomersWithoutAnswer => 'No dejes a tus clientes sin respuesta, visita Malouapp.';

  @override
  String get draft => 'Borrador';

  @override
  String get drafts => 'Borradores';

  @override
  String get duplicate => 'Duplicar';

  @override
  String get eachTime => 'Cada vez';

  @override
  String get edit => 'Editar';

  @override
  String get editWithAI => 'Editar con IA';

  @override
  String get el => 'Griego';

  @override
  String get email => 'Correo electrónico';

  @override
  String get emailSelected => 'Desde correo electrónico';

  @override
  String get emptyDeviceMedias => 'Todavía no tienes ningún medio en tu galería';

  @override
  String get emptyGalleryMedias => 'Todavía no tienes ningún medio en tu galería';

  @override
  String get emptyPosts => 'No hay publicaciones aquí';

  @override
  String get emptySocialPosts => 'Todavía no has conectado Malouapp a tus cuentas de redes sociales... pero ya puedes crear borradores';

  @override
  String get en => 'Inglés';

  @override
  String get enableNotifications => 'Activar notificaciones';

  @override
  String get ended => 'Completado';

  @override
  String get enterPassword => 'Ingresa tu contraseña';

  @override
  String get eo => 'Esperanto';

  @override
  String get errorCreatingPost => 'Error al crear la publicación';

  @override
  String get errorPublishing => 'Error al publicar';

  @override
  String get errorRequired => 'Campo requerido';

  @override
  String get es => 'Español';

  @override
  String get et => 'Estonio';

  @override
  String get eu => 'Vasco';

  @override
  String get expeditiousness => 'tiempo de espera';

  @override
  String get fa => 'Persa';

  @override
  String get favorite => 'Favoritos';

  @override
  String feedbackMarkAsClosed(String name) {
    return '$name ha cerrado la conversación';
  }

  @override
  String feedbackMarkAsReopen(String name) {
    return '$name ha reabierto la conversación';
  }

  @override
  String get feedbacks => 'Feedback';

  @override
  String get feedbackYouTag => '(Tú)';

  @override
  String get fi => 'Finés';

  @override
  String get filter => 'Filtros';

  @override
  String get food => 'comida';

  @override
  String get forgot_password_explanation => 'Te enviaremos un enlace para restablecer tu contraseña';

  @override
  String forgot_password_has_been_sent(String email) {
    return 'A la dirección proporcionada:\n$email';
  }

  @override
  String get forgot_password_sent => 'Se ha enviado un correo electrónico para restablecer la contraseña';

  @override
  String get forgotPassword => '¿Olvidaste tu contraseña?';

  @override
  String get fr => 'Francés';

  @override
  String get frequency => 'Frecuencia';

  @override
  String get friday => 'Viernes';

  @override
  String get from => 'desde';

  @override
  String get fy => 'Frisón';

  @override
  String get ga => 'Irlandés';

  @override
  String get gd => 'Gaélico escocés';

  @override
  String get general => 'General';

  @override
  String get generate => 'Generar';

  @override
  String get generateListOfHashtags => 'Generar una lista de hashtags';

  @override
  String get generatePostPrefixText => 'Generar una publicación sobre ';

  @override
  String get generateReviewReply => 'Générer une réponse à un avis';

  @override
  String get generateReviewReplyPlaceholder => 'Bonjour, merci pour votre note ... ';

  @override
  String get giveAdvice => 'Dar consejos';

  @override
  String get giveaway => 'Un sorteo durante un mes para los suscriptores que den like a la publicación y etiqueten a un amigo en los comentarios, con el premio de ';

  @override
  String get gl => 'Gallego';

  @override
  String get gmbMessagesNotAvailable => 'Google ha desactivado permanentemente su función de mensajería, ya no puedes enviar mensajes en esta conversación';

  @override
  String get google => 'Google';

  @override
  String get googleConnectedButNoPosts => 'Aún no has conectado Malouapp a tu cuenta de Google... ¡pero ya puedes crear borradores!';

  @override
  String get gu => 'Gujarati';

  @override
  String get ha => 'Hausa';

  @override
  String get hashtagAdvice1 => 'Entre 3 y 6 hashtags';

  @override
  String get hashtagAdvice2 => '3 o más categorías\nVarían los hashtags de una publicación a otra';

  @override
  String get hashtags => 'Hashtags';

  @override
  String get hashtagsCategories => 'Categorías de hashtags';

  @override
  String get hashtagsDisplay => 'Visualización de hashtags';

  @override
  String get hashtagsRequiredForAI => 'No tienes una lista de hashtags.';

  @override
  String get haw => 'Hawaiano';

  @override
  String get he => 'Hebreo';

  @override
  String get hi => 'Hindi';

  @override
  String get hide_my_keywords => 'Ocultar mis otras palabras clave';

  @override
  String get hmn => 'Hmong';

  @override
  String get home => 'Inicio';

  @override
  String get hour => 'Hora';

  @override
  String get hr => 'Croata';

  @override
  String get ht => 'Criollo haitiano';

  @override
  String get hu => 'Húngaro';

  @override
  String get hy => 'Armenio';

  @override
  String get hygiene => 'higiene';

  @override
  String get ia => '(IA)';

  @override
  String get iaGenerationExampleHintText => 'Ejemplo: Genera una publicación sobre nuestro nuevo plato de burrata de pistacho con un tono formal';

  @override
  String get id => 'Indonesio';

  @override
  String get ig => 'Igbo';

  @override
  String get includeHashtagOfTheRestaurant => 'Incluir el hashtag del restaurante';

  @override
  String get includeReviewsWithAutoAnswer => 'Incluir reseñas con respuesta automática';

  @override
  String get inFirstComment => 'Como primer comentario';

  @override
  String get inLegend => 'En la descripción';

  @override
  String get invalidCredentials => 'Por favor, verifica tus credenciales de inicio de sesión';

  @override
  String get invalidEmailMessage => 'Por favor, ingresa una dirección de correo electrónico válida';

  @override
  String get invalidLink => 'Enlace no válido';

  @override
  String get invalidPassword => 'Por favor, ingresa tu contraseña';

  @override
  String get it => 'Italiano';

  @override
  String get iw => 'Hebreo';

  @override
  String get ja => 'Japonés';

  @override
  String get jw => 'Javanés';

  @override
  String get ka => 'Georgiano';

  @override
  String get keywords => 'Palabras clave';

  @override
  String get keywords_score => 'Puntuación';

  @override
  String get keywordsAdvices => 'Consejos de palabras clave';

  @override
  String get keywordScoreAdviceResponseTime => 'Respuesta en menos de 72 horas';

  @override
  String get keywordScoreAdviceRestaurantName => 'Agregar el nombre del restaurante';

  @override
  String get keywordScoreAdviceReviewerName => 'Incluir el nombre del cliente';

  @override
  String get keywordScoreAdviceSimpleBricksNumber => 'Usar palabras clave:';

  @override
  String get keywordScoreAdviceSimpleBricksVariety => 'Variedad de palabras clave:';

  @override
  String get keywordScoreAdviceSimpleBricksVarietyTip => '(Usar especialidad, ubicación o categoría)';

  @override
  String get keywordScoreAdviceSorry => 'Disculpas';

  @override
  String get keywordScoreAdviceSorryTip => '(disculpa, lamento...)';

  @override
  String get keywordScoreAdviceTextLength => 'Longitud mínima del texto:';

  @override
  String get kk => 'Kazajo';

  @override
  String get km => 'Camboyano';

  @override
  String get kn => 'Canarés';

  @override
  String get ko => 'Coreano';

  @override
  String get ku => 'Kurdo (kurmanji)';

  @override
  String get ky => 'Kirguís';

  @override
  String get la => 'Latín';

  @override
  String get landsquape => 'paisaje';

  @override
  String get languages => 'Idiomas';

  @override
  String get lastSevenDays => 'Últimos 7 días';

  @override
  String get lastSixmonths => 'Últimos 6 meses';

  @override
  String get lastThirtyDays => '\n(Últimos 30 días)';

  @override
  String get lastThirtyDays_date_range => 'Últimos 30 días';

  @override
  String get lastThreeMonths => 'Últimos 3 meses';

  @override
  String get lastTwelveMonths => 'Últimos 12 meses';

  @override
  String get later => 'Más tarde';

  @override
  String get lb => 'Luxemburgués';

  @override
  String get learnMore => 'Más información';

  @override
  String get leave => '¿Salir?';

  @override
  String get leaveAndLoseModifications => 'Salir y perder mis modificaciones';

  @override
  String get legalMentions => 'Avisos legales';

  @override
  String get legalMentions1 => 'I- Disposiciones claramente establecidas tras el lanzamiento de una aplicación.';

  @override
  String get legalMentions1Details => 'Lanzar una aplicación en un mercado virtual presupone la regularización legal de los avisos legales que deben mostrarse al usuario, las condiciones generales de uso.';

  @override
  String get legalMentionsA => 'A- Menciones legales a respetar.';

  @override
  String get legalMentionsADetails => 'Incluso antes de considerar la idea de lanzar una aplicación en el mercado, se deben respetar ciertos procedimientos legales: por lo tanto, su preparación debe llevarse a cabo antes de cualquier comercialización del producto. Este es el caso, en primer lugar, con los avisos legales. Estos son obligatorios porque permiten que el usuario de la aplicación tenga los datos de contacto del editor de la aplicación, es decir, su desarrollador o la persona que patrocina el desarrollo de una aplicación, a través de un estándar abierto. Estos avisos legales deben estar disponibles para el usuario a través de su descripción sin tener que descargar, de manera gratuita o de pago, este último. El incumplimiento de esta obligación de avisos legales expone al editor de la aplicación a una pena de prisión de un año y una multa de 75,000 euros. La información a indicar es la siguiente. En primer lugar, el editor de la aplicación debe incluir su nombre comercial, domicilio social, número de teléfono, número de registro en el registro mercantil o directorio de oficios, el monto de su capital social, el nombre del director y del co-director de la aplicación. Además, los avisos legales deben incluir el nombre, la razón social o el nombre comercial, la dirección y el número de teléfono';

  @override
  String get legalMentionsTitle => 'Avisos legales';

  @override
  String get legendLabel => 'Descripción *';

  @override
  String get linkOfOffer => 'Enlace de la oferta';

  @override
  String get lo => 'Laosiano';

  @override
  String get loginPageSubtitle => 'Digital al servicio del crecimiento del establecimiento';

  @override
  String get lt => 'Lituano';

  @override
  String get lv => 'Letón';

  @override
  String get mainHashtag => 'Hashtag principal';

  @override
  String get malouGallery => 'Galería de Malou';

  @override
  String get manageNotifs => 'Administra las notificaciones que recibes tú mismo 🔔.';

  @override
  String get mandatoryField => 'Campo obligatorio';

  @override
  String manyPlatformDisconnectedWarning(String platform) {
    return '$platform se ha desconectado';
  }

  @override
  String get markedAsRead => 'Conversación marcada como leída';

  @override
  String get markedAsUnread => 'Conversación marcada como no leída';

  @override
  String get markFeebackMessagesAsClosed => 'Marcar conversación como cerrada';

  @override
  String get mediasAccessDescription => 'Esto te permitirá enviar fotos y videos desde tu teléfono.';

  @override
  String get mediasTab => 'Medios';

  @override
  String get mentionnedYouInStory => 'Te mencionaron en una historia';

  @override
  String get messageNotAvailable => 'Mensaje no disponible';

  @override
  String get messages => 'Mensajes';

  @override
  String get messaging => 'Mensajería';

  @override
  String get mg => 'Malgache';

  @override
  String get mi => 'Maorí';

  @override
  String get midnight => 'medianoche';

  @override
  String minOccurences(int minOccurences) {
    return '(mínimo $minOccurences)';
  }

  @override
  String get mk => 'Macedonio';

  @override
  String get ml => 'Malayalam';

  @override
  String get mn => 'Mongol';

  @override
  String get monday => 'Lunes';

  @override
  String get mr => 'Marathi';

  @override
  String get ms => 'Malayo';

  @override
  String get mt => 'Maltés';

  @override
  String get my => 'Birmano (birmanie)';

  @override
  String get myFirstName => 'Mi nombre';

  @override
  String get myMedias => 'Mis Medios';

  @override
  String get myName => 'Mi nombre';

  @override
  String get myTemplates => 'Mis plantillas';

  @override
  String get name => 'nombre';

  @override
  String nbReplies(Object nbReplies, Object nbReviews) {
    return 'Respuesta ($nbReplies/$nbReviews)';
  }

  @override
  String get ne => 'Nepalí';

  @override
  String get needIdeas => '¿Necesitas ideas?';

  @override
  String get newReview => 'Nueva reseña';

  @override
  String get nl => 'Neerlandés';

  @override
  String get no => 'Noruego';

  @override
  String get no_results => 'Sin resultados';

  @override
  String get noAccessToMedias => 'La aplicación “MalouApp” no tiene acceso a tus medios';

  @override
  String get noAccountQuestion => '¿No tienes una cuenta?';

  @override
  String get noFeedbacksYet => 'Sin feedbacks';

  @override
  String get noInternetConnection => 'No estás conectado a internet';

  @override
  String get noKeywordsYet => 'Todavía no tienes ninguna palabra clave';

  @override
  String get noMediaInGallery => 'Aún no tienes ningún medio en tu galería';

  @override
  String get noMessagesYet => 'Todavía no tienes mensajes';

  @override
  String get noMoreItemsInTheList => 'Desplázate hacia arriba, no hay nada más que ver';

  @override
  String get noMoreScheduledPosts => 'No hay más publicaciones programadas';

  @override
  String get noon => 'mediodía';

  @override
  String get nop => 'Non';

  @override
  String get noRestaurantForYourResearch => 'Ningún establecimiento coincide con tu búsqueda.';

  @override
  String get noRestaurantQuestion => 'Bueno, ¿no tienes un establecimiento?';

  @override
  String get noResults => 'Sin resultados';

  @override
  String get noReviews => 'No tienes ninguna reseña de clientes';

  @override
  String get noSocialNetworksConnected => 'Todavía no has conectado Malouapp a tus cuentas de redes sociales... ¡pero ya puedes crear borradores!';

  @override
  String get noSocialPosts => 'No hay publicaciones aquí';

  @override
  String get noStories => 'No se encontró ninguna historia... ¡No tengas miedo, inténtalo!';

  @override
  String get notAnsweredMessages => 'Mensajes no respondidos';

  @override
  String get notAnsweredReviews => 'Reseñas no respondidas';

  @override
  String get noTemplates => 'Todavía no tienes ninguna plantilla';

  @override
  String noTemplatesDetails(Object entity) {
    return 'Comienza agregando algunas en MalouApp para responder más rápido a tu $entity.';
  }

  @override
  String get notEnoughCreditToMakeAiApiCall => 'Ya no tienes suficientes créditos para realizar esta acción.';

  @override
  String get notifCenter => 'Centro de notificaciones';

  @override
  String get notificationsDisabledDescription => 'Activar las notificaciones te permitirá recibir alertas cuando tengas una acción que realizar.';

  @override
  String get notificationsDisabledTitle => 'Notificaciones desactivadas';

  @override
  String get now => 'Ahora';

  @override
  String numberOfHashtags(int hashtagsLength) {
    return 'Número de hashtags: $hashtagsLength/10';
  }

  @override
  String get ny => 'Chichewa';

  @override
  String get object => 'Objeto';

  @override
  String get onceADay => 'Una vez al día';

  @override
  String get onceADayWithParenthesis => ' (1 vez / día)';

  @override
  String get onlyAvailableInstagram => 'Sólo disponible en Instagram';

  @override
  String get onlyAvailableOnMapstr => 'Solo disponible en Mapstr';

  @override
  String get onlyAvailableOnMeta => 'Disponible solo en Facebook e Instagram';

  @override
  String get onlyForGoogleReviews => 'Solo se refiere a reseñas de Google, las demás plataformas solo se recuperan una vez al día.';

  @override
  String get onlyVisibleByAdmin => 'Visible solo por administradores';

  @override
  String get onlyVisibleByAdmins => 'Visible solo por administradores';

  @override
  String open_day(String closeTime, String openDay, String openTime) {
    return 'abierto el $openDay de $openTime a $closeTime, ';
  }

  @override
  String open_day_more(String closeTime2, String openTime2) {
    return 'y de $openTime2 AM a $closeTime2 PM, ';
  }

  @override
  String open_days(String closeTime, String endDay, String openTime, String startDay) {
    return 'abierto desde $startDay hasta $endDay de $openTime a $closeTime, ';
  }

  @override
  String get optimize => 'Optimizar';

  @override
  String get or => 'Odia';

  @override
  String get order => 'Orden';

  @override
  String orderDetails(String amount, String orderCount) {
    return 'Pelido de $amount pr $orderCount vez';
  }

  @override
  String get oupsSeeNothing => 'Ups, no podemos ver nada';

  @override
  String get overallExperience => 'en general';

  @override
  String get pa => 'Punjabí';

  @override
  String get participate => 'Participar';

  @override
  String get password => 'Contraseña';

  @override
  String get period => 'Periodo';

  @override
  String get pl => 'Polaco';

  @override
  String platformDisconnectedWarning(String platform) {
    return '$platform se ha desconectado';
  }

  @override
  String get platformsDisconnected => 'No tienes plataformas conectadas';

  @override
  String get pleaseCreateHashtagsFromYourSettings => 'Crea al menos uno en la configuración de tu restaurante.';

  @override
  String get portrait => 'Retrato';

  @override
  String get postCreated => 'Publicación creada con éxito';

  @override
  String get postDeleted => 'Publicación eliminada con éxito';

  @override
  String get postNotFound => 'Puesto no encontrado';

  @override
  String get postOnPlatforms => 'Publicar en plataformas';

  @override
  String get posts => 'Publicaciones';

  @override
  String get postsSyncError => 'Error de sincronización';

  @override
  String get postsSyncSuccess => 'Sincronización completada con éxito';

  @override
  String get postSuccessfullyModified => 'Post modificada con éxito';

  @override
  String get postsUpdated => 'Publicaciones actualizadas con éxito';

  @override
  String get postTopic => 'Tipo de publicación';

  @override
  String get postTopicEvent => 'Evento';

  @override
  String get postTopicNew => 'Nuevo';

  @override
  String get postTopicOffer => 'Oferta';

  @override
  String get presentAPlace => 'Presentar un lugar ';

  @override
  String get price => 'precio';

  @override
  String get probablyDeleted => 'Probablemente ha sido borrado';

  @override
  String get processing => 'Procesando...';

  @override
  String get promoOrCoupon => 'Código promocional o cupón';

  @override
  String get proposals => 'Propositions';

  @override
  String get ps => 'Pastún';

  @override
  String get pt => 'Portugués';

  @override
  String get publicationDate => 'Fecha de publicación';

  @override
  String get publicationError => 'Error de publicación';

  @override
  String get publish => 'Publicar';

  @override
  String get published => 'Publicado';

  @override
  String get publishing => 'Publicación en curso...';

  @override
  String get purchaseAIPlus => 'Actualiza a la opción de AI Plus para acceso ilimitado a AI';

  @override
  String get pursue => '¿Continuar?';

  @override
  String get pursueEdit => 'Continuar con mis modificaciones';

  @override
  String get question => '¿Alguna pregunta?';

  @override
  String get questionsAnswers => 'Preguntas frecuentes';

  @override
  String get read => 'Leer';

  @override
  String receivedAMedia(String mediaType) {
    return 'Recibiste un $mediaType';
  }

  @override
  String get receivedAStory => 'Recibiste una historia';

  @override
  String get receivedReviews => 'Reseñas recibidas';

  @override
  String get recommandations => 'Recomendaciones';

  @override
  String get recommandationsForUse => 'Instrucciones de uso';

  @override
  String reconnectPlatform(String missingPlatform) {
    return 'Reconectar $missingPlatform';
  }

  @override
  String get reconnectToInternet => 'Vuelve a conectarte a una red para acceder a esta página.';

  @override
  String get refreshed => '¡Actualizado!';

  @override
  String get refreshPage => 'Actualizar página';

  @override
  String remainingAICredits(int nbCredits) {
    return '$nbCredits créditos restantes';
  }

  @override
  String remainingAICreditsFormField(int nbCredits) {
    return '$nbCredits/500';
  }

  @override
  String remarksNumber(int count) {
    return '$count comentarios';
  }

  @override
  String get reply => 'Responder';

  @override
  String get replyByMail => 'Responder por correo electrónico';

  @override
  String get restaurantAddress => 'Dirección';

  @override
  String get restaurantMenu => 'Menú';

  @override
  String get restaurantName => 'Nombre del restaurante';

  @override
  String get restaurantOpeningHours => 'Horario de apertura';

  @override
  String get restaurantPhone => 'Teléfono';

  @override
  String get restaurantWebsite => 'Sitio web';

  @override
  String get retry => 'Reintentar';

  @override
  String get review => 'Reseña';

  @override
  String get reviewAnalyseFailed => 'La reseña no pudo ser analizada';

  @override
  String get reviewAnalysesRunning => 'El análisis está en curso';

  @override
  String get reviewAnalysesUnknownStatus => 'Estado desconocido para el análisis semántico';

  @override
  String get reviewAnalyzedError => 'Error durante el análisis de la reseña';

  @override
  String get reviewAnalyzedNoResult => 'Esta reseña no tiene una categoría asociada con un sentimiento.';

  @override
  String get reviewAnalyzedTooOld => 'La reseña es demasiado antigua para ser analizada.';

  @override
  String get reviewDoesNotExistAnymore => 'Parece que esta reseña ha sido eliminada por el usuario.';

  @override
  String get reviewFrom => 'De ...';

  @override
  String get reviewNotYetAnalyzed => 'Esta reseña aún no ha sido analizada. Podrás ver el resultado en un plazo de 24 horas.';

  @override
  String get reviewReplyError => '¡Ups! Tu respuesta no pudo ser enviada, por favor inténtalo de nuevo más tarde.';

  @override
  String get reviewReplySentSuccessfully => '¡Tu respuesta ha sido enviada con éxito!';

  @override
  String get reviews => 'Reseñas de clientes';

  @override
  String get reviewScores => 'Puntuaciones de reseñas relevantes';

  @override
  String get reviewsRating => 'Calificación promedio de las reseñas';

  @override
  String get reviewsSyncError => 'Error de sincronización para las plataformas: ';

  @override
  String get reviewsSyncSuccess => 'Sincronización de reseñas completada con éxito';

  @override
  String get reviewTemplates => 'Modèles d\'avis';

  @override
  String get ro => 'Rumano';

  @override
  String get ru => 'Ruso';

  @override
  String get saturday => 'Sábado';

  @override
  String get savedLinks => 'Enlaces guardados';

  @override
  String get saveDraft => 'Guardar como borrador';

  @override
  String get saveModifications => 'Guardar modificaciones';

  @override
  String get savePost => 'Guardar publicación';

  @override
  String get schedule => 'Programar';

  @override
  String get scheduled => 'Programado';

  @override
  String get sd => 'Sindhi';

  @override
  String get search => 'Buscar';

  @override
  String get searchPlace => 'Buscar un lugar';

  @override
  String get see => 'Ver';

  @override
  String see_other_keywords(int noRevelantBricksLength) {
    return 'Ver mis otras palabras clave $noRevelantBricksLength';
  }

  @override
  String get seeFeed => 'Ver feed';

  @override
  String get seeList => 'Ver lista';

  @override
  String get seeListOfHashtags => 'Ver lista de hashtags';

  @override
  String get seeMenu => 'Leer el menú';

  @override
  String seeOriginalReviewText(String lang) {
    return 'Ver el original (en $lang)';
  }

  @override
  String get semanticAnalyse => 'Análisis semántico';

  @override
  String get sendError => 'Error al enviar.';

  @override
  String get sendPromotion => 'Enviar oferta';

  @override
  String sendPromotionOf(String amount) {
    return 'Enviar oferta de : $amount';
  }

  @override
  String sentAMedia(String mediaType) {
    return 'Enviaste un $mediaType';
  }

  @override
  String get service => 'servicio';

  @override
  String get set_email => 'Ingrese su correo electrónico';

  @override
  String get settings => 'Configuración';

  @override
  String get showInFeed => 'Mostrar en el feed de Instagram';

  @override
  String get si => 'Cingalés';

  @override
  String get signOut => 'Cerrar sesión';

  @override
  String get signup => 'Registrarse';

  @override
  String get sk => 'Eslovaco';

  @override
  String get sl => 'Esloveno';

  @override
  String get sm => 'Samoano';

  @override
  String get sn => 'Shona';

  @override
  String get so => 'Somalí';

  @override
  String get socialNetworks => 'Redes sociales';

  @override
  String get somethingWentWrong => 'Oops, algo salió mal';

  @override
  String get sq => 'Albanés';

  @override
  String get square => 'Cuadrado';

  @override
  String get sr => 'Serbio';

  @override
  String get st => 'Sesotho';

  @override
  String get stories => 'Historias';

  @override
  String get storyDeleted => 'Historia eliminada con éxito';

  @override
  String get storyExpired => 'Historia caducada';

  @override
  String get su => 'Sundanés';

  @override
  String get sunday => 'Domingo';

  @override
  String get sv => 'Sueco';

  @override
  String get sw => 'Swahili';

  @override
  String get ta => 'Tamil';

  @override
  String get tagAccounts => 'Etiqueta cuentas';

  @override
  String get te => 'Telugu';

  @override
  String get tg => 'Tayiko';

  @override
  String get th => 'Tailandés';

  @override
  String get the => 'el';

  @override
  String get theEvent => 'el evento';

  @override
  String get theEventPostGenerationAI => ' el evento ';

  @override
  String get theOffer => 'la oferta';

  @override
  String get thursday => 'Jueves';

  @override
  String get timeLimitReplyReviewExceeded => 'Has superado el plazo para responder a la reseña';

  @override
  String get titleLabel => 'Título *';

  @override
  String get tl => 'Filipino';

  @override
  String get to => 'a';

  @override
  String get toCheck => 'Para tratar';

  @override
  String get todo => 'Por hacer';

  @override
  String get tooManyNotif => '¿Demasiadas notificaciones, no es así?';

  @override
  String totem(String chipName) {
    return 'Tótem $chipName';
  }

  @override
  String get totemDisplayName => 'Totem';

  @override
  String get tr => 'Turco';

  @override
  String get translate => 'Traducir';

  @override
  String get translated => 'Traducido -';

  @override
  String translateInLang(String lang) {
    return 'Traducir en $lang';
  }

  @override
  String get try_another_search => 'Prueba otra búsqueda';

  @override
  String get tryAgain => 'Intentar de nuevo';

  @override
  String get tryAgainLater => 'Inténtalo de nuevo más tarde';

  @override
  String get tuesday => 'Martes';

  @override
  String get typeAndLocalisation => 'Tipo y ubicación de tu establecimiento';

  @override
  String get typeAndLocalisationExample => '(ej: ItalianoParís14)';

  @override
  String get typeYourLang => 'Escribe tu idioma';

  @override
  String get typeYourText => 'Escribe tu texto';

  @override
  String get ug => 'Uigur';

  @override
  String get uk => 'Ucraniano';

  @override
  String get unAnswered => 'Sin respuesta';

  @override
  String get unansweredReviews => 'Reseñas sin respuesta';

  @override
  String get unarchive => 'Desarchivar';

  @override
  String get unauthorized => 'No estás autorizado para realizar esta acción.';

  @override
  String get unavailableMessage => 'Mensaje no disponible';

  @override
  String get undetermined => 'Indeterminado';

  @override
  String get unknownError => 'Ocurrió un error desconocido';

  @override
  String get unread => 'No leído';

  @override
  String get unreadMessages => 'Conversaciones no leídas';

  @override
  String get unsupportedAttachment => 'El formato del archivo adjunto no es compatible.';

  @override
  String get updateFeedbackMessageError => 'Error durante la actualización del mensaje de retroalimentación, por favor inténtalo de nuevo.';

  @override
  String get ur => 'Urdu';

  @override
  String get urgent => 'Urgente';

  @override
  String get useImageForPostGeneration => 'Usar la imagen para generar la leyenda';

  @override
  String get uz => 'Uzbeko';

  @override
  String get validate => 'Validar';

  @override
  String get varietyOfHashtags => 'Variedad de hashtags';

  @override
  String get vi => 'Vietnamita';

  @override
  String get video => 'Video';

  @override
  String get videosForbiddenOnMapstr => 'Los videos no están permitidos en Mapstr';

  @override
  String get videosInCarrouselForbiddenOnFacebook => 'No puedes publicar un carrusel que contenga videos en Facebook';

  @override
  String get videosOnGoogleForbiddenMessage => 'No puedes publicar videos en Google';

  @override
  String get videoTooShortMessage => 'No puedes publicar videos de menos de 3 segundos de duración';

  @override
  String get waitingForImageAnalysis => 'Esperando el análisis de la imagen';

  @override
  String get we_are => 'Somos ';

  @override
  String get wednesday => 'Miércoles';

  @override
  String get whatsNew => '¿Qué hay de nuevo?';

  @override
  String get writeAnswerReview => 'Escribe tu respuesta';

  @override
  String get writeLegendHere => 'Escribe tu leyenda aquí o ';

  @override
  String get writeMessage => 'Escribe un mensaje';

  @override
  String get writePostWithAi => 'Escribir una publicación con IA';

  @override
  String get writeTitleHere => 'Escribe tu título aquí...';

  @override
  String get writeYourFeedback => 'Escribe un comentario';

  @override
  String get xh => 'Xhosa';

  @override
  String get year => 'Últimos 12 meses';

  @override
  String get yi => 'Yidis';

  @override
  String get yo => 'Yoruba';

  @override
  String get youAnsweredAStory => 'Respondiste a una historia';

  @override
  String get youCanSelectOnlyOne => 'Solo puedes seleccionar una imagen';

  @override
  String get youCanSelectOnlyOnevideo => 'Solo puedes agregar un video a un Reel';

  @override
  String get youCanSelectVideosOnly => 'Solo puedes seleccionar videos';

  @override
  String get youMentionnedInStory => 'Fuiste mencionado en tu historia';

  @override
  String get your_businesses => 'Tus establecimientos';

  @override
  String get youreDisconnectedFromInternet => 'Parece que ya no estás conectado a internet.';

  @override
  String get yourHashtags => 'Sus hashtags';

  @override
  String get youWillLoseDraft => 'Si te vas, perderás este borrador de publicación.';

  @override
  String get youWillLoseYourModifications => 'Luego perderás tus cambios.';

  @override
  String get zu => 'Zulú';
}
