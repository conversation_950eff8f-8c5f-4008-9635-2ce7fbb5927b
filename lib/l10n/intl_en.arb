{"aboutToDeleteMessage": "You're about to delete the message.", "aboutToSignOut": "You are about to sign out", "activate": "Activate", "actives": "Active", "add": "Add", "addARestaurant": "Add a restaurant", "addCoupon": "A promo code or a coupon", "addDraft": "Create a draft", "addFIrstPost": "Create my first post", "addHashtags": "Add hashtags", "addImageToUse": "Ajouter une image pour utiliser cette fonctionnalité", "addInfosAboutOffer": "Add information about the offer", "addLinkToOffer": "A link to the offer", "addLocation": "Add a location", "addNow": "Add now", "addSeoPost": "Google Post", "addSocialPost": "Social media post", "addTitle": "Add a title", "addYourFirstRestaurant": "Add your first restaurant to fully enjoy the MalouApp", "aDish": "Our new menu with ", "advice": "an advice ", "af": "Afrikaans", "aIcreditsBackSoon": "The proposed answers will be available again when your credits are renewed.", "aiPostCaptionFormfieldHint": "our perfect chocolate lava cake to finish your meal", "aiPostCaptionFormfieldLabel": "Generate a post about...", "all": "All", "am": "Amharic", "analyzeReviewsNotAvailable": "Semantic analysis is not available for this restaurant.", "and": " and", "anErrorOccured": "An error occurred", "announceAnEvent": "An upcoming event with", "answeredYourStory": "Replied to your story", "aPlace": "a place", "ar": "Arabic", "archive": "Archive", "archived": "Archived", "areYouSureYouWantToDeleteThisPost": "Do you want to delete this post?", "areYouSureYouWantToDeleteThisPostPublished": "Do you want to delete this post? It will also be removed from the platforms on which it was posted.", "areYouSureYouWantToDeleteThisStory": "Do you want to delete this story?", "artificialIntelligence": "Artificial Intelligence", "askForDemo": "Request a demo", "atmosphere": "atmosphere", "authorizeCamera": "Allow access to my camera", "authorizeMedias": "Allow access to my media", "autoReply": "Auto-reply", "az": "Azerbaijani", "be": "Belarusian", "bg": "Bulgarian", "bn": "Bengali", "book": "Book", "brandAccount": "Brand account", "bs": "Bosnian", "businessName": "Business name", "buy": "Buy", "byDefault": " (By default)", "ca": "Catalan", "call": "Call", "cameraNoAccessDescription": "This will allow you to send photos from your camera.", "cameraNoAccessTitle": "MalouApp does not have access to your camera", "cancel": "Cancel", "cancelEdit": "Cancel modifications", "cannotFindYourLang": "You cannot find your language?", "cant_edit_medias_post_published": "", "cantAnalyzeImage": "Unable to analyze the image", "cantAnalyzeVideos": "Unable to analyze the videos", "cantEditMediaPublished": "You cannot modify the media of an article that has already been published.", "carrouselForbiddenOnGooGle": "You cannot publish a carousel on Google", "carrouselForbiddenOnGoogleMessage": "You cannot publish a carousel on Google", "carrouselMediasLimitReachedMessage": "You cannot select more than 10 media", "ceb": "Cebuano", "changeLanguage": "Change language", "chooseActionButton": "Choose an action button", "clientName": "Client name", "closed_day": "closed on {closedDay}, ", "@closed_day": {"placeholders": {"closedDay": {"type": "String"}}}, "closed_days": "closed from {startDay} to {endDay}, ", "@closed_days": {"placeholders": {"endDay": {"type": "String"}, "startDay": {"type": "String"}}}, "co": "Corsican", "commentsTab": "<PERSON><PERSON><PERSON>", "completeCaptionToGenerateHashtagsFromAi": "Complete the caption of your post so that the AI can generate hashtags", "concernedRatings": "Ratings of concerned reviews", "concernsOnlyNegativeReviews": "Only for negative reviews that have not been answered for more than 48hrs", "conditionOffer": "Terms of use", "conditionsOfOffer": "Terms of use", "confirm": "Confirm", "connectGoogle": "Connect my Google account", "connectMyPlatforms": "Connect my platforms", "connectSocialNetworks": "Connect my social media accounts", "contactMalouOps": "Contact your Malou reference if you can't find what you're looking for in our FAQ 🤗.", "conversation": "Messaging", "conversationArchived": "Conversation archived", "conversationUnarchived": "Conversation unarchived", "courier_to_restaurant": "Delivery", "cover": "Cover", "create_reel": "Create a reel", "createCompletionError": "An error occurred during AI generation, please try again.", "createDraft": "Create a draft", "cs": "Czech", "customer_to_restaurant": "Customer", "customerReviews": "Reviews", "customized": "Customized ", "cy": "Welsh", "da": "Danish", "dailyUnansweredReviewsNotificationText": "⭐️ Psst, {count, plural, one {1 new review received} other {{count} new reviews received}}", "@dailyUnansweredReviewsNotificationText": {"placeholders": {"count": {"type": "int"}}}, "dailyUnreadMessagesNotificationText": "💬 Psst, {count, plural, one {1 new message} other {{count} new messages}}", "@dailyUnreadMessagesNotificationText": {"placeholders": {"count": {"type": "int"}}}, "date": "Date", "dateOf": "Date of", "daysLeftToReply": " days left to reply", "de": "German", "delete": "Delete", "deleteFeedbackMessageError": "Error during the deletion of the feedback, please try again.", "describeDish": "Our new menu with ", "describeGiveaway": "An upcoming event with ", "descriptionTab": "Description", "displayAnalyzeReviews": "Display semantic analysis", "displayOnlyNeverPosted": "Show only never posted media", "dontForgetToAnswer": "Don't forget to respond from the Malouapp.", "dontLetYourCustomersWithoutAnswer": "Don't leave your customers unanswered, visit the Malouapp.", "draft": "Draft", "drafts": "Drafts", "duplicate": "Duplicate", "eachTime": "Each time", "edit": "Edit", "editWithAI": "Edit with AI", "el": "Greek", "email": "Email", "emailSelected": "From email", "emptyDeviceMedias": "You don't have any media in your gallery yet", "emptyGalleryMedias": "You don't have any media in your gallery yet", "emptyPosts": "No posts here", "emptySocialPosts": "You haven't yet connected <PERSON><PERSON><PERSON><PERSON> to your social media accounts... but you can already create drafts", "en": "English", "enableNotifications": "Enable notifications", "ended": "Ended", "enterPassword": "Enter your password", "eo": "Esperanto", "errorCreatingPost": "Error occured while creating post", "errorPublishing": "Error during publication", "errorRequired": "Field required", "es": "Spanish", "et": "Estonian", "eu": "Basque", "expeditiousness": "wait time", "fa": "Persian", "favorite": "Favorites", "feedbackMarkAsClosed": "{name} has closed the conversation", "@feedbackMarkAsClosed": {"placeholders": {"name": {"type": "String"}}}, "feedbackMarkAsReopen": "{name} has reopened the conversation", "@feedbackMarkAsReopen": {"placeholders": {"name": {"type": "String"}}}, "feedbacks": "<PERSON><PERSON><PERSON>", "feedbackYouTag": "(You)", "fi": "Finnish", "filter": "Filters", "food": "food", "forgot_password_explanation": "We will send you a link to reset your password", "forgot_password_has_been_sent": "To the provided address:\n{email}", "@forgot_password_has_been_sent": {"placeholders": {"email": {"type": "String"}}}, "forgot_password_sent": "A password reset email has been sent to you", "forgotPassword": "Forgot password?", "fr": "French", "frequency": "Frequency", "friday": "Friday", "from": "from", "fy": "Frisian", "ga": "Irish", "gd": "Scots gaelic", "general": "General", "generate": "Generate", "generateListOfHashtags": "Generate a hashtag list", "generatePostPrefixText": "Generate a post about ", "generateReviewReply": "Generate a reply to a review", "generateReviewReplyPlaceholder": "Hello, thank you for your review ... ", "giveAdvice": "To give advice", "giveaway": "A one-month giveaway for subscribers who like the post and tag a friend in the comments, with a prize of ", "gl": "Galician", "gmbMessagesNotAvailable": "Google has permanently disabled its messaging feature, you can no longer send messages in this conversation", "google": "Google", "googleConnectedButNoPosts": "You haven't yet connected <PERSON><PERSON><PERSON><PERSON> to your Google account... but you can already create drafts!", "gu": "Gujarati", "ha": "Hausa", "hashtagAdvice1": "Between 3 and 6 hashtags", "hashtagAdvice2": "3 or more categories\nVary the hashtags from one post to the next", "hashtags": "Hashtags", "hashtagsCategories": "Hashtag categories", "hashtagsDisplay": "Hashtag display", "hashtagsRequiredForAI": "You don't have a list of hashtags.", "haw": "Hawaiian", "he": "Hebrew", "hi": "Hindi", "hide_my_keywords": "Hide my other keywords", "hmn": "Hmong", "home": "Home", "hour": "Time", "hr": "Croatian", "ht": "Haitian creole", "hu": "Hungarian", "hy": "Armenian", "hygiene": "hygiene", "ia": "(AI)", "iaGenerationExampleHintText": "Example : Generate a post about our new pistachio burrata dish with a formal tone", "id": "Indonesian", "ig": "Igbo", "includeHashtagOfTheRestaurant": "Include business hashtag", "includeReviewsWithAutoAnswer": "Include reviews with auto-answer", "inFirstComment": "In first comment", "inLegend": "In caption", "invalidCredentials": "Please check your login credentials", "invalidEmailMessage": "Please enter a valid email address", "invalidLink": "Invalid link", "invalidPassword": "Please enter your password", "it": "Italian", "iw": "Hebrew", "ja": "Japanese", "jw": "Javanese", "ka": "Georgian", "keywords": "Keywords", "keywords_score": "Score", "keywordsAdvices": "Keywords advices", "keywordScoreAdviceResponseTime": "Response within 72 hours", "keywordScoreAdviceRestaurantName": "Add restaurant name", "keywordScoreAdviceReviewerName": "Include client name", "keywordScoreAdviceSimpleBricksNumber": "Use keywords : ", "keywordScoreAdviceSimpleBricksVariety": "Keywords variety :", "keywordScoreAdviceSimpleBricksVarietyTip": "(Use speciality, a location or a category)", "keywordScoreAdviceSorry": "Apologies", "keywordScoreAdviceSorryTip": "(sorry, regret ...)", "keywordScoreAdviceTextLength": "Minimum character count:", "kk": "Kazakh", "km": "Khmer", "kn": "Kannada", "ko": "Korean", "ku": "Kurdish (kurmanji)", "ky": "Kyrgyz", "la": "Latin", "landsquape": "Landscape", "languages": "Languages", "lastSevenDays": "7 last days", "lastSixmonths": "6 last months", "lastThirtyDays": "\n(Last 30 days)", "lastThirtyDays_date_range": "30 last days", "lastThreeMonths": "3 last months", "lastTwelveMonths": "12 last months", "later": "Later", "lb": "Luxembourgish", "learnMore": "Learn more", "leave": "Leave?", "leaveAndLoseModifications": "Leave and lose my modifications", "legalMentions": "Legal Mentions", "legalMentions1": "I- Clearly stated provisions following the launch of an application.", "legalMentions1Details": "Launching an application on a virtual market presupposes the legal regularization of the legal notices to be displayed for the user, the general conditions of use.", "legalMentionsA": "A- Legal mentions to be respected.", "legalMentionsADetails": "Even before considering the idea of launching an application on the market, certain legal procedures must be respected: therefore, their preparation must be carried out before any marketing of the product. This is the case first of all with legal notices. These are mandatory because they allow the application user to have the contact details of the application's publisher, namely its developer or the person who is the sponsor of the development of an application, through an open standard. These legal notices must thus be available to the user through its description without having to download, in a paid or free manner, the latter. Failure to comply with this obligation of legal notices exposes the application publisher to a penalty of one year's imprisonment and a fine of 75,000 euros. The information to be indicated is as follows. First, the application's publisher must include its corporate name, registered office, telephone number, registration number with the trade and companies register or directory of trades, the amount of its share capital, the name of the director and that of the co-director of the application. In addition, the legal notices must include the name, corporate name or business name, address and telephone number", "legalMentionsTitle": "Legal Mentions", "legendLabel": "Caption *", "linkOfOffer": "Link of the offer", "lo": "Lao", "loginPageSubtitle": "Digital at the service of restaurant growth", "lt": "Lithuanian", "lv": "Latvian", "mainHashtag": "Brand hashtag", "malouGallery": "Malou Gallery", "manageNotifs": "Manage the notifications you receive yourself 🔔.", "mandatoryField": "Required field", "manyPlatformDisconnectedWarning": "{platform} have been disconnected", "@manyPlatformDisconnectedWarning": {"placeholders": {"platform": {"type": "String"}}}, "markedAsRead": "Conversation marked as read", "markedAsUnread": "Conversation marked as unread", "markFeebackMessagesAsClosed": "Mark conversation as closed", "mediasAccessDescription": "This will allow you to send photos and videos from your phone.", "mediasTab": "Media", "mentionnedYouInStory": "Mentioned you in a story", "messageNotAvailable": "Message not available", "messages": "Messages", "messaging": "Messaging", "mg": "Malagasy", "mi": "<PERSON><PERSON>", "midnight": "midnight", "minOccurences": "(minimum {minOccurences})", "@minOccurences": {"placeholders": {"minOccurences": {"type": "int"}}}, "mk": "Macedonian", "ml": "Malayalam", "mn": "Mongolian", "modifiedOn": "Modified on", "monday": "Monday", "mr": "Marathi", "ms": "Malay", "mt": "Maltese", "my": "Myanmar (burmese)", "myFirstName": "My first name", "myMedias": "My Media", "myName": "My name", "myTemplates": "My templates", "name": "name", "nbReplies": "Reply ({nbReplies}/{nbReviews})", "ne": "Nepali", "needIdeas": "Need ideas?", "newReview": "New review", "nl": "Dutch", "no": "Norwegian", "no_results": "No results", "noAccessToMedias": "The “MalouApp” does not have access to your media", "noAccountQuestion": "Don't have an account?", "noFeedbacksYet": "No feedbacks", "noInternetConnection": "You are not connected to the internet", "noKeywordsYet": "You don't have configured any keywords yet", "noMediaInGallery": "You don't have any media in your gallery yet", "noMessagesYet": "You don't have any messages yet", "noMoreItemsInTheList": "Scroll up, there's nothing else to see", "noMoreScheduledPosts": "No post scheduled", "noon": "noon", "nop": "Non", "noRestaurantForYourResearch": "No restaurant matches your search.", "noRestaurantQuestion": "Well, don't you have an restaurant?", "noResults": "No results", "noReviews": "You don't have any reviews", "noSocialNetworksConnected": "You haven't yet connected <PERSON><PERSON><PERSON><PERSON> to your social media accounts... but you can already create drafts!", "noSocialPosts": "No posts here", "noStories": "No story found... Don't be afraid, go for it!", "notAnsweredMessages": "Unanswered messages", "notAnsweredReviews": "Unanswered reviews", "noTemplates": "You don't have any templates yet", "noTemplatesDetails": "Start by adding some on the MalouApp to respond faster to your {entity}.", "notEnoughCreditToMakeAiApiCall": "You no longer have enough credit to carry out this action.", "notifCenter": "Notification center", "notificationsDisabledDescription": "Enabling notifications will allow you to be alerted when you have an action to take.", "notificationsDisabledTitle": "Notifications disabled", "now": "Now", "numberOfHashtags": "Number of hashtag: {hashtagsLength}/10", "@numberOfHashtags": {"placeholders": {"hashtagsLength": {"type": "int"}}}, "ny": "Chichewa", "object": "Object", "onceADay": "Once a day", "onceADayWithParenthesis": " (1 time / day)", "onlyAvailableInstagram": "Only available on Instagram", "onlyAvailableOnMapstr": "Only available on Mapstr", "onlyAvailableOnMeta": "Available only on Facebook and Instagram", "onlyForGoogleReviews": "Only concerns google reviews, other platforms are only retrieved once a day.", "onlyVisibleByAdmin": "Visible only by admins", "onlyVisibleByAdmins": "Visible only by admins", "open_day": "open on {openDay} from {openTime} to {closeTime}, ", "@open_day": {"placeholders": {"closeTime": {"type": "String"}, "openDay": {"type": "String"}, "openTime": {"type": "String"}}}, "open_day_more": "and from {openTime2} AM to {closeTime2} PM, ", "@open_day_more": {"placeholders": {"closeTime2": {"type": "String"}, "openTime2": {"type": "String"}}}, "open_days": "open from {startDay} to {endDay} from {openTime} to {closeTime}, ", "@open_days": {"placeholders": {"closeTime": {"type": "String"}, "endDay": {"type": "String"}, "openTime": {"type": "String"}, "startDay": {"type": "String"}}}, "optimize": "Optimize", "or": "Odia", "order": "Order", "orderDetails": "Order of {amount} for the {orderCount} time", "@orderDetails": {"placeholders": {"amount": {"type": "String"}, "orderCount": {"type": "String"}}}, "oupsSeeNothing": "Oops, we can't see anything", "overallExperience": "overall", "pa": "Punjabi", "participate": "Join", "password": "Password", "period": "Period", "pl": "Polish", "platformDisconnectedWarning": "{platform} has been disconnected", "@platformDisconnectedWarning": {"placeholders": {"platform": {"type": "String"}}}, "platformsDisconnected": "You have no connected platforms", "pleaseCreateHashtagsFromYourSettings": "Please create at least one in your restaurant settings.", "portrait": "Portrait", "postCreated": "Post created successfully", "postDeleted": "Post deleted successfully", "postNotFound": "Post not found", "postOnPlatforms": "Post on platforms", "posts": "Posts", "postsSyncError": "Synchronization error", "postsSyncSuccess": "Synchronization completed successfully", "postSuccessfullyModified": "Post modifié avec succès", "postsUpdated": "Posts updated successfully", "postTopic": "Type of post", "postTopicEvent": "Event", "postTopicNew": "New", "postTopicOffer": "Offer", "presentAPlace": "Present a place ", "price": "price", "probablyDeleted": "Probably deleted", "processing": "Processing ...", "promoOrCoupon": "A discount code or coupon", "proposals": "Propositions", "ps": "Pashto", "pt": "Portuguese", "publicationDate": "Publication date", "publicationError": "Publication error", "publish": "Publish", "published": "Published", "publishing": "Post in progress...", "purchaseAIPlus": "Upgrade to the AI Plus option for unlimited access to AI", "pursue": "Continue?", "pursueEdit": "Pursue edit", "question": "Any questions?", "questionsAnswers": "FAQ", "read": "Read", "receivedAMedia": "You received a {mediaType}", "@receivedAMedia": {"placeholders": {"mediaType": {"type": "String"}}}, "receivedAStory": "You received a story", "receivedReviews": "Received reviews", "recommandations": "Recommendations", "recommandationsForUse": "Usage tips", "reconnectPlatform": "Reconnect {missingPlatform}", "@reconnectPlatform": {"placeholders": {"missingPlatform": {"type": "String"}}}, "reconnectToInternet": "Reconnect to a network to access this page.", "refreshed": "Refreshed !", "refreshPage": "Refresh the page", "remainingAICredits": "{nbCredits} credits remaining", "@remainingAICredits": {"placeholders": {"nbCredits": {"type": "int"}}}, "remainingAICreditsFormField": "{nbCredits}/500", "@remainingAICreditsFormField": {"placeholders": {"nbCredits": {"type": "int"}}}, "remarksNumber": "{count} feedbacks", "@remarksNumber": {"placeholders": {"count": {"type": "int"}}}, "reply": "Reply", "replyByMail": "Reply by email", "restaurantAddress": "Address", "restaurantMenu": "<PERSON><PERSON>", "restaurantName": "Restaurant name", "restaurantOpeningHours": "Opening hours", "restaurantPhone": "Phone", "restaurantWebsite": "Website", "retry": "Retry", "review": "Review", "reviewAnalyseFailed": "Review could not be analyzed", "reviewAnalysesRunning": "The analysis is in progress", "reviewAnalysesUnknownStatus": "Unknown status for semantic analysis", "reviewAnalyzedError": "Error during review analysis", "reviewAnalyzedNoResult": "This review has no category associated with a feeling.", "reviewAnalyzedTooOld": "The review is too old to be analyzed.", "reviewDoesNotExistAnymore": "It seems that this review has been deleted by the user.", "reviewFrom": "From ...", "reviewNotYetAnalyzed": "This review has not yet been analyzed. You will be able to see the result within 24 hours.", "reviewReplyError": "Whoops ! Your reply could not be sent, please try again later.", "reviewReplySentSuccessfully": "Your reply has been sent successfully! ", "reviews": "Reviews", "reviewScores": "Scores of relevant reviews", "reviewsRating": "Average reviews rating", "reviewsSyncError": "Synchronization error for platforms: ", "reviewsSyncSuccess": "Reviews synchronization completed successfully", "reviewTemplates": "<PERSON><PERSON><PERSON><PERSON>", "ro": "Romanian", "ru": "Russian", "saturday": "Saturday", "savedLinks": "Saved links", "saveDraft": "Save as draft", "saveModifications": "Save modifications", "savePost": "Save post", "schedule": "Schedule", "scheduled": "Scheduled", "sd": "Sindhi", "search": "Search", "searchPlace": "Search a place", "see": "See", "see_other_keywords": "See my other keywords {noRevelantBricksLength}", "@see_other_keywords": {"placeholders": {"noRevelantBricksLength": {"type": "int"}}}, "seeFeed": "See feed", "seeList": "See list", "seeListOfHashtags": "See list of hashtags", "seeMenu": "menu", "seeOriginalReviewText": "View the original (in {lang})", "@seeOriginalReviewText": {"placeholders": {"lang": {"type": "String"}}}, "semanticAnalyse": "Semantic Analysis", "sendError": "Error sending.", "sendPromotion": "Send offer", "sendPromotionOf": "Send offer of : {amount}", "@sendPromotionOf": {"placeholders": {"amount": {"type": "String"}}}, "sentAMedia": "You sent a {mediaType}", "@sentAMedia": {"placeholders": {"mediaType": {"type": "String"}}}, "service": "service", "set_email": "Enter your email", "settings": "Settings", "showInFeed": "Show on Instagram feed", "si": "Sinhala", "signOut": "Sign me out", "signup": "Sign up", "sk": "Slovak", "sl": "Slovenian", "sm": "Samoan", "sn": "<PERSON><PERSON><PERSON>", "so": "Somali", "socialNetworks": "Social Media", "somethingWentWrong": "Oops, something went wrong", "sq": "Albanian", "square": "Square", "sr": "Serbian", "st": "<PERSON><PERSON><PERSON><PERSON>", "stories": "Stories", "storyDeleted": "Story deleted successfully", "storyExpired": "Story expired", "su": "Sundanese", "sunday": "Sunday", "sv": "Swedish", "sw": "Swahili", "ta": "Tamil", "tagAccounts": "Tag accounts", "te": "Telugu", "tg": "Tajik", "th": "Thai", "the": "the", "theEvent": "the event", "theEventPostGenerationAI": "the event ", "theOffer": "the offer", "thursday": "Thursday", "timeLimitReplyReviewExceeded": "You have exceeded the deadline to respond to the review", "titleLabel": "Title *", "tl": "Filipino", "to": "to", "toCheck": "To check", "todo": "To do", "tooManyNotif": "Too many notifications, not enough?", "totem": "Totem {chipName}", "@totem": {"placeholders": {"chipName": {"type": "String"}}}, "totemDisplayName": "Totem", "tr": "Turkish", "translate": "Translate", "translated": "Translated -", "translateInLang": "Translate in {lang}", "@translateInLang": {"placeholders": {"lang": {"type": "String"}}}, "try_another_search": "Try another search", "tryAgain": "Retry", "tryAgainLater": "Please try again later", "tuesday": "Tuesday", "typeAndLocalisation": "Type and location of your restaurant", "typeAndLocalisationExample": "(e.g. ItalianParis14)", "typeYourLang": "Type your language", "typeYourText": "Type your text", "ug": "Uyghur", "uk": "Ukrainian", "unAnswered": "Unanswered", "unansweredReviews": "Unanswered reviews", "unarchive": "Unarchive", "unauthorized": "You are not authorized to perform this action.", "unavailableMessage": "Unavailable message", "undetermined": "Undetermined", "unknownError": "An unknown error occurred", "unread": "Unread", "unreadMessages": "Unread conversations", "unsupportedAttachment": "The attachment format is not supported.", "updateFeedbackMessageError": "Error during the update of the feedback, please try again.", "ur": "Urdu", "urgent": "<PERSON><PERSON>", "useImageForPostGeneration": "Use the image to generate the caption", "uz": "Uzbek", "validate": "Confirm", "varietyOfHashtags": "Hashtags variety", "vi": "Vietnamese", "video": "Video", "videosForbiddenOnMapstr": "Videos are not allowed on Mapstr", "videosInCarrouselForbiddenOnFacebook": "You cannot publish a carousel containing videos on Facebook", "videosOnGoogleForbiddenMessage": "You can't publish videos on Google", "videoTooShortMessage": "You cannot post videos of less than 3 seconds long", "waitingForImageAnalysis": "Waiting for image analysis", "we_are": "We are ", "wednesday": "Wednesday", "whatsNew": "What's poppin ?", "writeAnswerReview": "Write your answer", "writeLegendHere": "Type your caption here or ", "writeMessage": "Write a message", "writePostWithAi": "Write a post with AI", "writeTitleHere": "Type your title here...", "writeYourFeedback": "Write a feedback", "xh": "Xhosa", "year": "12 last months", "yi": "Yiddish", "yo": "Yoruba", "youAnsweredAStory": "You replied to a story", "youCanSelectOnlyOne": "You can only select one image", "youCanSelectOnlyOnevideo": "You can only add one video to a Reel", "youCanSelectVideosOnly": "You can only select videos", "youMentionnedInStory": "You mentioned in your story", "your_businesses": "Your restaurants", "youreDisconnectedFromInternet": "It seems that you are no longer connected to the internet.", "yourHashtags": "Your hashtags", "youWillLoseDraft": "You will lose your draft if you continue", "youWillLoseYourModifications": "You will then lose your changes.", "zu": "Zulu"}