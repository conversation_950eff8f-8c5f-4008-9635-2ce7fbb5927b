import 'package:device_preview/device_preview.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/services/auth/auth.provider.dart';
import 'package:malou/shared/services/keyword/keyword.provider.dart';
import 'package:malou/shared/services/language/language.provider.dart';
import 'package:malou/shared/services/medias/medias.provider.dart';
import 'package:malou/shared/services/messaging/messaging.provider.dart';
import 'package:malou/shared/services/platform/platform.provider.dart';
import 'package:malou/shared/services/restaurant/restaurant.provider.dart';
import 'package:malou/shared/services/review/review.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:malou/shared/theme/main_theme.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

Future<void> wrapWithApp(
  WidgetTester tester,
  SharedPreferences preferences,
  Widget child, {
  UserProvider? userProvider,
  RestaurantProvider? restaurantProvider,
  PlatformProvider? platformProvider,
  MessagingProvider? messagingProvider,
  MediasProvider? mediasProvider,
  ReviewProvider? reviewProvider,
  KeywordProvider? keywordProvider,
  LanguageProvider? languageProvider,
  AuthProvider? authProvider,
}) async {
  userProvider ??= UserProvider();
  restaurantProvider ??= RestaurantProvider();
  languageProvider ??= LanguageProvider(preferences: preferences);
  authProvider ??= AuthProvider(
    userProvider: userProvider,
    restaurantProvider: restaurantProvider,
  );
  platformProvider ??= PlatformProvider(
    restaurantProvider: restaurantProvider,
  );
  messagingProvider ??= MessagingProvider(
    restaurantProvider: restaurantProvider,
    platformProvider: platformProvider,
  );
  mediasProvider ??= MediasProvider(
    restaurantProvider: restaurantProvider,
  );
  reviewProvider ??= ReviewProvider(
    preferences: preferences,
    restaurantProvider: restaurantProvider,
    platformProvider: platformProvider,
  );
  keywordProvider ??= KeywordProvider(
    restaurantProvider: restaurantProvider,
  );

  await tester.pumpWidget(MultiProvider(
    /* 
        This is for providing a global state to the app
        so we can access it from anywhere in the app
      */
    providers: [
      ChangeNotifierProvider(create: (_) => userProvider),
      ChangeNotifierProvider(create: (_) => restaurantProvider),
      ChangeNotifierProvider(create: (_) => platformProvider),
      ChangeNotifierProvider(create: (_) => messagingProvider),
      ChangeNotifierProxyProvider2<UserProvider, RestaurantProvider,
          AuthProvider>(
        update: (context, userProvider, restaurantProvider, authProvider) =>
            authProvider!,
        create: (context) => authProvider!,
      ),
      ChangeNotifierProxyProvider<RestaurantProvider, PlatformProvider>(
        update: (context, restaurantProvider, platformProvider) {
          if (platformProvider == null) {
            return platformProvider!;
          }
          platformProvider.restaurantProvider = restaurantProvider;
          return platformProvider;
        },
        create: (context) => platformProvider!,
      ),
      ChangeNotifierProxyProvider2<RestaurantProvider, PlatformProvider,
          MessagingProvider>(
        update:
            (context, restaurantProvider, platformProvider, messagingProvider) {
          if (messagingProvider == null) {
            return messagingProvider!;
          }
          return messagingProvider;
        },
        create: (context) => messagingProvider!,
      ),
      ChangeNotifierProxyProvider<RestaurantProvider, MediasProvider>(
        update: (context, restaurantProvider, platformProvider) =>
            mediasProvider!,
        create: (context) => mediasProvider!,
      ),
      ChangeNotifierProxyProvider2<RestaurantProvider, PlatformProvider,
          ReviewProvider>(
        update:
            (context, restaurantProvider, platformProvider, reviewProvider) {
          if (reviewProvider == null) {
            return reviewProvider!;
          }
          reviewProvider.platformProvider = platformProvider;
          reviewProvider.restaurantProvider = restaurantProvider;
          return reviewProvider;
        },
        create: (context) => reviewProvider!,
      ),
      ChangeNotifierProxyProvider<RestaurantProvider, KeywordProvider>(
        update: (context, restaurantProvider, keywordProvider) {
          if (keywordProvider == null) {
            return keywordProvider!;
          }
          keywordProvider.restaurantProvider = restaurantProvider;
          return keywordProvider;
        },
        create: (context) => keywordProvider!,
      ),
      ChangeNotifierProvider(
          create: (context) => LanguageProvider(preferences: preferences)),
    ],
    child: Consumer<LanguageProvider>(
      builder: (context, languageProvider, _) {
        return MaterialApp(
            builder: DevicePreview.appBuilder,
            locale: Locale(languageProvider.language.name),
            localizationsDelegates: const [
              Translation.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: Translation.supportedLocales,
            title: 'Malou',
            theme: MainTheme.theme,
            home: child);
      },
    ),
  ));
}
