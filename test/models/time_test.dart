import 'package:flutter/material.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/shared/helpers/time.dart';
import 'package:malou/shared/models/restaurant.model.dart';
import 'package:test/test.dart';

void main() {
  group('generateTextFromHours', () {
    late Translation translation;

    setUp(() async {
      translation = await Translation.delegate.load(const Locale('fr'));
    });

    test(
        'should return a correct text when all days have the same opening hours',
        () {
      final hours = [
        TimePeriod(
          closeDay: "MONDAY",
          closeTime: "01:00",
          isClosed: false,
          openDay: "MONDAY",
          openTime: "10:00",
        ),
        TimePeriod(
          closeDay: "TUESDAY",
          closeTime: "01:00",
          isClosed: false,
          openDay: "TUESDAY",
          openTime: "10:00",
        ),
        TimePeriod(
          closeDay: "WEDNESDAY",
          closeTime: "01:00",
          isClosed: false,
          openDay: "WEDNESDAY",
          openTime: "10:00",
        ),
        TimePeriod(
          closeDay: "THURSDAY",
          closeTime: "01:00",
          isClosed: false,
          openDay: "THURSDAY",
          openTime: "10:00",
        ),
        TimePeriod(
          closeDay: "FRIDAY",
          closeTime: "01:00",
          isClosed: false,
          openDay: "FRIDAY",
          openTime: "10:00",
        ),
        TimePeriod(
          closeDay: "SATURDAY",
          closeTime: "01:00",
          isClosed: false,
          openDay: "SATURDAY",
          openTime: "10:00",
        ),
        TimePeriod(
          closeDay: "SUNDAY",
          closeTime: "01:00",
          isClosed: false,
          openDay: "SUNDAY",
          openTime: "10:00",
        ),
      ];
      final text = generateTextFromHours(hours, 'fr', translation);
      expect(text, "Nous sommes ouvert du lundi au dimanche de 10h à 01h");
    });

    test('should return a correct text when only one opening hour is set', () {
      final hours = [
        TimePeriod(
          closeDay: "MONDAY",
          closeTime: "01:00",
          isClosed: false,
          openDay: "MONDAY",
          openTime: "10:00",
        )
      ];
      final text = generateTextFromHours(hours, 'fr', translation);
      expect(text, "Nous sommes ouvert le lundi de 10h à 01h");
    });

    test('should return an empty text when no opening hours are set', () {
      List<TimePeriod> hours = [];
      final text = generateTextFromHours(hours, 'fr', translation);
      expect(text, "");
    });
    test(
        'should return correct text when there is multiple hours for the same day',
        () {
      final hours = [
        TimePeriod(
          closeDay: "MONDAY",
          openTime: "11:00",
          closeTime: "15:00",
          isClosed: false,
          openDay: "MONDAY",
        ),
        TimePeriod(
          closeDay: "MONDAY",
          openTime: "18:00",
          closeTime: "22:00",
          isClosed: false,
          openDay: "MONDAY",
        )
      ];
      final text = generateTextFromHours(hours, 'fr', translation);
      expect(text, "Nous sommes ouvert le lundi de 11h à 15h et de 18h à 22h");
    });
  });
}
