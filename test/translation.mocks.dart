// Mocks generated by <PERSON><PERSON><PERSON> 5.4.2 from annotations
// in malou/test/pages/login.page.unit_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:malou/generated/l10n.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [Translation].
///
/// See the documentation for <PERSON><PERSON><PERSON>'s code generation for more information.
class MockTranslation extends _i1.Mock implements _i2.Translation {
  MockTranslation() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get aboutToSignOut => (super.noSuchMethod(
        Invocation.getter(#aboutToSignOut),
        returnValue: '',
      ) as String);
  @override
  String get addARestaurant => (super.noSuchMethod(
        Invocation.getter(#addARestaurant),
        returnValue: '',
      ) as String);
  @override
  String get addNow => (super.noSuchMethod(
        Invocation.getter(#addNow),
        returnValue: '',
      ) as String);
  @override
  String get addYourFirstRestaurant => (super.noSuchMethod(
        Invocation.getter(#addYourFirstRestaurant),
        returnValue: '',
      ) as String);
  @override
  String get all => (super.noSuchMethod(
        Invocation.getter(#all),
        returnValue: '',
      ) as String);
  @override
  String get analyzeReviewsNotAvailable => (super.noSuchMethod(
        Invocation.getter(#analyzeReviewsNotAvailable),
        returnValue: '',
      ) as String);
  @override
  String get and => (super.noSuchMethod(
        Invocation.getter(#and),
        returnValue: '',
      ) as String);
  @override
  String get anErrorOccured => (super.noSuchMethod(
        Invocation.getter(#anErrorOccured),
        returnValue: '',
      ) as String);
  @override
  String get answeredYourStory => (super.noSuchMethod(
        Invocation.getter(#answeredYourStory),
        returnValue: '',
      ) as String);
  @override
  String get archive => (super.noSuchMethod(
        Invocation.getter(#archive),
        returnValue: '',
      ) as String);
  @override
  String get archived => (super.noSuchMethod(
        Invocation.getter(#archived),
        returnValue: '',
      ) as String);
  @override
  String get askForDemo => (super.noSuchMethod(
        Invocation.getter(#askForDemo),
        returnValue: '',
      ) as String);
  @override
  String get atmosphere => (super.noSuchMethod(
        Invocation.getter(#atmosphere),
        returnValue: '',
      ) as String);
  @override
  String get authorizeMedias => (super.noSuchMethod(
        Invocation.getter(#authorizeMedias),
        returnValue: '',
      ) as String);
  @override
  String get autoReply => (super.noSuchMethod(
        Invocation.getter(#autoReply),
        returnValue: '',
      ) as String);
  @override
  String get brandAccount => (super.noSuchMethod(
        Invocation.getter(#brandAccount),
        returnValue: '',
      ) as String);
  @override
  String get cancel => (super.noSuchMethod(
        Invocation.getter(#cancel),
        returnValue: '',
      ) as String);
  @override
  String get changeLanguage => (super.noSuchMethod(
        Invocation.getter(#changeLanguage),
        returnValue: '',
      ) as String);
  @override
  String get clientName => (super.noSuchMethod(
        Invocation.getter(#clientName),
        returnValue: '',
      ) as String);
  @override
  String get concernedRatings => (super.noSuchMethod(
        Invocation.getter(#concernedRatings),
        returnValue: '',
      ) as String);
  @override
  String get connectMyPlatforms => (super.noSuchMethod(
        Invocation.getter(#connectMyPlatforms),
        returnValue: '',
      ) as String);
  @override
  String get contactMalouOps => (super.noSuchMethod(
        Invocation.getter(#contactMalouOps),
        returnValue: '',
      ) as String);
  @override
  String get conversation => (super.noSuchMethod(
        Invocation.getter(#conversation),
        returnValue: '',
      ) as String);
  @override
  String get conversationArchived => (super.noSuchMethod(
        Invocation.getter(#conversationArchived),
        returnValue: '',
      ) as String);
  @override
  String get conversationUnarchived => (super.noSuchMethod(
        Invocation.getter(#conversationUnarchived),
        returnValue: '',
      ) as String);
  @override
  String get courier_to_restaurant => (super.noSuchMethod(
        Invocation.getter(#courier_to_restaurant),
        returnValue: '',
      ) as String);
  @override
  String get customer_to_restaurant => (super.noSuchMethod(
        Invocation.getter(#customer_to_restaurant),
        returnValue: '',
      ) as String);
  @override
  String get customerReviews => (super.noSuchMethod(
        Invocation.getter(#customerReviews),
        returnValue: '',
      ) as String);
  @override
  String get customized => (super.noSuchMethod(
        Invocation.getter(#customized),
        returnValue: '',
      ) as String);
  @override
  String get daysLeftToReply => (super.noSuchMethod(
        Invocation.getter(#daysLeftToReply),
        returnValue: '',
      ) as String);
  @override
  String get displayAnalyzeReviews => (super.noSuchMethod(
        Invocation.getter(#displayAnalyzeReviews),
        returnValue: '',
      ) as String);
  @override
  String get dontForgetToAnswer => (super.noSuchMethod(
        Invocation.getter(#dontForgetToAnswer),
        returnValue: '',
      ) as String);
  @override
  String get dontLetYourCustomersWithoutAnswer => (super.noSuchMethod(
        Invocation.getter(#dontLetYourCustomersWithoutAnswer),
        returnValue: '',
      ) as String);
  @override
  String get eachTime => (super.noSuchMethod(
        Invocation.getter(#eachTime),
        returnValue: '',
      ) as String);
  @override
  String get email => (super.noSuchMethod(
        Invocation.getter(#email),
        returnValue: '',
      ) as String);
  @override
  String get emailSelected => (super.noSuchMethod(
        Invocation.getter(#emailSelected),
        returnValue: '',
      ) as String);
  @override
  String get emptyDeviceMedias => (super.noSuchMethod(
        Invocation.getter(#emptyDeviceMedias),
        returnValue: '',
      ) as String);
  @override
  String get en => (super.noSuchMethod(
        Invocation.getter(#en),
        returnValue: '',
      ) as String);
  @override
  String get enableNotifications => (super.noSuchMethod(
        Invocation.getter(#enableNotifications),
        returnValue: '',
      ) as String);
  @override
  String get enterPassword => (super.noSuchMethod(
        Invocation.getter(#enterPassword),
        returnValue: '',
      ) as String);
  @override
  String get errorRequired => (super.noSuchMethod(
        Invocation.getter(#errorRequired),
        returnValue: '',
      ) as String);
  @override
  String get es => (super.noSuchMethod(
        Invocation.getter(#es),
        returnValue: '',
      ) as String);
  @override
  String get expeditiousness => (super.noSuchMethod(
        Invocation.getter(#expeditiousness),
        returnValue: '',
      ) as String);
  @override
  String get favorite => (super.noSuchMethod(
        Invocation.getter(#favorite),
        returnValue: '',
      ) as String);
  @override
  String get filter => (super.noSuchMethod(
        Invocation.getter(#filter),
        returnValue: '',
      ) as String);
  @override
  String get food => (super.noSuchMethod(
        Invocation.getter(#food),
        returnValue: '',
      ) as String);
  @override
  String get forgot_password_explanation => (super.noSuchMethod(
        Invocation.getter(#forgot_password_explanation),
        returnValue: '',
      ) as String);
  @override
  String get forgot_password_sent => (super.noSuchMethod(
        Invocation.getter(#forgot_password_sent),
        returnValue: '',
      ) as String);
  @override
  String get forgotPassword => (super.noSuchMethod(
        Invocation.getter(#forgotPassword),
        returnValue: '',
      ) as String);
  @override
  String get fr => (super.noSuchMethod(
        Invocation.getter(#fr),
        returnValue: '',
      ) as String);
  @override
  String get frequency => (super.noSuchMethod(
        Invocation.getter(#frequency),
        returnValue: '',
      ) as String);
  @override
  String get friday => (super.noSuchMethod(
        Invocation.getter(#friday),
        returnValue: '',
      ) as String);
  @override
  String get general => (super.noSuchMethod(
        Invocation.getter(#general),
        returnValue: '',
      ) as String);
  @override
  String get home => (super.noSuchMethod(
        Invocation.getter(#home),
        returnValue: '',
      ) as String);
  @override
  String get hygiene => (super.noSuchMethod(
        Invocation.getter(#hygiene),
        returnValue: '',
      ) as String);
  @override
  String get includeReviewsWithAutoAnswer => (super.noSuchMethod(
        Invocation.getter(#includeReviewsWithAutoAnswer),
        returnValue: '',
      ) as String);
  @override
  String get invalidCredentials => (super.noSuchMethod(
        Invocation.getter(#invalidCredentials),
        returnValue: '',
      ) as String);
  @override
  String get invalidEmailMessage => (super.noSuchMethod(
        Invocation.getter(#invalidEmailMessage),
        returnValue: '',
      ) as String);
  @override
  String get invalidPassword => (super.noSuchMethod(
        Invocation.getter(#invalidPassword),
        returnValue: '',
      ) as String);
  @override
  String get it => (super.noSuchMethod(
        Invocation.getter(#it),
        returnValue: '',
      ) as String);
  @override
  String get keywords => (super.noSuchMethod(
        Invocation.getter(#keywords),
        returnValue: '',
      ) as String);
  @override
  String get keywords_score => (super.noSuchMethod(
        Invocation.getter(#keywords_score),
        returnValue: '',
      ) as String);
  @override
  String get keywordsAdvices => (super.noSuchMethod(
        Invocation.getter(#keywordsAdvices),
        returnValue: '',
      ) as String);
  @override
  String get keywordScoreAdviceRestaurantName => (super.noSuchMethod(
        Invocation.getter(#keywordScoreAdviceRestaurantName),
        returnValue: '',
      ) as String);
  @override
  String get keywordScoreAdviceSimpleBricksNumber => (super.noSuchMethod(
        Invocation.getter(#keywordScoreAdviceSimpleBricksNumber),
        returnValue: '',
      ) as String);
  @override
  String get keywordScoreAdviceSimpleBricksVariety => (super.noSuchMethod(
        Invocation.getter(#keywordScoreAdviceSimpleBricksVariety),
        returnValue: '',
      ) as String);
  @override
  String get keywordScoreAdviceSimpleBricksVarietyTip => (super.noSuchMethod(
        Invocation.getter(#keywordScoreAdviceSimpleBricksVarietyTip),
        returnValue: '',
      ) as String);
  @override
  String get keywordScoreAdviceSorry => (super.noSuchMethod(
        Invocation.getter(#keywordScoreAdviceSorry),
        returnValue: '',
      ) as String);
  @override
  String get keywordScoreAdviceSorryTip => (super.noSuchMethod(
        Invocation.getter(#keywordScoreAdviceSorryTip),
        returnValue: '',
      ) as String);
  @override
  String get languages => (super.noSuchMethod(
        Invocation.getter(#languages),
        returnValue: '',
      ) as String);
  @override
  String get lastThirtyDays => (super.noSuchMethod(
        Invocation.getter(#lastThirtyDays),
        returnValue: '',
      ) as String);
  @override
  String get later => (super.noSuchMethod(
        Invocation.getter(#later),
        returnValue: '',
      ) as String);
  @override
  String get legalMentions => (super.noSuchMethod(
        Invocation.getter(#legalMentions),
        returnValue: '',
      ) as String);
  @override
  String get legalMentions1 => (super.noSuchMethod(
        Invocation.getter(#legalMentions1),
        returnValue: '',
      ) as String);
  @override
  String get legalMentions1Details => (super.noSuchMethod(
        Invocation.getter(#legalMentions1Details),
        returnValue: '',
      ) as String);
  @override
  String get legalMentionsA => (super.noSuchMethod(
        Invocation.getter(#legalMentionsA),
        returnValue: '',
      ) as String);
  @override
  String get legalMentionsADetails => (super.noSuchMethod(
        Invocation.getter(#legalMentionsADetails),
        returnValue: '',
      ) as String);
  @override
  String get legalMentionsTitle => (super.noSuchMethod(
        Invocation.getter(#legalMentionsTitle),
        returnValue: '',
      ) as String);
  @override
  String get loginPageSubtitle => (super.noSuchMethod(
        Invocation.getter(#loginPageSubtitle),
        returnValue: '',
      ) as String);
  @override
  String get malouGallery => (super.noSuchMethod(
        Invocation.getter(#malouGallery),
        returnValue: '',
      ) as String);
  @override
  String get manageNotifs => (super.noSuchMethod(
        Invocation.getter(#manageNotifs),
        returnValue: '',
      ) as String);
  @override
  String get markedAsRead => (super.noSuchMethod(
        Invocation.getter(#markedAsRead),
        returnValue: '',
      ) as String);
  @override
  String get markedAsUnread => (super.noSuchMethod(
        Invocation.getter(#markedAsUnread),
        returnValue: '',
      ) as String);
  @override
  String get mediasAccessDescription => (super.noSuchMethod(
        Invocation.getter(#mediasAccessDescription),
        returnValue: '',
      ) as String);
  @override
  String get mentionnedYouInStory => (super.noSuchMethod(
        Invocation.getter(#mentionnedYouInStory),
        returnValue: '',
      ) as String);
  @override
  String get messageNotAvailable => (super.noSuchMethod(
        Invocation.getter(#messageNotAvailable),
        returnValue: '',
      ) as String);
  @override
  String get messages => (super.noSuchMethod(
        Invocation.getter(#messages),
        returnValue: '',
      ) as String);
  @override
  String get midnight => (super.noSuchMethod(
        Invocation.getter(#midnight),
        returnValue: '',
      ) as String);
  @override
  String get monday => (super.noSuchMethod(
        Invocation.getter(#monday),
        returnValue: '',
      ) as String);
  @override
  String get myMedias => (super.noSuchMethod(
        Invocation.getter(#myMedias),
        returnValue: '',
      ) as String);
  @override
  String get myName => (super.noSuchMethod(
        Invocation.getter(#myName),
        returnValue: '',
      ) as String);
  @override
  String get myTemplates => (super.noSuchMethod(
        Invocation.getter(#myTemplates),
        returnValue: '',
      ) as String);
  @override
  String get name => (super.noSuchMethod(
        Invocation.getter(#name),
        returnValue: '',
      ) as String);
  @override
  String get newReview => (super.noSuchMethod(
        Invocation.getter(#newReview),
        returnValue: '',
      ) as String);
  @override
  String get no_results => (super.noSuchMethod(
        Invocation.getter(#no_results),
        returnValue: '',
      ) as String);
  @override
  String get noAccessToMedias => (super.noSuchMethod(
        Invocation.getter(#noAccessToMedias),
        returnValue: '',
      ) as String);
  @override
  String get noAccountQuestion => (super.noSuchMethod(
        Invocation.getter(#noAccountQuestion),
        returnValue: '',
      ) as String);
  @override
  String get noKeywordsYet => (super.noSuchMethod(
        Invocation.getter(#noKeywordsYet),
        returnValue: '',
      ) as String);
  @override
  String get noMessagesYet => (super.noSuchMethod(
        Invocation.getter(#noMessagesYet),
        returnValue: '',
      ) as String);
  @override
  String get noMoreItemsInTheList => (super.noSuchMethod(
        Invocation.getter(#noMoreItemsInTheList),
        returnValue: '',
      ) as String);
  @override
  String get noon => (super.noSuchMethod(
        Invocation.getter(#noon),
        returnValue: '',
      ) as String);
  @override
  String get noRestaurantForYourResearch => (super.noSuchMethod(
        Invocation.getter(#noRestaurantForYourResearch),
        returnValue: '',
      ) as String);
  @override
  String get noRestaurantQuestion => (super.noSuchMethod(
        Invocation.getter(#noRestaurantQuestion),
        returnValue: '',
      ) as String);
  @override
  String get noResults => (super.noSuchMethod(
        Invocation.getter(#noResults),
        returnValue: '',
      ) as String);
  @override
  String get noReviews => (super.noSuchMethod(
        Invocation.getter(#noReviews),
        returnValue: '',
      ) as String);
  @override
  String get notAnsweredMessages => (super.noSuchMethod(
        Invocation.getter(#notAnsweredMessages),
        returnValue: '',
      ) as String);
  @override
  String get notAnsweredReviews => (super.noSuchMethod(
        Invocation.getter(#notAnsweredReviews),
        returnValue: '',
      ) as String);
  @override
  String get noTemplates => (super.noSuchMethod(
        Invocation.getter(#noTemplates),
        returnValue: '',
      ) as String);
  @override
  String get notifCenter => (super.noSuchMethod(
        Invocation.getter(#notifCenter),
        returnValue: '',
      ) as String);
  @override
  String get object => (super.noSuchMethod(
        Invocation.getter(#object),
        returnValue: '',
      ) as String);
  @override
  String get onceADay => (super.noSuchMethod(
        Invocation.getter(#onceADay),
        returnValue: '',
      ) as String);
  @override
  String get onceADayWithParenthesis => (super.noSuchMethod(
        Invocation.getter(#onceADayWithParenthesis),
        returnValue: '',
      ) as String);
  @override
  String get onlyForGoogleReviews => (super.noSuchMethod(
        Invocation.getter(#onlyForGoogleReviews),
        returnValue: '',
      ) as String);
  @override
  String get oupsSeeNothing => (super.noSuchMethod(
        Invocation.getter(#oupsSeeNothing),
        returnValue: '',
      ) as String);
  @override
  String get overallExperience => (super.noSuchMethod(
        Invocation.getter(#overallExperience),
        returnValue: '',
      ) as String);
  @override
  String get password => (super.noSuchMethod(
        Invocation.getter(#password),
        returnValue: '',
      ) as String);
  @override
  String get platformsDisconnected => (super.noSuchMethod(
        Invocation.getter(#platformsDisconnected),
        returnValue: '',
      ) as String);
  @override
  String get price => (super.noSuchMethod(
        Invocation.getter(#price),
        returnValue: '',
      ) as String);
  @override
  String get question => (super.noSuchMethod(
        Invocation.getter(#question),
        returnValue: '',
      ) as String);
  @override
  String get questionsAnswers => (super.noSuchMethod(
        Invocation.getter(#questionsAnswers),
        returnValue: '',
      ) as String);
  @override
  String get read => (super.noSuchMethod(
        Invocation.getter(#read),
        returnValue: '',
      ) as String);
  @override
  String get receivedAStory => (super.noSuchMethod(
        Invocation.getter(#receivedAStory),
        returnValue: '',
      ) as String);
  @override
  String get receivedReviews => (super.noSuchMethod(
        Invocation.getter(#receivedReviews),
        returnValue: '',
      ) as String);
  @override
  String get refreshed => (super.noSuchMethod(
        Invocation.getter(#refreshed),
        returnValue: '',
      ) as String);
  @override
  String get reply => (super.noSuchMethod(
        Invocation.getter(#reply),
        returnValue: '',
      ) as String);
  @override
  String get replyByMail => (super.noSuchMethod(
        Invocation.getter(#replyByMail),
        returnValue: '',
      ) as String);
  @override
  String get restaurantAddress => (super.noSuchMethod(
        Invocation.getter(#restaurantAddress),
        returnValue: '',
      ) as String);
  @override
  String get restaurantMenu => (super.noSuchMethod(
        Invocation.getter(#restaurantMenu),
        returnValue: '',
      ) as String);
  @override
  String get restaurantName => (super.noSuchMethod(
        Invocation.getter(#restaurantName),
        returnValue: '',
      ) as String);
  @override
  String get restaurantOpeningHours => (super.noSuchMethod(
        Invocation.getter(#restaurantOpeningHours),
        returnValue: '',
      ) as String);
  @override
  String get restaurantPhone => (super.noSuchMethod(
        Invocation.getter(#restaurantPhone),
        returnValue: '',
      ) as String);
  @override
  String get restaurantWebsite => (super.noSuchMethod(
        Invocation.getter(#restaurantWebsite),
        returnValue: '',
      ) as String);
  @override
  String get retry => (super.noSuchMethod(
        Invocation.getter(#retry),
        returnValue: '',
      ) as String);
  @override
  String get reviewAnalyseFailed => (super.noSuchMethod(
        Invocation.getter(#reviewAnalyseFailed),
        returnValue: '',
      ) as String);
  @override
  String get reviewAnalysesRunning => (super.noSuchMethod(
        Invocation.getter(#reviewAnalysesRunning),
        returnValue: '',
      ) as String);
  @override
  String get reviewAnalysesUnknownStatus => (super.noSuchMethod(
        Invocation.getter(#reviewAnalysesUnknownStatus),
        returnValue: '',
      ) as String);
  @override
  String get reviewAnalyzedError => (super.noSuchMethod(
        Invocation.getter(#reviewAnalyzedError),
        returnValue: '',
      ) as String);
  @override
  String get reviewAnalyzedNoResult => (super.noSuchMethod(
        Invocation.getter(#reviewAnalyzedNoResult),
        returnValue: '',
      ) as String);
  @override
  String get reviewAnalyzedTooOld => (super.noSuchMethod(
        Invocation.getter(#reviewAnalyzedTooOld),
        returnValue: '',
      ) as String);
  @override
  String get reviewFrom => (super.noSuchMethod(
        Invocation.getter(#reviewFrom),
        returnValue: '',
      ) as String);
  @override
  String get reviewNotYetAnalyzed => (super.noSuchMethod(
        Invocation.getter(#reviewNotYetAnalyzed),
        returnValue: '',
      ) as String);
  @override
  String get reviewReplyError => (super.noSuchMethod(
        Invocation.getter(#reviewReplyError),
        returnValue: '',
      ) as String);
  @override
  String get reviewReplySentSuccessfully => (super.noSuchMethod(
        Invocation.getter(#reviewReplySentSuccessfully),
        returnValue: '',
      ) as String);
  @override
  String get reviews => (super.noSuchMethod(
        Invocation.getter(#reviews),
        returnValue: '',
      ) as String);
  @override
  String get reviewScores => (super.noSuchMethod(
        Invocation.getter(#reviewScores),
        returnValue: '',
      ) as String);
  @override
  String get reviewsRating => (super.noSuchMethod(
        Invocation.getter(#reviewsRating),
        returnValue: '',
      ) as String);
  @override
  String get reviewsSyncError => (super.noSuchMethod(
        Invocation.getter(#reviewsSyncError),
        returnValue: '',
      ) as String);
  @override
  String get reviewsSyncSuccess => (super.noSuchMethod(
        Invocation.getter(#reviewsSyncSuccess),
        returnValue: '',
      ) as String);
  @override
  String get saturday => (super.noSuchMethod(
        Invocation.getter(#saturday),
        returnValue: '',
      ) as String);
  @override
  String get search => (super.noSuchMethod(
        Invocation.getter(#search),
        returnValue: '',
      ) as String);
  @override
  String get semanticAnalyse => (super.noSuchMethod(
        Invocation.getter(#semanticAnalyse),
        returnValue: '',
      ) as String);
  @override
  String get sendError => (super.noSuchMethod(
        Invocation.getter(#sendError),
        returnValue: '',
      ) as String);
  @override
  String get service => (super.noSuchMethod(
        Invocation.getter(#service),
        returnValue: '',
      ) as String);
  @override
  String get set_email => (super.noSuchMethod(
        Invocation.getter(#set_email),
        returnValue: '',
      ) as String);
  @override
  String get settings => (super.noSuchMethod(
        Invocation.getter(#settings),
        returnValue: '',
      ) as String);
  @override
  String get signOut => (super.noSuchMethod(
        Invocation.getter(#signOut),
        returnValue: '',
      ) as String);
  @override
  String get storyExpired => (super.noSuchMethod(
        Invocation.getter(#storyExpired),
        returnValue: '',
      ) as String);
  @override
  String get sunday => (super.noSuchMethod(
        Invocation.getter(#sunday),
        returnValue: '',
      ) as String);
  @override
  String get thursday => (super.noSuchMethod(
        Invocation.getter(#thursday),
        returnValue: '',
      ) as String);
  @override
  String get timeLimitReplyReviewExceeded => (super.noSuchMethod(
        Invocation.getter(#timeLimitReplyReviewExceeded),
        returnValue: '',
      ) as String);
  @override
  String get todo => (super.noSuchMethod(
        Invocation.getter(#todo),
        returnValue: '',
      ) as String);
  @override
  String get tooManyNotif => (super.noSuchMethod(
        Invocation.getter(#tooManyNotif),
        returnValue: '',
      ) as String);
  @override
  String get try_another_search => (super.noSuchMethod(
        Invocation.getter(#try_another_search),
        returnValue: '',
      ) as String);
  @override
  String get tuesday => (super.noSuchMethod(
        Invocation.getter(#tuesday),
        returnValue: '',
      ) as String);
  @override
  String get unAnswered => (super.noSuchMethod(
        Invocation.getter(#unAnswered),
        returnValue: '',
      ) as String);
  @override
  String get unansweredReviews => (super.noSuchMethod(
        Invocation.getter(#unansweredReviews),
        returnValue: '',
      ) as String);
  @override
  String get unarchive => (super.noSuchMethod(
        Invocation.getter(#unarchive),
        returnValue: '',
      ) as String);
  @override
  String get unauthorized => (super.noSuchMethod(
        Invocation.getter(#unauthorized),
        returnValue: '',
      ) as String);
  @override
  String get unavailableMessage => (super.noSuchMethod(
        Invocation.getter(#unavailableMessage),
        returnValue: '',
      ) as String);
  @override
  String get unread => (super.noSuchMethod(
        Invocation.getter(#unread),
        returnValue: '',
      ) as String);
  @override
  String get unreadMessages => (super.noSuchMethod(
        Invocation.getter(#unreadMessages),
        returnValue: '',
      ) as String);
  @override
  String get unsupportedAttachment => (super.noSuchMethod(
        Invocation.getter(#unsupportedAttachment),
        returnValue: '',
      ) as String);
  @override
  String get urgent => (super.noSuchMethod(
        Invocation.getter(#urgent),
        returnValue: '',
      ) as String);
  @override
  String get validate => (super.noSuchMethod(
        Invocation.getter(#validate),
        returnValue: '',
      ) as String);
  @override
  String get we_are => (super.noSuchMethod(
        Invocation.getter(#we_are),
        returnValue: '',
      ) as String);
  @override
  String get wednesday => (super.noSuchMethod(
        Invocation.getter(#wednesday),
        returnValue: '',
      ) as String);
  @override
  String get whatsNew => (super.noSuchMethod(
        Invocation.getter(#whatsNew),
        returnValue: '',
      ) as String);
  @override
  String get writeAnswerReview => (super.noSuchMethod(
        Invocation.getter(#writeAnswerReview),
        returnValue: '',
      ) as String);
  @override
  String get writeMessage => (super.noSuchMethod(
        Invocation.getter(#writeMessage),
        returnValue: '',
      ) as String);
  @override
  String get youAnsweredAStory => (super.noSuchMethod(
        Invocation.getter(#youAnsweredAStory),
        returnValue: '',
      ) as String);
  @override
  String get youMentionnedInStory => (super.noSuchMethod(
        Invocation.getter(#youMentionnedInStory),
        returnValue: '',
      ) as String);
  @override
  String get your_businesses => (super.noSuchMethod(
        Invocation.getter(#your_businesses),
        returnValue: '',
      ) as String);
  @override
  String closed_day(String? closedDay) => (super.noSuchMethod(
        Invocation.method(
          #closed_day,
          [closedDay],
        ),
        returnValue: '',
      ) as String);
  @override
  String closed_days(
    String? startDay,
    String? endDay,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #closed_days,
          [
            startDay,
            endDay,
          ],
        ),
        returnValue: '',
      ) as String);
  @override
  String dailyUnansweredReviewsNotificationText(int? count) =>
      (super.noSuchMethod(
        Invocation.method(
          #dailyUnansweredReviewsNotificationText,
          [count],
        ),
        returnValue: '',
      ) as String);
  @override
  String dailyUnreadMessagesNotificationText(int? count) => (super.noSuchMethod(
        Invocation.method(
          #dailyUnreadMessagesNotificationText,
          [count],
        ),
        returnValue: '',
      ) as String);
  @override
  String forgot_password_has_been_sent(String? email) => (super.noSuchMethod(
        Invocation.method(
          #forgot_password_has_been_sent,
          [email],
        ),
        returnValue: '',
      ) as String);
  @override
  String manyPlatformDisconnectedWarning(String? platform) =>
      (super.noSuchMethod(
        Invocation.method(
          #manyPlatformDisconnectedWarning,
          [platform],
        ),
        returnValue: '',
      ) as String);
  @override
  String minOccurences(int? minOccurences) => (super.noSuchMethod(
        Invocation.method(
          #minOccurences,
          [minOccurences],
        ),
        returnValue: '',
      ) as String);
  @override
  String nbReplies(
    Object? nbReplies,
    Object? nbReviews,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #nbReplies,
          [
            nbReplies,
            nbReviews,
          ],
        ),
        returnValue: '',
      ) as String);
  @override
  String noTemplatesDetails(Object? entity) => (super.noSuchMethod(
        Invocation.method(
          #noTemplatesDetails,
          [entity],
        ),
        returnValue: '',
      ) as String);
  @override
  String open_day(
    String? openDay,
    String? openTime,
    String? closeTime,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #open_day,
          [
            openDay,
            openTime,
            closeTime,
          ],
        ),
        returnValue: '',
      ) as String);
  @override
  String open_day_more(
    String? openTime2,
    String? closeTime2,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #open_day_more,
          [
            openTime2,
            closeTime2,
          ],
        ),
        returnValue: '',
      ) as String);
  @override
  String open_days(
    String? startDay,
    String? openTime,
    String? endDay,
    String? closeTime,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #open_days,
          [
            startDay,
            openTime,
            endDay,
            closeTime,
          ],
        ),
        returnValue: '',
      ) as String);
  @override
  String platformDisconnectedWarning(String? platform) => (super.noSuchMethod(
        Invocation.method(
          #platformDisconnectedWarning,
          [platform],
        ),
        returnValue: '',
      ) as String);
  @override
  String receivedAMedia(String? mediaType) => (super.noSuchMethod(
        Invocation.method(
          #receivedAMedia,
          [mediaType],
        ),
        returnValue: '',
      ) as String);
  @override
  String reconnectPlatform(String? missingPlatform) => (super.noSuchMethod(
        Invocation.method(
          #reconnectPlatform,
          [missingPlatform],
        ),
        returnValue: '',
      ) as String);
  @override
  String sentAMedia(String? mediaType) => (super.noSuchMethod(
        Invocation.method(
          #sentAMedia,
          [mediaType],
        ),
        returnValue: '',
      ) as String);
}
