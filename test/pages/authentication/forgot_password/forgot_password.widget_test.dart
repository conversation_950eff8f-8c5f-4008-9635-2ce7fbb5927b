import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:malou/pages/authentication/forgot_password/forgot_password.page.dart';
import 'package:malou/shared/widgets/buttons/round_button.widget.dart';
import 'package:mockito/mockito.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../utils/wrapper.dart';

class MockSharedPreferences extends Mock implements SharedPreferences {}

void main() {
  group('ForgotPasswordPage Unit tests', () {
    late SharedPreferences preferences;

    setUp(() async {
      SharedPreferences.setMockInitialValues({});
      KeyboardVisibilityTesting.setVisibilityForTesting(true);
      preferences = MockSharedPreferences();
      when(preferences.getString('language')).thenReturn('fr');
    });

    testWidgets('Should display all the elements of the login page',
        (WidgetTester tester) async {
      await wrapWithApp(
          tester,
          preferences,
          const TickerMode(
            enabled: false,
            child: ForgotPasswordPage(),
          ));
      await tester.pumpAndSettle();

      // email field
      expect(find.byType(TextFormField), findsOneWidget);
      // submit button
      expect(find.byKey(const Key('forgot_password_submit_button')),
          findsOneWidget);
      // logo
      expect(find.byType(SvgPicture), findsOneWidget);
    });

    testWidgets('Should display error message when email is empty',
        (WidgetTester tester) async {
      await wrapWithApp(
          tester,
          preferences,
          const TickerMode(
            enabled: false,
            child: ForgotPasswordPage(),
          ));
      await tester.pumpAndSettle();

      final emailField = find.byType(TextFormField).first;

      // Enter invalid email
      await tester.enterText(emailField, 'blabada');
      await tester.pumpAndSettle(const Duration(milliseconds: 500));

      expect(find.byType(Tooltip), findsOneWidget);
      expect(find.byIcon(Icons.warning_amber_outlined), findsOneWidget);
    });

    testWidgets('Login page shows tapable button when form is valid',
        (WidgetTester tester) async {
      await wrapWithApp(
          tester,
          preferences,
          const TickerMode(
            enabled: false,
            child: ForgotPasswordPage(),
          ));
      await tester.pumpAndSettle(const Duration(seconds: 4));

      final emailField = find.byType(TextFormField).first;
      final submitButton =
          find.byKey(const Key('forgot_password_submit_button'));

      // Enter valid email and password
      await tester.enterText(emailField, '<EMAIL>');

      // Tap login button
      await tester.pumpAndSettle(const Duration(milliseconds: 500));

      // Expect error message to be shown
      expect(find.byType(Tooltip), findsNothing);
      expect(find.byIcon(Icons.warning_amber_outlined), findsNothing);
      // check if submitButton is tapable
      expect(submitButton, findsOneWidget);
      expect(
        tester.widget<RoundButton>(submitButton).isFormValid,
        true,
      );
      expect(tester.widget<RoundButton>(submitButton).isSubmitting, false);
    });
  });
}
