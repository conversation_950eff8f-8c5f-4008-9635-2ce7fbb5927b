import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:malou/pages/authentication/forgot_password/forgot_password_email_sent.page.dart';
import 'package:mockito/mockito.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../utils/wrapper.dart';

class MockSharedPreferences extends Mo<PERSON> implements SharedPreferences {}

void main() {
  group('ForgotPasswordPage Unit tests', () {
    late SharedPreferences preferences;

    setUp(() async {
      SharedPreferences.setMockInitialValues({});
      KeyboardVisibilityTesting.setVisibilityForTesting(true);
      preferences = MockSharedPreferences();
      when(preferences.getString('language')).thenReturn('fr');
    });

    testWidgets('Should display all the elements of the login page',
        (WidgetTester tester) async {
      await wrapWithApp(
          tester,
          preferences,
          const TickerMode(
            enabled: false,
            child: ForgotPasswordEmailSent(
              email: '<EMAIL>',
            ),
          ));
      await tester.pumpAndSettle();

      // email field
      expect(find.byType(Text), findsNWidgets(2));
      // text should contain the email
      expect(find.textContaining('<EMAIL>'), findsOneWidget);
      // submit button
      expect(find.byKey(const Key('forgot_password_email_sent_button')),
          findsOneWidget);
      // logo
      expect(find.byType(SvgPicture), findsOneWidget);
    });
  });
}
