import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:malou/generated/l10n.dart';
import 'package:malou/pages/authentication/login/login.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/services/auth/auth.provider.dart';
import 'package:malou/shared/services/user/user.provider.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import '../../../translation.mocks.dart';

@GenerateMocks([AuthProvider, UserProvider])
void main() {
  final Translation translation = MockTranslation();
  group('LoginPage Unit', () {
    group('clarifyError', () {
      test('returns invalidCredentials when status code is 404', () {
        final res = ApiResult(statusCode: 404, message: 'Not Found');
        when(translation.invalidCredentials).thenReturn('Invalid credentials');

        expect(Login.clarifyError(res, translation: translation),
            equals('Invalid credentials'));
      });

      test('returns message when status code is not 404', () {
        final res =
            ApiResult(statusCode: 500, message: 'Internal Server Error');
        expect(Login.clarifyError(res, translation: translation),
            equals('Internal Server Error'));
      });
    });

    group('emailValidatorFn', () {
      test('returns null when email is valid', () {
        expect(
            Login.emailValidatorFn('<EMAIL>', translation: translation),
            isNull);
      });

      test('returns invalidEmailMessage when email is invalid', () {
        when(translation.invalidEmailMessage)
            .thenReturn('Invalid email message');

        expect(Login.emailValidatorFn('test', translation: translation),
            equals('Invalid email message'));
      });
    });

    group('checkIsFormValid', () {
      test('returns true when email and password are valid', () {
        final emailController = TextEditingController(text: '<EMAIL>');
        final passwordController = TextEditingController(text: 'password');

        final result =
            Login.checkIsFormValid(emailController, passwordController);

        expect(result, true);
      });

      test('returns false when email is invalid', () {
        final emailController = TextEditingController(text: 'invalid-email');
        final passwordController = TextEditingController(text: 'password');

        final result =
            Login.checkIsFormValid(emailController, passwordController);

        expect(result, false);
      });

      test('returns false when password is empty', () {
        final emailController = TextEditingController(text: '<EMAIL>');
        final passwordController = TextEditingController(text: '');

        final result =
            Login.checkIsFormValid(emailController, passwordController);

        expect(result, false);
      });
    });
  });
}
