import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:malou/pages/authentication/login/login.page.dart';
import 'package:malou/shared/helpers/api_result.model.dart';
import 'package:malou/shared/services/auth/auth.provider.dart';

import 'package:malou/shared/widgets/buttons/round_button.widget.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../utils/wrapper.dart';
import 'login.page.unit_test.mocks.dart';

class MockSharedPreferences extends Mock implements SharedPreferences {}

@GenerateMocks([AuthProvider])
void main() {
  group('Login Page widgets', () {
    late SharedPreferences preferences;
    late MockAuthProvider authProvider;

    setUp(() async {
      SharedPreferences.setMockInitialValues({});
      KeyboardVisibilityTesting.setVisibilityForTesting(true);
      preferences = MockSharedPreferences();
      authProvider = MockAuthProvider();
      when(preferences.getString('language')).thenReturn('fr');
    });

    testWidgets('Should display all the elements of the login page',
        (WidgetTester tester) async {
      await wrapWithApp(
          tester,
          preferences,
          const TickerMode(
            enabled: false,
            child: LoginPage(),
          ));
      await tester.pumpAndSettle(const Duration(seconds: 4));
      // submit button
      expect(find.byIcon(Icons.arrow_forward), findsOneWidget);
      // form fields
      expect(find.byType(TextFormField), findsNWidgets(2));
      // logo
      expect(find.byType(SvgPicture), findsOneWidget);
      // forgot password
      expect(find.byType(TextButton), findsOneWidget);
    });

    testWidgets('Login page shows error message when email is invalid',
        (WidgetTester tester) async {
      await wrapWithApp(
          tester,
          preferences,
          const TickerMode(
            enabled: false,
            child: LoginPage(),
          ));
      await tester.pumpAndSettle(const Duration(seconds: 4));

      final emailField = find.byType(TextFormField).first;
      final passwordField = find.byType(TextFormField).at(1);

      // Enter invalid email
      await tester.enterText(emailField, 'invalid-email');
      await tester.enterText(passwordField, 'password');

      // Tap login button
      await tester.pumpAndSettle(const Duration(seconds: 1));

      // Expect error message to be shown
      expect(find.byType(Tooltip), findsOneWidget);
      expect(find.byIcon(Icons.warning_amber_outlined), findsOneWidget);
    });

    testWidgets('Login page shows error message when password is empty',
        (WidgetTester tester) async {
      await wrapWithApp(
          tester,
          preferences,
          const TickerMode(
            enabled: false,
            child: LoginPage(),
          ));
      await tester.pumpAndSettle(const Duration(seconds: 4));

      final emailField = find.byType(TextFormField).first;
      final passwordField = find.byType(TextFormField).at(1);

      // Enter valid email but empty password
      await tester.enterText(emailField, '<EMAIL>');
      await tester.enterText(passwordField, 'x');
      // clear
      await tester.enterText(passwordField, '');

      // Tap login button
      await tester.pumpAndSettle(const Duration(seconds: 1));

      // Expect error message to be shown
      expect(find.byType(Tooltip), findsOneWidget);
      expect(find.byIcon(Icons.warning_amber_outlined), findsOneWidget);
    });

    testWidgets('Login page shows tapable button when form is valid',
        (WidgetTester tester) async {
      await wrapWithApp(
          tester,
          preferences,
          const TickerMode(
            enabled: false,
            child: LoginPage(),
          ));
      await tester.pumpAndSettle(const Duration(seconds: 4));

      final emailField = find.byType(TextFormField).first;
      final passwordField = find.byType(TextFormField).at(1);
      final submitButton = find.byKey(const Key('login_submit_button'));

      // Enter valid email and password
      await tester.enterText(emailField, '<EMAIL>');
      await tester.enterText(passwordField, 'password');

      // Tap login button
      await tester.pumpAndSettle(const Duration(seconds: 1));

      // Expect error message to be shown
      expect(find.byType(Tooltip), findsNothing);
      expect(find.byIcon(Icons.warning_amber_outlined), findsNothing);
      // check if submitButton is tapable
      expect(submitButton, findsOneWidget);
      expect(
        tester.widget<RoundButton>(submitButton).isFormValid,
        true,
      );
      expect(tester.widget<RoundButton>(submitButton).isSubmitting, false);
    });

    testWidgets('Login page shows error dialog when wrong credentials',
        (WidgetTester tester) async {
      when(authProvider.checkForSession()).thenAnswer(
          (_) async => Future.delayed(const Duration(seconds: 1), () => false));
      when(authProvider.login(any)).thenAnswer((_) async => Future.delayed(
          const Duration(seconds: 1),
          () => ApiResult(
                error: true,
                message: 'veuillez vérifier vos identifiants',
              )));
      await wrapWithApp(
          tester,
          preferences,
          const TickerMode(
            enabled: false,
            child: LoginPage(),
          ),
          authProvider: authProvider);
      await tester.pumpAndSettle(const Duration(seconds: 4));

      final emailField = find.byType(TextFormField).first;
      final passwordField = find.byType(TextFormField).at(1);
      final submitButton = find.byKey(const Key('login_submit_button'));

      // Enter valid email and password
      await tester.enterText(emailField, '<EMAIL>');
      await tester.enterText(passwordField, 'password');

      await tester.pump(const Duration(seconds: 1));

      // Tap login button
      await tester.tap(submitButton);
      await tester.pump(const Duration(milliseconds: 500));
      // Expect loader to be shown
      expect(find.byKey(const Key('login_submit_loader')), findsOneWidget);

      await tester.pump(const Duration(seconds: 2));
      // Expect error dialog to be shown
      expect(find.byKey(const Key('login_error_dialog')), findsOneWidget);
    });

    // testWidgets('Login page navigates to next screen when credentials are ok',
    //     (WidgetTester tester) async {
    //   when(authProvider.login(any)).thenAnswer((_) async => Future.delayed(
    //       const Duration(seconds: 1),
    //       () => ApiResult(
    //             error: false,
    //             message: 'success',
    //           )));
    //   await wrapWithApp(
    //       tester,
    //       preferences,
    //       const TickerMode(
    //         enabled: false,
    //         child: LoginPage(),
    //       ),
    //       authProvider: authProvider);
    //   await tester.pumpAndSettle(const Duration(seconds: 4));

    //   final emailField = find.byType(TextFormField).first;
    //   final passwordField = find.byType(TextFormField).at(1);
    //   final submitButton = find.byKey(const Key('login_submit_button'));

    //   // Enter valid email and password
    //   await tester.enterText(emailField, '<EMAIL>');
    //   await tester.enterText(passwordField, 'password');

    //   await tester.pump(const Duration(seconds: 1));

    //   // Tap login button
    //   await tester.tap(submitButton);
    //   await tester.pump(const Duration(milliseconds: 500));
    //   // Expect loader to be shown
    //   expect(find.byKey(const Key('login_submit_loader')), findsOneWidget);
    //   await tester.pump(const Duration(milliseconds: 1500));
    // });
  });
}
