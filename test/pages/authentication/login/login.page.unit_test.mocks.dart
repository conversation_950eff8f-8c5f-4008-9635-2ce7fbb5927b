// Mocks generated by <PERSON><PERSON><PERSON> 5.4.2 from annotations
// in malou/test/pages/authentication/login/login.page.unit_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i7;
import 'dart:ui' as _i9;

import 'package:malou/shared/helpers/api_result.model.dart' as _i2;
import 'package:malou/shared/models/organization.model.dart' as _i10;
import 'package:malou/shared/models/user.model.dart' as _i3;
import 'package:malou/shared/services/auth/auth.model.dart' as _i8;
import 'package:malou/shared/services/auth/auth.provider.dart' as _i4;
import 'package:malou/shared/services/restaurant/restaurant.provider.dart'
    as _i6;
import 'package:malou/shared/services/user/user.provider.dart' as _i5;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeApiResult_0<T> extends _i1.SmartFake implements _i2.ApiResult<T> {
  _FakeApiResult_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUser_1 extends _i1.SmartFake implements _i3.User {
  _FakeUser_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [AuthProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthProvider extends _i1.Mock implements _i4.AuthProvider {
  MockAuthProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  set userProvider(_i5.UserProvider? _userProvider) => super.noSuchMethod(
        Invocation.setter(
          #userProvider,
          _userProvider,
        ),
        returnValueForMissingStub: null,
      );
  @override
  set restaurantProvider(_i6.RestaurantProvider? _restaurantProvider) =>
      super.noSuchMethod(
        Invocation.setter(
          #restaurantProvider,
          _restaurantProvider,
        ),
        returnValueForMissingStub: null,
      );
  @override
  set token(String? _token) => super.noSuchMethod(
        Invocation.setter(
          #token,
          _token,
        ),
        returnValueForMissingStub: null,
      );
  @override
  bool get isLogged => (super.noSuchMethod(
        Invocation.getter(#isLogged),
        returnValue: false,
      ) as bool);
  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
      ) as bool);
  @override
  _i7.Future<dynamic> logout() => (super.noSuchMethod(
        Invocation.method(
          #logout,
          [],
        ),
        returnValue: _i7.Future<dynamic>.value(),
      ) as _i7.Future<dynamic>);
  @override
  _i7.Future<_i2.ApiResult<_i8.AuthResponse>> login(_i8.AuthPayload? payload) =>
      (super.noSuchMethod(
        Invocation.method(
          #login,
          [payload],
        ),
        returnValue: _i7.Future<_i2.ApiResult<_i8.AuthResponse>>.value(
            _FakeApiResult_0<_i8.AuthResponse>(
          this,
          Invocation.method(
            #login,
            [payload],
          ),
        )),
      ) as _i7.Future<_i2.ApiResult<_i8.AuthResponse>>);
  @override
  _i7.Future<bool> checkForSession() => (super.noSuchMethod(
        Invocation.method(
          #checkForSession,
          [],
        ),
        returnValue: _i7.Future<bool>.value(false),
      ) as _i7.Future<bool>);
  @override
  _i7.Future<_i2.ApiResult<dynamic>> forgotPassword(String? email) =>
      (super.noSuchMethod(
        Invocation.method(
          #forgotPassword,
          [email],
        ),
        returnValue:
            _i7.Future<_i2.ApiResult<dynamic>>.value(_FakeApiResult_0<dynamic>(
          this,
          Invocation.method(
            #forgotPassword,
            [email],
          ),
        )),
      ) as _i7.Future<_i2.ApiResult<dynamic>>);
  @override
  _i7.Future<_i3.User> getUserFromToken(String? token) => (super.noSuchMethod(
        Invocation.method(
          #getUserFromToken,
          [token],
        ),
        returnValue: _i7.Future<_i3.User>.value(_FakeUser_1(
          this,
          Invocation.method(
            #getUserFromToken,
            [token],
          ),
        )),
      ) as _i7.Future<_i3.User>);
  @override
  void addListener(_i9.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );
  @override
  void removeListener(_i9.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );
  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [UserProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserProvider extends _i1.Mock implements _i5.UserProvider {
  MockUserProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  set currentUser(_i3.User? user) => super.noSuchMethod(
        Invocation.setter(
          #currentUser,
          user,
        ),
        returnValueForMissingStub: null,
      );
  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
      ) as bool);
  @override
  void clearUser() => super.noSuchMethod(
        Invocation.method(
          #clearUser,
          [],
        ),
        returnValueForMissingStub: null,
      );
  @override
  dynamic setDisplaySemanticAnalyses(
    bool? value,
    String? restaurantId,
  ) =>
      super.noSuchMethod(Invocation.method(
        #setDisplaySemanticAnalyses,
        [
          value,
          restaurantId,
        ],
      ));
  @override
  void addDeviceToken(String? token) => super.noSuchMethod(
        Invocation.method(
          #setDeviceToken,
          [token],
        ),
        returnValueForMissingStub: null,
      );
  @override
  _i7.Future<void> updateUser(Map<String, dynamic>? data) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateUser,
          [data],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);
  @override
  _i7.Future<void> refetchUser() => (super.noSuchMethod(
        Invocation.method(
          #refetchUser,
          [],
        ),
        returnValue: _i7.Future<void>.value(),
        returnValueForMissingStub: _i7.Future<void>.value(),
      ) as _i7.Future<void>);
  @override
  _i10.Organization? getOrganizationById(String? organizationId) =>
      (super.noSuchMethod(Invocation.method(
        #getOrganizationById,
        [organizationId],
      )) as _i10.Organization?);
  @override
  void addListener(_i9.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );
  @override
  void removeListener(_i9.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );
  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
