# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.

name: Pull Request Checks

on:
    pull_request:
        branches: ["main", "release**"]

jobs:
    build:
        runs-on: ubuntu-latest

        steps:
            - uses: actions/checkout@v3

            # Note: This workflow uses the latest stable version of the Dart SDK.
            # You can specify other versions if desired, see documentation here:
            # https://github.com/dart-lang/setup-dart/blob/main/README.md
            - uses: dart-lang/setup-dart@v1

            - uses: subosito/flutter-action@v2
              with:
                  flutter-version: "3.29.1"
                  channel: "stable"
                  cache: true
                  cache-key: ${{ runner.os }}-flutter-${{ hashFiles('**/pubspec.lock') }}
                  cache-path: ${{ runner.tool_cache }}/flutter-${{ hashFiles('**/pubspec.lock') }}

            - run: flutter --version

            - name: Install dependencies
              run: flutter pub get

            - name: Verify formatting
              run: dart format --output=none .

            # - name: Analyze project source
            #   run: flutter analyze --no-fatal-infos --no-fatal-warnings

            # Your project will need to have tests in test/ and a dependency on
            # package:test for this step to succeed. Note that Flutter projects will
            # want to change this to 'flutter test'.
            # - name: Run tests
            #   run: flutter test test
