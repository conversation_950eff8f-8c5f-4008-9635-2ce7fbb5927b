name: ALL - PR Labeler
on:
  pull_request:
    types: [opened]
    branches: [main]

jobs:
  pr-labeler:
    runs-on: ubuntu-latest
    steps:
      - uses: TimonVS/pr-labeler-action@v3
        with:
          configuration-path: .github/pr-labeler.yml
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: PR Repo Labeler
        uses: TinkurLab/monorepo-pr-labeler-action@4.1.4
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
