name: Create Release Branch and Bump Version

on:
    workflow_dispatch:

jobs:
    create_release_branch_and_bump_version:
        name: Create Release Branch and Bump Version
        runs-on: ubuntu-latest

        steps:
            - name: Checkout code
              uses: actions/checkout@v3

            - uses: subosito/flutter-action@v2
              with:
                  flutter-version: "3.29.1"
                  channel: "stable"
                  cache: true
                  cache-key: ${{ runner.os }}-flutter-${{ hashFiles('**/pubspec.lock') }}
                  cache-path: ${{ runner.tool_cache }}/flutter-${{ hashFiles('**/pubspec.lock') }}

            - run: flutter --version

            - name: Get the current version from pubspec.yaml
              id: get_version_from_pubspec
              run: |
                  VERSION=$(grep "version: " pubspec.yaml | awk '{print $2}')
                  echo "::set-output name=version::$VERSION"

            - name: Bump version and create release branch
              run: |
                  NEW_BRANCH="release_v${{ steps.get_version_from_pubspec.outputs.version }}"
                  git config --global user.name "${GITHUB_ACTOR}"
                  git config --global user.email "${GITHUB_ACTOR}@users.noreply.github.com"
                  git checkout -b "$NEW_BRANCH"
                  git push origin "$NEW_BRANCH"

            - name: Create GitHub tag
              run: |
                  NEW_VERSION="${{ steps.get_version_from_pubspec.outputs.version }}"
                  git tag "v$NEW_VERSION"
                  git push origin "v$NEW_VERSION"

            - name: Update main branch minor version in pubspec.yaml
              run: |
                  git checkout main
                  NEW_VERSION="${{ steps.get_version_from_pubspec.outputs.version }}"
                  MAJOR_VERSION=$(echo "$NEW_VERSION" | awk -F. '{print $1}')
                  MINOR_VERSION=$(echo "$NEW_VERSION" | awk -F. '{print $2}')
                  NEW_MINOR_VERSION=$((MINOR_VERSION + 1))
                  sed -i "s/version: $NEW_VERSION/version: $MAJOR_VERSION.$NEW_MINOR_VERSION.0/g" pubspec.yaml
                  git config --global user.name "${GITHUB_ACTOR}"
                  git config --global user.email "${GITHUB_ACTOR}@users.noreply.github.com"
                  git commit -am "Bump version to $MAJOR_VERSION.$NEW_MINOR_VERSION"
                  git push origin main
