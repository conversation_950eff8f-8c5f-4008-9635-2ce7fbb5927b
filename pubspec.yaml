name: malou
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.29.0
environment:
  sdk: ">=3.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  animated_text_kit: ^4.2.2
  app_settings: ^6.1.1
  audioplayers: ^5.1.0
  aws_cloudwatch: ^1.0.1
  cached_network_image: ^3.2.3
  camera: ^0.10.5+1
  carousel_slider: ^5.0.0
  collection: ^1.17.1
  connectivity_plus: ^5.0.2
  cupertino_icons: ^1.0.2
  device_preview: ^1.1.0
  diacritic: ^0.1.3
  dio: ^5.1.1
  dropdown_button2: ^2.3.9
  email_validator: ^2.1.17
  firebase_core:
  firebase_messaging:
  flutter:
    sdk: flutter
  flutter_animate: ^4.2.0+1
  flutter_app_badge: ^2.0.2
  flutter_keyboard_visibility: ^6.0.0
  flutter_launcher_icons: ^0.13.1
  flutter_local_notifications: ^17.2.1+2
  flutter_localizations:
    sdk: flutter
  flutter_slidable: 3.1.2
  flutter_speed_dial: ^7.0.0
  flutter_svg: ^2.0.5
  flutter_reorderable_grid_view: ^5.4.1
  go_router: ^10.1.2
  google_fonts: ^4.0.3
  growthbook_sdk_flutter: ^3.9.2
  heap_flutter_bridge: ^0.1.0
  html: ^0.15.4
  http: null
  infinite_scroll_pagination: ^4.0.0
  intl: any
  json_annotation: ^4.8.1
  just_audio: ^0.9.33
  jwt_decoder: ^2.0.1
  objectid: ^3.0.0
  package_info_plus: ^4.1.0
  page_transition: ^2.0.9
  path: ^1.9.1
  path_provider: ^2.0.14
  photo_manager: ^3.7.1
  photo_manager_image_provider: ^2.2.0
  provider: ^6.0.5
  pull_to_refresh: ^2.0.0
  pusher_channels_flutter: ^2.2.0
  reactive_forms: ^16.0.1
  rxdart: ^0.27.7
  sentry_flutter: ^8.14.0
  shared_preferences: ^2.1.0
  shimmer: ^2.0.0
  skeleton_loader: ^2.0.0+4
  story: ^1.1.0
  timeago: any
  transparent_image: ^2.0.1
  upgrader: ^8.1.0
  url_launcher: ^6.3.1
  uuid: ^3.0.7
  vector_math: ^2.1.4
  video_player: ^2.9.2
  video_thumbnail:
    git:
      url: https://github.com/malou-io/video_thumbnail.git
      ref: master
dependency_overrides:
  intl: ^0.19.0
  path: ^1.9.1

dev_dependencies:
  build_runner: ^2.4.6
  flutter_lints: ^2.0.0
  flutter_test:
    sdk: flutter
  json_serializable: ^6.6.2
  mockito: ^5.4.2
  sentry_dart_plugin: ^2.0.0
  test: ^1.24.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter packages.
flutter:
  generate: true

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
  - assets/logos/
  - assets/illustrations/
  - assets/other/
  - assets/icons/
  - assets/fonts/google_fonts/
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
  - family: maloufont
    fonts:
    - asset: assets/fonts/maloufont.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

sentry:
  upload_debug_symbols: true
  upload_source_maps: true
  upload_sources: true
  wait_for_processing: true
  log_level: error
  commits: auto
  ignore_missing: true
  project: malou-mobile
  org: malou
  auth_token: 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
