# Malou

Malou is a mobile app developed with Flutter that allows to use more mobile friendly feature from app.malou.io

## Installation

1. Install Flutter (Version 3 or Higher): [https://flutter.dev/docs/get-started/install](https://flutter.dev/docs/get-started/install)
2. Clone the repository: `https://github.com/malou-io/malou-app-mobile.git`
3. Install dependencies: `flutter pub get`
4. Run the app: `flutter run`

## Usage

1. Launch the app on your device or simulator.
2. Enjoy the app!

## Configuration

The app includes different environments for development and production. To switch between environments, use the `--flavor` flag when running the app:

- Development: `flutter run --flavor development "--dart-define-from-file=env.json`
- Production: `flutter run --flavor Production "--dart-define-from-file=env.prod.json`


Or just launch it from vscode available configs.

Happy coding ! 🔥

flutter pub run build_runner watch --delete-conflicting-outputs

## Local work

To test the app locally, you need to create a file named `env.local.json` in the root of the project and add the following content:

```json
{
    "CURRENT_ENVIRONNEMENT": "local",
    "API_URI": "http://************:3000/api/v1", // Replace with your local API URL (ipconfig getifaddr en0 for Mac)
    "WEB_URI": "https://v3.omni.malou.io",
    "PUSHER_KEY": "<key>",
    "APP_NAME": "development",
    "HEAP_ID": "2391935765",
    "IS_RUNNING_ON_MY_COMPUTER": "true",
    "GROWTHBOOK_HOST_URL": "https://experimentation.malou.io:3300/",
    "GROWTHBOOK_API_KEY": "<key>"
}
```